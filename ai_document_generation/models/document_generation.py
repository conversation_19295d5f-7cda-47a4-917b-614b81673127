from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging
import json
import re
import base64
import requests
from datetime import datetime

_logger = logging.getLogger(__name__)


class DocumentGeneration(models.Model):
    _name = 'document.generation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Document Generation'
    _order = 'create_date desc'

    name = fields.Char(string='Reference', readonly=True, copy=False, default='New')

    # Related records
    template_id = fields.Many2one('document.template', string='Template', required=True,
                                 ondelete='restrict', tracking=True)
    document_request_id = fields.Many2one('legal.document.request', string='Document Request',
                                         ondelete='cascade', tracking=True)

    # Document content
    emmet_content = fields.Text(string='Emmet Content', tracking=True,
                               help="Minified HTML content received from AI")
    html_content = fields.Html(string='HTML Content', sanitize=False, tracking=True,
                              help="Expanded HTML content for the document")

    # Document metadata
    language_id = fields.Many2one('res.lang', string='Language', tracking=True)

    # Document files
    pdf_file = fields.Binary(string='PDF File', attachment=True)
    pdf_filename = fields.Char(string='PDF Filename')

    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generated', 'Generated'),
        ('review', 'Under Review'),
        ('approved', 'Approved'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    # Review
    review_ids = fields.One2many('document.review', 'generation_id', string='Reviews')
    review_count = fields.Integer(string='Review Count', compute='_compute_review_count')

    # Delivery
    delivery_date = fields.Datetime(string='Delivery Date', tracking=True)
    delivery_email = fields.Char(string='Delivery Email', tracking=True)
    delivery_phone = fields.Char(string='Delivery Phone', tracking=True)

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    @api.depends('review_ids')
    def _compute_review_count(self):
        for record in self:
            record.review_count = len(record.review_ids)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('document.generation') or 'New'

        return super().create(vals_list)

    def action_generate_document(self):
        """Generate document from AI"""
        self.ensure_one()

        if not self.document_request_id:
            raise ValidationError(_("Document request is required to generate a document"))

        # Get the template
        template = self.template_id

        # Get the AI prompt
        prompt = template.ai_prompt or "Generate a legal document in Emmet format based on the provided information."

        # Get the document request data
        request_data = self._prepare_request_data()

        # Call AI to generate the document
        try:
            emmet_content = self._call_ai_for_document(prompt, request_data)

            # Convert Emmet to HTML
            html_content = self._convert_emmet_to_html(emmet_content)

            # Update the record
            self.write({
                'emmet_content': emmet_content,
                'html_content': html_content,
                'state': 'generated',
            })

            # Create a review
            self.env['document.review'].create({
                'generation_id': self.id,
                'name': _('Initial Review'),
                'state': 'pending',
            })

            # Update the document request
            self.document_request_id.write({
                'state': 'document_generated',
            })

            return {
                'type': 'ir.actions.act_window',
                'res_model': 'document.generation',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'current',
            }

        except Exception as e:
            raise ValidationError(_("Error generating document: %s") % str(e))

    def _prepare_request_data(self):
        """Prepare data from the document request for AI"""
        self.ensure_one()

        request = self.document_request_id

        # Get conversation history
        conversation = self.env['legal.document.conversation'].search([
            ('request_id', '=', request.id)
        ], limit=1)

        conversation_history = []
        if conversation:
            for message in conversation.message_ids:
                conversation_history.append({
                    'role': 'user' if message.is_user else 'assistant',
                    'content': message.content,
                })

        # Get form responses
        form_responses = []
        for bridge in self.env['ai.survey.bridge'].search([
            ('document_request_id', '=', request.id)
        ]):
            if bridge.survey_id:
                # Get the latest user input (response)
                user_input = self.env['survey.user_input'].search([
                    ('survey_id', '=', bridge.survey_id.id),
                    ('state', '=', 'done')
                ], limit=1, order='create_date desc')

                if user_input:
                    # Get the answers
                    answers = {}
                    for answer in self.env['survey.user_input.line'].search([
                        ('user_input_id', '=', user_input.id)
                    ]):
                        question = answer.question_id

                        # Skip page/section questions
                        if question.is_page:
                            continue

                        # Get the answer value based on question type
                        if question.question_type == 'text_box':
                            value = answer.value_text_box
                        elif question.question_type == 'numerical_box':
                            value = answer.value_numerical_box
                        elif question.question_type == 'date':
                            value = answer.value_date
                        elif question.question_type == 'datetime':
                            value = answer.value_datetime
                        elif question.question_type in ['simple_choice', 'multiple_choice']:
                            if answer.suggested_answer_id:
                                value = answer.suggested_answer_id.value
                            else:
                                value = None
                        else:
                            value = None

                        if value is not None:
                            answers[question.title] = value

                    form_responses.append({
                        'survey_title': bridge.survey_id.title,
                        'answers': answers,
                    })

        # Compile all data
        data = {
            'request_id': request.id,
            'service': request.service_id.name,
            'language': request.language_id.name,
            'customer': {
                'name': request.partner_id.name,
                'email': request.partner_id.email,
                'phone': request.partner_id.phone,
                'address': request.partner_id.contact_address,
            },
            'conversation_history': conversation_history,
            'form_responses': form_responses,
        }

        return data

    def _call_ai_for_document(self, prompt, data):
        """Call AI to generate the document in Emmet format"""
        # This is a placeholder for the actual AI call
        # In a real implementation, this would call the AI API

        # For now, return a simple Emmet example
        return "div.document>h1{Document Title}+p{This is a sample document generated for " + data['customer']['name'] + ".}"

    def _convert_emmet_to_html(self, emmet_content):
        """Convert Emmet to HTML"""
        # This is a simplified implementation
        # In a real implementation, this would use a proper Emmet parser

        # For now, just do some basic replacements
        html = emmet_content

        # Replace div.class with <div class="class">
        html = re.sub(r'div\.([a-zA-Z0-9_-]+)', r'<div class="\1">', html)

        # Replace h1{text} with <h1>text</h1>
        html = re.sub(r'h1{([^}]+)}', r'<h1>\1</h1>', html)

        # Replace p{text} with <p>text</p>
        html = re.sub(r'p{([^}]+)}', r'<p>\1</p>', html)

        # Replace > with closing and opening tags
        html = html.replace('>', '</div>')

        # Add final closing tag
        html += '</div>'

        return html

    def action_send_for_review(self):
        """Send document for review"""
        self.ensure_one()

        if self.state != 'generated':
            raise ValidationError(_("Document must be in 'Generated' state to send for review"))

        self.write({'state': 'review'})

        # Notify reviewers
        self.message_post(
            body=_("Document has been sent for review"),
            message_type='notification',
            subtype_xmlid='mail.mt_comment',
        )

    def action_approve_document(self):
        """Approve the document"""
        self.ensure_one()

        if self.state != 'review':
            raise ValidationError(_("Document must be in 'Under Review' state to approve"))

        # Generate PDF
        self._generate_pdf()

        self.write({'state': 'approved'})

        # Notify
        self.message_post(
            body=_("Document has been approved"),
            message_type='notification',
            subtype_xmlid='mail.mt_comment',
        )

    def _generate_pdf(self):
        """Generate PDF from HTML content"""
        self.ensure_one()

        # This is a placeholder for the actual PDF generation
        # In a real implementation, this would use wkhtmltopdf or another PDF generator

        # For now, just create a dummy PDF
        dummy_pdf = base64.b64encode(b'%PDF-1.5\nDummy PDF content\n%%EOF')

        filename = f"{self.name.replace('/', '_')}.pdf"

        self.write({
            'pdf_file': dummy_pdf,
            'pdf_filename': filename,
        })

    def action_deliver_document(self):
        """Deliver the document to the customer"""
        self.ensure_one()

        if self.state != 'approved':
            raise ValidationError(_("Document must be in 'Approved' state to deliver"))

        if not self.pdf_file:
            raise ValidationError(_("PDF file is required for delivery"))

        # Get delivery information
        partner = self.document_request_id.partner_id
        email = partner.email
        phone = partner.phone

        if not email and not phone:
            raise ValidationError(_("Email or phone is required for delivery"))

        # Send email
        if email:
            self._send_email(email)

        # Send WhatsApp
        if phone:
            self._send_whatsapp(phone)

        self.write({
            'state': 'delivered',
            'delivery_date': fields.Datetime.now(),
            'delivery_email': email,
            'delivery_phone': phone,
        })

        # Update the document request
        self.document_request_id.write({
            'state': 'completed',
        })

        # Notify
        self.message_post(
            body=_("Document has been delivered to the customer"),
            message_type='notification',
            subtype_xmlid='mail.mt_comment',
        )

    def _send_email(self, email):
        """Send document via email"""
        self.ensure_one()

        template = self.env.ref('ai_document_generation.email_template_document_delivery')
        if template:
            template.send_mail(self.id, force_send=True)

    def _send_whatsapp(self, phone):
        """Send document via WhatsApp"""
        self.ensure_one()

        # This is a placeholder for the actual WhatsApp integration
        # In a real implementation, this would call the WhatsApp API

        _logger.info(f"Sending document {self.name} to {phone} via WhatsApp")

    def action_view_reviews(self):
        """View document reviews"""
        self.ensure_one()

        return {
            'name': _('Reviews'),
            'type': 'ir.actions.act_window',
            'res_model': 'document.review',
            'view_mode': 'tree,form',
            'domain': [('generation_id', '=', self.id)],
            'context': {'default_generation_id': self.id},
        }

    def action_preview_document(self):
        """Preview the document"""
        self.ensure_one()

        if not self.html_content:
            raise ValidationError(_("No content available for preview"))

        return {
            'type': 'ir.actions.act_url',
            'url': f'/document/preview/{self.id}',
            'target': 'new',
        }

    def action_download_pdf(self):
        """Download the PDF document"""
        self.ensure_one()

        if not self.pdf_file:
            raise ValidationError(_("No PDF file available for download"))

        return {
            'type': 'ir.actions.act_url',
            'url': f'/document/download/{self.id}',
            'target': 'self',
        }

    def action_cancel(self):
        """Cancel the document generation"""
        self.ensure_one()

        if self.state == 'delivered':
            raise ValidationError(_("Cannot cancel a delivered document"))

        self.write({'state': 'cancelled'})

        # Notify
        self.message_post(
            body=_("Document generation has been cancelled"),
            message_type='notification',
            subtype_xmlid='mail.mt_comment',
        )
