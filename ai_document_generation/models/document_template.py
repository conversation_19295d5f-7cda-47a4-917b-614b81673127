from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class DocumentTemplate(models.Model):
    _name = 'document.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Document Template'
    _order = 'name'

    name = fields.Char(string='Name', required=True, tracking=True)
    code = fields.Char(string='Code', required=True, tracking=True,
                      help="Unique code to identify this template")
    description = fields.Text(string='Description', tracking=True)

    # Template content
    html_template = fields.Html(string='HTML Template', sanitize=False, tracking=True)
    css_styles = fields.Text(string='CSS Styles', tracking=True)

    # Document settings
    page_size = fields.Selection([
        ('a4', 'A4'),
        ('letter', 'Letter'),
        ('legal', 'Legal'),
    ], string='Page Size', default='a4', required=True, tracking=True)

    orientation = fields.Selection([
        ('portrait', 'Portrait'),
        ('landscape', 'Landscape'),
    ], string='Orientation', default='portrait', required=True, tracking=True)

    margin_top = fields.Float(string='Top Margin (mm)', default=20, required=True)
    margin_right = fields.Float(string='Right Margin (mm)', default=20, required=True)
    margin_bottom = fields.Float(string='Bottom Margin (mm)', default=20, required=True)
    margin_left = fields.Float(string='Left Margin (mm)', default=20, required=True)

    # Language support
    language_id = fields.Many2one('res.lang', string='Language', tracking=True)

    # Related fields
    service_id = fields.Many2one('legal.document.service', string='Service', tracking=True)

    # Status
    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    # AI prompt
    ai_prompt = fields.Text(string='AI Prompt', tracking=True,
                           help="Prompt to send to AI for document generation")

    # Document generation count
    generation_count = fields.Integer(string='Generated Documents', compute='_compute_generation_count')

    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Template code must be unique!')
    ]

    @api.depends('name')
    def _compute_generation_count(self):
        for record in self:
            record.generation_count = self.env['document.generation'].search_count([
                ('template_id', '=', record.id)
            ])

    @api.constrains('code')
    def _check_code(self):
        for record in self:
            if record.code:
                if not record.code.isalnum():
                    raise ValidationError(_("Template code must contain only letters and numbers"))

    def action_view_generations(self):
        self.ensure_one()
        return {
            'name': _('Generated Documents'),
            'type': 'ir.actions.act_window',
            'res_model': 'document.generation',
            'view_mode': 'tree,form',
            'domain': [('template_id', '=', self.id)],
            'context': {'default_template_id': self.id},
        }

    def get_template_variables(self):
        """Get the variables used in the template"""
        self.ensure_one()

        variables = []
        if self.html_template:
            import re
            # Find all variables in the format {{ variable }}
            pattern = r'{{\s*([a-zA-Z0-9_\.]+)\s*}}'
            matches = re.findall(pattern, self.html_template)

            for match in matches:
                if match not in variables:
                    variables.append(match)

        return variables

    def preview_template(self):
        """Preview the template with sample data"""
        self.ensure_one()

        return {
            'name': _('Preview Template'),
            'type': 'ir.actions.act_window',
            'res_model': 'document.template.preview.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_template_id': self.id},
        }
