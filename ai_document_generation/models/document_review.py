from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class DocumentReview(models.Model):
    _name = 'document.review'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Document Review'
    _order = 'create_date desc'

    name = fields.Char(string='Title', required=True, tracking=True)
    generation_id = fields.Many2one('document.generation', string='Document', required=True,
                                   ondelete='cascade', tracking=True)

    # Review details
    reviewer_id = fields.Many2one('res.users', string='Reviewer', default=lambda self: self.env.user,
                                 required=True, tracking=True)
    review_date = fields.Datetime(string='Review Date', tracking=True)

    # Review content
    original_content = fields.Html(string='Original Content', sanitize=False, tracking=True)
    modified_content = fields.Html(string='Modified Content', sanitize=False, tracking=True)

    # Review comments
    comments = fields.Text(string='Comments', tracking=True)

    # Status
    state = fields.Selection([
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected')
    ], string='Status', default='pending', tracking=True)

    # Document request for reference
    document_request_id = fields.Many2one('legal.document.request', related='generation_id.document_request_id',
                                         string='Document Request', store=True)

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    @api.model_create_multi
    def create(self, vals_list):
        reviews = super().create(vals_list)

        # Copy the original content from the document generation
        for review in reviews:
            if not review.original_content and review.generation_id:
                review.original_content = review.generation_id.html_content

        return reviews

    def action_start_review(self):
        """Start the review process"""
        self.ensure_one()

        if self.state != 'pending':
            raise ValidationError(_("Review must be in 'Pending' state to start"))

        # Copy the original content to modified content if not already set
        if not self.modified_content:
            self.modified_content = self.original_content

        self.write({
            'state': 'in_progress',
            'review_date': fields.Datetime.now(),
        })

    def action_complete_review(self):
        """Complete the review process"""
        self.ensure_one()

        if self.state != 'in_progress':
            raise ValidationError(_("Review must be in 'In Progress' state to complete"))

        if not self.modified_content:
            raise ValidationError(_("Modified content is required to complete the review"))

        # Update the document with the modified content
        self.generation_id.write({
            'html_content': self.modified_content,
        })

        self.write({
            'state': 'completed',
        })

        # Notify
        self.message_post(
            body=_("Review has been completed"),
            message_type='notification',
            subtype_xmlid='mail.mt_comment',
        )

    def action_reject_document(self):
        """Reject the document"""
        self.ensure_one()

        if self.state not in ['pending', 'in_progress']:
            raise ValidationError(_("Review must be in 'Pending' or 'In Progress' state to reject"))

        if not self.comments:
            raise ValidationError(_("Comments are required when rejecting a document"))

        self.write({
            'state': 'rejected',
            'review_date': fields.Datetime.now(),
        })

        # Update the document generation
        self.generation_id.write({
            'state': 'draft',
        })

        # Notify
        self.message_post(
            body=_("Document has been rejected"),
            message_type='notification',
            subtype_xmlid='mail.mt_comment',
        )

    def action_request_changes(self):
        """Request changes from AI"""
        self.ensure_one()

        if self.state not in ['pending', 'in_progress', 'rejected']:
            raise ValidationError(_("Review must be in 'Pending', 'In Progress', or 'Rejected' state to request changes"))

        if not self.comments:
            raise ValidationError(_("Comments are required when requesting changes"))

        # Create a new message in the conversation
        conversation = self.env['legal.document.conversation'].search([
            ('request_id', '=', self.document_request_id.id)
        ], limit=1)

        if conversation:
            # Create expert message
            self.env['legal.document.message'].create({
                'conversation_id': conversation.id,
                'content': _("Expert review comments: %s") % self.comments,
                'is_user': False,
                'is_expert': True,
            })

            # Update document request
            self.document_request_id.write({
                'state': 'in_progress',
            })

            # Notify
            self.message_post(
                body=_("Changes have been requested from AI"),
                message_type='notification',
                subtype_xmlid='mail.mt_comment',
            )
        else:
            raise ValidationError(_("No conversation found for this document request"))
