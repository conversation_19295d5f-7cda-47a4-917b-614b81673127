<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Document Generation Views -->
    <record id="view_document_generation_form" model="ir.ui.view">
        <field name="name">document.generation.form</field>
        <field name="model">document.generation</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_generate_document" string="Generate Document" type="object" class="oe_highlight"
                            invisible="state != 'draft'"/>
                    <button name="action_send_for_review" string="Send for Review" type="object" class="oe_highlight"
                            invisible="state != 'generated'"/>
                    <button name="action_approve_document" string="Approve" type="object" class="oe_highlight"
                            invisible="state != 'review'" groups="ai_document_generation.group_document_reviewer"/>
                    <button name="action_deliver_document" string="Deliver" type="object" class="oe_highlight"
                            invisible="state != 'approved'"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            invisible="state in ['delivered', 'cancelled']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,generated,review,approved,delivered"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_reviews" type="object" class="oe_stat_button" icon="fa-check-square-o">
                            <field name="review_count" widget="statinfo" string="Reviews"/>
                        </button>
                        <button name="action_preview_document" type="object" class="oe_stat_button" icon="fa-eye"
                                invisible="state == 'draft'">
                            <span>Preview</span>
                        </button>
                        <button name="action_download_pdf" type="object" class="oe_stat_button" icon="fa-download"
                                invisible="not pdf_file">
                            <span>Download PDF</span>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="template_id" options="{'no_create': True}" readonly="state != 'draft'"/>
                            <field name="document_request_id" options="{'no_create': True}" readonly="state != 'draft'"/>
                            <field name="language_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="delivery_date" readonly="1" invisible="not delivery_date"/>
                            <field name="delivery_email" readonly="1" invisible="not delivery_email"/>
                            <field name="delivery_phone" readonly="1" invisible="not delivery_phone"/>
                            <!-- Hidden fields for conditions -->
                            <field name="pdf_file" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="HTML Content" name="html_content" invisible="state == 'draft'">
                            <field name="html_content" widget="html" options="{'collaborative': true, 'resizable': true}"
                                   readonly="state in ['approved', 'delivered', 'cancelled']"/>
                        </page>
                        <page string="Emmet Content" name="emmet_content" invisible="state == 'draft'" groups="ai_document_generation.group_document_generation_manager">
                            <field name="emmet_content" widget="text" readonly="1"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_document_generation_tree" model="ir.ui.view">
        <field name="name">document.generation.tree</field>
        <field name="model">document.generation</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" decoration-muted="state == 'cancelled'" decoration-success="state == 'delivered'">
                <field name="name"/>
                <field name="template_id"/>
                <field name="document_request_id"/>
                <field name="language_id"/>
                <field name="review_count"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_document_generation_search" model="ir.ui.view">
        <field name="name">document.generation.search</field>
        <field name="model">document.generation</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="template_id"/>
                <field name="document_request_id"/>
                <field name="language_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Generated" name="generated" domain="[('state', '=', 'generated')]"/>
                <filter string="Under Review" name="review" domain="[('state', '=', 'review')]"/>
                <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                <filter string="Delivered" name="delivered" domain="[('state', '=', 'delivered')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Template" name="groupby_template" context="{'group_by': 'template_id'}"/>
                    <filter string="Document Request" name="groupby_request" context="{'group_by': 'document_request_id'}"/>
                    <filter string="Language" name="groupby_language" context="{'group_by': 'language_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_document_generation" model="ir.actions.act_window">
        <field name="name">Generated Documents</field>
        <field name="res_model">document.generation</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_document_generation_documents"
              name="Generated Documents"
              parent="menu_document_generation"
              action="action_document_generation"
              sequence="10"/>
</odoo>
