<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Document Review Views -->
    <record id="view_document_review_form" model="ir.ui.view">
        <field name="name">document.review.form</field>
        <field name="model">document.review</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_start_review" string="Start Review" type="object" class="oe_highlight"
                            invisible="state != 'pending'"/>
                    <button name="action_complete_review" string="Complete Review" type="object" class="oe_highlight"
                            invisible="state != 'in_progress'"/>
                    <button name="action_reject_document" string="Reject Document" type="object"
                            invisible="state not in ['pending', 'in_progress']"/>
                    <button name="action_request_changes" string="Request Changes from AI" type="object"
                            invisible="state not in ['pending', 'in_progress', 'rejected']"/>
                    <field name="state" widget="statusbar" statusbar_visible="pending,in_progress,completed,rejected"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Review Title"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="generation_id" options="{'no_create': True}"/>
                            <field name="document_request_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="reviewer_id" options="{'no_create': True}"/>
                            <field name="review_date" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Comments" name="comments">
                            <field name="comments" placeholder="Enter your review comments here..."/>
                        </page>
                        <page string="Original Content" name="original_content">
                            <field name="original_content" widget="html" readonly="1"/>
                        </page>
                        <page string="Modified Content" name="modified_content" invisible="state == 'pending'">
                            <field name="modified_content" widget="html" options="{'collaborative': true, 'resizable': true}"
                                   readonly="state in ['completed', 'rejected']"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_document_review_tree" model="ir.ui.view">
        <field name="name">document.review.tree</field>
        <field name="model">document.review</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'pending'" decoration-warning="state == 'in_progress'" decoration-success="state == 'completed'" decoration-danger="state == 'rejected'">
                <field name="name"/>
                <field name="generation_id"/>
                <field name="reviewer_id"/>
                <field name="review_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_document_review_search" model="ir.ui.view">
        <field name="name">document.review.search</field>
        <field name="model">document.review</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="generation_id"/>
                <field name="reviewer_id"/>
                <field name="document_request_id"/>
                <separator/>
                <filter string="Pending" name="pending" domain="[('state', '=', 'pending')]"/>
                <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                <filter string="My Reviews" name="my_reviews" domain="[('reviewer_id', '=', uid)]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Document" name="groupby_generation" context="{'group_by': 'generation_id'}"/>
                    <filter string="Reviewer" name="groupby_reviewer" context="{'group_by': 'reviewer_id'}"/>
                    <filter string="Document Request" name="groupby_request" context="{'group_by': 'document_request_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_document_review" model="ir.actions.act_window">
        <field name="name">Document Reviews</field>
        <field name="res_model">document.review</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_my_reviews': 1}</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_document_review"
              name="Reviews"
              parent="menu_document_generation"
              action="action_document_review"
              sequence="20"/>
</odoo>
