<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Document Template Views -->
    <record id="view_document_template_form" model="ir.ui.view">
        <field name="name">document.template.form</field>
        <field name="model">document.template</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_generations" type="object" class="oe_stat_button" icon="fa-file-text-o">
                            <field name="generation_count" widget="statinfo" string="Documents"/>
                        </button>
                        <button name="preview_template" type="object" class="oe_stat_button" icon="fa-eye">
                            <span>Preview</span>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Template Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="service_id"/>
                            <field name="language_id"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="page_size"/>
                            <field name="orientation"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Template description..."/>
                        </page>
                        <page string="HTML Template" name="html_template">
                            <field name="html_template" widget="html" options="{'collaborative': true, 'resizable': true}"/>
                        </page>
                        <page string="CSS Styles" name="css_styles">
                            <field name="css_styles" widget="text"/>
                        </page>
                        <page string="Margins" name="margins">
                            <group>
                                <group>
                                    <field name="margin_top"/>
                                    <field name="margin_bottom"/>
                                </group>
                                <group>
                                    <field name="margin_left"/>
                                    <field name="margin_right"/>
                                </group>
                            </group>
                        </page>
                        <page string="AI Prompt" name="ai_prompt">
                            <field name="ai_prompt" placeholder="Prompt to send to AI for document generation..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_document_template_tree" model="ir.ui.view">
        <field name="name">document.template.tree</field>
        <field name="model">document.template</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="code"/>
                <field name="service_id"/>
                <field name="language_id"/>
                <field name="generation_count"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="view_document_template_search" model="ir.ui.view">
        <field name="name">document.template.search</field>
        <field name="model">document.template</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="service_id"/>
                <field name="language_id"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Service" name="groupby_service" context="{'group_by': 'service_id'}"/>
                    <filter string="Language" name="groupby_language" context="{'group_by': 'language_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_document_template" model="ir.actions.act_window">
        <field name="name">Document Templates</field>
        <field name="res_model">document.template</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_document_generation_root"
              name="Document Generation"
              sequence="50"/>

    <menuitem id="menu_document_generation"
              name="Documents"
              parent="menu_document_generation_root"
              sequence="10"/>

    <menuitem id="menu_document_template"
              name="Templates"
              parent="menu_document_generation_root"
              action="action_document_template"
              sequence="20"/>
</odoo>
