<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Document Preview Template -->
    <template id="document_preview" name="Document Preview">
        <t t-call="website.layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                                <h3 class="mb-0"><t t-esc="document.name"/></h3>
                                <div>
                                    <a t-if="document.pdf_file" t-att-href="'/document/download/%s' % document.id" class="btn btn-light btn-sm">
                                        <i class="fa fa-download me-1"></i> Download PDF
                                    </a>
                                    <a href="#" onclick="window.print(); return false;" class="btn btn-light btn-sm ms-2">
                                        <i class="fa fa-print me-1"></i> Print
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="document-preview">
                                    <t t-raw="document.html_content"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Extend Legal Document Portal Detail -->
    <template id="portal_legal_document_extension" name="Portal Legal Document Extension" inherit_id="ai_legal_document_core.portal_my_legal_document">
        <xpath expr="//div[@class='d-flex flex-wrap']" position="inside">
            <button t-if="document_request.state == 'in_progress'" type="button" class="btn btn-warning me-2 mb-2 generate-document-btn" t-att-data-request-id="document_request.id">
                <i class="fa fa-file-text-o me-1"></i> Generate Document
            </button>
        </xpath>
    </template>

    <!-- Template Selection Modal -->
    <template id="document_template_selection_modal" name="Document Template Selection Modal">
        <div class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Select Document Template</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <t t-foreach="templates" t-as="template">
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <h5 class="card-title"><t t-esc="template.name"/></h5>
                                            <p class="card-text"><t t-esc="template.description"/></p>
                                        </div>
                                        <div class="card-footer">
                                            <button type="button" class="btn btn-primary select-template-btn" t-att-data-template-id="template.id">
                                                Select
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </template>
</odoo>
