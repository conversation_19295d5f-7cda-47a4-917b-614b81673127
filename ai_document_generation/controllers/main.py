from odoo import http, _
from odoo.http import request
import logging
import json
import base64

_logger = logging.getLogger(__name__)


class DocumentGenerationController(http.Controller):
    
    @http.route(['/document/preview/<int:document_id>'], type='http', auth='user', website=True)
    def preview_document(self, document_id, **kw):
        """Preview a generated document"""
        document = request.env['document.generation'].sudo().browse(document_id)
        
        if not document.exists():
            return request.render('website.404')
        
        values = {
            'document': document,
        }
        
        return request.render('ai_document_generation.document_preview', values)
    
    @http.route(['/document/download/<int:document_id>'], type='http', auth='user', website=True)
    def download_document(self, document_id, **kw):
        """Download a document as PDF"""
        document = request.env['document.generation'].sudo().browse(document_id)
        
        if not document.exists() or not document.pdf_file:
            return request.render('website.404')
        
        return request.make_response(
            base64.b64decode(document.pdf_file),
            headers=[
                ('Content-Type', 'application/pdf'),
                ('Content-Disposition', f'attachment; filename="{document.pdf_filename}"'),
            ]
        )
    
    @http.route(['/document/generate'], type='json', auth='user')
    def generate_document(self, **post):
        """Generate a document from AI"""
        document_request_id = post.get('document_request_id')
        template_id = post.get('template_id')
        
        if not document_request_id or not template_id:
            return {'error': 'Document request and template are required'}
        
        try:
            # Create document generation record
            document = request.env['document.generation'].sudo().create({
                'document_request_id': document_request_id,
                'template_id': template_id,
                'language_id': request.env['legal.document.request'].browse(document_request_id).language_id.id,
            })
            
            # Generate the document
            document.action_generate_document()
            
            return {
                'success': True,
                'document_id': document.id,
                'message': _('Document generated successfully'),
            }
        except Exception as e:
            _logger.error("Error generating document: %s", str(e))
            return {'error': str(e)}
    
    @http.route(['/document/update'], type='json', auth='user')
    def update_document(self, **post):
        """Update a document's HTML content"""
        document_id = post.get('document_id')
        html_content = post.get('html_content')
        
        if not document_id or not html_content:
            return {'error': 'Document ID and HTML content are required'}
        
        try:
            document = request.env['document.generation'].sudo().browse(document_id)
            
            if not document.exists():
                return {'error': 'Document not found'}
            
            document.write({
                'html_content': html_content,
            })
            
            return {
                'success': True,
                'message': _('Document updated successfully'),
            }
        except Exception as e:
            _logger.error("Error updating document: %s", str(e))
            return {'error': str(e)}
    
    @http.route(['/document/review/update'], type='json', auth='user')
    def update_review(self, **post):
        """Update a document review"""
        review_id = post.get('review_id')
        modified_content = post.get('modified_content')
        comments = post.get('comments')
        
        if not review_id:
            return {'error': 'Review ID is required'}
        
        try:
            review = request.env['document.review'].sudo().browse(review_id)
            
            if not review.exists():
                return {'error': 'Review not found'}
            
            vals = {}
            if modified_content:
                vals['modified_content'] = modified_content
            if comments:
                vals['comments'] = comments
            
            if vals:
                review.write(vals)
            
            return {
                'success': True,
                'message': _('Review updated successfully'),
            }
        except Exception as e:
            _logger.error("Error updating review: %s", str(e))
            return {'error': str(e)}
