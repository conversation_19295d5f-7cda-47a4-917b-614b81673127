<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Document Generation Security Groups -->
        <record id="group_document_generation_user" model="res.groups">
            <field name="name">Document Generation User</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <record id="group_document_generation_manager" model="res.groups">
            <field name="name">Document Generation Manager</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('group_document_generation_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
        
        <record id="group_document_reviewer" model="res.groups">
            <field name="name">Document Reviewer</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('group_document_generation_user'))]"/>
        </record>
    </data>
    
    <data noupdate="1">
        <!-- Multi-company rules -->
        <record id="document_template_comp_rule" model="ir.rule">
            <field name="name">Document Template: multi-company</field>
            <field name="model_id" ref="model_document_template"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="document_generation_comp_rule" model="ir.rule">
            <field name="name">Document Generation: multi-company</field>
            <field name="model_id" ref="model_document_generation"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="document_review_comp_rule" model="ir.rule">
            <field name="name">Document Review: multi-company</field>
            <field name="model_id" ref="model_document_review"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <!-- Document reviewer rules -->
        <record id="document_review_reviewer_rule" model="ir.rule">
            <field name="name">Document Review: reviewers only</field>
            <field name="model_id" ref="model_document_review"/>
            <field name="groups" eval="[(4, ref('group_document_reviewer'))]"/>
            <field name="domain_force">[('reviewer_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>
