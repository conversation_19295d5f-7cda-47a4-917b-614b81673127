/**
 * Simple Document Editor for Odoo 17 Community Edition
 * Uses plain JavaScript and jQuery without Odoo's widget system
 */
$(document).ready(function() {
    'use strict';

    var DocumentEditor = {
        documentId: null,
        readOnly: false,
        editor: null,
        
        /**
         * Initialize the document editor
         */
        init: function(documentId, readOnly) {
            this.documentId = documentId;
            this.readOnly = readOnly || false;
            this.bindEvents();
            this.initEditor();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;
            $(document).on('click', '.save-document-btn', function() {
                self.onSaveDocument();
            });
            $(document).on('click', '.preview-document-btn', function() {
                self.onPreviewDocument();
            });
        },
        
        /**
         * Initialize the CKEditor
         */
        initEditor: function() {
            var self = this;
            if (window.CKEDITOR && $('#document-editor').length) {
                CKEDITOR.replace('document-editor', {
                    height: 500,
                    removePlugins: 'elementspath,scayt,wsc',
                    disableNativeSpellChecker: false,
                    extraPlugins: 'autogrow,justify,font,colorbutton,colordialog,tableresize,tabletools',
                    autoGrow_minHeight: 500,
                    autoGrow_maxHeight: 800,
                    readOnly: this.readOnly,
                    toolbar: [
                        {name: 'document', items: ['Source', '-', 'Preview', 'Print']},
                        {name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo']},
                        {name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll']},
                        {name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat']},
                        {name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock']},
                        {name: 'links', items: ['Link', 'Unlink', 'Anchor']},
                        {name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar', 'PageBreak']},
                        {name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize']},
                        {name: 'colors', items: ['TextColor', 'BGColor']},
                    ],
                    on: {
                        instanceReady: function(ev) {
                            self.editor = ev.editor;
                        }
                    }
                });
            }
        },
        
        /**
         * Handle save document button click
         */
        onSaveDocument: function() {
            var self = this;
            if (!this.editor) {
                return;
            }
            
            var content = this.editor.getData();
            
            $.ajax({
                url: '/document/update',
                method: 'POST',
                data: {
                    'document_id': this.documentId,
                    'html_content': content,
                    'csrf_token': $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(result) {
                    if (result.success) {
                        self.showNotification('success', 'Success', result.message || 'Document saved successfully');
                    } else {
                        self.showNotification('error', 'Error', result.error || 'Failed to save document');
                    }
                },
                error: function(xhr, status, error) {
                    self.showNotification('error', 'Error', 'An error occurred while saving the document: ' + error);
                }
            });
        },
        
        /**
         * Handle preview document button click
         */
        onPreviewDocument: function() {
            if (this.documentId) {
                window.open('/document/preview/' + this.documentId, '_blank');
            }
        },
        
        /**
         * Show notification
         */
        showNotification: function(type, title, message) {
            var alertClass = 'alert-primary';
            switch(type) {
                case 'success':
                    alertClass = 'alert-success';
                    break;
                case 'error':
                    alertClass = 'alert-danger';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    break;
                case 'info':
                    alertClass = 'alert-info';
                    break;
            }
            
            var notificationHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 10000; min-width: 300px;" role="alert">
                    <strong>${this.escapeHtml(title)}</strong> ${this.escapeHtml(message)}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            var $notification = $(notificationHtml);
            $('body').append($notification);
            
            // Auto-remove after 5 seconds
            setTimeout(function() {
                $notification.alert('close');
            }, 5000);
        },
        
        /**
         * Escape HTML to prevent XSS
         */
        escapeHtml: function(text) {
            if (!text) return '';
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize document editor if the element exists
    if ($('#document-editor').length) {
        var documentId = $('#document-editor').data('document-id');
        var readOnly = $('#document-editor').data('read-only') || false;
        DocumentEditor.init(documentId, readOnly);
    }
    
    // Export for global access
    window.DocumentEditor = DocumentEditor;
});
