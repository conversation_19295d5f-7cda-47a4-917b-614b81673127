/**
 * Document Generation Frontend JavaScript
 * Handles template selection and document generation from the portal
 */

$(document).ready(function() {
    'use strict';

    // Document Generation Handler
    var DocumentGeneration = {
        
        /**
         * Initialize the document generation functionality
         */
        init: function() {
            this.bindEvents();
        },
        
        /**
         * Bind events to elements
         */
        bindEvents: function() {
            $(document).on('click', '.generate-document-btn', this.onGenerateDocument.bind(this));
        },
        
        /**
         * Handle generate document button click
         * @param {Event} ev - The click event
         */
        onGenerateDocument: function(ev) {
            ev.preventDefault();
            var $button = $(ev.currentTarget);
            var requestId = $button.data('request-id');
            
            if (!requestId) {
                this.showNotification('error', 'Error', 'Request ID not found');
                return;
            }
            
            this.showTemplateSelectionModal(requestId);
        },
        
        /**
         * Show template selection modal
         * @param {Integer} requestId - The document request ID
         */
        showTemplateSelectionModal: function(requestId) {
            var self = this;
            
            // Show loading
            this.showLoading('Loading templates...');
            
            // Get templates via AJAX
            $.ajax({
                url: '/document/templates',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    self.hideLoading();
                    
                    if (response.success && response.templates) {
                        self.renderTemplateModal(response.templates, requestId);
                    } else {
                        self.showNotification('error', 'Error', response.error || 'Failed to load templates');
                    }
                },
                error: function(xhr, status, error) {
                    self.hideLoading();
                    self.showNotification('error', 'Error', 'Failed to load templates: ' + error);
                }
            });
        },
        
        /**
         * Render template selection modal
         * @param {Array} templates - List of templates
         * @param {Integer} requestId - The document request ID
         */
        renderTemplateModal: function(templates, requestId) {
            var self = this;
            
            // Create modal HTML
            var modalHtml = `
                <div class="modal fade" id="templateSelectionModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Select Document Template</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    ${templates.map(template => `
                                        <div class="col-md-6 mb-3">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h5 class="card-title">${this.escapeHtml(template.name)}</h5>
                                                    <p class="card-text">${this.escapeHtml(template.description || '')}</p>
                                                </div>
                                                <div class="card-footer">
                                                    <button type="button" class="btn btn-primary select-template-btn" data-template-id="${template.id}">
                                                        Select
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add modal to body
            var $modal = $(modalHtml);
            $modal.appendTo('body');
            
            // Show modal
            $modal.modal('show');
            
            // Handle template selection
            $modal.on('click', '.select-template-btn', function(ev) {
                var templateId = $(ev.currentTarget).data('template-id');
                self.generateDocument(requestId, templateId);
                $modal.modal('hide');
            });
            
            // Remove modal when hidden
            $modal.on('hidden.bs.modal', function() {
                $modal.remove();
            });
        },
        
        /**
         * Generate document
         * @param {Integer} requestId - The document request ID
         * @param {Integer} templateId - The template ID
         */
        generateDocument: function(requestId, templateId) {
            var self = this;
            
            // Show loading
            this.showLoading('Generating document...');
            
            // Generate document via AJAX
            $.ajax({
                url: '/document/generate',
                method: 'POST',
                data: {
                    'document_request_id': requestId,
                    'template_id': templateId,
                    'csrf_token': $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(response) {
                    self.hideLoading();
                    
                    if (response.success) {
                        self.showNotification('success', 'Success', response.message || 'Document generated successfully');
                        
                        // Reload page after a short delay
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        self.showNotification('error', 'Error', response.error || 'Failed to generate document');
                    }
                },
                error: function(xhr, status, error) {
                    self.hideLoading();
                    self.showNotification('error', 'Error', 'Failed to generate document: ' + error);
                }
            });
        },
        
        /**
         * Show loading overlay
         * @param {String} message - Loading message
         */
        showLoading: function(message) {
            var loadingHtml = `
                <div id="documentGenerationLoading" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
                    <div class="bg-white p-4 rounded shadow">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border text-primary me-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span>${message || 'Loading...'}</span>
                        </div>
                    </div>
                </div>
            `;
            
            $('#documentGenerationLoading').remove();
            $('body').append(loadingHtml);
        },
        
        /**
         * Hide loading overlay
         */
        hideLoading: function() {
            $('#documentGenerationLoading').remove();
        },
        
        /**
         * Show notification
         * @param {String} type - Notification type (success, error, warning, info)
         * @param {String} title - Notification title
         * @param {String} message - Notification message
         */
        showNotification: function(type, title, message) {
            var alertClass = 'alert-primary';
            switch(type) {
                case 'success':
                    alertClass = 'alert-success';
                    break;
                case 'error':
                    alertClass = 'alert-danger';
                    break;
                case 'warning':
                    alertClass = 'alert-warning';
                    break;
                case 'info':
                    alertClass = 'alert-info';
                    break;
            }
            
            var notificationHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 10000; min-width: 300px;" role="alert">
                    <strong>${this.escapeHtml(title)}</strong> ${this.escapeHtml(message)}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            var $notification = $(notificationHtml);
            $('body').append($notification);
            
            // Auto-remove after 5 seconds
            setTimeout(function() {
                $notification.alert('close');
            }, 5000);
        },
        
        /**
         * Escape HTML to prevent XSS
         * @param {String} text - Text to escape
         * @return {String} - Escaped text
         */
        escapeHtml: function(text) {
            if (!text) return '';
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize document generation
    DocumentGeneration.init();
    
    // Export for global access if needed
    window.DocumentGeneration = DocumentGeneration;
});
