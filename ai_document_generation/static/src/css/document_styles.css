/* Document Styles */

/* Document Preview */
.document-preview {
    background-color: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    min-height: 500px;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }
    
    .document-preview, .document-preview * {
        visibility: visible;
    }
    
    .document-preview {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 0;
        margin: 0;
        border: none;
        box-shadow: none;
    }
    
    .card-header, .card-footer {
        display: none;
    }
}

/* Document Editor */
.document-editor {
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.document-editor .toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 10px;
}

.document-editor .toolbar .btn-group {
    margin-right: 10px;
}

.document-editor .editor-content {
    padding: 20px;
    min-height: 500px;
}

/* Document Review */
.document-review {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.document-review .review-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-review .review-content {
    display: flex;
    flex: 1;
}

.document-review .original-content,
.document-review .modified-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    border-right: 1px solid #ddd;
}

.document-review .review-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
    padding: 10px;
    display: flex;
    justify-content: flex-end;
}

/* Document Template Selection Modal */
.template-selection-modal .template-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.template-selection-modal .template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.template-selection-modal .template-card.selected {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.5);
}

/* Loading Indicator */
.o_loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 9999;
    font-size: 18px;
    font-weight: bold;
    color: #4a6cf7;
}
