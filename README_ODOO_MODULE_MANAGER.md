# Odoo Module Manager via XML-RPC

A comprehensive Python toolkit for managing Odoo modules remotely using XML-RPC. This tool provides install, upgrade, uninstall, and monitoring capabilities for Odoo modules.

## 🚀 Features

- **Module Operations**: Install, upgrade, uninstall modules
- **Batch Operations**: Handle multiple modules simultaneously
- **Status Monitoring**: Check module states and dependencies
- **Export/Import**: Export module lists to JSON
- **CLI Interface**: Command-line tool for quick operations
- **Error Handling**: Robust error handling and logging
- **Multiple Environments**: Support for production, staging, local environments

## 📋 Requirements

- Python 3.6+
- Access to Odoo server with XML-RPC enabled
- Valid Odoo user credentials with module management permissions

## 🛠️ Installation

1. **Download the files**:
   ```bash
   # Download all Python files to your working directory
   wget https://your-server/odoo_module_manager.py
   wget https://your-server/odoo_config.py
   wget https://your-server/odoo_cli.py
   wget https://your-server/usage_examples.py
   ```

2. **Configure credentials**:
   Edit `odoo_config.py` with your Odoo server details:
   ```python
   ODOO_CONFIG = {
       'url': 'https://oneclickvakil.com',
       'db': 'oneclickvakil.com',
       'username': 'your_username',
       'password': 'your_password',
   }
   ```

3. **Make scripts executable**:
   ```bash
   chmod +x odoo_cli.py
   chmod +x usage_examples.py
   ```

## 🔧 Configuration

### Basic Configuration

Edit `odoo_config.py`:

```python
ODOO_CONFIG = {
    'url': 'https://your-odoo-server.com',
    'db': 'your_database_name',
    'username': 'your_username',
    'password': 'your_password',
}
```

### Multiple Environments

The tool supports multiple environments:

```python
CONFIGS = {
    'production': {
        'url': 'https://oneclickvakil.com',
        'db': 'oneclickvakil.com',
        'username': 'admin',
        'password': 'production_password',
    },
    'staging': {
        'url': 'https://staging.oneclickvakil.com',
        'db': 'staging_db',
        'username': 'admin',
        'password': 'staging_password',
    },
    'local': {
        'url': 'http://localhost:8069',
        'db': 'local_db',
        'username': 'admin',
        'password': 'admin',
    }
}
```

## 📖 Usage

### Command Line Interface (Recommended)

#### Basic Operations

```bash
# Install a module
python odoo_cli.py install ai_module_generator

# Upgrade a module
python odoo_cli.py upgrade ai_module_generator

# Uninstall a module
python odoo_cli.py uninstall ovakil_customer_feedback

# Check module status
python odoo_cli.py status ai_module_generator

# Check multiple modules
python odoo_cli.py status ai_module_generator ai_chatbot_integration
```

#### List Operations

```bash
# List all installed modules
python odoo_cli.py list installed

# List modules needing upgrade
python odoo_cli.py list to_upgrade

# List uninstalled modules (limit to 10)
python odoo_cli.py list uninstalled --limit 10
```

#### Batch Operations

```bash
# Batch upgrade multiple modules
python odoo_cli.py batch-upgrade ai_module_generator,ai_chatbot_integration,ai_cash_management

# Batch install multiple modules
python odoo_cli.py batch-install module1,module2,module3
```

#### Export Operations

```bash
# Export all modules to JSON
python odoo_cli.py export-list

# Export only installed modules
python odoo_cli.py export-list --state installed --output installed_modules.json

# Update module list
python odoo_cli.py update-list
```

#### Environment Selection

```bash
# Use staging environment
python odoo_cli.py --env staging upgrade ai_module_generator

# Use local environment
python odoo_cli.py --env local list installed
```

### Python API

#### Basic Usage

```python
from odoo_module_manager import OdooModuleManager
from odoo_config import get_config

# Initialize manager
config = get_config('production')
manager = OdooModuleManager(**config)

# Install a module
success = manager.install_module('ai_module_generator')

# Upgrade a module
success = manager.upgrade_module('ai_module_generator')

# Check module status
module_info = manager.get_module_info('ai_module_generator')
print(f"State: {module_info['state']}")
```

#### Batch Operations

```python
# Batch upgrade
modules = ['ai_module_generator', 'ai_chatbot_integration']
results = manager.batch_upgrade(modules)

for module, success in results.items():
    print(f"{module}: {'Success' if success else 'Failed'}")
```

#### Module Analysis

```python
# List installed modules
installed = manager.list_modules('installed')
print(f"Installed modules: {len(installed)}")

# Get dependencies
deps = manager.get_module_dependencies('ai_module_generator')
print(f"Dependencies: {deps}")

# Export module list
filename = manager.export_module_list(state='installed')
print(f"Exported to: {filename}")
```

## 🎯 Common Use Cases

### 1. Upgrade AI Module Generator

```bash
# Check current status
python odoo_cli.py status ai_module_generator

# Upgrade the module
python odoo_cli.py upgrade ai_module_generator

# Verify upgrade
python odoo_cli.py status ai_module_generator
```

### 2. Install Generated Module

```bash
# Install a newly generated module
python odoo_cli.py install ovakil_customer_feedback

# Check if installation was successful
python odoo_cli.py status ovakil_customer_feedback
```

### 3. Batch Upgrade AI Modules

```bash
# Upgrade all AI-related modules
python odoo_cli.py batch-upgrade ai_module_generator,ai_chatbot_integration,ai_cash_management,ai_dynamic_forms
```

### 4. System Maintenance

```bash
# Update module list
python odoo_cli.py update-list

# Check which modules need upgrade
python odoo_cli.py list to_upgrade

# Export current state for backup
python odoo_cli.py export-list --state installed --output backup_$(date +%Y%m%d).json
```

## 🔍 Troubleshooting

### Connection Issues

1. **Check credentials**: Verify username/password in `odoo_config.py`
2. **Check URL**: Ensure the URL is correct and accessible
3. **Check database**: Verify the database name exists
4. **Check permissions**: User must have module management permissions

### Module Operation Issues

1. **Module not found**: Run `python odoo_cli.py update-list` first
2. **Permission denied**: Check user has module installation rights
3. **Dependency issues**: Check module dependencies are satisfied
4. **State conflicts**: Verify module is in correct state for operation

### Common Error Messages

- `Authentication failed`: Check username/password
- `Module not found`: Update module list or check module name
- `Permission denied`: User lacks required permissions
- `Connection refused`: Server not accessible or XML-RPC disabled

## 📊 Logging

The tool creates detailed logs in `odoo_module_manager.log`:

```bash
# View recent logs
tail -f odoo_module_manager.log

# Search for errors
grep ERROR odoo_module_manager.log
```

## 🔒 Security Notes

1. **Credentials**: Store credentials securely, don't commit to version control
2. **Permissions**: Use dedicated user with minimal required permissions
3. **Network**: Use HTTPS for production environments
4. **Logging**: Log files may contain sensitive information

## 📝 Examples

See `usage_examples.py` for comprehensive examples:

```bash
# Run all examples
python usage_examples.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
1. Check the troubleshooting section
2. Review the log files
3. Test with a simple operation first
4. Verify credentials and permissions

---

**Happy Module Managing! 🚀**
