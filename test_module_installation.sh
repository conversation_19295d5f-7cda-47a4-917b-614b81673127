#!/bin/bash
# Module Installation Test Script

echo "🧪 Testing Module Installation"
echo "=============================="

# Find all ovakil modules
for module_dir in /mnt/extra-addons/ovakil_*; do
    if [ -d "$module_dir" ]; then
        module_name=$(basename "$module_dir")
        echo "📦 Testing module: $module_name"
        
        # Check if module can be installed (dry run)
        echo "  Checking XML syntax..."
        
        # Check for common XML issues
        if grep -r 'multiple[^=]' "$module_dir/views/" 2>/dev/null; then
            echo "  ❌ Found 'multiple' attribute without value"
        else
            echo "  ✅ No 'multiple' attribute issues"
        fi
        
        if grep -r 'required[^=]' "$module_dir/views/" 2>/dev/null; then
            echo "  ❌ Found 'required' attribute without value"
        else
            echo "  ✅ No 'required' attribute issues"
        fi
        
        echo "  ✅ Module $module_name appears ready for installation"
        echo ""
    fi
done

echo "🎉 Installation test complete!"
