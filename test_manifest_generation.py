#!/usr/bin/env python3
"""
Test script to verify that the AI Module Generator now correctly generates
manifest files with the 'assets' section to prevent 500 errors.
"""

import os
import tempfile
import shutil

def test_manifest_generation():
    """Test that manifest generation includes assets section"""
    
    print("🧪 Testing AI Module Generator Manifest Generation...")
    
    # Create a temporary directory for testing
    temp_dir = tempfile.mkdtemp(prefix='test_manifest_')
    module_path = os.path.join(temp_dir, 'test_module')
    os.makedirs(module_path, exist_ok=True)
    
    try:
        # Simulate the manifest generation logic from the wizard
        module_name = 'test_module'
        include_website = True
        
        # Data files (simplified)
        data_files = [
            'security/ir.model.access.csv',
            'data/data.xml',
            'views/views.xml',
            'views/website_templates.xml'
        ]
        
        demo_data = ['demo/demo.xml']
        
        # Generate assets section for website modules (NEW FIX)
        assets_section = ""
        if include_website:
            assets_section = f"""    'assets': {{
        'web.assets_frontend': [
            '{module_name}/static/src/css/enhanced_forms.css',
            '{module_name}/static/src/js/enhanced_forms.js',
        ],
    }},"""

        # Generate manifest content (FIXED VERSION)
        manifest_content = f'''{{
    'name': 'Test Module',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Test Module',
    'description': """Test Module for Manifest Generation""",
    'author': 'AI Module Generator',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web', 'mail', 'portal', 'sale', 'website', 'project', 'account', 'payment'],
    'data': {data_files},
    'demo': {demo_data},{assets_section}
    'installable': True,
    'auto_install': False,
    'application': True,
}}'''
        
        # Write manifest file
        manifest_path = os.path.join(module_path, '__manifest__.py')
        with open(manifest_path, 'w') as f:
            f.write(manifest_content)
        
        # Verify the manifest file was created
        if not os.path.exists(manifest_path):
            print("❌ FAIL: Manifest file was not created")
            return False
        
        # Read and verify the manifest content
        with open(manifest_path, 'r') as f:
            content = f.read()
        
        # Check for required sections
        checks = [
            ("'name':", "Module name"),
            ("'depends':", "Dependencies"),
            ("'data':", "Data files"),
            ("'assets':", "Assets section (CRITICAL FIX)"),
            ("'web.assets_frontend':", "Frontend assets"),
            ("'enhanced_forms.css'", "CSS file reference"),
            ("'enhanced_forms.js'", "JavaScript file reference"),
            ("'installable': True", "Installable flag"),
        ]
        
        print("\n📋 Verification Results:")
        all_passed = True
        
        for check, description in checks:
            if check in content:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: MISSING")
                all_passed = False
        
        # Additional check: Ensure assets section is properly formatted
        if "'assets':" in content and "'web.assets_frontend':" in content:
            print("✅ Assets section properly formatted")
        else:
            print("❌ Assets section malformed or missing")
            all_passed = False
        
        # Check for proper Python syntax
        try:
            # Try to evaluate the manifest as Python code
            manifest_dict = eval(content)
            if isinstance(manifest_dict, dict):
                print("✅ Manifest has valid Python syntax")
                
                # Check if assets key exists
                if 'assets' in manifest_dict:
                    print("✅ Assets key exists in manifest dictionary")
                    
                    # Check if web.assets_frontend exists
                    if 'web.assets_frontend' in manifest_dict['assets']:
                        print("✅ web.assets_frontend key exists in assets")
                        
                        # Check if CSS and JS files are listed
                        frontend_assets = manifest_dict['assets']['web.assets_frontend']
                        if any('enhanced_forms.css' in asset for asset in frontend_assets):
                            print("✅ CSS file reference found in assets")
                        else:
                            print("❌ CSS file reference missing from assets")
                            all_passed = False
                            
                        if any('enhanced_forms.js' in asset for asset in frontend_assets):
                            print("✅ JavaScript file reference found in assets")
                        else:
                            print("❌ JavaScript file reference missing from assets")
                            all_passed = False
                    else:
                        print("❌ web.assets_frontend missing from assets")
                        all_passed = False
                else:
                    print("❌ Assets key missing from manifest")
                    all_passed = False
            else:
                print("❌ Manifest does not evaluate to a dictionary")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Manifest has invalid Python syntax: {e}")
            all_passed = False
        
        # Final result
        print(f"\n🎯 Overall Result: {'✅ PASS' if all_passed else '❌ FAIL'}")
        
        if all_passed:
            print("🎉 SUCCESS: AI Module Generator will now create proper manifest files!")
            print("🚀 Future generated modules will NOT have 500 Internal Server Errors!")
        else:
            print("⚠️  WARNING: Issues found in manifest generation!")
            print("🔧 Additional fixes may be required!")
        
        return all_passed
        
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)

def test_old_vs_new_manifest():
    """Compare old problematic manifest vs new fixed manifest"""
    
    print("\n🔍 Comparing Old vs New Manifest Generation...")
    
    # OLD PROBLEMATIC MANIFEST (missing assets)
    old_manifest = '''{
    'name': 'Test Module',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Test Module',
    'description': """Test Module""",
    'author': 'AI Module Generator',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web', 'mail', 'portal', 'website'],
    'data': ['views/views.xml'],
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': True,
}'''
    
    # NEW FIXED MANIFEST (with assets)
    new_manifest = '''{
    'name': 'Test Module',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Test Module',
    'description': """Test Module""",
    'author': 'AI Module Generator',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web', 'mail', 'portal', 'website'],
    'data': ['views/views.xml'],
    'demo': [],
    'assets': {
        'web.assets_frontend': [
            'test_module/static/src/css/enhanced_forms.css',
            'test_module/static/src/js/enhanced_forms.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
}'''
    
    print("📊 Comparison Results:")
    print("❌ OLD MANIFEST: Missing 'assets' section → Causes 500 errors")
    print("✅ NEW MANIFEST: Includes 'assets' section → Prevents 500 errors")
    
    # Check if assets key exists
    try:
        old_dict = eval(old_manifest)
        new_dict = eval(new_manifest)
        
        old_has_assets = 'assets' in old_dict
        new_has_assets = 'assets' in new_dict
        
        print(f"\n🔍 Assets Key Analysis:")
        print(f"   Old Manifest has 'assets': {old_has_assets}")
        print(f"   New Manifest has 'assets': {new_has_assets}")
        
        if not old_has_assets and new_has_assets:
            print("✅ FIX CONFIRMED: New manifest includes assets section!")
            return True
        else:
            print("❌ FIX NOT WORKING: Assets section issue persists!")
            return False
            
    except Exception as e:
        print(f"❌ Error comparing manifests: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AI Module Generator Manifest Fix Verification")
    print("=" * 60)
    
    # Test 1: Manifest generation
    test1_result = test_manifest_generation()
    
    # Test 2: Old vs New comparison
    test2_result = test_old_vs_new_manifest()
    
    print("\n" + "=" * 60)
    print("📋 FINAL TEST SUMMARY:")
    print(f"   Manifest Generation Test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Old vs New Comparison:    {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ AI Module Generator fix is working correctly!")
        print("🚀 Future modules will NOT have 500 Internal Server Errors!")
    else:
        print("\n⚠️  SOME TESTS FAILED!")
        print("🔧 Additional investigation required!")
    
    print("=" * 60)
