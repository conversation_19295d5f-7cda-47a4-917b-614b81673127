from odoo import models, fields, api


class Business(models.Model):
    _name = 'x.business.onboarding'
    # _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Business Onboarding'

    name = fields.Char(string='Business Name', required=True)
    x_contact_person = fields.Char(string='Contact Person', required=True)
    x_phone = fields.Char(string='Phone Number', required=True)
    x_email = fields.Char(string='Email', required=True)
    x_street1 = fields.Char(string='Street 1', required=True)
    x_street2 = fields.Char(string='Street 2')
    x_village_id = fields.Many2one('x_village', string='Village', required=True)
    x_taluka_id = fields.Many2one('x_taluka', string='Taluka', required=True)
    x_district_id = fields.Many2one('x_district', string='District', required=True)
    x_state_id = fields.Many2one('res.country.state', string='State', required=True)
    x_postal_code = fields.Char(string='Postal Code', required=True)
    x_company_details = fields.Text(string='Company Details', required=True)
    x_company_type = fields.Selection(
        [('individual', 'Individual'), ('proprietorship', 'Proprietorship'), ('partnership_firm', 'Partnership Firm'),
         ('llp_firm', 'LLP Firm'), ('opc', 'OPC'), ('public_company', 'Public company'),
         ('private_company', 'Private Company'), ('foreign_company', 'Foreign Company'),
         ('non_profit_company', 'Non Profit Company'), ('non_profit_organization', 'Non Profit Organization')],
        string='company Types',
        store=True)
    x_company_name = fields.Char(string='Company Name', store=True)
    x_owner_name = fields.Char(string='Owner Name', store=True)
    x_registration_id = fields.Char(string='Registration ID', store=True)
    x_company_logo = fields.Binary(string='Company Logo', store=True)
    x_established_year = fields.Char(string='Established Year', store=True)
    x_gst_id = fields.Char(string='GST ID', required=True)
    x_bank_name = fields.Char(string='Bank Name', required=True)
    x_bank_account_name = fields.Char(string='Bank Account Name', required=True)
    x_bank_account_number = fields.Char(string='Bank Account Number', required=True)
    x_bank_ifsc = fields.Char(string='IFSC Code', required=True)
    x_bank_branch = fields.Char(string='Bank Branch', required=True)
    x_bank_address = fields.Text(string='Bank Address', required=True)
    x_warehouse_image = fields.Binary(string='Warehouse Image')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    @api.model
    def create_warehouse(self, business):
        warehouse_name = f"{business.name} Warehouse"
        short_code = business.x_village_id.name[:3].upper()

        existing_warehouses = self.env['stock.warehouse'].search([('x_village_id', '=', business.x_village_id.id)])
        if existing_warehouses:
            short_code += chr(97 + len(existing_warehouses))

        warehouse_vals = {
            'name': warehouse_name,
            'code': short_code,
            'address_id': business.id,
            'x_village_id': business.x_village_id.id,
            'x_taluka_id': business.x_taluka_id.id,
            'x_district_id': business.x_district_id.id,
            'x_state_id': business.x_state_id.id,
            'x_warehouse_image': business.x_warehouse_image,
        }

        warehouse = self.env['stock.warehouse'].create(warehouse_vals)
        return warehouse.id

    @api.model
    def create_partner(self, business):
        partner_vals = {
            'name': business.name,
            'street': business.x_street1,
            'street2': business.x_street2,
            'village_id': business.x_village_id.id,
            'taluka_id': business.x_taluka_id.id,
            'district_id': business.x_district_id.id,
            'state_id': business.x_state_id.id,
            'zip': business.x_postal_code,
            'country_id': business.x_country_id.id,
            'email': business.x_email,
            'phone': business.x_phone,
            'vat': business.x_tax_id,
            'bank_ids': [(0, 0, {
                'bank_name': business.x_bank_name,
                'acc_number': business.x_bank_account_number,
                'bank_branch': business.x_bank_branch,
                'ifsc_code': business.x_bank_ifsc,
                'bank_address': business.x_bank_address,
                'bank_acc_name': business.x_bank_account_name,
            })]
        }

        partner = self.env['res.partner'].create(partner_vals)
        return partner.id

    def approve_business(self):
        for business in self:
            self.create_partner(business)
            self.create_warehouse(business)
            business.write({'state': 'approved'})
