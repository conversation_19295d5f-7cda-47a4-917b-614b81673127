<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form View -->
         <record id="view_business_onboarding_form" model="ir.ui.view">
            <field name="name">x.business.onboarding.form</field>
            <field name="model">x.business.onboarding</field>
            <field name="arch" type="xml">
                <form string="Business Onboarding">
                    <sheet>
                        <group>
                            <group string="Personal Details">
                                <field name="name"/>
                                <field name="x_contact_person"/>
                                <field name="x_phone"/>
                                <field name="x_email"/>
                            </group>

                            <!-- Address Group -->
                            <group string="Address">
                                <field name="x_street1"/>
                                <field name="x_street2"/>
                                <field name="x_village_id"/>
                                <field name="x_taluka_id" />
                                <field name="x_district_id" />
                                <field name="x_state_id" />
                                <field name="x_postal_code"/>
                                
                            </group>

                            <!-- Business Details Group -->
                            <group string="Business Details">
                                <field name="x_company_name"/>
                                <field name="x_company_type"/>
                                <field name="x_company_logo"/>
                                <field name="x_owner_name"/>
                                <field name="x_established_year"/>
                                <field name="x_registration_id"/>
                                <field name="x_gst_id"/>
                                <field name="x_warehouse_image"/>
                            </group>

                            <!-- Bank Details Group -->
                            <group string="Bank Details">
                                <field name="x_bank_name"/>
                                <field name="x_bank_account_name"/>
                                <field name="x_bank_account_number"/>
                                <field name="x_bank_ifsc"/>
                                <field name="x_bank_branch"/>
                                <field name="x_bank_address"/>
                            </group>
                        </group>
                        <footer>
                            <!-- <button name="approve_business" type="object" string="Approve" class="oe_highlight"/> -->
                            <!-- <button name="action_cancel" type="object" string="Cancel" class="oe_link"/> -->
                        </footer>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Search View -->
        <record id="view_business_onboarding_search" model="ir.ui.view">
            <field name="name">x.business.onboarding.search</field>
            <field name="model">x.business.onboarding</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="x_contact_person"/>
                    <field name="x_phone"/>
                    <field name="x_email"/>
                    
                </search>
            </field>
        </record>

        <!-- Tree View -->
        <record id="view_business_onboarding_tree" model="ir.ui.view">
            <field name="name">x.business.onboarding.tree</field>
            <field name="model">x.business.onboarding</field>
            <field name="arch" type="xml">
                <tree string="Business Onboarding">
                    <field name="name"/>
                    <field name="x_contact_person"/>
                    <field name="x_phone"/>
                    <field name="x_email"/>
                </tree>
            </field>
        </record>

        <!-- Action -->
        <record id="action_business_onboarding" model="ir.actions.act_window">
            <field name="name">Business Onboarding</field>
            <field name="res_model">x.business.onboarding</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_business_onboarding_tree"/>
            <field name="search_view_id" ref="view_business_onboarding_search"/>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_business_onboarding" name="Business Onboarding" sequence="10"/>
        <menuitem id="menu_business_onboarding_items" name="Business Onboarding Items" parent="menu_business_onboarding" action="action_business_onboarding" sequence="10"/>
    </data>
</odoo>
