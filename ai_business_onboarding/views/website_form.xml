<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="business_onboarding_form_template" name="Business Onboarding Form">
    <t t-call="website.layout">
        <style>
                    .form-box {
                        max-width: 1000px;
                        margin: 35px auto;
                        padding: 35px;
                        background: #fff; /* White background for form container */
                        border-radius: 10px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                        border: 1px solid #ccc; /* Border around form box */
                    }
        </style>
        <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
        <section class="s_website_form pt16 pb16 o_colored_level" data-vcss="001" data-snippet="s_website_form" data-name="Form" style="background-color: rgba(214, 239, 214, 0.41) !important; background-image: none;">
                <div class="form-box">
                    <form id="business_onboarding_form" action="/website/form/" method="post" enctype="multipart/form-data" class="o_mark_required" data-mark="*" data-model_name="x.business.onboarding" data-success-mode="redirect" data-success-page="/business_onboarding/success" data-pre-fill="true">
                        <!-- <form id="business_onboarding_form" class="oe_form form-horizontal" method="post" action="/business_onboarding/submit"> -->
                            <!-- Personal Details Section -->
                            <h2>Personal Details</h2>
                            <!-- <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" name="name" class="form-control" required="required"/>
                            </div> -->
                            <div class="nice-form-group">
                                <label for="x_contact_person">Contact Person</label>
                                <input type="text" name="x_contact_person" class="form-control" required="required"/>
                            </div>
                            <div class="nice-form-group">
                                <label for="x_phone">Phone</label>
                                <input type="text" name="x_phone" class="form-control" required="required"/>
                            </div>
                            <div class="nice-form-group">
                                <label for="x_email">Email</label>
                                <input type="email" name="x_email" class="form-control" required="required"/>
                            </div>

                            <!-- Address Section -->
                            <h2>Address</h2>
                            <div class="row" style="">
                                <div class="nice-form-group col">
                                    <label for="x_street1">Street 1</label>
                                    <input type="text" name="x_street1" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_street2">Street 2</label>
                                    <input type="text" name="x_street2" class="form-control"/>
                                </div>
                            </div>
                            <div class="row" style="">
                                <div class="nice-form-group col">
                                  <select class="form-control form-select s_website_form_input" id="stateSelect" autocomplete="off" name="x_state" required="1">
                                    <option value="0">Select A State (રાજ્ય)</option>
                                  </select>
                                </div>
                                <div class="nice-form-group col">
                                  <select class="form-control form-select s_website_form_input x_district"  id="districtSelect" autocomplete="off" name="x_district" required="1">
                                    <option value="0">Select A District (જિલ્લો)</option>
                                  </select>
                                </div>
                              </div>
                              <div class="row" style="">
                                <div class="nice-form-group col">
                                  <select class="form-control s_website_form_input x_taluka" id="talukaSelect" autocomplete="off" name="x_taluka" required="1">
                                    <option value="0">Select A Taluka/City (તાલુકો/શહેર)</option>
                                  </select>
                                </div>
                                <div class="nice-form-group col">
                                  <select class="form-control form-select s_website_form_input" id="villageSelect" autocomplete="off" name="x_village" required="1">
                                    <option value="0">Select Village/Area(ગામ/વિસ્તાર)</option>
                                  </select>
                                </div>
                              </div>
                            <div class="nice-form-group">
                                <label for="x_postal_code">Postal Code</label>
                                <input type="text" name="x_postal_code" class="form-control" required="required"/>
                            </div>

                            <!-- Business Details Section -->
                            <h2>Business Details</h2>
                            <div class="row" style="">
                                <div class="nice-form-group col">
                                    <label for="x_company_name">Company Name</label>
                                    <input type="text" name="x_company_name" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col" >
                                    <label for="x_company_types">Company Types</label>
                                    <input type="text" name="x_company_types" class="form-control" required="required"/>
                                </div>
                            </div>
                            <div class="row" style="">    
                                <div class="nice-form-group col">
                                    <label for="x_owner_name">Owner Name</label>
                                    <input type="text" name="x_owner_name" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_established_years">Established Years</label>
                                    <input type="number" name="x_established_years" class="form-control" required="required"/>
                                </div>
                            </div>
                            <div class="row" style="">
                                <div class="nice-form-group col">
                                    <label for="x_registration_id">Registration ID</label>
                                    <input type="text" name="x_registration_id" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_gst_id">GST ID</label>
                                    <input type="text" name="x_gst_id" class="form-control" required="required"/>
                                </div>
                            </div>
                            <div class="row" style="">    
                                <div class="nice-form-group col">
                                    <label for="x_company_logo">Company Logo</label>
                                    <input type="file" name="x_company_logo" class="form-control"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_warehouse_image">Warehouse Image</label>
                                    <input type="file" name="x_warehouse_image" class="form-control"/>
                                </div>
                            </div>
                            

                            <!-- Bank Details Section -->
                            <h2>Bank Details</h2>
                            <div class="row" style="">
                                <div class="nice-form-group col">
                                    <label for="x_bank_name">Bank Name</label>
                                    <input type="text" name="x_bank_name" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_bank_account_name">Bank Account Name</label>
                                    <input type="text" name="x_bank_account_name" class="form-control" required="required"/>
                                </div>
                            </div>
                            <div class="row" style="">    
                                <div class="nice-form-group col">
                                    <label for="x_bank_account_number">Bank Account Number</label>
                                    <input type="text" name="x_bank_account_number" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_bank_ifsc">Bank IFSC</label>
                                    <input type="text" name="x_bank_ifsc" class="form-control" required="required"/>
                                </div>
                            </div>
                            <div class="row" style="">    
                                <div class="nice-form-group col">
                                    <label for="x_bank_branch">Bank Branch</label>
                                    <input type="text" name="x_bank_branch" class="form-control" required="required"/>
                                </div>
                                <div class="nice-form-group col">
                                    <label for="x_bank_address">Bank Address</label>
                                    <input type="text" name="x_bank_address" class="form-control" required="required"/>
                                </div>
                           </div>
                            
                            <div class="nice-form-group">
                                <button type="submit" class="btn btn-primary">Submit</button>
                            </div>
                <script>
              var stateSelect = document.getElementById("stateSelect");
              <!--var districtSelect = document.querySelector("select[name='x_district']");-->
              <!--var talukaSelect = document.querySelector("select[name='x_taluka']");-->
              var districtSelect = document.querySelector(".x_district");
              var talukaSelect = document.querySelector(".x_taluka");
              var villageSelect = document.querySelector("select[name='x_village']");
              var selectedStateId = 0; // Initialize with a default value
              var selectedDistrictId = 0; // Initialize with a default value
              var selectedTalukaId = 0; // Initialize with a default value
              // Function to populate the State dropdown
              function populateStates() {
                  stateSelect.innerHTML = '&lt;option value="0"&gt;Select A State&lt;/option&gt;';
                  var countryId = 104; // Assuming 104 is the ID for India in your database, change it if needed.
                  fetch('/get_states/' + countryId)
                      .then(response =&gt; response.json())
                      .then(data =&gt; {
                          data.forEach(state =&gt; {
                              var option = document.createElement("option");
                              option.value = state.id;
                              option.textContent = state.name;
                              stateSelect.appendChild(option);
                          });
                      });
              }
              // Function to populate the District dropdown
              function populateDistricts() {
                  districtSelect.innerHTML = '&lt;option value="0"&gt;Select A District&lt;/option&gt;';
                  if (selectedStateId === 0) {
                      return;
                  }
                  fetch('/get_districts/' + selectedStateId)
                      .then(response =&gt; response.json())
                      .then(data =&gt; {
                          data.forEach(district =&gt; {
                              var option = document.createElement("option");
                              option.value = district.id;
                              option.textContent = district.name;
                              districtSelect.appendChild(option);
                          });
                      });
              }
              // Function to populate the Taluka/City dropdown
              function populateTalukas() {
                  talukaSelect.innerHTML = '&lt;option value="0"&gt;Select A Taluka/City&lt;/option&gt;';
                  if (selectedDistrictId === 0) {
                      return;
                  }
                  fetch('/get_talukas/' + selectedDistrictId)
                      .then(response =&gt; response.json())
                      .then(data =&gt; {
                          data.forEach(taluka =&gt; {
                              var option = document.createElement("option");
                              option.value = taluka.id;
                              option.textContent = taluka.name;
                              talukaSelect.appendChild(option);
                          });
                      });
              }
              // Function to populate the Village/Area dropdown
              function populateVillages() {
                  villageSelect.innerHTML = '&lt;option value="0"&gt;Select Village/Area&lt;/option&gt;';
                  if (selectedTalukaId === 0) {
                      return;
                  }
                  fetch('/get_villages/' + selectedTalukaId)
                      .then(response =&gt; response.json())
                      .then(data =&gt; {
                          data.forEach(village =&gt; {
                              var option = document.createElement("option");
                              option.value = village.id;
                              option.textContent = village.name;
                              villageSelect.appendChild(option);
                          });
                      });
              }
              // Add event listeners to call the functions when dropdown values change
              stateSelect.addEventListener("change", function () {
                  selectedStateId = this.value; // Update the selected state ID
                  populateDistricts();
              });
              districtSelect.addEventListener("change", function () {
                  selectedDistrictId = this.value; // Update the selected district ID
                  populateTalukas();
              });
              talukaSelect.addEventListener("change", function () {
                  selectedTalukaId = this.value; // Update the selected taluka ID
                  populateVillages();
              });
              // Initialize the State dropdown
              populateStates();
            </script>
                        </form>
                </div>
        </section>
    </t>
</template>
</odoo> 