from odoo import http
from odoo.http import request


class BusinessOnboardingController(http.Controller):

    @http.route('/business_onboarding/submit', type='http', auth="public", website=True, csrf=False)
    def submit_business_onboarding(self, **kwargs):
        values = {key: value for key, value in kwargs.items()}
        
        # Create a new record in the x.business.onboarding model
        request.env['x.business.onboarding'].sudo().create(values)

        # Redirect to a success page or back to the form
        return request.redirect('/business_onboarding/success')

    @http.route('/business_onboarding', type='http', auth="public", website=True)
    def business_onboarding_form(self, **kwargs):
        return request.render('ai_business_onboarding.business_onboarding_form_template')

    @http.route('/business_onboarding/success', type='http', auth="public", website=True)
    def business_onboarding_success(self, **kwargs):
        return request.render('ai_business_onboarding.business_onboarding_success_template')
