from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import datetime


class SlideChannel(models.Model):
    _inherit = 'slide.channel'

    parent_slide_names = fields.Char(
        string='Parent Slide Names',
        compute='_compute_parent_slide_names',
        store=True
    )

    total_child_slide_count = fields.Integer(
        string='Total Child Slide Count',
        compute='_compute_total_child_slide_count',
        store=True
    )

    @api.depends('slide_ids.parent_slide_names')
    def _compute_parent_slide_names(self):
        for channel in self:
            parent_slide_names = ', '.join(filter(None, channel.slide_ids.mapped('parent_slide_names')))
            channel.parent_slide_names = parent_slide_names

    @api.depends('slide_ids.child_slide_count')
    def _compute_total_child_slide_count(self):
        for channel in self:
            total_count = sum(channel.slide_ids.mapped('child_slide_count'))
            channel.total_child_slide_count = total_count


class Slide(models.Model):
    _inherit = 'slide.slide'

    parent_slide_id = fields.Many2one(
        'slide.slide',
        string='Parent Slide',
        domain="[('channel_id', '=', channel_id), ('id', '!=', id)]"
        # Ensures the slide belongs to the same course and is not the current slide itself
    )
    child_slide_ids = fields.One2many('slide.slide', 'parent_slide_id', string='Child Slides')

    child_slide_count = fields.Integer(
        string='Number of Child Slides',
        compute='_compute_child_slide_count',
        store=True
    )

    parent_slide_names = fields.Char(
        string='Parent Slide Names',
        compute='_compute_parent_slide_names',
        store=True
    )
    total_child_slide_count = fields.Integer(
        string='Total Child Slide Count',
        compute='_compute_total_child_slide_count',
        store=True
    )

    @api.depends('child_slide_ids')
    def _compute_total_child_slide_count(self):
        for slide in self:
            total_count = 0
            if slide.child_slide_ids:
                total_count = len(slide.child_slide_ids)
            slide.total_child_slide_count = total_count
    @api.depends('parent_slide_id')
    def _compute_parent_slide_names(self):
        for slide in self:
            parent_slide_names = []
            if slide.parent_slide_id:
                parent_slide_names.append(slide.parent_slide_id.name)
            slide.parent_slide_names = ', '.join(parent_slide_names)

    def _compute_child_slide_count(self):
        for slide in self:
            slide.child_slide_count = len(slide.child_slide_ids)
