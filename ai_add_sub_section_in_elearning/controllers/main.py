from odoo import http
from odoo.http import request


class WebsiteSlidesController(http.Controller):

    @http.route(['/course/<model("slide.channel"):channel>'], type='http', auth="public", website=True)
    def course_main(self, channel, **kwargs):
        # Fetch all slides for the given channel
        slides = request.env['slide.slide'].search([('channel_id', '=', channel.id)])

        # Render the course page with the slides data
        return request.render('website_slides.course_main', {
            'channel': channel,
            'slide_ids': slides
        })
