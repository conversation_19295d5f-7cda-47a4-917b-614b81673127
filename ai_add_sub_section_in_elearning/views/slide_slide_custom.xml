<odoo>
    <record id="view_slide_slide_form_inherit" model="ir.ui.view">
        <field name="name">slide.slide.form.inherit</field>
        <field name="model">slide.slide</field>
        <field name="inherit_id" ref="website_slides.view_slide_slide_form"/>
        <field name="arch" type="xml">
            <!-- Locate the 'Document' page and add the new page after it -->
            <xpath expr="//page[@name='document']" position="after">
                <page name="sub_sections" string="Sub Sections">
                    <group>
                        <field name="parent_slide_id" domain="[('channel_id', '=', channel_id), ('id', '!=', id)]" />
                        <field name="child_slide_ids" readonly="1">
                            <tree>
                                <field name="name"/>
                            </tree>
                        </field>
                        <!-- Add other custom fields here -->
                        <!-- Example: <field name="custom_field_1"/> -->
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
