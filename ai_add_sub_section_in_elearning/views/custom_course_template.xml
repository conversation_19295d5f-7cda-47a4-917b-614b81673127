<odoo>
    <data>
        <!-- Inherit the existing view and apply XPath modifications -->
        <template id="slide_aside_training_category_inherit" inherit_id="website_slides.slide_aside_training_category">
            <!-- Target the ul with the specified classes -->
            <xpath expr="//ul[contains(@class, 'o_wslides_lesson_aside_list_links')]" position="inside">
                <t t-foreach="category_slide_ids" t-as="aside_slide">
                    <!-- Check if the slide has child slides -->
                    <t t-if="aside_slide.child_slide_ids">
                        <!-- Iterate through child slides -->
                        <t t-foreach="aside_slide.child_slide_ids" t-as="child_slide">
                            <li class="ps-3">
                                <a t-att-href="'/slides/slide/%s' % (slug(child_slide))" class="text-decoration-none small">
                                    <i class="fa fa-link"/><span t-field="child_slide.name"/>
                                </a>
                            </li>
                        </t>
                    </t>
                </t>
            </xpath>
        </template>
    </data>
</odoo>
