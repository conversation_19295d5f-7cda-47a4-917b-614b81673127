#!/usr/bin/env python3
"""
Odoo Module Manager via XML-RPC
===============================

A comprehensive Python script to manage Odoo modules remotely using XML-RPC.
Supports install, upgrade, uninstall, and status checking of modules.

Features:
- Install modules
- Upgrade modules
- Uninstall modules
- Check module status
- List available modules
- Batch operations
- Error handling and logging
- Progress tracking

Author: AI Assistant
Version: 1.0.0
"""

import xmlrpc.client
import time
import logging
from typing import List, Dict, Optional, Union
import sys
import json
from datetime import datetime

class OdooModuleManager:
    """
    Odoo Module Manager using XML-RPC
    """
    
    def __init__(self, url: str, db: str, username: str, password: str):
        """
        Initialize the Odoo Module Manager
        
        Args:
            url (str): Odoo server URL (e.g., 'https://oneclickvakil.com')
            db (str): Database name
            username (str): Username for authentication
            password (str): Password for authentication
        """
        self.url = url.rstrip('/')
        self.db = db
        self.username = username
        self.password = password
        self.uid = None
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('odoo_module_manager.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # XML-RPC endpoints
        self.common = None
        self.models = None
        
        # Connect to Odoo
        self._connect()
    
    def _connect(self):
        """Establish connection to Odoo server"""
        try:
            self.logger.info(f"Connecting to Odoo server: {self.url}")
            
            # Common endpoint for authentication
            self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
            
            # Test connection
            version_info = self.common.version()
            self.logger.info(f"Connected to Odoo {version_info.get('server_version', 'Unknown')}")
            
            # Authenticate
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            
            if not self.uid:
                raise Exception("Authentication failed. Please check credentials.")
            
            self.logger.info(f"Authenticated successfully. User ID: {self.uid}")
            
            # Models endpoint for operations
            self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
            
        except Exception as e:
            self.logger.error(f"Connection failed: {str(e)}")
            raise
    
    def _execute(self, model: str, method: str, *args, **kwargs):
        """Execute a method on Odoo model"""
        try:
            return self.models.execute_kw(
                self.db, self.uid, self.password,
                model, method, args, kwargs
            )
        except Exception as e:
            self.logger.error(f"Error executing {model}.{method}: {str(e)}")
            raise
    
    def update_module_list(self) -> bool:
        """Update the list of available modules"""
        try:
            self.logger.info("Updating module list...")
            self._execute('ir.module.module', 'update_list')
            self.logger.info("Module list updated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to update module list: {str(e)}")
            return False
    
    def get_module_info(self, module_name: str) -> Optional[Dict]:
        """Get information about a specific module"""
        try:
            modules = self._execute('ir.module.module', 'search_read',
                                  [('name', '=', module_name)],
                                  {'fields': ['name', 'state', 'summary', 'author', 'version', 'depends_id']})
            
            if modules:
                return modules[0]
            else:
                self.logger.warning(f"Module '{module_name}' not found")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting module info for '{module_name}': {str(e)}")
            return None
    
    def list_modules(self, state: Optional[str] = None) -> List[Dict]:
        """
        List modules with optional state filter
        
        Args:
            state (str): Filter by state ('installed', 'uninstalled', 'to upgrade', etc.)
        
        Returns:
            List of module dictionaries
        """
        try:
            domain = []
            if state:
                domain.append(('state', '=', state))
            
            modules = self._execute('ir.module.module', 'search_read',
                                  domain,
                                  {'fields': ['name', 'state', 'summary', 'author', 'version']})
            
            self.logger.info(f"Found {len(modules)} modules" + (f" with state '{state}'" if state else ""))
            return modules
            
        except Exception as e:
            self.logger.error(f"Error listing modules: {str(e)}")
            return []
    
    def install_module(self, module_name: str, update_list: bool = True) -> bool:
        """
        Install a module
        
        Args:
            module_name (str): Name of the module to install
            update_list (bool): Whether to update module list first
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Installing module: {module_name}")
            
            if update_list:
                self.update_module_list()
            
            # Check if module exists
            module_info = self.get_module_info(module_name)
            if not module_info:
                self.logger.error(f"Module '{module_name}' not found")
                return False
            
            # Check current state
            current_state = module_info['state']
            if current_state == 'installed':
                self.logger.info(f"Module '{module_name}' is already installed")
                return True
            
            # Install the module
            module_id = module_info['id']
            self._execute('ir.module.module', 'button_immediate_install', [module_id])
            
            # Wait for installation to complete
            self._wait_for_module_state(module_name, 'installed')
            
            self.logger.info(f"Module '{module_name}' installed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to install module '{module_name}': {str(e)}")
            return False
    
    def upgrade_module(self, module_name: str) -> bool:
        """
        Upgrade a module
        
        Args:
            module_name (str): Name of the module to upgrade
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Upgrading module: {module_name}")
            
            # Check if module exists and is installed
            module_info = self.get_module_info(module_name)
            if not module_info:
                self.logger.error(f"Module '{module_name}' not found")
                return False
            
            current_state = module_info['state']
            if current_state != 'installed':
                self.logger.error(f"Module '{module_name}' is not installed (current state: {current_state})")
                return False
            
            # Upgrade the module
            module_id = module_info['id']
            self._execute('ir.module.module', 'button_immediate_upgrade', [module_id])
            
            # Wait for upgrade to complete
            self._wait_for_module_state(module_name, 'installed')
            
            self.logger.info(f"Module '{module_name}' upgraded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to upgrade module '{module_name}': {str(e)}")
            return False
    
    def uninstall_module(self, module_name: str) -> bool:
        """
        Uninstall a module
        
        Args:
            module_name (str): Name of the module to uninstall
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Uninstalling module: {module_name}")
            
            # Check if module exists and is installed
            module_info = self.get_module_info(module_name)
            if not module_info:
                self.logger.error(f"Module '{module_name}' not found")
                return False
            
            current_state = module_info['state']
            if current_state == 'uninstalled':
                self.logger.info(f"Module '{module_name}' is already uninstalled")
                return True
            
            if current_state != 'installed':
                self.logger.error(f"Module '{module_name}' cannot be uninstalled (current state: {current_state})")
                return False
            
            # Uninstall the module
            module_id = module_info['id']
            self._execute('ir.module.module', 'button_immediate_uninstall', [module_id])
            
            # Wait for uninstallation to complete
            self._wait_for_module_state(module_name, 'uninstalled')
            
            self.logger.info(f"Module '{module_name}' uninstalled successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to uninstall module '{module_name}': {str(e)}")
            return False

    def _wait_for_module_state(self, module_name: str, expected_state: str, timeout: int = 300) -> bool:
        """
        Wait for a module to reach the expected state

        Args:
            module_name (str): Name of the module
            expected_state (str): Expected state ('installed', 'uninstalled', etc.)
            timeout (int): Maximum time to wait in seconds

        Returns:
            bool: True if state reached, False if timeout
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            module_info = self.get_module_info(module_name)
            if module_info and module_info['state'] == expected_state:
                return True

            self.logger.info(f"Waiting for module '{module_name}' to reach state '{expected_state}'...")
            time.sleep(5)

        self.logger.error(f"Timeout waiting for module '{module_name}' to reach state '{expected_state}'")
        return False

    def batch_install(self, module_names: List[str]) -> Dict[str, bool]:
        """
        Install multiple modules

        Args:
            module_names (List[str]): List of module names to install

        Returns:
            Dict[str, bool]: Results for each module
        """
        results = {}
        self.logger.info(f"Starting batch installation of {len(module_names)} modules")

        for module_name in module_names:
            results[module_name] = self.install_module(module_name, update_list=False)

        return results

    def batch_upgrade(self, module_names: List[str]) -> Dict[str, bool]:
        """
        Upgrade multiple modules

        Args:
            module_names (List[str]): List of module names to upgrade

        Returns:
            Dict[str, bool]: Results for each module
        """
        results = {}
        self.logger.info(f"Starting batch upgrade of {len(module_names)} modules")

        for module_name in module_names:
            results[module_name] = self.upgrade_module(module_name)

        return results

    def batch_uninstall(self, module_names: List[str]) -> Dict[str, bool]:
        """
        Uninstall multiple modules

        Args:
            module_names (List[str]): List of module names to uninstall

        Returns:
            Dict[str, bool]: Results for each module
        """
        results = {}
        self.logger.info(f"Starting batch uninstallation of {len(module_names)} modules")

        for module_name in module_names:
            results[module_name] = self.uninstall_module(module_name)

        return results

    def get_module_dependencies(self, module_name: str) -> List[str]:
        """
        Get dependencies of a module

        Args:
            module_name (str): Name of the module

        Returns:
            List[str]: List of dependency module names
        """
        try:
            module_info = self.get_module_info(module_name)
            if not module_info:
                return []

            if module_info.get('depends_id'):
                dep_ids = module_info['depends_id']
                dependencies = self._execute('ir.module.module', 'read', dep_ids, ['name'])
                return [dep['name'] for dep in dependencies]

            return []

        except Exception as e:
            self.logger.error(f"Error getting dependencies for '{module_name}': {str(e)}")
            return []

    def export_module_list(self, filename: str = None, state: str = None) -> str:
        """
        Export module list to JSON file

        Args:
            filename (str): Output filename (optional)
            state (str): Filter by state (optional)

        Returns:
            str: Filename of exported file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"odoo_modules_{timestamp}.json"

        modules = self.list_modules(state)

        export_data = {
            'export_date': datetime.now().isoformat(),
            'server_url': self.url,
            'database': self.db,
            'filter_state': state,
            'total_modules': len(modules),
            'modules': modules
        }

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

        self.logger.info(f"Module list exported to: {filename}")
        return filename

    def print_module_status(self, module_names: List[str]):
        """
        Print status of multiple modules in a formatted table

        Args:
            module_names (List[str]): List of module names to check
        """
        print("\n" + "="*80)
        print(f"{'Module Name':<30} {'State':<15} {'Version':<15} {'Summary':<20}")
        print("="*80)

        for module_name in module_names:
            module_info = self.get_module_info(module_name)
            if module_info:
                name = module_info['name'][:29]
                state = module_info['state'][:14]
                version = (module_info.get('version') or 'N/A')[:14]
                summary = (module_info.get('summary') or 'N/A')[:19]
                print(f"{name:<30} {state:<15} {version:<15} {summary:<20}")
            else:
                print(f"{module_name:<30} {'NOT FOUND':<15} {'N/A':<15} {'N/A':<20}")

        print("="*80)


def main():
    """
    Example usage and CLI interface
    """
    # Example configuration - Replace with your actual credentials
    config = {
        'url': 'https://oneclickvakil.com',
        'db': 'oneclickvakil.com',
        'username': 'your_username',
        'password': 'your_password'
    }

    print("🚀 Odoo Module Manager via XML-RPC")
    print("="*50)

    try:
        # Initialize the manager
        manager = OdooModuleManager(**config)

        # Example operations
        print("\n📋 Available Operations:")
        print("1. List installed modules")
        print("2. Install a module")
        print("3. Upgrade a module")
        print("4. Uninstall a module")
        print("5. Check module status")
        print("6. Export module list")

        # Interactive mode
        while True:
            choice = input("\nEnter your choice (1-6) or 'q' to quit: ").strip()

            if choice == 'q':
                break
            elif choice == '1':
                modules = manager.list_modules('installed')
                print(f"\n📦 Installed Modules ({len(modules)}):")
                for module in modules[:10]:  # Show first 10
                    print(f"  - {module['name']} ({module['state']})")
                if len(modules) > 10:
                    print(f"  ... and {len(modules) - 10} more")

            elif choice == '2':
                module_name = input("Enter module name to install: ").strip()
                if module_name:
                    success = manager.install_module(module_name)
                    print(f"✅ Installation {'successful' if success else 'failed'}")

            elif choice == '3':
                module_name = input("Enter module name to upgrade: ").strip()
                if module_name:
                    success = manager.upgrade_module(module_name)
                    print(f"✅ Upgrade {'successful' if success else 'failed'}")

            elif choice == '4':
                module_name = input("Enter module name to uninstall: ").strip()
                if module_name:
                    success = manager.uninstall_module(module_name)
                    print(f"✅ Uninstallation {'successful' if success else 'failed'}")

            elif choice == '5':
                module_names = input("Enter module names (comma-separated): ").strip().split(',')
                module_names = [name.strip() for name in module_names if name.strip()]
                if module_names:
                    manager.print_module_status(module_names)

            elif choice == '6':
                filename = manager.export_module_list()
                print(f"✅ Module list exported to: {filename}")

            else:
                print("❌ Invalid choice. Please try again.")

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
