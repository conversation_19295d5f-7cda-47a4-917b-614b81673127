#!/usr/bin/env python3
"""
Module Testing Suite
Comprehensive testing for AI Module Generator and Generated Modules
"""

import os
import sys
import json
import time
import xml.etree.ElementTree as ET
from pathlib import Path

def log_message(message, level="INFO"):
    """Log a message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {level}: {message}")

def test_xml_validity(file_path):
    """Test if XML file is valid"""
    try:
        ET.parse(file_path)
        return True, None
    except ET.ParseError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def test_python_syntax(file_path):
    """Test if Python file has valid syntax"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        compile(content, file_path, 'exec')
        return True, None
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, str(e)

def test_manifest_validity(manifest_path):
    """Test if manifest file is valid"""
    try:
        with open(manifest_path, 'r') as f:
            content = f.read()
        
        # Try to evaluate as Python dict
        manifest_dict = eval(content)
        
        if not isinstance(manifest_dict, dict):
            return False, "Manifest is not a dictionary"
        
        # Check required keys
        required_keys = ['name', 'version', 'depends', 'data']
        missing_keys = [key for key in required_keys if key not in manifest_dict]
        
        if missing_keys:
            return False, f"Missing required keys: {missing_keys}"
        
        return True, "Valid manifest"
        
    except Exception as e:
        return False, str(e)

def test_module_structure(module_path):
    """Test complete module structure"""
    log_message(f"🧪 Testing module structure: {os.path.basename(module_path)}")
    
    if not os.path.exists(module_path):
        log_message(f"❌ Module not found: {module_path}", "ERROR")
        return False
    
    test_results = {
        "module_path": module_path,
        "tests": [],
        "overall_status": "PASS"
    }
    
    # Test 1: Required files exist
    required_files = [
        "__manifest__.py",
        "__init__.py",
        "models/__init__.py",
        "security/ir.model.access.csv"
    ]

    # Add views.xml requirement only for generated modules, not the generator itself
    module_name = os.path.basename(module_path)
    if module_name != "ai_module_generator":
        required_files.append("views/views.xml")
    
    for file_path in required_files:
        full_path = os.path.join(module_path, file_path)
        if os.path.exists(full_path):
            test_results["tests"].append({
                "test": f"File exists: {file_path}",
                "status": "PASS"
            })
        else:
            test_results["tests"].append({
                "test": f"File exists: {file_path}",
                "status": "FAIL",
                "error": "File not found"
            })
            test_results["overall_status"] = "FAIL"
    
    # Test 2: Manifest validity
    manifest_path = os.path.join(module_path, "__manifest__.py")
    if os.path.exists(manifest_path):
        is_valid, error = test_manifest_validity(manifest_path)
        test_results["tests"].append({
            "test": "Manifest validity",
            "status": "PASS" if is_valid else "FAIL",
            "error": error if not is_valid else None
        })
        if not is_valid:
            test_results["overall_status"] = "FAIL"
    
    # Test 3: XML files validity
    for root, dirs, files in os.walk(module_path):
        for file in files:
            if file.endswith('.xml'):
                xml_path = os.path.join(root, file)
                is_valid, error = test_xml_validity(xml_path)
                relative_path = os.path.relpath(xml_path, module_path)
                test_results["tests"].append({
                    "test": f"XML validity: {relative_path}",
                    "status": "PASS" if is_valid else "FAIL",
                    "error": error if not is_valid else None
                })
                if not is_valid:
                    test_results["overall_status"] = "FAIL"
    
    # Test 4: Python files syntax
    for root, dirs, files in os.walk(module_path):
        for file in files:
            if file.endswith('.py'):
                py_path = os.path.join(root, file)
                is_valid, error = test_python_syntax(py_path)
                relative_path = os.path.relpath(py_path, module_path)
                test_results["tests"].append({
                    "test": f"Python syntax: {relative_path}",
                    "status": "PASS" if is_valid else "FAIL",
                    "error": error if not is_valid else None
                })
                if not is_valid:
                    test_results["overall_status"] = "FAIL"
    
    # Test 5: Portal templates (if they exist)
    portal_dir = os.path.join(module_path, "views", "portal")
    if os.path.exists(portal_dir):
        portal_files = [f for f in os.listdir(portal_dir) if f.endswith('.xml')]
        if portal_files:
            test_results["tests"].append({
                "test": "Portal templates exist",
                "status": "PASS",
                "details": f"Found {len(portal_files)} portal template files"
            })
        else:
            test_results["tests"].append({
                "test": "Portal templates exist",
                "status": "WARN",
                "error": "Portal directory exists but no XML files found"
            })
    
    # Log results
    passed_tests = len([t for t in test_results["tests"] if t["status"] == "PASS"])
    failed_tests = len([t for t in test_results["tests"] if t["status"] == "FAIL"])
    total_tests = len(test_results["tests"])
    
    log_message(f"📊 Test Results: {passed_tests}/{total_tests} passed, {failed_tests} failed")
    
    if test_results["overall_status"] == "PASS":
        log_message("✅ All tests passed!")
    else:
        log_message("❌ Some tests failed:", "ERROR")
        for test in test_results["tests"]:
            if test["status"] == "FAIL":
                log_message(f"   - {test['test']}: {test.get('error', 'Unknown error')}", "ERROR")
    
    return test_results["overall_status"] == "PASS", test_results

def test_ai_module_generator():
    """Test AI Module Generator functionality"""
    log_message("🧪 Testing AI Module Generator")
    
    generator_path = "/mnt/extra-addons/ai_module_generator"
    
    if not os.path.exists(generator_path):
        log_message("❌ AI Module Generator not found", "ERROR")
        return False
    
    # Test generator structure
    success, results = test_module_structure(generator_path)
    
    # Additional tests specific to AI Module Generator
    wizard_path = os.path.join(generator_path, "wizards", "module_generator_wizard.py")
    if os.path.exists(wizard_path):
        log_message("✅ Module generator wizard found")
        
        # Check if our enhancements are present
        with open(wizard_path, 'r') as f:
            content = f.read()
        
        if "views/portal/{form.code}_portal.xml" in content:
            log_message("✅ Portal template enhancement found")
        else:
            log_message("❌ Portal template enhancement not found", "ERROR")
            success = False
    
    return success

def run_comprehensive_tests():
    """Run all tests"""
    log_message("🎯 Starting Comprehensive Module Testing")
    log_message("=" * 60)
    
    all_results = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "tests": {}
    }
    
    overall_success = True
    
    # Test 1: AI Module Generator
    log_message("\n📋 Test Suite 1: AI Module Generator")
    ai_gen_success = test_ai_module_generator()
    all_results["tests"]["ai_module_generator"] = {
        "status": "PASS" if ai_gen_success else "FAIL"
    }
    if not ai_gen_success:
        overall_success = False
    
    # Test 2: Generated Module (ovakil_customer_feedback)
    log_message("\n📋 Test Suite 2: Generated Module (ovakil_customer_feedback)")
    customer_feedback_path = "/mnt/extra-addons/ovakil_customer_feedback"
    cf_success, cf_results = test_module_structure(customer_feedback_path)
    all_results["tests"]["ovakil_customer_feedback"] = cf_results
    if not cf_success:
        overall_success = False
    
    # Save test results
    results_path = "/mnt/extra-addons/test_results.json"
    with open(results_path, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    log_message(f"\n📄 Test results saved: {results_path}")
    
    # Final summary
    log_message("\n" + "=" * 60)
    if overall_success:
        log_message("🎉 ALL TESTS PASSED!")
        log_message("✅ Modules are ready for deployment")
    else:
        log_message("❌ SOME TESTS FAILED!", "ERROR")
        log_message("🔧 Please review and fix the issues above")
    
    return overall_success

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
