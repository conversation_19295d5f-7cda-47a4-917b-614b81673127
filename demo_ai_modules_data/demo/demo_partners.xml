<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Law Firm Partners -->
        
        <!-- Law Firm Company -->
        <record id="partner_law_firm" model="res.partner">
            <field name="name">Johnson, Chen & Associates Law Firm</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-LAW-FIRM</field>
            <field name="website">https://www.jcalaw.com</field>
            <field name="street">1000 Legal Plaza</field>
            <field name="street2">Suite 2500</field>
            <field name="city">New York</field>
            <field name="state_id" ref="base.state_us_27"/>
            <field name="zip">10005</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="category_id" eval="[(6, 0, [])]"/>
        </record>
        
        <!-- Corporate Clients -->
        <record id="partner_tech_startup" model="res.partner">
            <field name="name">InnovateTech Solutions Inc.</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-TECH-01</field>
            <field name="website">https://www.innovatetech.com</field>
            <field name="street">500 Innovation Drive</field>
            <field name="city">San Francisco</field>
            <field name="state_id" ref="base.state_us_5"/>
            <field name="zip">94105</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="parent_id" ref="partner_law_firm"/>
        </record>
        
        <record id="partner_manufacturing_corp" model="res.partner">
            <field name="name">Global Manufacturing Corp</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-MANUF-1</field>
            <field name="website">https://www.globalmanuf.com</field>
            <field name="street">2000 Industrial Blvd</field>
            <field name="city">Detroit</field>
            <field name="state_id" ref="base.state_us_23"/>
            <field name="zip">48201</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="parent_id" ref="partner_law_firm"/>
        </record>
        
        <record id="partner_real_estate_group" model="res.partner">
            <field name="name">Premier Real Estate Group</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-REAL-EST</field>
            <field name="website">https://www.premierrealestate.com</field>
            <field name="street">300 Property Lane</field>
            <field name="city">Miami</field>
            <field name="state_id" ref="base.state_us_10"/>
            <field name="zip">33101</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="parent_id" ref="partner_law_firm"/>
        </record>
        
        <!-- Individual Clients -->
        <record id="partner_individual_client_1" model="res.partner">
            <field name="name">Dr. Amanda Foster</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-CLIENT-1</field>
            <field name="street">123 Medical Center Dr</field>
            <field name="city">Boston</field>
            <field name="state_id" ref="base.state_us_22"/>
            <field name="zip">02101</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="False"/>
            <field name="function">Medical Doctor</field>
            <field name="parent_id" ref="partner_law_firm"/>
        </record>
        
        <record id="partner_individual_client_2" model="res.partner">
            <field name="name">James Thompson</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-CLIENT-2</field>
            <field name="street">456 Entrepreneur Ave</field>
            <field name="city">Austin</field>
            <field name="state_id" ref="base.state_us_44"/>
            <field name="zip">73301</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="False"/>
            <field name="function">Business Owner</field>
            <field name="parent_id" ref="partner_law_firm"/>
        </record>
        
        <record id="partner_individual_client_3" model="res.partner">
            <field name="name">Maria Gonzalez</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-CLIENT-3</field>
            <field name="street">789 Family Court Rd</field>
            <field name="city">Phoenix</field>
            <field name="state_id" ref="base.state_us_4"/>
            <field name="zip">85001</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="False"/>
            <field name="function">Teacher</field>
            <field name="parent_id" ref="partner_law_firm"/>
        </record>
        
        <!-- Vendor/Service Provider Partners -->
        <record id="partner_court_reporting" model="res.partner">
            <field name="name">Elite Court Reporting Services</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-COURT-01</field>
            <field name="street">100 Justice Way</field>
            <field name="city">Washington</field>
            <field name="state_id" ref="base.state_us_47"/>
            <field name="zip">20001</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="supplier_rank" eval="1"/>
        </record>
        
        <record id="partner_legal_research" model="res.partner">
            <field name="name">LegalResearch Pro</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-RESEARCH</field>
            <field name="street">200 Library Street</field>
            <field name="city">Chicago</field>
            <field name="state_id" ref="base.state_us_14"/>
            <field name="zip">60601</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="supplier_rank" eval="1"/>
        </record>
        
        <record id="partner_document_storage" model="res.partner">
            <field name="name">SecureDoc Storage Solutions</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-STORAGE</field>
            <field name="street">300 Archive Avenue</field>
            <field name="city">Denver</field>
            <field name="state_id" ref="base.state_us_6"/>
            <field name="zip">80201</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
            <field name="supplier_rank" eval="1"/>
        </record>
    </data>
</odoo>
