<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Users for AI Legal System -->
        
        <!-- Legal Assistant User -->
        <record id="user_legal_assistant" model="res.users">
            <field name="name"><PERSON></field>
            <field name="login">legal.assistant</field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_user')])]"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
        
        <!-- Senior Lawyer User -->
        <record id="user_senior_lawyer" model="res.users">
            <field name="name"><PERSON></field>
            <field name="login">senior.lawyer</field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_user')])]"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
        
        <!-- Document Reviewer User -->
        <record id="user_document_reviewer" model="res.users">
            <field name="name">Emily Rodriguez</field>
            <field name="login">document.reviewer</field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_user')])]"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
        
        <!-- Client Portal User -->
        <record id="user_client_portal" model="res.users">
            <field name="name">Robert Wilson</field>
            <field name="login">client.portal</field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_portal')])]"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
        
        <!-- AI System Administrator -->
        <record id="user_ai_admin" model="res.users">
            <field name="name">David Kim</field>
            <field name="login">ai.admin</field>
            <field name="email"><EMAIL></field>
            <field name="password">demo123</field>
            <field name="groups_id" eval="[(6, 0, [ref('base.group_system')])]"/>
            <field name="company_id" ref="base.main_company"/>
        </record>
        
        <!-- Update partner information for demo users -->
        <record id="partner_legal_assistant" model="res.partner">
            <field name="name">Sarah Johnson</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-1001</field>
            <field name="function">Legal Assistant</field>
            <field name="is_company" eval="False"/>
            <field name="user_ids" eval="[(6, 0, [ref('user_legal_assistant')])]"/>
        </record>
        
        <record id="partner_senior_lawyer" model="res.partner">
            <field name="name">Michael Chen</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-1002</field>
            <field name="function">Senior Lawyer</field>
            <field name="is_company" eval="False"/>
            <field name="user_ids" eval="[(6, 0, [ref('user_senior_lawyer')])]"/>
        </record>
        
        <record id="partner_document_reviewer" model="res.partner">
            <field name="name">Emily Rodriguez</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-1003</field>
            <field name="function">Document Reviewer</field>
            <field name="is_company" eval="False"/>
            <field name="user_ids" eval="[(6, 0, [ref('user_document_reviewer')])]"/>
        </record>
        
        <record id="partner_client_portal" model="res.partner">
            <field name="name">Robert Wilson</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-1004</field>
            <field name="function">Business Owner</field>
            <field name="is_company" eval="False"/>
            <field name="user_ids" eval="[(6, 0, [ref('user_client_portal')])]"/>
        </record>
        
        <record id="partner_ai_admin" model="res.partner">
            <field name="name">David Kim</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-1005</field>
            <field name="function">AI System Administrator</field>
            <field name="is_company" eval="False"/>
            <field name="user_ids" eval="[(6, 0, [ref('user_ai_admin')])]"/>
        </record>
    </data>
</odoo>
