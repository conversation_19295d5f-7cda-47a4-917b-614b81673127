<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo AI Conversations -->
        <record id="conversation_employment_contract_1" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I need help drafting an employment contract for a Software Developer position. The employee will be <PERSON>, and the employer is ACME Corporation.</field>
            <field name="is_ai" eval="False"/>
        </record>

        <record id="conversation_employment_contract_2" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I'd be happy to help you draft an employment contract. To create a comprehensive agreement, I'll need some additional information:

1. What is the job title and department?
2. What is the salary and payment schedule?
3. What are the working hours and location?
4. What benefits will be provided?
5. What is the start date?
6. Are there any specific terms or conditions you'd like to include?

Please provide these details so I can create a tailored employment contract for you.</field>
            <field name="is_ai" eval="True"/>
        </record>

        <record id="conversation_employment_contract_3" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Here are the details:
- Job Title: Senior Software Developer
- Department: Engineering
- Salary: $95,000 annually, paid bi-weekly
- Working Hours: 40 hours per week, Monday-Friday 9 AM to 5 PM
- Location: Hybrid (3 days in office, 2 days remote)
- Benefits: Health insurance, dental, vision, 401k with 4% match, 3 weeks PTO
- Start Date: Next Monday
- Special terms: 6-month probationary period, non-compete clause for 1 year</field>
            <field name="is_ai" eval="False"/>
        </record>

        <record id="conversation_nda_1" model="legal.document.conversation">
            <field name="request_id" ref="request_nda_jane"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I need a non-disclosure agreement for a consulting project. Jane Smith will be working with our company on a confidential software development project.</field>
            <field name="is_ai" eval="False"/>
        </record>

        <record id="conversation_nda_2" model="legal.document.conversation">
            <field name="request_id" ref="request_nda_jane"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I'll help you create a comprehensive NDA for your consulting project. To ensure proper protection of your confidential information, please provide:

1. What type of confidential information will be shared? (technical specifications, business plans, customer data, etc.)
2. What is the duration of the confidentiality obligation?
3. Are there any specific exceptions to confidentiality?
4. What are the permitted uses of the confidential information?
5. What jurisdiction should govern the agreement?
6. Are there any specific penalties for breach?

This information will help me draft an NDA that properly protects your interests.</field>
            <field name="is_ai" eval="True"/>
        </record>

        <record id="conversation_business_formation_1" model="legal.document.conversation">
            <field name="request_id" ref="request_business_formation_acme"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">We need to form a subsidiary company for our new product line. The parent company is ACME Corporation, and we want to create ACME Digital Solutions LLC.</field>
            <field name="is_ai" eval="False"/>
        </record>

        <record id="conversation_business_formation_2" model="legal.document.conversation">
            <field name="request_id" ref="request_business_formation_acme"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I'll assist you with forming ACME Digital Solutions LLC as a subsidiary. For the formation documents, I need:

1. State of incorporation
2. Business purpose and activities
3. Initial capital contribution
4. Management structure (member-managed or manager-managed)
5. Ownership percentage of parent company
6. Registered agent information
7. Principal business address
8. Any specific operating agreement terms

Once I have these details, I can prepare the Articles of Organization, Operating Agreement, and other necessary formation documents.</field>
            <field name="is_ai" eval="True"/>
        </record>

        <record id="conversation_will_1" model="legal.document.conversation">
            <field name="request_id" ref="request_will_preparation"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I need help preparing a will. I want to ensure my assets are properly distributed and my children are taken care of if something happens to me.</field>
            <field name="is_ai" eval="False"/>
        </record>

        <record id="conversation_will_2" model="legal.document.conversation">
            <field name="request_id" ref="request_will_preparation"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I understand this is an important and sensitive matter. I'm here to help you create a comprehensive will that protects your loved ones. To draft your will properly, I'll need information about:

1. Your full legal name and address
2. Marital status and spouse's information
3. Children's names and ages
4. List of major assets (real estate, investments, personal property)
5. Beneficiaries and how you want assets distributed
6. Guardian preferences for minor children
7. Executor selection
8. Any specific bequests or charitable donations
9. Funeral and burial preferences

Would you like to start with any particular section, or shall we go through these systematically?</field>
            <field name="is_ai" eval="True"/>
        </record>
    </data>
</odoo>
