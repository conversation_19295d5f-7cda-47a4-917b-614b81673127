<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Form Responses -->
        
        <!-- Employment Contract Form Response -->
        <record id="form_response_employment_1" model="legal.document.form.response">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="conversation_id" ref="conversation_employment_contract_2"/>
            <field name="form_data"><![CDATA[{
                "title": "Employment Contract Information",
                "description": "Please provide the following details for your employment contract",
                "fields": [
                    {
                        "name": "company_name",
                        "label": "Company Name",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "company_address",
                        "label": "Company Address",
                        "type": "textarea",
                        "required": true
                    },
                    {
                        "name": "employee_name",
                        "label": "Employee Full Name",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "job_title",
                        "label": "Job Title",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "annual_salary",
                        "label": "Annual Salary",
                        "type": "number",
                        "required": true
                    },
                    {
                        "name": "start_date",
                        "label": "Start Date",
                        "type": "date",
                        "required": true
                    },
                    {
                        "name": "work_location",
                        "label": "Work Location",
                        "type": "select",
                        "options": [
                            {"value": "office", "label": "Office Only"},
                            {"value": "remote", "label": "Remote Only"},
                            {"value": "hybrid", "label": "Hybrid"}
                        ],
                        "required": true
                    }
                ]
            }]]></field>
            <field name="response_data"><![CDATA[{
                "company_name": "ACME Corporation",
                "company_address": "789 Business Blvd, Chicago, IL 60601",
                "employee_name": "John Doe",
                "job_title": "Senior Software Developer",
                "annual_salary": "95000",
                "start_date": "2024-02-01",
                "work_location": "hybrid"
            }]]></field>
        </record>
        
        <!-- NDA Form Response -->
        <record id="form_response_nda_1" model="legal.document.form.response">
            <field name="request_id" ref="request_nda_jane"/>
            <field name="conversation_id" ref="conversation_nda_2"/>
            <field name="form_data"><![CDATA[{
                "title": "Non-Disclosure Agreement Details",
                "description": "Please provide information for your NDA",
                "fields": [
                    {
                        "name": "disclosing_party",
                        "label": "Disclosing Party (Company sharing information)",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "receiving_party",
                        "label": "Receiving Party (Person/Company receiving information)",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "confidential_info_type",
                        "label": "Type of Confidential Information",
                        "type": "select",
                        "options": [
                            {"value": "technical", "label": "Technical Specifications"},
                            {"value": "business", "label": "Business Plans"},
                            {"value": "financial", "label": "Financial Information"},
                            {"value": "customer", "label": "Customer Data"},
                            {"value": "mixed", "label": "Mixed/Multiple Types"}
                        ],
                        "required": true
                    },
                    {
                        "name": "purpose",
                        "label": "Purpose of Information Sharing",
                        "type": "textarea",
                        "required": true
                    },
                    {
                        "name": "duration",
                        "label": "Duration of Confidentiality",
                        "type": "select",
                        "options": [
                            {"value": "1_year", "label": "1 Year"},
                            {"value": "2_years", "label": "2 Years"},
                            {"value": "5_years", "label": "5 Years"},
                            {"value": "indefinite", "label": "Indefinite"}
                        ],
                        "required": true
                    }
                ]
            }]]></field>
            <field name="response_data"><![CDATA[{
                "disclosing_party": "ACME Corporation",
                "receiving_party": "Jane Smith",
                "confidential_info_type": "technical",
                "purpose": "Software development consulting project for new mobile application",
                "duration": "2_years"
            }]]></field>
        </record>
        
        <!-- Business Formation Form Response -->
        <record id="form_response_business_1" model="legal.document.form.response">
            <field name="request_id" ref="request_business_formation_acme"/>
            <field name="conversation_id" ref="conversation_business_formation_2"/>
            <field name="form_data"><![CDATA[{
                "title": "Business Formation Information",
                "description": "Details for forming your new business entity",
                "fields": [
                    {
                        "name": "business_name",
                        "label": "Business Name",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "business_type",
                        "label": "Business Type",
                        "type": "select",
                        "options": [
                            {"value": "llc", "label": "Limited Liability Company (LLC)"},
                            {"value": "corporation", "label": "Corporation"},
                            {"value": "partnership", "label": "Partnership"},
                            {"value": "sole_proprietorship", "label": "Sole Proprietorship"}
                        ],
                        "required": true
                    },
                    {
                        "name": "state_incorporation",
                        "label": "State of Incorporation",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "business_purpose",
                        "label": "Business Purpose",
                        "type": "textarea",
                        "required": true
                    },
                    {
                        "name": "initial_capital",
                        "label": "Initial Capital Contribution",
                        "type": "number",
                        "required": true
                    },
                    {
                        "name": "registered_agent",
                        "label": "Registered Agent",
                        "type": "text",
                        "required": true
                    }
                ]
            }]]></field>
            <field name="response_data"><![CDATA[{
                "business_name": "ACME Digital Solutions LLC",
                "business_type": "llc",
                "state_incorporation": "Illinois",
                "business_purpose": "Software development, digital solutions consulting, and technology services",
                "initial_capital": "50000",
                "registered_agent": "ACME Corporation"
            }]]></field>
        </record>
        
        <!-- Will Preparation Form Response -->
        <record id="form_response_will_1" model="legal.document.form.response">
            <field name="request_id" ref="request_will_preparation"/>
            <field name="conversation_id" ref="conversation_will_2"/>
            <field name="form_data"><![CDATA[{
                "title": "Will Preparation Information",
                "description": "Personal and asset information for your will",
                "fields": [
                    {
                        "name": "testator_name",
                        "label": "Your Full Legal Name",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "marital_status",
                        "label": "Marital Status",
                        "type": "select",
                        "options": [
                            {"value": "single", "label": "Single"},
                            {"value": "married", "label": "Married"},
                            {"value": "divorced", "label": "Divorced"},
                            {"value": "widowed", "label": "Widowed"}
                        ],
                        "required": true
                    },
                    {
                        "name": "spouse_name",
                        "label": "Spouse Name (if applicable)",
                        "type": "text",
                        "required": false
                    },
                    {
                        "name": "children_info",
                        "label": "Children Information",
                        "type": "textarea",
                        "required": false
                    },
                    {
                        "name": "executor_name",
                        "label": "Preferred Executor",
                        "type": "text",
                        "required": true
                    },
                    {
                        "name": "primary_beneficiary",
                        "label": "Primary Beneficiary",
                        "type": "text",
                        "required": true
                    }
                ]
            }]]></field>
            <field name="response_data"><![CDATA[{
                "testator_name": "Jane Smith",
                "marital_status": "married",
                "spouse_name": "Robert Smith",
                "children_info": "Two children: Emily Smith (age 16) and Michael Smith (age 14)",
                "executor_name": "Robert Smith",
                "primary_beneficiary": "Robert Smith (spouse), with provisions for children"
            }]]></field>
        </record>
    </data>
</odoo>
