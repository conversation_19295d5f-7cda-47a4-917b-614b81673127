<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Complete Workflow Demo: AI Chat → Form Generation → Payment → Document -->
        
        <!-- Step 1: Initial AI Conversation with Form Generation -->
        <record id="workflow_conversation_1" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Hi! I need help creating an employment contract for a new software developer we're hiring. Can you help me with this?</field>
            <field name="is_ai" eval="False"/>
        </record>
        
        <record id="workflow_conversation_2" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">I'd be happy to help you create a comprehensive employment contract! To ensure we include all the necessary details and create a legally sound agreement, I'll need to gather some specific information from you.

Let me create a form to collect all the essential details systematically. This will help ensure we don't miss any important elements of the employment contract.</field>
            <field name="is_ai" eval="True"/>
            <field name="has_form" eval="True"/>
            <field name="form_data"><![CDATA[{
                "title": "Employment Contract Information",
                "description": "Please provide the following details to create your customized employment contract",
                "form_id": "employment_contract_form",
                "fields": [
                    {
                        "name": "company_name",
                        "label": "Company Legal Name",
                        "type": "text",
                        "required": true,
                        "placeholder": "Enter the full legal name of your company"
                    },
                    {
                        "name": "company_address",
                        "label": "Company Address",
                        "type": "textarea",
                        "required": true,
                        "placeholder": "Complete business address including city, state, and ZIP code"
                    },
                    {
                        "name": "employee_name",
                        "label": "Employee Full Name",
                        "type": "text",
                        "required": true,
                        "placeholder": "Employee's complete legal name"
                    },
                    {
                        "name": "job_title",
                        "label": "Job Title",
                        "type": "text",
                        "required": true,
                        "placeholder": "Official position title"
                    },
                    {
                        "name": "department",
                        "label": "Department",
                        "type": "select",
                        "required": true,
                        "options": [
                            {"value": "engineering", "label": "Engineering"},
                            {"value": "product", "label": "Product"},
                            {"value": "design", "label": "Design"},
                            {"value": "marketing", "label": "Marketing"},
                            {"value": "sales", "label": "Sales"},
                            {"value": "other", "label": "Other"}
                        ]
                    },
                    {
                        "name": "annual_salary",
                        "label": "Annual Salary (USD)",
                        "type": "number",
                        "required": true,
                        "placeholder": "Enter annual salary amount"
                    },
                    {
                        "name": "start_date",
                        "label": "Employment Start Date",
                        "type": "date",
                        "required": true
                    },
                    {
                        "name": "work_arrangement",
                        "label": "Work Arrangement",
                        "type": "select",
                        "required": true,
                        "options": [
                            {"value": "office", "label": "Office Only"},
                            {"value": "remote", "label": "Remote Only"},
                            {"value": "hybrid", "label": "Hybrid (Office + Remote)"}
                        ]
                    },
                    {
                        "name": "benefits",
                        "label": "Benefits Package",
                        "type": "textarea",
                        "required": false,
                        "placeholder": "Describe health insurance, retirement plans, PTO, etc."
                    },
                    {
                        "name": "probation_period",
                        "label": "Probationary Period (months)",
                        "type": "number",
                        "required": false,
                        "placeholder": "Enter number of months (e.g., 3, 6)"
                    }
                ]
            }]]></field>
        </record>
        
        <!-- Step 2: User Submits Form Response -->
        <record id="workflow_form_response" model="legal.document.form.response">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="conversation_id" ref="workflow_conversation_2"/>
            <field name="form_data"><![CDATA[{
                "title": "Employment Contract Information",
                "form_id": "employment_contract_form"
            }]]></field>
            <field name="response_data"><![CDATA[{
                "company_name": "ACME Corporation",
                "company_address": "789 Business Blvd, Suite 200, Chicago, IL 60601",
                "employee_name": "John Doe",
                "job_title": "Senior Software Developer",
                "department": "engineering",
                "annual_salary": "95000",
                "start_date": "2024-02-01",
                "work_arrangement": "hybrid",
                "benefits": "Health insurance (medical, dental, vision), 401k with 4% company match, 3 weeks PTO, flexible work hours",
                "probation_period": "6"
            }]]></field>
        </record>
        
        <!-- Step 3: AI Processes Form and Asks Follow-up Questions -->
        <record id="workflow_conversation_3" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Perfect! I have all the basic employment information. Based on what you've provided, I have a few follow-up questions to make this contract comprehensive:

**Compensation Details:**
- Will the salary be paid bi-weekly, monthly, or another schedule?
- Are there any bonus structures or commission plans?

**Work Specifics:**
- For the hybrid arrangement, how many days per week in office?
- What are the standard working hours?

**Additional Terms:**
- Do you need any confidentiality/non-disclosure clauses?
- Will there be any non-compete restrictions?
- Are there specific termination notice requirements?

Please let me know these details, and then I can prepare your employment contract for review!</field>
            <field name="is_ai" eval="True"/>
        </record>
        
        <!-- Step 4: User Provides Additional Details -->
        <record id="workflow_conversation_4" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Great questions! Here are the additional details:

- Salary paid bi-weekly
- No bonus structure initially, but performance reviews every 6 months
- Hybrid: 3 days in office, 2 days remote
- Standard hours: 9 AM to 5 PM, with some flexibility
- Yes, need confidentiality clause for proprietary software
- 1-year non-compete within 50-mile radius
- 2 weeks notice required for termination by either party</field>
            <field name="is_ai" eval="False"/>
        </record>
        
        <!-- Step 5: AI Confirms Details and Requests Payment -->
        <record id="workflow_conversation_5" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Excellent! I now have all the information needed to create a comprehensive employment contract for John Doe at ACME Corporation. 

**Contract Summary:**
- Position: Senior Software Developer, Engineering Department
- Compensation: $95,000 annually, paid bi-weekly
- Work: Hybrid (3 office/2 remote), 9 AM-5 PM with flexibility
- Benefits: Health insurance, 401k match, 3 weeks PTO
- Terms: 6-month probation, confidentiality clause, 1-year non-compete
- Notice: 2 weeks required by either party

The contract will include all standard employment provisions plus your specific requirements for confidentiality and non-compete protections.

**To proceed with document generation, please complete the payment for our Employment Contract Drafting service ($299).**

Once payment is confirmed, I'll generate your customized employment contract within 24 hours. The document will be professionally formatted and ready for signatures.

Would you like to proceed with payment?</field>
            <field name="is_ai" eval="True"/>
        </record>
        
        <!-- Step 6: Payment Confirmation and Document Generation -->
        <record id="workflow_conversation_6" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Yes, I'd like to proceed with payment. Please send me the payment link.</field>
            <field name="is_ai" eval="False"/>
        </record>
        
        <record id="workflow_conversation_7" model="legal.document.conversation">
            <field name="request_id" ref="request_employment_contract_john"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="message">Perfect! I'm processing your payment request now. You'll receive a secure payment link shortly.

**Next Steps:**
1. ✅ Information collected and verified
2. 🔄 Payment processing (you'll receive a link)
3. ⏳ Document generation (within 24 hours after payment)
4. 📧 Delivery via email and portal access

Once payment is complete, our legal document generation system will create your employment contract using the information provided. You'll be able to:
- Download the contract in PDF format
- Access it through your client portal
- Request revisions if needed
- Get guidance on execution and signatures

Thank you for choosing our AI Legal Document service! I'll notify you as soon as your contract is ready.</field>
            <field name="is_ai" eval="True"/>
        </record>
        
        <!-- AI Survey Bridge for this workflow -->
        <record id="workflow_survey_bridge" model="ai.survey.bridge">
            <field name="name">Employment Contract Workflow Form</field>
            <field name="description">Complete employment contract information collection</field>
            <field name="source">ai</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="document_request_id" ref="request_employment_contract_john"/>
            <field name="state">active</field>
            <field name="ai_response">I'll create a comprehensive form to collect all employment contract details systematically.</field>
            <field name="form_json"><![CDATA[{
                "title": "Employment Contract Information",
                "description": "Complete details for your employment agreement",
                "workflow_stage": "information_collection",
                "questions": [
                    {
                        "title": "Company Legal Name",
                        "question_type": "text_box",
                        "is_required": true,
                        "sequence": 1
                    },
                    {
                        "title": "Employee Full Name",
                        "question_type": "text_box", 
                        "is_required": true,
                        "sequence": 2
                    },
                    {
                        "title": "Job Title and Department",
                        "question_type": "text_box",
                        "is_required": true,
                        "sequence": 3
                    },
                    {
                        "title": "Annual Salary",
                        "question_type": "numerical_box",
                        "is_required": true,
                        "sequence": 4
                    },
                    {
                        "title": "Work Arrangement",
                        "question_type": "simple_choice",
                        "is_required": true,
                        "sequence": 5,
                        "answers": [
                            {"value": "office", "text": "Office Only"},
                            {"value": "remote", "text": "Remote Only"},
                            {"value": "hybrid", "text": "Hybrid"}
                        ]
                    }
                ]
            }]]></field>
        </record>
    </data>
</odoo>
