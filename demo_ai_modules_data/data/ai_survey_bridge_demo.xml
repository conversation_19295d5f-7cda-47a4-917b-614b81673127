<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo AI Survey Bridge Records -->
        
        <!-- Employment Contract Survey Bridge -->
        <record id="survey_bridge_employment" model="ai.survey.bridge">
            <field name="name">Employment Contract Information Form</field>
            <field name="description">AI-generated form for collecting employment contract details</field>
            <field name="source">ai</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="document_request_id" ref="request_employment_contract_john"/>
            <field name="state">active</field>
            <field name="ai_response">I'll help you create an employment contract. To ensure all necessary details are included, please provide the following information through this form.</field>
            <field name="form_json"><![CDATA[{
                "title": "Employment Contract Information",
                "description": "Please provide the following details for your employment contract",
                "questions": [
                    {
                        "title": "Company Name",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Enter the full legal name of the company"
                    },
                    {
                        "title": "Company Address",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Full business address including city, state, and ZIP"
                    },
                    {
                        "title": "Employee Full Name",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Employee's full legal name"
                    },
                    {
                        "title": "Job Title",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Official job title/position"
                    },
                    {
                        "title": "Department",
                        "question_type": "simple_choice",
                        "is_required": true,
                        "answers": [
                            {"value": "engineering", "text": "Engineering"},
                            {"value": "marketing", "text": "Marketing"},
                            {"value": "sales", "text": "Sales"},
                            {"value": "hr", "text": "Human Resources"},
                            {"value": "finance", "text": "Finance"},
                            {"value": "other", "text": "Other"}
                        ]
                    },
                    {
                        "title": "Annual Salary",
                        "question_type": "numerical_box",
                        "is_required": true,
                        "placeholder": "Enter annual salary amount"
                    },
                    {
                        "title": "Start Date",
                        "question_type": "date",
                        "is_required": true
                    }
                ]
            }]]></field>
        </record>
        
        <!-- NDA Survey Bridge -->
        <record id="survey_bridge_nda" model="ai.survey.bridge">
            <field name="name">NDA Information Collection</field>
            <field name="description">Form for gathering non-disclosure agreement details</field>
            <field name="source">ai</field>
            <field name="survey_id" ref="survey_nda_details"/>
            <field name="document_request_id" ref="request_nda_jane"/>
            <field name="state">active</field>
            <field name="ai_response">To create a comprehensive NDA that protects your confidential information, I need some specific details. Please fill out this form.</field>
            <field name="form_json"><![CDATA[{
                "title": "Non-Disclosure Agreement Details",
                "description": "Provide information to create your customized NDA",
                "questions": [
                    {
                        "title": "Disclosing Party (Company/Individual sharing information)",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Name of party sharing confidential information"
                    },
                    {
                        "title": "Receiving Party (Company/Individual receiving information)",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Name of party receiving confidential information"
                    },
                    {
                        "title": "Type of Confidential Information",
                        "question_type": "multiple_choice",
                        "is_required": true,
                        "answers": [
                            {"value": "technical", "text": "Technical specifications and trade secrets"},
                            {"value": "business", "text": "Business plans and strategies"},
                            {"value": "financial", "text": "Financial information and data"},
                            {"value": "customer", "text": "Customer lists and data"},
                            {"value": "proprietary", "text": "Proprietary software or processes"},
                            {"value": "other", "text": "Other confidential information"}
                        ]
                    },
                    {
                        "title": "Purpose of Information Sharing",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Describe the business purpose for sharing this information"
                    },
                    {
                        "title": "Duration of Confidentiality Obligation",
                        "question_type": "simple_choice",
                        "is_required": true,
                        "answers": [
                            {"value": "1_year", "text": "1 Year"},
                            {"value": "2_years", "text": "2 Years"},
                            {"value": "3_years", "text": "3 Years"},
                            {"value": "5_years", "text": "5 Years"},
                            {"value": "indefinite", "text": "Indefinite"}
                        ]
                    }
                ]
            }]]></field>
        </record>
        
        <!-- Business Formation Survey Bridge -->
        <record id="survey_bridge_business_formation" model="ai.survey.bridge">
            <field name="name">Business Formation Details</field>
            <field name="description">Comprehensive form for business entity formation</field>
            <field name="source">ai</field>
            <field name="document_request_id" ref="request_business_formation_acme"/>
            <field name="state">active</field>
            <field name="ai_response">I'll help you with the business formation process. Please provide the required information through this detailed form.</field>
            <field name="form_json"><![CDATA[{
                "title": "Business Formation Information",
                "description": "Complete details needed for forming your business entity",
                "questions": [
                    {
                        "title": "Proposed Business Name",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Enter your desired business name"
                    },
                    {
                        "title": "Business Entity Type",
                        "question_type": "simple_choice",
                        "is_required": true,
                        "answers": [
                            {"value": "llc", "text": "Limited Liability Company (LLC)"},
                            {"value": "corporation", "text": "Corporation (C-Corp)"},
                            {"value": "s_corp", "text": "S-Corporation"},
                            {"value": "partnership", "text": "Partnership"},
                            {"value": "sole_prop", "text": "Sole Proprietorship"}
                        ]
                    },
                    {
                        "title": "State of Incorporation",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "State where you want to incorporate"
                    },
                    {
                        "title": "Business Purpose and Activities",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Describe the main business activities and purpose"
                    },
                    {
                        "title": "Initial Capital Contribution",
                        "question_type": "numerical_box",
                        "is_required": true,
                        "placeholder": "Initial investment amount"
                    },
                    {
                        "title": "Management Structure",
                        "question_type": "simple_choice",
                        "is_required": true,
                        "answers": [
                            {"value": "member_managed", "text": "Member-Managed (LLC)"},
                            {"value": "manager_managed", "text": "Manager-Managed (LLC)"},
                            {"value": "board_managed", "text": "Board of Directors (Corporation)"}
                        ]
                    },
                    {
                        "title": "Registered Agent Information",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Name and address of registered agent"
                    }
                ]
            }]]></field>
        </record>
        
        <!-- Will Preparation Survey Bridge -->
        <record id="survey_bridge_will" model="ai.survey.bridge">
            <field name="name">Will and Testament Information</field>
            <field name="description">Sensitive information collection for will preparation</field>
            <field name="source">ai</field>
            <field name="document_request_id" ref="request_will_preparation"/>
            <field name="state">active</field>
            <field name="ai_response">Creating a will is an important step in protecting your loved ones. I'll guide you through this process with care and attention to detail.</field>
            <field name="form_json"><![CDATA[{
                "title": "Will and Testament Information",
                "description": "Personal and estate information for your will preparation",
                "questions": [
                    {
                        "title": "Your Full Legal Name",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Enter your complete legal name as it appears on official documents"
                    },
                    {
                        "title": "Current Address",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Your current residential address"
                    },
                    {
                        "title": "Marital Status",
                        "question_type": "simple_choice",
                        "is_required": true,
                        "answers": [
                            {"value": "single", "text": "Single"},
                            {"value": "married", "text": "Married"},
                            {"value": "divorced", "text": "Divorced"},
                            {"value": "widowed", "text": "Widowed"},
                            {"value": "separated", "text": "Separated"}
                        ]
                    },
                    {
                        "title": "Spouse Information (if applicable)",
                        "question_type": "text_box",
                        "is_required": false,
                        "placeholder": "Spouse's full name and any relevant details"
                    },
                    {
                        "title": "Children Information",
                        "question_type": "text_box",
                        "is_required": false,
                        "placeholder": "Names and ages of all children, including stepchildren"
                    },
                    {
                        "title": "Primary Executor",
                        "question_type": "text_box",
                        "is_required": true,
                        "placeholder": "Name of person you want to execute your will"
                    },
                    {
                        "title": "Alternate Executor",
                        "question_type": "text_box",
                        "is_required": false,
                        "placeholder": "Backup executor in case primary is unavailable"
                    },
                    {
                        "title": "Guardian for Minor Children (if applicable)",
                        "question_type": "text_box",
                        "is_required": false,
                        "placeholder": "Preferred guardian for any minor children"
                    }
                ]
            }]]></field>
        </record>
    </data>
</odoo>
