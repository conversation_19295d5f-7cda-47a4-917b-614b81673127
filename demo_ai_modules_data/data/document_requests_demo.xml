<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Document Requests -->
        <record id="demo_partner_john_doe" model="res.partner">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0123</field>
            <field name="street">123 Main Street</field>
            <field name="city">New York</field>
            <field name="state_id" ref="base.state_us_27"/>
            <field name="zip">10001</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="False"/>
        </record>

        <record id="demo_partner_jane_smith" model="res.partner">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0456</field>
            <field name="street">456 Oak Avenue</field>
            <field name="city">Los Angeles</field>
            <field name="state_id" ref="base.state_us_5"/>
            <field name="zip">90210</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="False"/>
        </record>

        <record id="demo_partner_acme_corp" model="res.partner">
            <field name="name">ACME Corporation</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0789</field>
            <field name="street">789 Business Blvd</field>
            <field name="city">Chicago</field>
            <field name="state_id" ref="base.state_us_14"/>
            <field name="zip">60601</field>
            <field name="country_id" ref="base.us"/>
            <field name="is_company" eval="True"/>
        </record>

        <!-- Demo Document Requests -->
        <record id="request_employment_contract_john" model="legal.document.request">
            <field name="partner_id" ref="demo_partner_john_doe"/>
            <field name="service_id" ref="service_contract_drafting"/>
            <field name="language_id" ref="base.lang_en"/>
            <field name="state">draft</field>
        </record>

        <record id="request_nda_jane" model="legal.document.request">
            <field name="partner_id" ref="demo_partner_jane_smith"/>
            <field name="service_id" ref="service_nda"/>
            <field name="language_id" ref="base.lang_en"/>
            <field name="state">in_progress</field>
        </record>

        <record id="request_business_formation_acme" model="legal.document.request">
            <field name="partner_id" ref="demo_partner_acme_corp"/>
            <field name="service_id" ref="service_business_formation"/>
            <field name="language_id" ref="base.lang_en"/>
            <field name="state">completed</field>
        </record>

        <record id="request_lease_agreement" model="legal.document.request">
            <field name="partner_id" ref="demo_partner_acme_corp"/>
            <field name="service_id" ref="service_lease_agreement"/>
            <field name="language_id" ref="base.lang_en"/>
            <field name="state">processing</field>
        </record>

        <record id="request_power_of_attorney" model="legal.document.request">
            <field name="partner_id" ref="demo_partner_john_doe"/>
            <field name="service_id" ref="service_power_of_attorney"/>
            <field name="language_id" ref="base.lang_en"/>
            <field name="state">draft</field>
        </record>

        <record id="request_will_preparation" model="legal.document.request">
            <field name="partner_id" ref="demo_partner_jane_smith"/>
            <field name="service_id" ref="service_will_preparation"/>
            <field name="language_id" ref="base.lang_en"/>
            <field name="state">in_progress</field>
        </record>
    </data>
</odoo>
