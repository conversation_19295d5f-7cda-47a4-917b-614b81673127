<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Products for Legal Services -->
        <record id="product_contract_drafting" model="product.product">
            <field name="name">Contract Drafting Service</field>
            <field name="type">service</field>
            <field name="list_price">500.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="product_will_preparation" model="product.product">
            <field name="name">Will Preparation Service</field>
            <field name="type">service</field>
            <field name="list_price">300.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="product_power_of_attorney" model="product.product">
            <field name="name">Power of Attorney Service</field>
            <field name="type">service</field>
            <field name="list_price">200.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="product_lease_agreement" model="product.product">
            <field name="name">Lease Agreement Service</field>
            <field name="type">service</field>
            <field name="list_price">250.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="product_nda" model="product.product">
            <field name="name">Non-Disclosure Agreement Service</field>
            <field name="type">service</field>
            <field name="list_price">150.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="product_business_formation" model="product.product">
            <field name="name">Business Formation Service</field>
            <field name="type">service</field>
            <field name="list_price">800.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="product_employment_law" model="product.product">
            <field name="name">Employment Law Service</field>
            <field name="type">service</field>
            <field name="list_price">400.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <!-- Demo Legal Document Services -->
        <record id="service_contract_drafting" model="legal.document.service">
            <field name="name">Contract Drafting</field>
            <field name="code">CONTRACT_DRAFT</field>
            <field name="description">Professional contract drafting service for business agreements, employment contracts, and service agreements.</field>
            <field name="service_type_id" ref="ai_legal_document_core.service_type_rti"/>
            <field name="product_id" ref="product_contract_drafting"/>
            <field name="chatbot_config_id" ref="chatbot_config_openai_contracts"/>
            <field name="active" eval="True"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
        </record>

        <record id="service_will_preparation" model="legal.document.service">
            <field name="name">Will Preparation</field>
            <field name="code">WILL_PREP</field>
            <field name="description">Comprehensive will and testament preparation service including asset distribution and executor designation.</field>
            <field name="service_type_id" ref="ai_legal_document_core.service_type_rti"/>
            <field name="product_id" ref="product_will_preparation"/>
            <field name="chatbot_config_id" ref="chatbot_config_claude_wills"/>
            <field name="active" eval="True"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
        </record>

        <record id="service_power_of_attorney" model="legal.document.service">
            <field name="name">Power of Attorney</field>
            <field name="code">POA</field>
            <field name="description">Power of Attorney document preparation for financial and healthcare decisions.</field>
            <field name="service_type_id" ref="ai_legal_document_core.service_type_rti"/>
            <field name="product_id" ref="product_power_of_attorney"/>
            <field name="chatbot_config_id" ref="chatbot_config_groq_legal"/>
            <field name="active" eval="True"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
        </record>

        <record id="service_lease_agreement" model="legal.document.service">
            <field name="name">Lease Agreement</field>
            <field name="code">LEASE_AGR</field>
            <field name="description">Residential and commercial lease agreement drafting with customizable terms.</field>
            <field name="service_type_id" ref="ai_legal_document_core.service_type_rti"/>
            <field name="product_id" ref="product_lease_agreement"/>
            <field name="chatbot_config_id" ref="chatbot_config_openai_contracts"/>
            <field name="active" eval="True"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
        </record>

        <record id="service_nda" model="legal.document.service">
            <field name="name">Non-Disclosure Agreement</field>
            <field name="code">NDA</field>
            <field name="description">Confidentiality and non-disclosure agreement for business partnerships and employment.</field>
            <field name="service_type_id" ref="ai_legal_document_core.service_type_rti"/>
            <field name="product_id" ref="product_nda"/>
            <field name="chatbot_config_id" ref="chatbot_config_openai_contracts"/>
            <field name="active" eval="True"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
        </record>

        <record id="service_business_formation" model="legal.document.service">
            <field name="name">Business Formation Documents</field>
            <field name="code">BIZ_FORM</field>
            <field name="description">Complete business formation documentation including articles of incorporation and bylaws.</field>
            <field name="service_type_id" ref="ai_legal_document_core.service_type_rti"/>
            <field name="product_id" ref="product_business_formation"/>
            <field name="chatbot_config_id" ref="chatbot_config_groq_legal"/>
            <field name="active" eval="True"/>
            <field name="language_ids" eval="[(6, 0, [ref('base.lang_en')])]"/>
        </record>



        <!-- Demo Service Features -->
        <!-- Contract Drafting Features -->
        <record id="feature_contract_ai_powered" model="legal.document.service.feature">
            <field name="name">AI-powered document creation</field>
            <field name="description">Advanced AI technology creates professional contracts</field>
            <field name="icon">fa-robot</field>
            <field name="sequence">10</field>
            <field name="service_id" ref="service_contract_drafting"/>
        </record>

        <record id="feature_contract_legal_formatting" model="legal.document.service.feature">
            <field name="name">Professional legal formatting</field>
            <field name="description">Documents formatted according to legal standards</field>
            <field name="icon">fa-file-text</field>
            <field name="sequence">20</field>
            <field name="service_id" ref="service_contract_drafting"/>
        </record>

        <record id="feature_contract_instant_download" model="legal.document.service.feature">
            <field name="name">Instant download</field>
            <field name="description">Download your document immediately after completion</field>
            <field name="icon">fa-download</field>
            <field name="sequence">30</field>
            <field name="service_id" ref="service_contract_drafting"/>
        </record>

        <!-- NDA Features -->
        <record id="feature_nda_confidentiality" model="legal.document.service.feature">
            <field name="name">Comprehensive confidentiality protection</field>
            <field name="description">Robust clauses to protect sensitive information</field>
            <field name="icon">fa-shield-alt</field>
            <field name="sequence">10</field>
            <field name="service_id" ref="service_nda"/>
        </record>

        <record id="feature_nda_customizable" model="legal.document.service.feature">
            <field name="name">Customizable terms</field>
            <field name="description">Tailor the agreement to your specific needs</field>
            <field name="icon">fa-cogs</field>
            <field name="sequence">20</field>
            <field name="service_id" ref="service_nda"/>
        </record>

        <!-- Business Formation Features -->
        <record id="feature_business_complete_package" model="legal.document.service.feature">
            <field name="name">Complete formation package</field>
            <field name="description">All documents needed to start your business</field>
            <field name="icon">fa-briefcase</field>
            <field name="sequence">10</field>
            <field name="service_id" ref="service_business_formation"/>
        </record>

        <record id="feature_business_compliance" model="legal.document.service.feature">
            <field name="name">Legal compliance assured</field>
            <field name="description">Documents meet all regulatory requirements</field>
            <field name="icon">fa-check-circle</field>
            <field name="sequence">20</field>
            <field name="service_id" ref="service_business_formation"/>
        </record>

        <record id="feature_business_expert_review" model="legal.document.service.feature">
            <field name="name">Expert legal review</field>
            <field name="description">Reviewed by qualified legal professionals</field>
            <field name="icon">fa-user-tie</field>
            <field name="sequence">30</field>
            <field name="service_id" ref="service_business_formation"/>
        </record>
    </data>
</odoo>
