#!/usr/bin/env python3
"""
Final Systematic Fix Workflow
============================

This is the production-ready workflow system for applying fixes to both
the generated module and the AI Module Generator.

Usage:
    workflow = SystematicFixWorkflow()
    
    # Apply a fix
    success = workflow.apply_fix(
        description="Fix description",
        generated_module_changes={
            "file_path": {"old_content": "new_content"}
        },
        generator_changes={
            "file_path": {"old_content": "new_content"}
        }
    )

Author: AI Assistant
Version: 1.0.0
"""

import os
import subprocess
import time
import logging
from typing import Dict, List, Tuple, Optional

class SystematicFixWorkflow:
    """
    Production-ready systematic fix workflow
    """
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.generated_module_path = "/mnt/extra-addons/ovakil_customer_feedback"
        self.generator_module_path = "/mnt/extra-addons/ai_module_generator"
        self.database = "oneclickvakil.com"
        self.website_url = "https://oneclickvakil.com/customer-feedback-main"
        
    def _setup_logging(self):
        """Setup logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('systematic_fix_workflow.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)
    
    def apply_fix(self, description: str, 
                  generated_module_changes: Dict[str, Dict[str, str]],
                  generator_changes: Dict[str, Dict[str, str]]) -> bool:
        """
        Apply a fix using the systematic workflow
        
        Args:
            description (str): Description of the fix
            generated_module_changes (Dict): Changes for generated module
            generator_changes (Dict): Changes for generator
        
        Returns:
            bool: True if successful
        """
        self.logger.info(f"🚀 Starting systematic fix: {description}")
        self.logger.info("="*80)
        
        # Step 1: Apply to generated module
        if not self._apply_changes_to_module(generated_module_changes, "generated module"):
            return False
        
        # Step 2: Test generated module
        if not self._test_module("ovakil_customer_feedback"):
            return False
        
        # Step 3: Verify website (optional, continue if fails)
        self._verify_website()
        
        # Step 4: Apply to generator
        if not self._apply_changes_to_generator(generator_changes):
            return False
        
        # Step 5: Test generator
        if not self._test_module("ai_module_generator"):
            return False
        
        # Step 6: Final verification
        if not self._final_verification():
            return False
        
        self.logger.info("="*80)
        self.logger.info("🎉 Systematic fix completed successfully!")
        return True
    
    def _apply_changes_to_module(self, changes: Dict[str, Dict[str, str]], module_type: str) -> bool:
        """Apply changes to a module"""
        self.logger.info(f"🔧 Applying changes to {module_type}...")
        
        base_path = self.generated_module_path if module_type == "generated module" else self.generator_module_path
        
        try:
            for file_path, replacements in changes.items():
                full_path = os.path.join(base_path, file_path)
                
                if not os.path.exists(full_path):
                    self.logger.warning(f"⚠️ File not found: {file_path}")
                    continue
                
                # Read file
                with open(full_path, 'r') as f:
                    content = f.read()
                
                # Apply replacements
                for old_content, new_content in replacements.items():
                    if old_content in content:
                        content = content.replace(old_content, new_content)
                        self.logger.info(f"✅ Applied change in {file_path}")
                    else:
                        self.logger.warning(f"⚠️ Pattern not found in {file_path}")
                
                # Write file
                with open(full_path, 'w') as f:
                    f.write(content)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error applying changes: {str(e)}")
            return False
    
    def _apply_changes_to_generator(self, changes: Dict[str, Dict[str, str]]) -> bool:
        """Apply changes to generator"""
        return self._apply_changes_to_module(changes, "generator")
    
    def _test_module(self, module_name: str) -> bool:
        """Test a module by upgrading it"""
        self.logger.info(f"🧪 Testing module: {module_name}")
        
        try:
            cmd = [
                "python3", "-m", "odoo",
                "--addons-path=/mnt/extra-addons",
                f"--database={self.database}",
                "-u", module_name,
                "--stop-after-init"
            ]
            
            result = subprocess.run(
                cmd,
                cwd="/mnt/extra-addons",
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            
            if success:
                self.logger.info(f"✅ Module {module_name} upgrade successful")
            else:
                self.logger.error(f"❌ Module {module_name} upgrade failed")
                self.logger.error(f"Error: {result.stderr}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Error testing module {module_name}: {str(e)}")
            return False
    
    def _verify_website(self) -> bool:
        """Verify website functionality (optional step)"""
        self.logger.info("🌐 Verifying website functionality...")
        
        try:
            import requests
            response = requests.get(self.website_url, timeout=10)
            
            if response.status_code == 200:
                self.logger.info("✅ Website is accessible")
                return True
            else:
                self.logger.warning(f"⚠️ Website returned status code: {response.status_code}")
                return False
                
        except ImportError:
            self.logger.info("ℹ️ Skipping website verification (requests not available)")
            return True
        except Exception as e:
            self.logger.warning(f"⚠️ Website verification failed: {str(e)}")
            return False
    
    def _final_verification(self) -> bool:
        """Final verification that changes are applied"""
        self.logger.info("🔍 Final verification...")
        
        # Check that both modules exist and are accessible
        if not os.path.exists(self.generated_module_path):
            self.logger.error("❌ Generated module path not found")
            return False
        
        if not os.path.exists(self.generator_module_path):
            self.logger.error("❌ Generator module path not found")
            return False
        
        self.logger.info("✅ Final verification passed")
        return True
    
    def get_recent_logs(self, lines: int = 50) -> str:
        """Get recent Odoo logs"""
        try:
            result = subprocess.run(
                ["tail", f"-n{lines}", "/var/log/odoo/odoo-server.log"],
                capture_output=True,
                text=True
            )
            return result.stdout
        except Exception as e:
            self.logger.error(f"❌ Failed to get logs: {str(e)}")
            return ""


# Example usage functions
def example_fix_widget_issue():
    """
    Example: Fix many2one widget issue
    """
    workflow = SystematicFixWorkflow()
    
    # Define the fix
    description = "Change many2one widget from searchable to dropdown"
    
    generated_module_changes = {
        "views/website_templates.xml": {
            'data-widget="many2one_searchable"': 'data-widget="many2one_dropdown"'
        }
    }
    
    generator_changes = {
        "wizards/module_generator_wizard.py": {
            "many2one_searchable": "many2one_dropdown"
        }
    }
    
    return workflow.apply_fix(description, generated_module_changes, generator_changes)


def example_add_css_enhancement():
    """
    Example: Add CSS enhancement
    """
    workflow = SystematicFixWorkflow()
    
    description = "Add enhanced form validation styles"
    
    css_enhancement = """
/* Enhanced validation styles */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
"""
    
    generated_module_changes = {
        "static/src/css/enhanced_forms.css": {
            "/* End of file */": css_enhancement + "\n/* End of file */"
        }
    }
    
    generator_changes = {
        "wizards/module_generator_wizard.py": {
            "/* Form styling */": "/* Form styling */" + css_enhancement
        }
    }
    
    return workflow.apply_fix(description, generated_module_changes, generator_changes)


def main():
    """
    Main function demonstrating the workflow
    """
    print("🚀 Systematic Fix Workflow System")
    print("="*50)
    
    # Example 1: Widget fix
    print("\n📋 Example 1: Widget Fix")
    success1 = example_fix_widget_issue()
    print(f"Result: {'✅ Success' if success1 else '❌ Failed'}")
    
    # Example 2: CSS enhancement
    print("\n📋 Example 2: CSS Enhancement")
    success2 = example_add_css_enhancement()
    print(f"Result: {'✅ Success' if success2 else '❌ Failed'}")
    
    print(f"\n🎯 Overall Success: {success1 and success2}")
    
    return 0 if (success1 and success2) else 1


if __name__ == "__main__":
    exit(main())
