<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="portal_legal_services_complete_list" name="Complete Legal Services Application List">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Complete Legal Services Application List</h3>
                            <div class="d-flex">
                                <form method="get" class="form-inline mr-3">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control" placeholder="Search records..."
                                               t-att-value="search_query" style="min-width: 200px;"/>
                                        <div class="input-group-append">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"/> Search
                                            </button>
                                        </div>
                                    </div>
                                    <input type="hidden" name="state" t-att-value="state_filter"/>
                                </form>
                                <form method="get" class="form-inline">
                                    <select name="state" class="form-control mr-2" onchange="this.form.submit()">
                                        <option value="">All States</option>
                                        <t t-foreach="available_states" t-as="state_option">
                                            <option t-att-value="state_option[0]"
                                                    t-att-selected="'selected' if state_filter == state_option[0] else None">
                                                <t t-esc="state_option[1]"/>
                                            </option>
                                        </t>
                                    </select>
                                    <input type="hidden" name="search" t-att-value="search_query"/>
                                </form>
                            </div>
                        </div>

                        <t t-if="search_query or state_filter">
                            <div class="alert alert-info">
                                <strong>Filters Applied:</strong>
                                <t t-if="search_query">
                                    Search: "<t t-esc="search_query"/>"
                                </t>
                                <t t-if="state_filter">
                                    State: <t t-esc="dict(available_states).get(state_filter, state_filter)"/>
                                </t>
                                <a href="/my/legal-services-complete" class="btn btn-sm btn-outline-secondary ml-2">Clear Filters</a>
                            </div>
                        </t>

                        <t t-if="legal_services_complete_records">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="legal_services_complete_records" t-as="record">
                                        <tr>
                                            <td><t t-esc="record.name"/></td>
                                            <td><span t-field="record.state" class="badge badge-info"/></td>
                                            <td><t t-esc="record.create_date"/></td>
                                            <td>
                                                <a t-attf-href="/my/legal-services-complete/{{record.id}}" class="btn btn-sm btn-primary">View</a>
                                                <t t-if="record.state == 'feedback_submitted'">
                                                    <a t-attf-href="/my/legal-services-complete/{{record.id}}/edit" class="btn btn-sm btn-warning ml-1">Edit</a>
                                                </t>
                                                <t t-if="record.sales_order_id and record.payment_status == 'pending'">
                                                    <a t-attf-href="/my/orders/{{record.sales_order_id.id}}" class="btn btn-sm btn-success ml-1">
                                                        <i class="fa fa-credit-card"/> Pay Now
                                                    </a>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </t>
                        <t t-else="">
                            <div class="alert alert-warning">
                                <t t-if="search_query or state_filter">
                                    <h5>No results found</h5>
                                    <p>No complete legal services application match your search criteria.</p>
                                    <a href="/my/legal-services-complete" class="btn btn-primary">View All Records</a>
                                </t>
                                <t t-else="">
                                    <h5>No records found</h5>
                                    <p>You haven't submitted any complete legal services application yet.</p>
                                </t>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_legal_services_complete_detail" name="Complete Legal Services Application Detail">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="mb-0"><t t-esc="legal_services_complete.name"/></h3>
                                <div>
                                    <span t-field="legal_services_complete.state" class="badge badge-info"/>
                                    <t t-if="legal_services_complete.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/legal-services-complete/{{ legal_services_complete.id }}/edit" class="btn btn-sm btn-warning ml-2">
                                            <i class="fa fa-edit"/> Edit
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <t t-if="request.params.get('updated')">
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        <strong>Success!</strong> Your information has been updated successfully.
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span>×</span>
                                        </button>
                                    </div>
                                </t>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Details</h5>
                                        <p><strong>Name:</strong> <t t-esc="legal_services_complete.name"/></p>
                                        <p><strong>Status:</strong> <span t-field="legal_services_complete.state" class="badge badge-info"/></p>
                                        <p><strong>Created:</strong> <t t-esc="legal_services_complete.create_date"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Actions</h5>
                                        <t t-if="legal_services_complete.state == 'feedback_submitted'">
                                            <p class="text-muted">You can edit this record while it's in initial state.</p>
                                            <a t-attf-href="/my/legal-services-complete/{{ legal_services_complete.id }}/edit" class="btn btn-warning">
                                                <i class="fa fa-edit"/> Edit Information
                                            </a>
                                        </t>
                                        <t t-else="">
                                            <p class="text-muted">This record cannot be edited in its current state.</p>
                                        </t>

                                        <!-- Payment Section -->
                                        <t t-if="legal_services_complete.sales_order_id">
                                            <h5 class="mt-4">Payment Information</h5>
                                            <div class="card">
                                                <div class="card-body">
                                                    <p><strong>Order Number:</strong> <t t-esc="legal_services_complete.sales_order_id.name"/></p>
                                                    <p><strong>Amount:</strong> <t t-esc="legal_services_complete.sales_order_id.amount_total"/> <t t-esc="legal_services_complete.sales_order_id.currency_id.name"/></p>
                                                    <p><strong>Payment Status:</strong>
                                                        <t t-if="legal_services_complete.payment_status == 'pending'">
                                                            <span class="badge badge-warning">Payment Pending</span>
                                                        </t>
                                                        <t t-elif="legal_services_complete.payment_status == 'paid'">
                                                            <span class="badge badge-success">Payment Completed</span>
                                                        </t>
                                                        <t t-elif="legal_services_complete.payment_status == 'failed'">
                                                            <span class="badge badge-danger">Payment Failed</span>
                                                        </t>
                                                        <t t-else="">
                                                            <span class="badge badge-secondary">No Payment Required</span>
                                                        </t>
                                                    </p>

                                                    <t t-if="legal_services_complete.payment_status == 'pending'">
                                                        <div class="mt-3">
                                                            <a t-attf-href="/my/orders/{{ legal_services_complete.sales_order_id.id }}" class="btn btn-success">
                                                                <i class="fa fa-credit-card"/> Make Payment
                                                            </a>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_legal_services_complete_edit" name="Complete Legal Services Application Edit">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="mb-0">Edit <t t-esc="legal_services_complete.name"/></h3>
                            </div>
                            <div class="card-body">
                                <form method="post" t-attf-action="/my/legal-services-complete/{{ legal_services_complete.id }}/update" class="needs-validation" novalidate="true">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" name="customer_name"
                                                       t-att-value="legal_services_complete.customer_name" required="true"/>
                                                <div class="invalid-feedback">Please provide a valid customer name.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="customer_email">Email Address <span class="text-danger">*</span></label>
                                                <input type="email" class="form-control" name="customer_email"
                                                       t-att-value="legal_services_complete.customer_email" required="true"/>
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="overall_rating">Overall Rating <span class="text-danger">*</span></label>
                                                <select class="form-control" name="overall_rating" required="true">
                                                    <option value="">Select Rating</option>
                                                    <option value="1" t-att-selected="'selected' if legal_services_complete.overall_rating == '1' else None">1 Star</option>
                                                    <option value="2" t-att-selected="'selected' if legal_services_complete.overall_rating == '2' else None">2 Stars</option>
                                                    <option value="3" t-att-selected="'selected' if legal_services_complete.overall_rating == '3' else None">3 Stars</option>
                                                    <option value="4" t-att-selected="'selected' if legal_services_complete.overall_rating == '4' else None">4 Stars</option>
                                                    <option value="5" t-att-selected="'selected' if legal_services_complete.overall_rating == '5' else None">5 Stars</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a rating.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="priority">Priority Level</label>
                                                <select class="form-control" name="priority">
                                                    <option value="">Select Priority</option>
                                                    <option value="low" t-att-selected="'selected' if legal_services_complete.priority == 'low' else None">Low Priority</option>
                                                    <option value="medium" t-att-selected="'selected' if legal_services_complete.priority == 'medium' else None">Medium Priority</option>
                                                    <option value="high" t-att-selected="'selected' if legal_services_complete.priority == 'high' else None">High Priority</option>
                                                    <option value="urgent" t-att-selected="'selected' if legal_services_complete.priority == 'urgent' else None">Urgent</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="feedback_comments">Comments &amp; Suggestions</label>
                                        <textarea class="form-control" name="feedback_comments" rows="4"
                                                  placeholder="Please share your detailed feedback..."><t t-esc="legal_services_complete.feedback_comments"/></textarea>
                                    </div>

                                    <div class="form-group text-right">
                                        <a t-attf-href="/my/legal-services-complete/{{ legal_services_complete.id }}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary">Update Information</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>