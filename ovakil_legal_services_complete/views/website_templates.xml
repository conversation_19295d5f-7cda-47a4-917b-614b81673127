<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Index Template -->
        <template id="index" name="Complete Legal Services Portal Index">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <h1 class="mt-4">Complete Legal Services Portal</h1>
                                <p class="lead">Comprehensive legal services portal showcasing all enhanced AI Module Generator features including step-by-step forms, payment integration, add-ons, and FAQs</p>

                                <div class="row mt-4">
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">Complete Legal Services Application</h5>
                                                <p class="card-text">Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI</p>
                                                <a href="/legal-services-complete" class="btn btn-primary">Access Form</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Complete Legal Services Application Website Template -->
        <template id="legal_services_complete_website_form" name="Complete Legal Services Application Form">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Enhanced Form Styling -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                    <link rel="stylesheet" href="/ovakil_legal_services_complete/static/src/css/enhanced_forms.css"/>
                </t>
                <div id="wrap" class="enhanced-form-wrapper">
                    <!-- Progress Steps -->
                    <div class="container">
                        <div class="form-progress-steps">
                            <div class="step-item active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-label">Client Information</div>
                            </div>
                            <div class="step-item" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-label">Legal Service Details</div>
                            </div>
                            <div class="step-item" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-label">Review &amp; Submit</div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-lg-10 offset-lg-1">
                                <!-- Form Header Card -->
                                <div class="form-header-card">
                                    <div class="form-header-content">
                                        <h1 class="form-title">Complete Legal Services Application</h1>
                                        <p class="form-description">Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI</p>
                                        <div class="form-meta">
                                            <span class="form-meta-item">
                                                <i class="fas fa-clock"></i>
                                                Estimated time: 5-10 minutes
                                            </span>
                                            <span class="form-meta-item">
                                                <i class="fas fa-shield-alt"></i>
                                                Secure &amp; Confidential
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Form Card -->
                                <div class="main-form-card">
                                    <form id="enhanced-form" action="/legal-services-complete/submit" method="post" class="enhanced-form" novalidate="novalidate">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                        <!-- Step 1: Client Information -->
                                        <div class="form-section" data-step="1">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-user"></i>
                                                Client Information
                                            </h3>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                        <label for="client_name" class="required">Full Name</label>
                                                        <input type="text" class="form-control" name="client_name" required="required"
                                                               placeholder="Enter your full name"
                                                               data-field-type="char" data-required="true" />
                                                        <div class="invalid-feedback">Please provide your full name.</div>
                                                        <div class="valid-feedback">Looks good!</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                        <label for="client_email" class="required">Email Address</label>
                                                        <input type="email" class="form-control" name="client_email" required="required"
                                                               placeholder="Enter your email address"
                                                               data-field-type="email" data-required="true" />
                                                        <div class="invalid-feedback">Please provide a valid email address.</div>
                                                        <div class="valid-feedback">Looks good!</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                        <label for="client_phone" class="required">Phone Number</label>
                                                        <input type="tel" class="form-control" name="client_phone" required="required"
                                                               placeholder="Enter your phone number"
                                                               data-field-type="phone" data-required="true" />
                                                        <div class="invalid-feedback">Please provide a valid phone number.</div>
                                                        <div class="valid-feedback">Looks good!</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                        <label for="assigned_lawyer">Preferred Lawyer</label>
                                                        <div class="many2one-searchable">
                                                            <input type="text" class="form-control"
                                                                   placeholder="Search for preferred lawyer..."
                                                                   data-widget="many2one_searchable" data-model="res.users"
                                                                   data-field-type="many2one" data-required="false"
                                                                   data-label="Preferred Lawyer"/>
                                                            <i class="fas fa-search search-icon"></i>
                                                            <div class="many2one-dropdown-results"></div>
                                                        </div>
                                                        <input type="hidden" name="assigned_lawyer" />
                                                        <div class="invalid-feedback">Please select a valid lawyer.</div>
                                                        <div class="valid-feedback">Selection confirmed!</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Step Navigation -->
                                            <div class="step-navigation mt-4">
                                                <button type="button" class="btn btn-primary btn-next-step">
                                                    Next: Legal Service Details <i class="fas fa-arrow-right"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Step 2: Legal Service Details -->
                                        <div class="form-section" data-step="2" style="display: none;">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-gavel"></i>
                                                Legal Service Details
                                            </h3>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                        <label for="service_type" class="required">Legal Service Required</label>
                                                        <select class="form-control" name="service_type" required="required"
                                                                data-field-type="selection" data-required="true">
                                                            <option value="">Select a service...</option>
                                                            <option value="business_registration">Business Registration</option>
                                                            <option value="legal_notice">Legal Notice</option>
                                                            <option value="contract_drafting">Contract Drafting</option>
                                                            <option value="court_representation">Court Representation</option>
                                                            <option value="property_documentation">Property Documentation</option>
                                                            <option value="trademark_registration">Trademark Registration</option>
                                                        </select>
                                                        <div class="invalid-feedback">Please select a legal service.</div>
                                                        <div class="valid-feedback">Service selected!</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                        <label for="urgency_level" class="required">Urgency Level</label>
                                                        <select class="form-control" name="urgency_level" required="required"
                                                                data-field-type="selection" data-required="true">
                                                            <option value="">Select urgency...</option>
                                                            <option value="standard">Standard (7-10 days)</option>
                                                            <option value="priority">Priority (3-5 days)</option>
                                                            <option value="urgent">Urgent (1-2 days)</option>
                                                            <option value="emergency">Emergency (Same day)</option>
                                                        </select>
                                                        <div class="invalid-feedback">Please select urgency level.</div>
                                                        <div class="valid-feedback">Urgency level selected!</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="nice-form-group">
                                                        <label for="case_details" class="required">Case Details &amp; Requirements</label>
                                                        <textarea class="form-control" name="case_details" required="required"
                                                                  placeholder="Please provide detailed information about your legal requirements, case background, and any specific needs..."
                                                                  rows="5" data-field-type="text" data-required="true"></textarea>
                                                        <div class="invalid-feedback">Please provide case details and requirements.</div>
                                                        <div class="valid-feedback">Details provided!</div>
                                                        <small class="form-text text-muted">
                                                            <i class="fas fa-info-circle"></i>
                                                            The more details you provide, the better we can assist you with your legal needs.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Step Navigation -->
                                            <div class="step-navigation mt-4">
                                                <button type="button" class="btn btn-secondary btn-prev-step">
                                                    <i class="fas fa-arrow-left"></i> Previous: Client Information
                                                </button>
                                                <button type="button" class="btn btn-primary btn-next-step">
                                                    Next: Review &amp; Submit <i class="fas fa-arrow-right"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Step 3: Review & Submit -->
                                        <div class="form-section" data-step="3" style="display: none;">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-check-circle"></i>
                                                Review &amp; Submit
                                            </h3>

                                            <!-- Review Summary -->
                                            <div class="review-summary">
                                                <div class="alert alert-info">
                                                    <h5><i class="fas fa-eye"></i> Please Review Your Information</h5>
                                                    <p>Please review all the information below before submitting your legal services application.</p>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="review-section">
                                                            <h6><i class="fas fa-user"></i> Client Information</h6>
                                                            <div class="review-item">
                                                                <strong>Name:</strong> <span id="review-client-name">-</span>
                                                            </div>
                                                            <div class="review-item">
                                                                <strong>Email:</strong> <span id="review-client-email">-</span>
                                                            </div>
                                                            <div class="review-item">
                                                                <strong>Phone:</strong> <span id="review-client-phone">-</span>
                                                            </div>
                                                            <div class="review-item">
                                                                <strong>Preferred Lawyer:</strong> <span id="review-assigned-lawyer">Not specified</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="review-section">
                                                            <h6><i class="fas fa-gavel"></i> Legal Service Details</h6>
                                                            <div class="review-item">
                                                                <strong>Service Type:</strong> <span id="review-service-type">-</span>
                                                            </div>
                                                            <div class="review-item">
                                                                <strong>Urgency Level:</strong> <span id="review-urgency-level">-</span>
                                                            </div>
                                                            <div class="review-item">
                                                                <strong>Case Details:</strong> <span id="review-case-details">-</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Submit Section -->
                                            <div class="form-submit-section mt-4">
                                                <div class="text-center">
                                                    <button type="submit" class="btn btn-submit-enhanced btn-lg"
                                                            data-original-text="Submit Legal Services Application">
                                                        <i class="fas fa-paper-plane"></i>
                                                        Submit Legal Services Application
                                                    </button>
                                                    <p class="form-text mt-3">
                                                        <i class="fas fa-lock"></i>
                                                        Your information is secure and will be processed confidentially.
                                                    </p>
                                                </div>
                                            </div>

                                            <!-- Step Navigation -->
                                            <div class="step-navigation mt-4">
                                                <button type="button" class="btn btn-secondary btn-prev-step">
                                                    <i class="fas fa-arrow-left"></i> Previous: Legal Service Details
                                                </button>
                                            </div>
                                        </div>
                                </form>

                                </div>

                                <!-- Success Section (Hidden initially) -->
                                <div class="main-form-card" id="success-section" style="display: none;">
                                    <div class="text-center py-5">
                                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        <h2 class="mt-3 mb-3">Thank You!</h2>
                                        <p class="lead">Your complete legal services application has been submitted successfully.</p>
                                        <p class="text-muted">You will receive a confirmation email shortly.</p>
                                        <a href="/legal_services_complete" class="btn btn-primary mt-3">
                                            <i class="fas fa-home"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Form JavaScript -->
                    <script src="/ovakil_legal_services_complete/static/src/js/enhanced_forms.js"></script>
                </div>
            </t>
        </template>

        <!-- Complete Legal Services Application Success Template -->
        <template id="legal_services_complete_success" name="Complete Legal Services Application Success">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8 offset-lg-2">
                                <h1 class="mt-4 text-success">Thank You!</h1>
                                <p class="lead">Your complete legal services application has been submitted successfully.</p>
                                <a href="/legal_services_complete" class="btn btn-primary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Portal Templates for Complete Legal Services Application -->
        <!-- Customer Portal List Template -->
        <template id="portal_legal_services_complete_list" name="Complete Legal Services Application Portal List">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Complete Legal Services Application</t>
                </t>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Your Complete Legal Services Application Records</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="legal_services_complete_records">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Payment Status</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="legal_services_complete_records" t-as="record">
                                                    <tr>
                                                        <td><a t-attf-href="/my/legal-services-complete/#{record.id}"><t t-esc="record.name"/></a></td>
                                                        <td><span class="badge bg-primary text-white" t-field="record.state"/></td>
                                                        <td>
                                                            <span t-attf-class="badge #{record.payment_status == 'paid' and 'bg-success text-white' or record.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                                <span t-field="record.payment_status"/>
                                                            </span>
                                                        </td>
                                                        <td><t t-esc="record.create_date" t-options="{'widget': 'date'}"/></td>
                                                        <td>
                                                            <a t-attf-href="/my/legal-services-complete/#{record.id}" class="btn btn-sm btn-primary">View</a>
                                                            <t t-if="record.state == 'feedback_submitted'">
                                                                <a t-attf-href="/my/legal-services-complete/#{record.id}/edit" class="btn btn-sm btn-secondary">Edit</a>
                                                            </t>
                                                            <t t-if="record.payment_status == 'pending' and record.payment_url">
                                                                <a t-att-href="record.payment_url" class="btn btn-sm btn-warning">Pay Now</a>
                                                            </t>
                                                            <t t-elif="record.payment_status == 'pending'">
                                                                <span class="btn btn-sm btn-outline-warning disabled">Payment Pending</span>
                                                            </t>
                                                            <t t-if="record.payment_status == 'paid'">
                                                                <a t-attf-href="/my/legal-services-complete/#{record.id}/download-pdf" class="btn btn-sm btn-success">
                                                                    <i class="fa fa-download"/> Download PDF
                                                                </a>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        <p>You don't have any complete legal services application records yet.</p>
                                        <a href="/legal-services-complete" class="btn btn-primary">Create New Record</a>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Portal Detail Template -->
        <template id="portal_legal_services_complete_detail" name="Complete Legal Services Application Portal Detail">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4><t t-esc="legal_services_complete.name"/></h4>
                                <div>
                                    <t t-if="legal_services_complete.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/edit" class="btn btn-secondary">Edit</a>
                                    </t>
                                    <t t-if="legal_services_complete.payment_status == 'paid'">
                                        <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/download-pdf" class="btn btn-success">
                                            <i class="fa fa-download"/> Download PDF
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Basic Information</h5>
                                        <p><strong>Name:</strong> <t t-esc="legal_services_complete.name"/></p>
                                        <p><strong>Status:</strong> <span class="badge bg-primary text-white" t-field="legal_services_complete.state"/></p>
                                        <p><strong>Created:</strong> <t t-esc="legal_services_complete.create_date" t-options="{'widget': 'datetime'}"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Rate:</strong> $<t t-esc="legal_services_complete.payment_rate"/></p>
                                        <p><strong>Payment Amount:</strong> $<t t-esc="legal_services_complete.payment_amount"/></p>
                                        <p><strong>Payment Status:</strong>
                                            <span t-attf-class="badge #{legal_services_complete.payment_status == 'paid' and 'bg-success text-white' or legal_services_complete.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                <span t-field="legal_services_complete.payment_status"/>
                                            </span>
                                        </p>
                                        <t t-if="legal_services_complete.payment_method">
                                            <p><strong>Payment Method:</strong>
                                                <span class="badge bg-info text-white" t-field="legal_services_complete.payment_method"/>
                                            </p>
                                        </t>
                                        <t t-if="legal_services_complete.payment_url and legal_services_complete.payment_status == 'pending'">
                                            <p><a t-att-href="legal_services_complete.payment_url" class="btn btn-warning">Pay Now</a></p>
                                        </t>
                                    </div>
                                </div>

                                <!-- AI Response Section (only for paid customers) -->
                                <t t-if="legal_services_complete.payment_status == 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">AI Response</h5>
                                                    <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/download-pdf" class="btn btn-light btn-sm">
                                                        <i class="fa fa-download"/> Download PDF
                                                    </a>
                                                </div>
                                                <div class="card-body">
                                                    <t t-raw="legal_services_complete.ai_response"/>

                                                    <!-- PDF Download Section -->
                                                    <div class="text-center mt-4 pt-3 border-top">
                                                        <a t-attf-href="/my/legal-services-complete/#{legal_services_complete.id}/download-pdf" class="btn btn-success btn-lg">
                                                            <i class="fa fa-download"/> Download PDF
                                                        </a>
                                                        <p class="text-muted mt-2">
                                                            <small>Click to download the AI response as PDF document</small>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                <t t-elif="legal_services_complete.payment_status != 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <h5>AI Response Available After Payment</h5>
                                                <p>Complete your payment to access the AI-generated response and download the PDF report.</p>
                                                <t t-if="legal_services_complete.payment_url">
                                                    <a t-att-href="legal_services_complete.payment_url" class="btn btn-warning">Complete Payment</a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>