from odoo import http
from odoo.http import request
import json


class CompleteLegalServicesPortalApiController(http.Controller):
    """
    REST API Controller for Complete Legal Services Portal
    Provides CRUD operations for all forms in the module
    """

    # Complete Legal Services Application API Endpoints

    @http.route('/api/legal_services_complete', type='json', auth='user', methods=['GET'])
    def legal_services_complete_list(self, **kwargs):
        """List Complete Legal Services Application records"""
        try:
            domain = kwargs.get('domain', [])
            limit = kwargs.get('limit', 100)
            offset = kwargs.get('offset', 0)

            records = request.env['ovakil_legal_services_complete.legal_services_complete'].search(domain, limit=limit, offset=offset)

            return {
                'success': True,
                'data': [{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                } for record in records],
                'total': request.env['ovakil_legal_services_complete.legal_services_complete'].search_count(domain)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/legal_services_complete/<int:record_id>', type='json', auth='user', methods=['GET'])
    def legal_services_complete_get(self, record_id, **kwargs):
        """Get specific Complete Legal Services Application record"""
        try:
            record = request.env['ovakil_legal_services_complete.legal_services_complete'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/legal_services_complete', type='json', auth='user', methods=['POST'])
    def legal_services_complete_create(self, **kwargs):
        """Create new Complete Legal Services Application record"""
        try:
            values = kwargs.get('values', {})
            record = request.env['ovakil_legal_services_complete.legal_services_complete'].create(values)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/legal_services_complete/<int:record_id>', type='json', auth='user', methods=['PUT'])
    def legal_services_complete_update(self, record_id, **kwargs):
        """Update Complete Legal Services Application record"""
        try:
            record = request.env['ovakil_legal_services_complete.legal_services_complete'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            values = kwargs.get('values', {})
            record.write(values)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/legal_services_complete/<int:record_id>', type='json', auth='user', methods=['DELETE'])
    def legal_services_complete_delete(self, record_id, **kwargs):
        """Delete Complete Legal Services Application record"""
        try:
            record = request.env['ovakil_legal_services_complete.legal_services_complete'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            record.unlink()

            return {'success': True, 'message': 'Record deleted successfully'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    # Many2one Field Data API Endpoints

    @http.route('/api/search_records', type='http', auth='user', methods=['POST'], csrf=False)
    def search_records(self, **kwargs):
        """Search records for Many2one fields"""
        try:
            # Parse JSON data from request body for POST requests
            if request.httprequest.method == 'POST':
                import json
                data = json.loads(request.httprequest.data.decode('utf-8'))
                if 'params' in data:
                    params = data['params']
                    model = params.get('model', 'res.partner')
                    query = params.get('query', '')
                    limit = int(params.get('limit', 10))
                else:
                    model = kwargs.get('model', 'res.partner')
                    query = kwargs.get('query', '')
                    limit = int(kwargs.get('limit', 10))
            else:
                model = kwargs.get('model', 'res.partner')
                query = kwargs.get('query', '')
                limit = int(kwargs.get('limit', 10))

            if not query or len(query) < 2:
                result = {'success': True, 'data': []}
                return request.make_response(
                    json.dumps(result),
                    headers=[('Content-Type', 'application/json')]
                )

            # Define searchable models and their search fields
            searchable_models = {
                'res.users': ['name', 'login'],
                'res.partner': ['name', 'email'],
                'product.product': ['name', 'default_code'],
                'product.template': ['name'],
            }

            if model not in searchable_models:
                result = {'success': False, 'error': 'Model not searchable'}
                return request.make_response(
                    json.dumps(result),
                    headers=[('Content-Type', 'application/json')]
                )

            # Build search domain
            search_fields = searchable_models[model]
            domain = []
            for field in search_fields:
                domain.append((field, 'ilike', query))

            # Use OR condition for multiple fields
            if len(domain) > 1:
                domain = ['|'] * (len(domain) - 1) + domain

            # Special filtering for users (lawyers)
            if model == 'res.users':
                # Add condition to filter active users only
                domain = domain + [('active', '=', True)]
                # You can add more specific filters here, e.g., users in lawyer group
                # domain = domain + [('groups_id', 'in', [lawyer_group_id])]

            records = request.env[model].search(domain, limit=limit)

            result = {
                'success': True,
                'data': [{
                    'id': record.id,
                    'name': record.name,
                    'display_name': record.display_name if hasattr(record, 'display_name') else record.name,
                } for record in records]
            }

            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            result = {'success': False, 'error': str(e)}
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/get_lawyers', type='http', auth='user', methods=['GET'], csrf=False)
    def get_lawyers(self, **kwargs):
        """Get list of lawyers/users for dropdown"""
        try:
            limit = int(kwargs.get('limit', 20))

            # Search for active users
            # You can customize this query to filter specific lawyer users
            domain = [('active', '=', True)]

            # Optional: Filter by specific groups (uncomment and modify as needed)
            # lawyer_group = request.env.ref('your_module.group_lawyers', raise_if_not_found=False)
            # if lawyer_group:
            #     domain.append(('groups_id', 'in', [lawyer_group.id]))

            # Debug: Check total users
            all_users = request.env['res.users'].search([])
            active_users = request.env['res.users'].search([('active', '=', True)])

            users = request.env['res.users'].search(domain, limit=limit, order='name')

            result = {
                'success': True,
                'data': [{
                    'id': user.id,
                    'name': user.name,
                    'display_name': user.name,
                    'email': user.email if user.email else '',
                } for user in users],
                'debug': {
                    'total_users': len(all_users),
                    'active_users': len(active_users),
                    'found_users': len(users),
                    'domain': domain
                }
            }

            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            result = {'success': False, 'error': str(e)}
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
