// Scratch Card JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the scratch card page
    const scratchCard = document.getElementById('scratch-card');
    if (!scratchCard) return;
    
    // Get game data
    const gameId = document.getElementById('game-id').value;
    const securityToken = document.getElementById('security-token').value;
    const bgColor = document.getElementById('scratch-bg-color').value || '#CCCCCC';
    const fgColor = document.getElementById('scratch-fg-color').value || '#FFFFFF';
    const revealPercentage = parseInt(document.getElementById('scratch-reveal-percentage').value || '50');
    
    // Set up the canvas
    const canvas = scratchCard;
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Variables to track scratching
    let isDrawing = false;
    let lastX = 0;
    let lastY = 0;
    let scratchedPixels = 0;
    let totalPixels = width * height;
    let gameCompleted = false;
    
    // Fill the canvas with the foreground color
    ctx.fillStyle = fgColor;
    ctx.fillRect(0, 0, width, height);
    
    // Event listeners for scratching
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouchStart);
    canvas.addEventListener('touchmove', handleTouchMove);
    canvas.addEventListener('touchend', stopDrawing);
    
    function handleTouchStart(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousedown', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    }
    
    function handleTouchMove(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    }
    
    function startDrawing(e) {
        if (gameCompleted) return;
        
        isDrawing = true;
        [lastX, lastY] = getCoordinates(e);
    }
    
    function draw(e) {
        if (!isDrawing || gameCompleted) return;
        
        const [currentX, currentY] = getCoordinates(e);
        
        // Draw a line
        ctx.globalCompositeOperation = 'destination-out';
        ctx.lineWidth = 30;
        ctx.lineCap = 'round';
        ctx.beginPath();
        ctx.moveTo(lastX, lastY);
        ctx.lineTo(currentX, currentY);
        ctx.stroke();
        
        // Update scratched pixels count
        updateScratchedPixels();
        
        // Check if enough has been scratched
        if (scratchedPixels / totalPixels * 100 >= revealPercentage && !gameCompleted) {
            completeGame();
        }
        
        [lastX, lastY] = [currentX, currentY];
    }
    
    function stopDrawing() {
        isDrawing = false;
    }
    
    function getCoordinates(e) {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        return [x, y];
    }
    
    function updateScratchedPixels() {
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;
        let scratched = 0;
        
        // Count transparent pixels (alpha = 0)
        for (let i = 3; i < data.length; i += 4) {
            if (data[i] === 0) {
                scratched++;
            }
        }
        
        scratchedPixels = scratched;
    }
    
    function completeGame() {
        gameCompleted = true;
        
        // Call the API to get the reward
        fetch('/game/api/play', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({
                game_id: gameId,
                security_token: securityToken
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show the reward
                showReward(data);
            } else {
                // Show error
                showError(data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('An error occurred. Please try again.');
        });
    }
    
    function showReward(data) {
        // Update the reward text
        const rewardText = document.getElementById('reward-text');
        rewardText.innerHTML = `<h3>${data.reward_name}</h3>`;
        
        // Show the result container
        const resultContainer = document.getElementById('result-container');
        resultContainer.classList.remove('d-none');
        
        // Update result message based on reward type
        const resultMessage = document.getElementById('result-message');
        const resultTitle = document.getElementById('result-title');
        
        if (data.result.reward_type === 'no_reward') {
            resultTitle.textContent = 'Better luck next time!';
            resultMessage.textContent = 'You didn\'t win a reward this time.';
            
            // Hide claim button
            const claimButton = document.getElementById('claim-button');
            claimButton.classList.add('d-none');
        } else {
            resultTitle.textContent = 'Congratulations!';
            
            if (data.result.reward_type === 'coupon') {
                resultMessage.textContent = 'You won a coupon!';
            } else if (data.result.reward_type === 'ewallet') {
                resultMessage.textContent = `You won ${data.result.value} in eWallet credit!`;
            } else if (data.result.reward_type === 'external') {
                resultMessage.textContent = 'You won an external coupon!';
            }
            
            // Set up claim button
            const claimButton = document.getElementById('claim-button');
            claimButton.href = `/game/reward/${data.reward_id}`;
        }
    }
    
    function showError(message) {
        // Show error message
        const rewardText = document.getElementById('reward-text');
        rewardText.innerHTML = `<div class="alert alert-danger">${message}</div>`;
    }
});
