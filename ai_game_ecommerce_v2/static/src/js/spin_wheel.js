// Spin Wheel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the spin wheel page
    const wheelCanvas = document.getElementById('wheel-canvas');
    if (!wheelCanvas) return;
    
    // Get game data
    const gameId = document.getElementById('game-id').value;
    const securityToken = document.getElementById('security-token').value;
    const segmentsCount = parseInt(document.getElementById('wheel-segments').value || '8');
    let wheelColors;
    
    try {
        wheelColors = JSON.parse(document.getElementById('wheel-colors').value);
    } catch (e) {
        wheelColors = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF", "#FFFFFF", "#000000"];
    }
    
    // Set up the canvas
    const canvas = wheelCanvas;
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 10;
    
    // Variables for wheel animation
    let isSpinning = false;
    let startAngle = 0;
    let spinTimeout = null;
    let spinTime = 0;
    let spinTimeTotal = 0;
    let currentRotation = 0;
    let gameCompleted = false;
    
    // Draw the wheel initially
    drawWheel();
    
    // Add event listener to spin button
    const spinButton = document.getElementById('spin-button');
    spinButton.addEventListener('click', startSpin);
    
    function drawWheel() {
        ctx.clearRect(0, 0, width, height);
        
        // Draw wheel background
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fillStyle = '#FFFFFF';
        ctx.fill();
        ctx.stroke();
        
        // Draw segments
        const arc = 2 * Math.PI / segmentsCount;
        
        for (let i = 0; i < segmentsCount; i++) {
            const angle = startAngle + i * arc;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, angle, angle + arc);
            ctx.lineTo(centerX, centerY);
            ctx.closePath();
            ctx.fillStyle = wheelColors[i % wheelColors.length];
            ctx.fill();
            ctx.stroke();
            
            // Draw text
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(angle + arc / 2);
            ctx.textAlign = 'right';
            ctx.fillStyle = '#000000';
            ctx.font = 'bold 16px Arial';
            ctx.fillText(`${i + 1}`, radius - 20, 5);
            ctx.restore();
        }
        
        // Draw center circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
        ctx.fillStyle = '#FFFFFF';
        ctx.fill();
        ctx.stroke();
    }
    
    function startSpin() {
        if (isSpinning || gameCompleted) return;
        
        isSpinning = true;
        spinButton.disabled = true;
        
        // Call the API to get the reward
        fetch('/game/api/play', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({
                game_id: gameId,
                security_token: securityToken
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Start the wheel animation
                spinTimeTotal = 5000; // 5 seconds
                spinTime = 0;
                
                // Store the result for later
                window.wheelResult = data;
                
                // Calculate the final position based on the reward
                const segmentIndex = Math.floor(Math.random() * segmentsCount);
                const arc = 2 * Math.PI / segmentsCount;
                const finalAngle = 2 * Math.PI - (segmentIndex * arc + arc / 2);
                
                // Add extra rotations for effect (5-10 full rotations)
                const extraRotations = 5 + Math.floor(Math.random() * 5);
                window.targetRotation = finalAngle + (extraRotations * 2 * Math.PI);
                
                // Start the animation
                rotateWheel();
            } else {
                // Show error
                showError(data.error);
                isSpinning = false;
                spinButton.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('An error occurred. Please try again.');
            isSpinning = false;
            spinButton.disabled = false;
        });
    }
    
    function rotateWheel() {
        spinTime += 30;
        if (spinTime >= spinTimeTotal) {
            stopRotateWheel();
            return;
        }
        
        // Easing function for smooth deceleration
        const spinFactor = easeOutCubic(spinTime, 0, 1, spinTimeTotal);
        const rotation = window.targetRotation * spinFactor;
        
        // Update the wheel position
        startAngle = currentRotation + rotation;
        drawWheel();
        
        spinTimeout = setTimeout(rotateWheel, 30);
    }
    
    function stopRotateWheel() {
        clearTimeout(spinTimeout);
        currentRotation = startAngle % (2 * Math.PI);
        isSpinning = false;
        gameCompleted = true;
        
        // Show the result
        showReward(window.wheelResult);
    }
    
    function easeOutCubic(t, b, c, d) {
        return c * ((t = t / d - 1) * t * t + 1) + b;
    }
    
    function showReward(data) {
        // Show the result container
        const resultContainer = document.getElementById('result-container');
        resultContainer.classList.remove('d-none');
        
        // Update result message based on reward type
        const resultMessage = document.getElementById('result-message');
        const resultTitle = document.getElementById('result-title');
        
        if (data.result.reward_type === 'no_reward') {
            resultTitle.textContent = 'Better luck next time!';
            resultMessage.textContent = 'You didn\'t win a reward this time.';
            
            // Hide claim button
            const claimButton = document.getElementById('claim-button');
            claimButton.classList.add('d-none');
        } else {
            resultTitle.textContent = 'Congratulations!';
            
            if (data.result.reward_type === 'coupon') {
                resultMessage.textContent = 'You won a coupon!';
            } else if (data.result.reward_type === 'ewallet') {
                resultMessage.textContent = `You won ${data.result.value} in eWallet credit!`;
            } else if (data.result.reward_type === 'external') {
                resultMessage.textContent = 'You won an external coupon!';
            }
            
            // Set up claim button
            const claimButton = document.getElementById('claim-button');
            claimButton.href = `/game/reward/${data.reward_id}`;
        }
    }
    
    function showError(message) {
        // Show error message
        const resultContainer = document.getElementById('result-container');
        resultContainer.classList.remove('d-none');
        
        const resultTitle = document.getElementById('result-title');
        resultTitle.textContent = 'Error';
        
        const resultMessage = document.getElementById('result-message');
        resultMessage.innerHTML = `<div class="alert alert-danger">${message}</div>`;
        
        // Hide claim button
        const claimButton = document.getElementById('claim-button');
        claimButton.classList.add('d-none');
    }
});
