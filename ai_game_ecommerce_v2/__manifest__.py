{
    'name': 'Website eCommerce Gamification V2',
    'version': '********.0',
    'category': 'Website/eCommerce',
    'license': 'LGPL-3',
    'summary': 'Add gamification elements like scratch cards and spin wheels to eCommerce',
    'description': """
        This module adds gamification elements to Odoo eCommerce:

        * Scratch cards for revealing rewards
        * Spin the wheel for winning prizes
        * Configurable reward probabilities
        * Event-based game distribution (signup, purchase, login)
        * Reward management (coupons, eWallet credits)
        * Security measures to prevent abuse
        * Detailed analytics and reporting

        Backend users can configure when and how games are awarded to website users,
        and define the rewards that can be won through these games.
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'web',
        'website',
        'website_sale',
        'sale',
        'loyalty',
    ],
    # 'suggests': [
    #     'coupon',
    # ],
    # 'data': [],
    # All views and data files are commented out since there's no user data
    # This will prevent any lock timeouts during uninstallation
    'data': [
        'security/security.xml',
        'security/ir_model_access.xml',
        'views/game_type_views.xml',
        'views/game_instance_views.xml',
        'views/game_reward_views.xml',
        'views/game_rule_views.xml',
        'views/user_game_views.xml',
        'views/user_reward_views.xml',
        'views/menu_views.xml',
        'views/templates.xml',
        # 'data/game_type_data.xml',  # Removed due to lock timeout
    ],
    # 'uninstall_hook': 'uninstall_hook',
    #     # 'security/security.xml',
    #     # 'security/ir_model_access.xml',
    #     'views/game_type_views.xml',
    #     # 'views/game_instance_views.xml',
    #     # 'views/game_reward_views.xml',
    #     # 'views/game_rule_views.xml',
    #     # 'views/user_game_views.xml',
    #     # 'views/user_reward_views.xml',
    #     # 'views/menu_views.xml',
    #     # 'views/templates.xml',
    #     # 'data/game_type_data.xml',  # Removed due to lock timeout
    # ],
    # 'assets': {
    #     'web.assets_frontend': [
    #         'ai_game_ecommerce_v2/static/src/css/scratch_card.css',
    #         'ai_game_ecommerce_v2/static/src/css/spin_wheel.css',
    #     ],
    #     'web.assets_frontend_lazy': [
    #         'ai_game_ecommerce_v2/static/src/js/scratch_card.js',
    #         'ai_game_ecommerce_v2/static/src/js/spin_wheel.js',
    #     ],
    # },
    'application': True,
    'installable': True,
    'auto_install': False,   
}
