<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Game Rule Tree View -->
    <record id="view_game_rule_tree" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.rule.tree</field>
        <field name="model">ai_game_ecommerce.game.rule</field>
        <field name="arch" type="xml">
            <tree string="Game Rules">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="game_instance_id"/>
                <field name="event_type"/>
                <field name="quantity"/>
                <field name="min_order_amount" optional="show"/>
                <field name="cooldown_days"/>
                <field name="max_per_user"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Game Rule Form View -->
    <record id="view_game_rule_form" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.rule.form</field>
        <field name="model">ai_game_ecommerce.game.rule</field>
        <field name="arch" type="xml">
            <form string="Game Rule">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Rule Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="game_instance_id"/>
                            <field name="event_type"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="quantity"/>
                            <field name="min_order_amount" modifiers="{'invisible': [('event_type', '!=', 'purchase')]}"/>
                            <field name="cooldown_days"/>
                            <field name="max_per_user"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Domain Filter">
                            <field name="domain" placeholder="[('field', '=', value)]"/>
                            <div class="text-muted">
                                Use Odoo domain format to specify additional conditions for this rule.
                                For example, [('product_id.categ_id.id', '=', 5)] would only apply this rule
                                when products from category with ID 5 are involved.
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Game Rule Search View -->
    <record id="view_game_rule_search" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.rule.search</field>
        <field name="model">ai_game_ecommerce.game.rule</field>
        <field name="arch" type="xml">
            <search string="Search Game Rules">
                <field name="name"/>
                <field name="game_instance_id"/>
                <field name="event_type"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Game Instance" name="groupby_game_instance" domain="[]" context="{'group_by': 'game_instance_id'}"/>
                    <filter string="Event Type" name="groupby_event_type" domain="[]" context="{'group_by': 'event_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Game Rule Action -->
    <record id="action_game_rule" model="ir.actions.act_window">
        <field name="name">Game Rules</field>
        <field name="res_model">ai_game_ecommerce.game.rule</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_game_rule_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new game rule
            </p>
            <p>
                Game rules define when and how games are awarded to users.
            </p>
        </field>
    </record>
    </data>
</odoo>
