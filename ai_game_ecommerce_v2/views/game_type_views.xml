<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Game Type Tree View -->
    <record id="view_game_type_tree" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.type.tree</field>
        <field name="model">ai_game_ecommerce.game.type</field>
        <field name="arch" type="xml">
            <tree string="Game Types">
                <field name="name"/>
                <field name="code"/>
                <field name="game_instance_count"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Game Type Form View -->
    <record id="view_game_type_form" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.type.form</field>
        <field name="model">ai_game_ecommerce.game.type</field>
        <field name="arch" type="xml">
            <form string="Game Type">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Game Type Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                        </group>
                        <group>
                            <field name="game_instance_count"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Describe this game type..."/>
                        </page>
                        <page string="Game Instances">
                            <field name="game_instance_ids">
                                <tree string="Game Instances">
                                    <field name="name"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Game Type Search View -->
    <record id="view_game_type_search" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.type.search</field>
        <field name="model">ai_game_ecommerce.game.type</field>
        <field name="arch" type="xml">
            <search string="Search Game Types">
                <field name="name"/>
                <field name="code"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Active" name="groupby_active" domain="[]" context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Game Type Action -->
    <record id="action_game_type" model="ir.actions.act_window">
        <field name="name">Game Types</field>
        <field name="res_model">ai_game_ecommerce.game.type</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_game_type_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new game type
            </p>
            <p>
                Game types define the different kinds of games that can be offered to users.
            </p>
        </field>
    </record>
    </data>
</odoo>
