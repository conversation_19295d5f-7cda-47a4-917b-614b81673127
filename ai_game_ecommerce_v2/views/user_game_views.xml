<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- User Game Tree View -->
    <record id="view_user_game_tree" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.user.game.tree</field>
        <field name="model">ai_game_ecommerce.user.game</field>
        <field name="arch" type="xml">
            <tree string="User Games">
                <field name="name"/>
                <field name="user_id"/>
                <field name="game_instance_id"/>
                <field name="rule_id"/>
                <field name="state"/>
                <field name="create_date"/>
                <field name="used_date"/>
                <field name="expiry_date"/>
            </tree>
        </field>
    </record>

    <!-- User Game Form View -->
    <record id="view_user_game_form" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.user.game.form</field>
        <field name="model">ai_game_ecommerce.user.game</field>
        <field name="arch" type="xml">
            <form string="User Game">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="available,used,expired"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="user_id"/>
                            <field name="game_instance_id"/>
                            <field name="rule_id"/>
                        </group>
                        <group>
                            <field name="create_date"/>
                            <field name="used_date"/>
                            <field name="expiry_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Result" invisible="state != 'used'">
                            <group>
                                <field name="user_reward_id"/>
                                <field name="result_data" widget="json_viewer"/>
                            </group>
                        </page>
                        <page string="Security">
                            <group>
                                <field name="security_token"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- User Game Search View -->
    <record id="view_user_game_search" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.user.game.search</field>
        <field name="model">ai_game_ecommerce.user.game</field>
        <field name="arch" type="xml">
            <search string="Search User Games">
                <field name="name"/>
                <field name="user_id"/>
                <field name="game_instance_id"/>
                <field name="rule_id"/>
                <filter string="Available" name="available" domain="[('state', '=', 'available')]"/>
                <filter string="Used" name="used" domain="[('state', '=', 'used')]"/>
                <filter string="Expired" name="expired" domain="[('state', '=', 'expired')]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="groupby_user" domain="[]" context="{'group_by': 'user_id'}"/>
                    <filter string="Game Instance" name="groupby_game_instance" domain="[]" context="{'group_by': 'game_instance_id'}"/>
                    <filter string="Rule" name="groupby_rule" domain="[]" context="{'group_by': 'rule_id'}"/>
                    <filter string="State" name="groupby_state" domain="[]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- User Game Action -->
    <record id="action_user_game" model="ir.actions.act_window">
        <field name="name">User Games</field>
        <field name="res_model">ai_game_ecommerce.user.game</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_user_game_search"/>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No user games found
            </p>
            <p>
                User games are created when game rules are triggered.
            </p>
        </field>
    </record>
    </data>
</odoo>
