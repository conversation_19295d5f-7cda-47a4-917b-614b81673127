<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Game Instance Tree View -->
    <record id="view_game_instance_tree" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.instance.tree</field>
        <field name="model">ai_game_ecommerce.game.instance</field>
        <field name="arch" type="xml">
            <tree string="Game Instances">
                <field name="name"/>
                <field name="game_type_id"/>
                <field name="display_type"/>
                <field name="user_game_count"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Game Instance Form View -->
    <record id="view_game_instance_form" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.instance.form</field>
        <field name="model">ai_game_ecommerce.game.instance</field>
        <field name="arch" type="xml">
            <form string="Game Instance">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <field name="image" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Game Instance Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="game_type_id"/>
                            <field name="display_type"/>
                        </group>
                        <group>
                            <field name="user_game_count"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Describe this game instance..."/>
                        </page>
                        <page string="Terms and Conditions">
                            <field name="terms_and_conditions" placeholder="Terms and conditions for this game..."/>
                        </page>
                        <page string="Scratch Card Settings" invisible="game_type_id.code != 'scratch_card'">
                            <group>
                                <field name="scratch_background_color" widget="color"/>
                                <field name="scratch_foreground_color" widget="color"/>
                                <field name="scratch_reveal_percentage"/>
                            </group>
                        </page>
                        <page string="Spin Wheel Settings" invisible="game_type_id.code != 'spin_wheel'">
                            <group>
                                <field name="wheel_segments"/>
                                <field name="wheel_colors"/>
                            </group>
                        </page>
                        <page string="Rewards">
                            <field name="game_reward_ids">
                                <tree string="Rewards" editable="bottom">
                                    <field name="name"/>
                                    <field name="reward_type"/>
                                    <field name="coupon_program_id" invisible="reward_type != 'coupon'"/>
                                    <field name="coupon_program_name" invisible="reward_type != 'coupon'"/>
                                    <field name="value" required="reward_type == 'ewallet'" invisible="reward_type != 'ewallet'"/>
                                    <field name="external_code" required="reward_type == 'external'" invisible="reward_type != 'external'"/>
                                    <field name="probability"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Rules">
                            <field name="game_rule_ids">
                                <tree string="Rules">
                                    <field name="name"/>
                                    <field name="event_type"/>
                                    <field name="quantity"/>
                                    <field name="min_order_amount" column_invisible="event_type != 'purchase'"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Game Instance Search View -->
    <record id="view_game_instance_search" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.instance.search</field>
        <field name="model">ai_game_ecommerce.game.instance</field>
        <field name="arch" type="xml">
            <search string="Search Game Instances">
                <field name="name"/>
                <field name="game_type_id"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Game Type" name="groupby_game_type" domain="[]" context="{'group_by': 'game_type_id'}"/>
                    <filter string="Display Type" name="groupby_display_type" domain="[]" context="{'group_by': 'display_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Game Instance Action -->
    <record id="action_game_instance" model="ir.actions.act_window">
        <field name="name">Game Instances</field>
        <field name="res_model">ai_game_ecommerce.game.instance</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_game_instance_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new game instance
            </p>
            <p>
                Game instances are specific configurations of games that can be awarded to users.
            </p>
        </field>
    </record>
    </data>
</odoo>
