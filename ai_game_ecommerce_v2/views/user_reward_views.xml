<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- User Reward Tree View -->
    <record id="view_user_reward_tree" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.user.reward.tree</field>
        <field name="model">ai_game_ecommerce.user.reward</field>
        <field name="arch" type="xml">
            <tree string="User Rewards">
                <field name="name"/>
                <field name="user_id"/>
                <field name="game_reward_id"/>
                <field name="reward_type"/>
                <field name="reference"/>
                <field name="state"/>
                <field name="create_date"/>
                <field name="claim_date"/>
                <field name="expiry_date"/>
            </tree>
        </field>
    </record>

    <!-- User Reward Form View -->
    <record id="view_user_reward_form" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.user.reward.form</field>
        <field name="model">ai_game_ecommerce.user.reward</field>
        <field name="arch" type="xml">
            <form string="User Reward">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="available,claimed,expired"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="user_id"/>
                            <field name="game_reward_id"/>
                            <field name="reward_type"/>
                        </group>
                        <group>
                            <field name="create_date"/>
                            <field name="claim_date"/>
                            <field name="expiry_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Reward Details">
                            <group>
                                <field name="reference"/>
                                <field name="value" invisible="reward_type != 'ewallet'"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- User Reward Search View -->
    <record id="view_user_reward_search" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.user.reward.search</field>
        <field name="model">ai_game_ecommerce.user.reward</field>
        <field name="arch" type="xml">
            <search string="Search User Rewards">
                <field name="name"/>
                <field name="user_id"/>
                <field name="game_reward_id"/>
                <field name="reward_type"/>
                <field name="reference"/>
                <filter string="Available" name="available" domain="[('state', '=', 'available')]"/>
                <filter string="Claimed" name="claimed" domain="[('state', '=', 'claimed')]"/>
                <filter string="Expired" name="expired" domain="[('state', '=', 'expired')]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="groupby_user" domain="[]" context="{'group_by': 'user_id'}"/>
                    <filter string="Game Reward" name="groupby_game_reward" domain="[]" context="{'group_by': 'game_reward_id'}"/>
                    <filter string="Reward Type" name="groupby_reward_type" domain="[]" context="{'group_by': 'reward_type'}"/>
                    <filter string="State" name="groupby_state" domain="[]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- User Reward Action -->
    <record id="action_user_reward" model="ir.actions.act_window">
        <field name="name">User Rewards</field>
        <field name="res_model">ai_game_ecommerce.user.reward</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_user_reward_search"/>
        <field name="context">{'search_default_available': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No user rewards found
            </p>
            <p>
                User rewards are created when users play games and win rewards.
            </p>
        </field>
    </record>
    </data>
</odoo>
