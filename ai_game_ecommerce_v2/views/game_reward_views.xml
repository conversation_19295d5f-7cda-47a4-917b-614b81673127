<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Game Reward Tree View -->
    <record id="view_game_reward_tree" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.reward.tree</field>
        <field name="model">ai_game_ecommerce.game.reward</field>
        <field name="arch" type="xml">
            <tree string="Game Rewards">
                <field name="name"/>
                <field name="game_instance_id"/>
                <field name="reward_type"/>
                <field name="probability"/>
                <field name="user_reward_count"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Game Reward Form View -->
    <record id="view_game_reward_form" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.reward.form</field>
        <field name="model">ai_game_ecommerce.game.reward</field>
        <field name="arch" type="xml">
            <form string="Game Reward">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Reward Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="game_instance_id"/>
                            <field name="reward_type"/>
                            <field name="probability"/>
                        </group>
                        <group>
                            <field name="coupon_program_id" invisible="reward_type != 'coupon'"/>
                            <field name="coupon_program_name" invisible="reward_type != 'coupon'"/>
                            <field name="value" invisible="reward_type != 'ewallet'" required="reward_type == 'ewallet'"/>
                            <field name="external_code" invisible="reward_type != 'external'" required="reward_type == 'external'"/>
                            <field name="user_reward_count"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Coupon Settings" invisible="reward_type != 'coupon'">
                            <group>
                                <field name="coupon_program_id"/>
                                <field name="coupon_program_name"/>
                                <div class="alert alert-info" role="alert">
                                    <p>If the coupon module is installed, you can select a coupon program by ID.</p>
                                    <p>Otherwise, you can provide a name for the coupon program for display purposes.</p>
                                </div>
                            </group>
                        </page>
                        <page string="External Description" invisible="reward_type != 'external'">
                            <field name="external_description" placeholder="Description for external coupon..."/>
                        </page>
                        <page string="User Rewards">
                            <field name="user_reward_ids">
                                <tree string="User Rewards">
                                    <field name="name"/>
                                    <field name="user_id"/>
                                    <field name="state"/>
                                    <field name="create_date"/>
                                    <field name="claim_date"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Game Reward Search View -->
    <record id="view_game_reward_search" model="ir.ui.view">
        <field name="name">ai_game_ecommerce.game.reward.search</field>
        <field name="model">ai_game_ecommerce.game.reward</field>
        <field name="arch" type="xml">
            <search string="Search Game Rewards">
                <field name="name"/>
                <field name="game_instance_id"/>
                <field name="reward_type"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Game Instance" name="groupby_game_instance" domain="[]" context="{'group_by': 'game_instance_id'}"/>
                    <filter string="Reward Type" name="groupby_reward_type" domain="[]" context="{'group_by': 'reward_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Game Reward Action -->
    <record id="action_game_reward" model="ir.actions.act_window">
        <field name="name">Game Rewards</field>
        <field name="res_model">ai_game_ecommerce.game.reward</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_game_reward_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new game reward
            </p>
            <p>
                Game rewards define what users can win when playing games.
            </p>
        </field>
    </record>
    </data>
</odoo>
