<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
    <!-- Game Dashboard Template -->
    <template id="dashboard" name="Game Dashboard">
        <t t-call="website.layout">
            <div class="container mt-4">
                <h1 class="text-center mb-4">Your Games</h1>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h3>Available Games</h3>
                            </div>
                            <div class="card-body">
                                <t t-if="available_games">
                                    <div class="list-group">
                                        <t t-foreach="available_games" t-as="game">
                                            <a t-att-href="'/game/play/%s' % game.id" class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h5 class="mb-1"><t t-esc="game.game_instance_id.name"/></h5>
                                                    <small><t t-esc="game.create_date" t-options="{'widget': 'date'}"/></small>
                                                </div>
                                                <p class="mb-1">
                                                    <t t-if="game.game_instance_id.game_type_id.code == 'scratch_card'">
                                                        Scratch Card
                                                    </t>
                                                    <t t-elif="game.game_instance_id.game_type_id.code == 'spin_wheel'">
                                                        Spin the Wheel
                                                    </t>
                                                </p>
                                                <small>Expires: <t t-esc="game.expiry_date" t-options="{'widget': 'date'}"/></small>
                                            </a>
                                        </t>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        You don't have any available games right now.
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h3>Available Rewards</h3>
                            </div>
                            <div class="card-body">
                                <t t-if="available_rewards">
                                    <div class="list-group">
                                        <t t-foreach="available_rewards" t-as="reward">
                                            <a t-att-href="'/game/reward/%s' % reward.id" class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between">
                                                    <h5 class="mb-1"><t t-esc="reward.game_reward_id.name"/></h5>
                                                    <small><t t-esc="reward.create_date" t-options="{'widget': 'date'}"/></small>
                                                </div>
                                                <p class="mb-1">
                                                    <t t-if="reward.reward_type == 'coupon'">
                                                        Coupon
                                                    </t>
                                                    <t t-elif="reward.reward_type == 'ewallet'">
                                                        eWallet Credit: <t t-esc="reward.value" t-options="{'widget': 'monetary'}"/>
                                                    </t>
                                                    <t t-elif="reward.reward_type == 'external'">
                                                        External Coupon
                                                    </t>
                                                </p>
                                                <small>Expires: <t t-esc="reward.expiry_date" t-options="{'widget': 'date'}"/></small>
                                            </a>
                                        </t>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        You don't have any available rewards right now.
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/game/rewards" class="btn btn-primary">View All Rewards</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Scratch Card Template -->
    <template id="scratch_card" name="Scratch Card">
        <t t-call="website.layout">
            <div class="container mt-4">
                <h1 class="text-center mb-4">Scratch Card</h1>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3><t t-esc="game_instance.name"/></h3>
                            </div>
                            <div class="card-body text-center">
                                <div id="scratch-card-container" class="scratch-card-container">
                                    <canvas id="scratch-card" width="300" height="300"></canvas>
                                    <div id="reward-container" class="reward-container">
                                        <div id="reward-text">Scratch to reveal your reward!</div>
                                    </div>
                                </div>

                                <div id="result-container" class="mt-4 d-none">
                                    <h3 id="result-title">Congratulations!</h3>
                                    <p id="result-message"></p>
                                    <a id="claim-button" href="#" class="btn btn-primary">Claim Reward</a>
                                </div>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">Scratch the card to reveal your reward.</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/game/dashboard" class="btn btn-secondary">Back to Dashboard</a>
                </div>

                <input type="hidden" id="game-id" t-att-value="user_game.id"/>
                <input type="hidden" id="security-token" t-att-value="security_token"/>
                <input type="hidden" id="scratch-bg-color" t-att-value="game_instance.scratch_background_color"/>
                <input type="hidden" id="scratch-fg-color" t-att-value="game_instance.scratch_foreground_color"/>
                <input type="hidden" id="scratch-reveal-percentage" t-att-value="game_instance.scratch_reveal_percentage"/>
            </div>
        </t>
    </template>

    <!-- Spin Wheel Template -->
    <template id="spin_wheel" name="Spin the Wheel">
        <t t-call="website.layout">
            <div class="container mt-4">
                <h1 class="text-center mb-4">Spin the Wheel</h1>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3><t t-esc="game_instance.name"/></h3>
                            </div>
                            <div class="card-body text-center">
                                <div id="wheel-container" class="wheel-container">
                                    <canvas id="wheel-canvas" width="400" height="400"></canvas>
                                    <div id="wheel-pointer"></div>
                                </div>

                                <button id="spin-button" class="btn btn-primary mt-4">Spin the Wheel</button>

                                <div id="result-container" class="mt-4 d-none">
                                    <h3 id="result-title">Congratulations!</h3>
                                    <p id="result-message"></p>
                                    <a id="claim-button" href="#" class="btn btn-primary">Claim Reward</a>
                                </div>
                            </div>
                            <div class="card-footer">
                                <small class="text-muted">Click the button to spin the wheel and win a reward.</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/game/dashboard" class="btn btn-secondary">Back to Dashboard</a>
                </div>

                <input type="hidden" id="game-id" t-att-value="user_game.id"/>
                <input type="hidden" id="security-token" t-att-value="security_token"/>
                <input type="hidden" id="wheel-segments" t-att-value="game_instance.wheel_segments"/>
                <input type="hidden" id="wheel-colors" t-att-value="game_instance.wheel_colors"/>
            </div>
        </t>
    </template>

    <!-- Rewards Template -->
    <template id="rewards" name="Rewards">
        <t t-call="website.layout">
            <div class="container mt-4">
                <h1 class="text-center mb-4">Your Rewards</h1>

                <ul class="nav nav-tabs" id="rewardsTab" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="available-tab" data-toggle="tab" href="#available" role="tab">Available</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="claimed-tab" data-toggle="tab" href="#claimed" role="tab">Claimed</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="expired-tab" data-toggle="tab" href="#expired" role="tab">Expired</a>
                    </li>
                </ul>

                <div class="tab-content mt-4" id="rewardsTabContent">
                    <div class="tab-pane fade show active" id="available" role="tabpanel">
                        <t t-if="available_rewards">
                            <div class="list-group">
                                <t t-foreach="available_rewards" t-as="reward">
                                    <a t-att-href="'/game/reward/%s' % reward.id" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h5 class="mb-1"><t t-esc="reward.game_reward_id.name"/></h5>
                                            <small><t t-esc="reward.create_date" t-options="{'widget': 'date'}"/></small>
                                        </div>
                                        <p class="mb-1">
                                            <t t-if="reward.reward_type == 'coupon'">
                                                Coupon
                                            </t>
                                            <t t-elif="reward.reward_type == 'ewallet'">
                                                eWallet Credit: <t t-esc="reward.value" t-options="{'widget': 'monetary'}"/>
                                            </t>
                                            <t t-elif="reward.reward_type == 'external'">
                                                External Coupon
                                            </t>
                                        </p>
                                        <small>Expires: <t t-esc="reward.expiry_date" t-options="{'widget': 'date'}"/></small>
                                    </a>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div class="alert alert-info">
                                You don't have any available rewards.
                            </div>
                        </t>
                    </div>

                    <div class="tab-pane fade" id="claimed" role="tabpanel">
                        <t t-if="claimed_rewards">
                            <div class="list-group">
                                <t t-foreach="claimed_rewards" t-as="reward">
                                    <a t-att-href="'/game/reward/%s' % reward.id" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h5 class="mb-1"><t t-esc="reward.game_reward_id.name"/></h5>
                                            <small>Claimed: <t t-esc="reward.claim_date" t-options="{'widget': 'date'}"/></small>
                                        </div>
                                        <p class="mb-1">
                                            <t t-if="reward.reward_type == 'coupon'">
                                                Coupon: <t t-esc="reward.reference"/>
                                            </t>
                                            <t t-elif="reward.reward_type == 'ewallet'">
                                                eWallet Credit: <t t-esc="reward.value" t-options="{'widget': 'monetary'}"/>
                                            </t>
                                            <t t-elif="reward.reward_type == 'external'">
                                                External Coupon: <t t-esc="reward.reference"/>
                                            </t>
                                        </p>
                                    </a>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div class="alert alert-info">
                                You don't have any claimed rewards.
                            </div>
                        </t>
                    </div>

                    <div class="tab-pane fade" id="expired" role="tabpanel">
                        <t t-if="expired_rewards">
                            <div class="list-group">
                                <t t-foreach="expired_rewards" t-as="reward">
                                    <div class="list-group-item list-group-item-action disabled">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h5 class="mb-1"><t t-esc="reward.game_reward_id.name"/></h5>
                                            <small>Expired: <t t-esc="reward.expiry_date" t-options="{'widget': 'date'}"/></small>
                                        </div>
                                        <p class="mb-1">
                                            <t t-if="reward.reward_type == 'coupon'">
                                                Coupon
                                            </t>
                                            <t t-elif="reward.reward_type == 'ewallet'">
                                                eWallet Credit: <t t-esc="reward.value" t-options="{'widget': 'monetary'}"/>
                                            </t>
                                            <t t-elif="reward.reward_type == 'external'">
                                                External Coupon
                                            </t>
                                        </p>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div class="alert alert-info">
                                You don't have any expired rewards.
                            </div>
                        </t>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/game/dashboard" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Reward Detail Template -->
    <template id="reward_detail" name="Reward Detail">
        <t t-call="website.layout">
            <div class="container mt-4">
                <h1 class="text-center mb-4">Reward Details</h1>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3><t t-esc="user_reward.game_reward_id.name"/></h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Reward Type:</strong>
                                            <t t-if="user_reward.reward_type == 'coupon'">
                                                Coupon
                                            </t>
                                            <t t-elif="user_reward.reward_type == 'ewallet'">
                                                eWallet Credit
                                            </t>
                                            <t t-elif="user_reward.reward_type == 'external'">
                                                External Coupon
                                            </t>
                                            <t t-elif="user_reward.reward_type == 'no_reward'">
                                                No Reward
                                            </t>
                                        </p>
                                        <p><strong>Status:</strong>
                                            <span t-att-class="'badge ' + ('badge-success' if user_reward.state == 'claimed' else 'badge-warning' if user_reward.state == 'available' else 'badge-danger')">
                                                <t t-if="user_reward.state == 'available'">Available</t>
                                                <t t-elif="user_reward.state == 'claimed'">Claimed</t>
                                                <t t-elif="user_reward.state == 'expired'">Expired</t>
                                            </span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Created:</strong> <t t-esc="user_reward.create_date" t-options="{'widget': 'datetime'}"/></p>
                                        <t t-if="user_reward.claim_date">
                                            <p><strong>Claimed:</strong> <t t-esc="user_reward.claim_date" t-options="{'widget': 'datetime'}"/></p>
                                        </t>
                                        <p><strong>Expires:</strong> <t t-esc="user_reward.expiry_date" t-options="{'widget': 'datetime'}"/></p>
                                    </div>
                                </div>

                                <hr/>

                                <div class="reward-details">
                                    <t t-if="user_reward.reward_type == 'coupon' and user_reward.state == 'claimed'">
                                        <div class="text-center">
                                            <h4>Coupon Code</h4>
                                            <div class="coupon-code">
                                                <span t-esc="user_reward.reference"/>
                                            </div>
                                            <p class="mt-3">Use this coupon code during checkout to apply the discount.</p>
                                        </div>
                                    </t>
                                    <t t-elif="user_reward.reward_type == 'ewallet' and user_reward.state == 'claimed'">
                                        <div class="text-center">
                                            <h4>eWallet Credit</h4>
                                            <div class="ewallet-amount">
                                                <span t-esc="user_reward.value" t-options="{'widget': 'monetary'}"/>
                                            </div>
                                            <p class="mt-3">This amount has been credited to your eWallet.</p>
                                        </div>
                                    </t>
                                    <t t-elif="user_reward.reward_type == 'external' and user_reward.state == 'claimed'">
                                        <div class="text-center">
                                            <h4>External Coupon Code</h4>
                                            <div class="coupon-code">
                                                <span t-esc="user_reward.reference"/>
                                            </div>
                                            <p class="mt-3">Use this coupon code on the external website.</p>
                                            <div t-if="user_reward.game_reward_id.external_description" class="mt-3">
                                                <t t-raw="user_reward.game_reward_id.external_description"/>
                                            </div>
                                        </div>
                                    </t>
                                    <t t-elif="user_reward.state == 'available'">
                                        <div class="text-center">
                                            <p>This reward is available to claim.</p>
                                            <a t-att-href="'/game/claim/%s' % user_reward.id" class="btn btn-primary mt-3">Claim Reward</a>
                                        </div>
                                    </t>
                                    <t t-elif="user_reward.state == 'expired'">
                                        <div class="text-center">
                                            <p>This reward has expired and can no longer be claimed.</p>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/game/rewards" class="btn btn-secondary">Back to Rewards</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Reward Claimed Template -->
    <template id="reward_claimed" name="Reward Claimed">
        <t t-call="website.layout">
            <div class="container mt-4">
                <h1 class="text-center mb-4">Reward Claimed</h1>

                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div t-att-class="'alert ' + ('alert-success' if success else 'alert-danger')">
                            <t t-esc="message"/>
                        </div>

                        <t t-if="success">
                            <div class="card">
                                <div class="card-header">
                                    <h3><t t-esc="user_reward.game_reward_id.name"/></h3>
                                </div>
                                <div class="card-body text-center">
                                    <t t-if="user_reward.reward_type == 'coupon'">
                                        <h4>Coupon Code</h4>
                                        <div class="coupon-code">
                                            <span t-esc="user_reward.reference"/>
                                        </div>
                                        <p class="mt-3">Use this coupon code during checkout to apply the discount.</p>
                                    </t>
                                    <t t-elif="user_reward.reward_type == 'ewallet'">
                                        <h4>eWallet Credit</h4>
                                        <div class="ewallet-amount">
                                            <span t-esc="user_reward.value" t-options="{'widget': 'monetary'}"/>
                                        </div>
                                        <p class="mt-3">This amount has been credited to your eWallet.</p>
                                    </t>
                                    <t t-elif="user_reward.reward_type == 'external'">
                                        <h4>External Coupon Code</h4>
                                        <div class="coupon-code">
                                            <span t-esc="user_reward.reference"/>
                                        </div>
                                        <p class="mt-3">Use this coupon code on the external website.</p>
                                        <div t-if="user_reward.game_reward_id.external_description" class="mt-3">
                                            <t t-raw="user_reward.game_reward_id.external_description"/>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="/game/rewards" class="btn btn-secondary">Back to Rewards</a>
                </div>
            </div>
        </t>
    </template>

    <!-- Add Game Dashboard to User Menu -->
    <template id="user_navbar_inherit_game" inherit_id="portal.user_dropdown">
        <xpath expr="//a[@href='/my/home']" position="after">
            <a href="/game/dashboard" class="dropdown-item">My Games</a>
        </xpath>
    </template>

    <!-- Add Game Dashboard to Website Menu -->
    <template id="website_menu_inherit_game" inherit_id="website.layout">
        <xpath expr="//header//ul[hasclass('navbar-nav')]" position="inside">
            <li class="nav-item">
                <a href="/game/dashboard" class="nav-link">Games</a>
            </li>
        </xpath>
    </template>
    </data>
</odoo>
