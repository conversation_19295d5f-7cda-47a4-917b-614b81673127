from odoo import http, _
from odoo.http import request
import logging
import json
from werkzeug.exceptions import BadRequest, Forbidden

_logger = logging.getLogger(__name__)

class GameEcommerceAPI(http.Controller):
    
    @http.route(['/game/api/play'], type='json', auth='user', website=True, csrf=True)
    def play_game(self, **kw):
        """API endpoint to play a game and get the result"""
        user = request.env.user
        
        # Get parameters
        game_id = kw.get('game_id')
        security_token = kw.get('security_token')
        
        if not game_id or not security_token:
            return {'success': False, 'error': _("Missing required parameters")}
        
        # Get the user game
        user_game = request.env['ai_game_ecommerce.user.game'].sudo().search([
            ('id', '=', int(game_id)),
            ('user_id', '=', user.id),
            ('state', '=', 'available'),
        ], limit=1)
        
        if not user_game:
            return {'success': False, 'error': _("Game not found or not available")}
        
        # Verify security token
        if not user_game.verify_security_token(security_token):
            _logger.warning("Invalid security token for game %s", game_id)
            return {'success': False, 'error': _("Invalid security token")}
        
        try:
            # Play the game
            result = user_game.play_game(security_token)
            
            return {
                'success': True,
                'result': result,
                'reward_name': user_game.user_reward_id.game_reward_id.name,
                'reward_type': user_game.user_reward_id.reward_type,
                'reward_id': user_game.user_reward_id.id,
            }
        except Exception as e:
            _logger.error("Error playing game: %s", str(e))
            return {'success': False, 'error': str(e)}
    
    @http.route(['/game/api/claim_reward'], type='json', auth='user', website=True, csrf=True)
    def claim_reward(self, **kw):
        """API endpoint to claim a reward"""
        user = request.env.user
        
        # Get parameters
        reward_id = kw.get('reward_id')
        
        if not reward_id:
            return {'success': False, 'error': _("Missing required parameters")}
        
        # Get the user reward
        user_reward = request.env['ai_game_ecommerce.user.reward'].sudo().search([
            ('id', '=', int(reward_id)),
            ('user_id', '=', user.id),
            ('state', '=', 'available'),
        ], limit=1)
        
        if not user_reward:
            return {'success': False, 'error': _("Reward not found or not available")}
        
        try:
            # Claim the reward
            result = user_reward.claim_reward()
            
            return {
                'success': True,
                'result': result,
            }
        except Exception as e:
            _logger.error("Error claiming reward: %s", str(e))
            return {'success': False, 'error': str(e)}
    
    @http.route(['/game/api/available_games'], type='json', auth='user', website=True)
    def available_games(self, **kw):
        """API endpoint to get available games for the user"""
        user = request.env.user
        
        available_games = user.get_available_games()
        
        result = []
        for game in available_games:
            result.append({
                'id': game.id,
                'name': game.game_instance_id.name,
                'game_type': game.game_instance_id.game_type_id.code,
                'security_token': game.security_token,
            })
        
        return {
            'success': True,
            'games': result,
        }
    
    @http.route(['/game/api/available_rewards'], type='json', auth='user', website=True)
    def available_rewards(self, **kw):
        """API endpoint to get available rewards for the user"""
        user = request.env.user
        
        available_rewards = user.get_available_rewards()
        
        result = []
        for reward in available_rewards:
            result.append({
                'id': reward.id,
                'name': reward.game_reward_id.name,
                'reward_type': reward.reward_type,
                'value': reward.value if reward.reward_type == 'ewallet' else 0,
            })
        
        return {
            'success': True,
            'rewards': result,
        }
    
    @http.route(['/game/api/trigger_event'], type='json', auth='user', website=True, csrf=True)
    def trigger_event(self, **kw):
        """API endpoint to trigger a game event"""
        user = request.env.user
        
        # Get parameters
        event_type = kw.get('event_type')
        event_data = kw.get('event_data', {})
        
        if not event_type:
            return {'success': False, 'error': _("Missing required parameters")}
        
        # Process the event
        try:
            awarded_games = user.process_game_event(event_type, event_data)
            
            result = []
            for game in awarded_games:
                result.append({
                    'id': game.id,
                    'name': game.game_instance_id.name,
                    'game_type': game.game_instance_id.game_type_id.code,
                })
            
            return {
                'success': True,
                'awarded_games': result,
                'count': len(result),
            }
        except Exception as e:
            _logger.error("Error processing game event: %s", str(e))
            return {'success': False, 'error': str(e)}
