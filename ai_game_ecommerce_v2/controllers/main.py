from odoo import http, _
from odoo.http import request
import logging
import json

_logger = logging.getLogger(__name__)

class GameEcommerceController(http.Controller):
    
    @http.route(['/game/dashboard'], type='http', auth='user', website=True)
    def game_dashboard(self, **kw):
        """User's game dashboard showing available games and rewards"""
        user = request.env.user
        
        values = {
            'user': user,
            'available_games': user.get_available_games(),
            'available_rewards': user.get_available_rewards(),
        }
        
        return request.render('ai_game_ecommerce.dashboard', values)
    
    @http.route(['/game/play/<int:game_id>'], type='http', auth='user', website=True)
    def play_game(self, game_id, **kw):
        """Play a specific game"""
        user = request.env.user
        
        # Get the user game
        user_game = request.env['ai_game_ecommerce.user.game'].sudo().search([
            ('id', '=', game_id),
            ('user_id', '=', user.id),
            ('state', '=', 'available'),
        ], limit=1)
        
        if not user_game:
            return request.redirect('/game/dashboard')
        
        # Get the game instance
        game_instance = user_game.game_instance_id
        
        values = {
            'user': user,
            'user_game': user_game,
            'game_instance': game_instance,
            'security_token': user_game.security_token,
        }
        
        # Render the appropriate template based on game type
        if game_instance.game_type_id.code == 'scratch_card':
            return request.render('ai_game_ecommerce.scratch_card', values)
        elif game_instance.game_type_id.code == 'spin_wheel':
            return request.render('ai_game_ecommerce.spin_wheel', values)
        else:
            return request.redirect('/game/dashboard')
    
    @http.route(['/game/rewards'], type='http', auth='user', website=True)
    def rewards(self, **kw):
        """View user rewards"""
        user = request.env.user
        
        values = {
            'user': user,
            'available_rewards': user.user_reward_ids.filtered(lambda r: r.state == 'available'),
            'claimed_rewards': user.user_reward_ids.filtered(lambda r: r.state == 'claimed'),
            'expired_rewards': user.user_reward_ids.filtered(lambda r: r.state == 'expired'),
        }
        
        return request.render('ai_game_ecommerce.rewards', values)
    
    @http.route(['/game/reward/<int:reward_id>'], type='http', auth='user', website=True)
    def view_reward(self, reward_id, **kw):
        """View a specific reward"""
        user = request.env.user
        
        # Get the user reward
        user_reward = request.env['ai_game_ecommerce.user.reward'].sudo().search([
            ('id', '=', reward_id),
            ('user_id', '=', user.id),
        ], limit=1)
        
        if not user_reward:
            return request.redirect('/game/rewards')
        
        values = {
            'user': user,
            'user_reward': user_reward,
        }
        
        return request.render('ai_game_ecommerce.reward_detail', values)
    
    @http.route(['/game/claim/<int:reward_id>'], type='http', auth='user', website=True)
    def claim_reward(self, reward_id, **kw):
        """Claim a specific reward"""
        user = request.env.user
        
        # Get the user reward
        user_reward = request.env['ai_game_ecommerce.user.reward'].sudo().search([
            ('id', '=', reward_id),
            ('user_id', '=', user.id),
            ('state', '=', 'available'),
        ], limit=1)
        
        if not user_reward:
            return request.redirect('/game/rewards')
        
        # Claim the reward
        try:
            result = user_reward.claim_reward()
            
            values = {
                'user': user,
                'user_reward': user_reward,
                'result': result,
                'success': True,
                'message': _("Reward claimed successfully!"),
            }
        except Exception as e:
            values = {
                'user': user,
                'user_reward': user_reward,
                'success': False,
                'message': str(e),
            }
        
        return request.render('ai_game_ecommerce.reward_claimed', values)
