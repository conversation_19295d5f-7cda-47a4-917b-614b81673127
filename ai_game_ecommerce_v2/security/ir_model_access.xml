<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Game Type Access Rights -->
        <record id="access_game_type_user" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.type.user</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_type"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_game_type_manager" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.type.manager</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_type"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
        
        <!-- Game Instance Access Rights -->
        <record id="access_game_instance_user" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.instance.user</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_instance"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_game_instance_manager" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.instance.manager</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_instance"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
        
        <!-- Game Reward Access Rights -->
        <record id="access_game_reward_user" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.reward.user</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_reward"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_game_reward_manager" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.reward.manager</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_reward"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
        
        <!-- Game Rule Access Rights -->
        <record id="access_game_rule_user" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.rule.user</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_rule"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_game_rule_manager" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.rule.manager</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_rule"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
        
        <!-- User Game Access Rights -->
        <record id="access_user_game_user" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.game.user</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_game"/>
            <field name="group_id" ref="base.group_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_user_game_manager" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.game.manager</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_game"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
        
        <!-- User Reward Access Rights -->
        <record id="access_user_reward_user" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.reward.user</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_reward"/>
            <field name="group_id" ref="base.group_user"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_user_reward_manager" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.reward.manager</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_reward"/>
            <field name="group_id" ref="ai_game_ecommerce_v2.group_game_ecommerce_manager"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
        </record>
        
        <!-- Public Access Rights -->
        <record id="access_game_type_public" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.type.public</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_type"/>
            <field name="group_id" ref="base.group_public"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_game_instance_public" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.instance.public</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_instance"/>
            <field name="group_id" ref="base.group_public"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_user_game_public" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.game.public</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_game"/>
            <field name="group_id" ref="base.group_public"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_user_reward_public" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.reward.public</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_reward"/>
            <field name="group_id" ref="base.group_public"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <!-- Portal Access Rights -->
        <record id="access_game_type_portal" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.type.portal</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_type"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_game_instance_portal" model="ir.model.access">
            <field name="name">ai_game_ecommerce.game.instance.portal</field>
            <field name="model_id" ref="model_ai_game_ecommerce_game_instance"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_user_game_portal" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.game.portal</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_game"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>
        
        <record id="access_user_reward_portal" model="ir.model.access">
            <field name="name">ai_game_ecommerce.user.reward.portal</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_reward"/>
            <field name="group_id" ref="base.group_portal"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>
    </data>
</odoo>
