<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Game eCommerce Security Groups -->
        <record id="group_game_ecommerce_user" model="res.groups">
            <field name="name">Game eCommerce User</field>
            <field name="category_id" ref="base.module_category_website_website"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="group_game_ecommerce_manager" model="res.groups">
            <field name="name">Game eCommerce Manager</field>
            <field name="category_id" ref="base.module_category_website_website"/>
            <field name="implied_ids" eval="[(4, ref('group_game_ecommerce_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>

    <data noupdate="1">

        <!-- User game access rules -->
        <record id="user_game_user_rule" model="ir.rule">
            <field name="name">User Game: own records only</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_game"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="user_game_manager_rule" model="ir.rule">
            <field name="name">User Game: all records</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_game"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('ai_game_ecommerce_v2.group_game_ecommerce_manager'))]"/>
        </record>

        <!-- User reward access rules -->
        <record id="user_reward_user_rule" model="ir.rule">
            <field name="name">User Reward: own records only</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_reward"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="user_reward_manager_rule" model="ir.rule">
            <field name="name">User Reward: all records</field>
            <field name="model_id" ref="model_ai_game_ecommerce_user_reward"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('ai_game_ecommerce_v2.group_game_ecommerce_manager'))]"/>
        </record>
    </data>
</odoo>
