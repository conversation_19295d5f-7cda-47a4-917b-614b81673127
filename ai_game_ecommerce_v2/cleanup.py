import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def cleanup_database(cr):
    """Cleanup database records related to ai_game_ecommerce_v2"""
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    try:
        # Delete game types
        game_types = env['ai_game_ecommerce.game.type'].search([])
        if game_types:
            _logger.info(f"Found {len(game_types)} game types to delete")
            game_types.unlink()
        
        # Delete views
        views = env['ir.ui.view'].search([
            ('model', 'like', 'ai_game_ecommerce%')
        ])
        if views:
            _logger.info(f"Found {len(views)} views to delete")
            views.unlink()
        
        # Delete menus
        menus = env['ir.ui.menu'].search([
            ('name', 'like', 'ai_game_ecommerce%')
        ])
        if menus:
            _logger.info(f"Found {len(menus)} menus to delete")
            menus.unlink()
        
        # Delete model data
        model_data = env['ir.model.data'].search([
            ('module', '=', 'ai_game_ecommerce_v2')
        ])
        if model_data:
            _logger.info(f"Found {len(model_data)} model data records to delete")
            model_data.unlink()
        
        _logger.info("Cleanup completed successfully")
        
    except Exception as e:
        _logger.error(f"Error during cleanup: {str(e)}")
        raise

def uninstall_hook(env):
    """Uninstall hook to clean up database records"""
    cr = env.cr
    cleanup_database(cr)

if __name__ == '__main__':
    # This script is meant to be run from Odoo's command line
    pass
