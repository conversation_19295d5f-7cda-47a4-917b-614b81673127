from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class GameRule(models.Model):
    _name = 'ai_game_ecommerce.game.rule'
    _description = 'Game Rule'
    _order = 'sequence, id'
    
    name = fields.Char(string='Name', required=True, translate=True)
    game_instance_id = fields.Many2one('ai_game_ecommerce.game.instance', string='Game Instance', required=True, ondelete='cascade')
    active = fields.Boolean(string='Active', default=True)
    sequence = fields.Integer(string='Sequence', default=10, help="Determines the order of rule evaluation")
    
    # Event configuration
    event_type = fields.Selection([
        ('signup', 'User Signup'),
        ('login', 'User Login'),
        ('purchase', 'Purchase Completed'),
        ('cart_add', 'Add to Cart'),
        ('wishlist_add', 'Add to Wishlist'),
        ('review', 'Product Review'),
        ('manual', 'Manual Assignment'),
    ], string='Event Type', default='signup', required=True)
    
    # Domain filter for events
    domain = fields.Char(string='Domain', default='[]', 
                        help="Domain filter to determine when this rule applies. Use Odoo domain format.")
    
    # Quantity of games to award
    quantity = fields.Integer(string='Quantity', default=1, 
                             help="Number of games to award when this rule is triggered")
    
    # Minimum order amount for purchase events
    min_order_amount = fields.Float(string='Minimum Order Amount', default=0.0,
                                   help="Minimum order amount required to trigger this rule (for purchase events)")
    
    # Cooldown period
    cooldown_days = fields.Integer(string='Cooldown Days', default=0,
                                  help="Number of days before this rule can be triggered again for the same user")
    
    # Maximum games per user
    max_per_user = fields.Integer(string='Maximum Per User', default=0,
                                 help="Maximum number of games a user can receive from this rule (0 = unlimited)")
    
    @api.constrains('domain')
    def _check_domain(self):
        for record in self:
            try:
                # Try to evaluate the domain to ensure it's valid
                domain = eval(record.domain)
                if not isinstance(domain, list):
                    raise ValidationError(_("Domain must evaluate to a list"))
            except Exception as e:
                raise ValidationError(_("Invalid domain: %s") % str(e))
    
    @api.constrains('quantity', 'min_order_amount', 'cooldown_days', 'max_per_user')
    def _check_positive_values(self):
        for record in self:
            if record.quantity <= 0:
                raise ValidationError(_("Quantity must be positive"))
            if record.min_order_amount < 0:
                raise ValidationError(_("Minimum order amount cannot be negative"))
            if record.cooldown_days < 0:
                raise ValidationError(_("Cooldown days cannot be negative"))
            if record.max_per_user < 0:
                raise ValidationError(_("Maximum per user cannot be negative"))
    
    def check_rule_eligibility(self, user, event_data=None):
        """
        Check if a user is eligible for this rule based on the event data
        
        Args:
            user: res.users record
            event_data: dict with event-specific data
            
        Returns:
            bool: Whether the user is eligible
        """
        self.ensure_one()
        
        if not self.active or not self.game_instance_id.active:
            return False
        
        # Check cooldown period
        if self.cooldown_days > 0:
            last_game = self.env['ai_game_ecommerce.user.game'].search([
                ('user_id', '=', user.id),
                ('game_instance_id', '=', self.game_instance_id.id),
                ('rule_id', '=', self.id),
                ('create_date', '>=', fields.Datetime.now() - fields.Datetime.to_timedelta(days=self.cooldown_days)),
            ], limit=1)
            
            if last_game:
                _logger.info("User %s is in cooldown period for rule %s", user.name, self.name)
                return False
        
        # Check maximum per user
        if self.max_per_user > 0:
            game_count = self.env['ai_game_ecommerce.user.game'].search_count([
                ('user_id', '=', user.id),
                ('game_instance_id', '=', self.game_instance_id.id),
                ('rule_id', '=', self.id),
            ])
            
            if game_count >= self.max_per_user:
                _logger.info("User %s has reached maximum games for rule %s", user.name, self.name)
                return False
        
        # Check event-specific conditions
        if self.event_type == 'purchase' and event_data and 'amount' in event_data:
            if event_data['amount'] < self.min_order_amount:
                _logger.info("Order amount %s is below minimum %s for rule %s", 
                            event_data['amount'], self.min_order_amount, self.name)
                return False
        
        # Check domain if specified
        if self.domain and self.domain != '[]':
            try:
                domain = eval(self.domain)
                if event_data:
                    # Apply domain to event data
                    # This is a simplified implementation and would need to be expanded
                    # based on the specific event types and data structure
                    match = True
                    for condition in domain:
                        if len(condition) == 3 and condition[0] in event_data:
                            field, operator, value = condition
                            if operator == '=' and event_data[field] != value:
                                match = False
                                break
                            # Add more operator implementations as needed
                    
                    if not match:
                        _logger.info("Event data does not match domain for rule %s", self.name)
                        return False
            except Exception as e:
                _logger.error("Error evaluating domain for rule %s: %s", self.name, str(e))
                return False
        
        return True
    
    def award_games(self, user, event_data=None):
        """
        Award games to a user based on this rule
        
        Args:
            user: res.users record
            event_data: dict with event-specific data
            
        Returns:
            list: Created user.game records
        """
        self.ensure_one()
        
        if not self.check_rule_eligibility(user, event_data):
            return []
        
        user_games = []
        for i in range(self.quantity):
            user_game = self.env['ai_game_ecommerce.user.game'].create({
                'user_id': user.id,
                'game_instance_id': self.game_instance_id.id,
                'rule_id': self.id,
                'state': 'available',
            })
            user_games.append(user_game)
        
        _logger.info("Awarded %s games to user %s based on rule %s", 
                    len(user_games), user.name, self.name)
        
        return user_games
