from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging
import random
import json

_logger = logging.getLogger(__name__)

class UserGame(models.Model):
    _name = 'ai_game_ecommerce.user.game'
    _description = 'User Game'
    _order = 'create_date desc'
    
    name = fields.Char(string='Name', compute='_compute_name', store=True)
    user_id = fields.Many2one('res.users', string='User', required=True, ondelete='cascade', index=True)
    game_instance_id = fields.Many2one('ai_game_ecommerce.game.instance', string='Game', required=True, ondelete='cascade')
    rule_id = fields.Many2one('ai_game_ecommerce.game.rule', string='Rule', ondelete='set null')
    
    state = fields.Selection([
        ('available', 'Available'),
        ('used', 'Used'),
        ('expired', 'Expired'),
    ], string='State', default='available', required=True, index=True)
    
    create_date = fields.Datetime(string='Created On', readonly=True)
    used_date = fields.Datetime(string='Used On', readonly=True)
    expiry_date = fields.Datetime(string='Expires On', compute='_compute_expiry_date', store=True)
    
    # Game result
    user_reward_id = fields.Many2one('ai_game_ecommerce.user.reward', string='Reward', readonly=True)
    result_data = fields.Text(string='Result Data', help="JSON data with game result information")
    
    # Security token to prevent tampering
    security_token = fields.Char(string='Security Token', readonly=True, copy=False)
    
    @api.depends('user_id', 'game_instance_id', 'create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                date_str = fields.Datetime.to_string(record.create_date)
            else:
                date_str = 'New'
            record.name = f"{record.game_instance_id.name} - {record.user_id.name} - {date_str}"
    
    @api.depends('create_date', 'game_instance_id')
    def _compute_expiry_date(self):
        for record in self:
            if record.create_date:
                # Default expiry is 30 days
                expiry_days = 30
                record.expiry_date = record.create_date + fields.Datetime.to_timedelta(days=expiry_days)
            else:
                record.expiry_date = False
    
    @api.model
    def create(self, vals):
        """Override create to generate security token"""
        record = super(UserGame, self).create(vals)
        record.security_token = self._generate_security_token(record)
        return record
    
    def _generate_security_token(self, record):
        """Generate a security token for this game"""
        # In a real implementation, this would use a more secure method
        import hashlib
        import time
        
        # Create a unique token based on record data and a secret
        secret = self.env['ir.config_parameter'].sudo().get_param('database.secret')
        token_base = f"{record.id}-{record.user_id.id}-{record.game_instance_id.id}-{time.time()}-{secret}"
        return hashlib.sha256(token_base.encode()).hexdigest()
    
    def verify_security_token(self, token):
        """Verify that the provided token matches this game's token"""
        self.ensure_one()
        return self.security_token and self.security_token == token
    
    def play_game(self, security_token=None):
        """
        Play the game and determine the reward
        
        Args:
            security_token: Security token to verify
            
        Returns:
            dict: Game result information
        """
        self.ensure_one()
        
        # Security checks
        if self.state != 'available':
            raise UserError(_("This game has already been played or has expired"))
        
        if security_token and not self.verify_security_token(security_token):
            _logger.warning("Invalid security token for game %s", self.id)
            raise UserError(_("Invalid security token"))
        
        # Get reward probabilities
        reward_probs = self.game_instance_id.get_reward_probabilities()
        if not reward_probs:
            raise UserError(_("No active rewards configured for this game"))
        
        # Select a reward based on probabilities
        reward = self._select_reward_by_probability(reward_probs)
        if not reward:
            raise UserError(_("Failed to select a reward"))
        
        # Generate the reward for the user
        game_reward = self.env['ai_game_ecommerce.game.reward'].browse(reward['id'])
        user_reward = game_reward.generate_reward(self.user_id.id)
        
        if not user_reward:
            raise UserError(_("Failed to generate reward"))
        
        # Update game state
        result_data = {
            'reward_id': reward['id'],
            'reward_name': reward['name'],
            'reward_type': reward['reward_type'],
            'value': reward['value'],
            'timestamp': fields.Datetime.now().isoformat(),
        }
        
        self.write({
            'state': 'used',
            'used_date': fields.Datetime.now(),
            'user_reward_id': user_reward.id,
            'result_data': json.dumps(result_data),
        })
        
        return result_data
    
    def _select_reward_by_probability(self, reward_probs):
        """
        Select a reward based on the configured probabilities
        
        Args:
            reward_probs: List of dicts with reward information including probabilities
            
        Returns:
            dict: Selected reward information
        """
        if not reward_probs:
            return None
        
        # Calculate cumulative probabilities
        cumulative_prob = 0
        cumulative_probs = []
        
        for reward in reward_probs:
            cumulative_prob += reward['probability']
            cumulative_probs.append((cumulative_prob, reward))
        
        # Generate a random number between 0 and the total probability
        rand_val = random.uniform(0, cumulative_prob)
        
        # Find the reward that corresponds to the random value
        for cum_prob, reward in cumulative_probs:
            if rand_val <= cum_prob:
                return reward
        
        # Fallback to the last reward if something goes wrong
        return reward_probs[-1]
    
    def expire_games(self):
        """Expire games that have passed their expiry date"""
        expired_games = self.search([
            ('state', '=', 'available'),
            ('expiry_date', '<', fields.Datetime.now()),
        ])
        
        if expired_games:
            expired_games.write({'state': 'expired'})
            _logger.info("Expired %s games", len(expired_games))
        
        return True
