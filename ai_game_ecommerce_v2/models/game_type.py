from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class GameType(models.Model):
    _name = 'ai_game_ecommerce.game.type'
    _description = 'Game Type'
    _order = 'name'
    
    name = fields.Char(string='Name', required=True, translate=True)
    code = fields.Char(string='Code', required=True, help="Technical code for this game type")
    description = fields.Text(string='Description', translate=True)
    active = fields.Boolean(string='Active', default=True)
    
    game_instance_ids = fields.One2many('ai_game_ecommerce.game.instance', 'game_type_id', string='Game Instances')
    game_instance_count = fields.Integer(compute='_compute_game_instance_count', string='Game Instance Count')
    
    _sql_constraints = [
        ('code_uniq', 'unique (code)', 'The code must be unique!'),
    ]
    
    @api.constrains('code')
    def _check_code(self):
        for record in self:
            if not record.code or not record.code.isalnum():
                raise ValidationError(_("Code must be alphanumeric and not empty"))
    
    @api.depends('game_instance_ids')
    def _compute_game_instance_count(self):
        for record in self:
            record.game_instance_count = len(record.game_instance_ids)
