from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging
import json

_logger = logging.getLogger(__name__)

class GameInstance(models.Model):
    _name = 'ai_game_ecommerce.game.instance'
    _description = 'Game Instance'
    _order = 'name'
    
    name = fields.Char(string='Name', required=True, translate=True)
    game_type_id = fields.Many2one('ai_game_ecommerce.game.type', string='Game Type', required=True, ondelete='cascade')
    active = fields.Boolean(string='Active', default=True)
    description = fields.Text(string='Description', translate=True)
    terms_and_conditions = fields.Html(string='Terms and Conditions', translate=True)
    
    # Display settings
    display_type = fields.Selection([
        ('popup', 'Popup'),
        ('fullscreen', 'Full Screen'),
    ], string='Display Type', default='popup', required=True)
    
    image = fields.Binary(string='Image', attachment=True)
    
    # Game configuration
    game_reward_ids = fields.One2many('ai_game_ecommerce.game.reward', 'game_instance_id', string='Rewards')
    game_rule_ids = fields.One2many('ai_game_ecommerce.game.rule', 'game_instance_id', string='Rules')
    
    # Statistics
    user_game_ids = fields.One2many('ai_game_ecommerce.user.game', 'game_instance_id', string='User Games')
    user_game_count = fields.Integer(compute='_compute_user_game_count', string='User Game Count')
    
    # For scratch card
    scratch_background_color = fields.Char(string='Scratch Background Color', default='#CCCCCC')
    scratch_foreground_color = fields.Char(string='Scratch Foreground Color', default='#FFFFFF')
    scratch_reveal_percentage = fields.Integer(string='Scratch Reveal Percentage', default=50,
                                              help="Percentage of the card that needs to be scratched to reveal the reward")
    
    # For spin wheel
    wheel_segments = fields.Integer(string='Wheel Segments', default=8)
    wheel_colors = fields.Text(string='Wheel Colors', default='["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF", "#FFFFFF", "#000000"]',
                              help="JSON array of colors for wheel segments")
    
    @api.depends('user_game_ids')
    def _compute_user_game_count(self):
        for record in self:
            record.user_game_count = len(record.user_game_ids)
    
    @api.constrains('wheel_colors', 'wheel_segments')
    def _check_wheel_colors(self):
        for record in self:
            if record.game_type_id.code == 'spin_wheel':
                try:
                    colors = json.loads(record.wheel_colors)
                    if not isinstance(colors, list):
                        raise ValidationError(_("Wheel colors must be a JSON array of color strings"))
                    if len(colors) < record.wheel_segments:
                        raise ValidationError(_("Number of colors must be at least equal to the number of segments"))
                except json.JSONDecodeError:
                    raise ValidationError(_("Wheel colors must be a valid JSON array"))
    
    def get_reward_probabilities(self):
        """Get the reward probabilities for this game instance"""
        self.ensure_one()
        rewards = self.game_reward_ids.filtered(lambda r: r.active)
        total_probability = sum(rewards.mapped('probability'))
        
        if total_probability <= 0:
            _logger.warning("Total probability for game instance %s is zero or negative", self.name)
            return []
        
        # Normalize probabilities to ensure they sum to 100
        normalized_rewards = []
        for reward in rewards:
            normalized_rewards.append({
                'id': reward.id,
                'name': reward.name,
                'probability': (reward.probability / total_probability) * 100,
                'reward_type': reward.reward_type,
                'value': reward.value,
            })
        
        return normalized_rewards
