from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class UserReward(models.Model):
    _name = 'ai_game_ecommerce.user.reward'
    _description = 'User Reward'
    _order = 'create_date desc'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    user_id = fields.Many2one('res.users', string='User', required=True, ondelete='cascade', index=True)
    game_reward_id = fields.Many2one('ai_game_ecommerce.game.reward', string='Game Reward', required=True, ondelete='cascade')

    state = fields.Selection([
        ('available', 'Available'),
        ('claimed', 'Claimed'),
        ('expired', 'Expired'),
    ], string='State', default='available', required=True, index=True)

    create_date = fields.Datetime(string='Created On', readonly=True)
    claim_date = fields.Datetime(string='Claimed On', readonly=True)
    expiry_date = fields.Datetime(string='Expires On', compute='_compute_expiry_date', store=True)

    # Reward details
    reward_type = fields.Selection(related='game_reward_id.reward_type', string='Reward Type', readonly=True)
    value = fields.Float(related='game_reward_id.value', string='Value', readonly=True)
    reference = fields.Char(string='Reference', readonly=True, help="Reference code for this reward")

    @api.depends('user_id', 'game_reward_id', 'create_date')
    def _compute_name(self):
        for record in self:
            if record.create_date:
                date_str = fields.Datetime.to_string(record.create_date)
            else:
                date_str = 'New'
            record.name = f"{record.game_reward_id.name} - {record.user_id.name} - {date_str}"

    @api.depends('create_date', 'game_reward_id')
    def _compute_expiry_date(self):
        for record in self:
            if record.create_date:
                # Default expiry is 30 days
                expiry_days = 30
                record.expiry_date = record.create_date + fields.Datetime.to_timedelta(days=expiry_days)
            else:
                record.expiry_date = False

    def claim_reward(self):
        """
        Claim the reward

        Returns:
            dict: Information about the claimed reward
        """
        self.ensure_one()

        if self.state != 'available':
            raise UserError(_("This reward has already been claimed or has expired"))

        # Process based on reward type
        result = {}

        if self.reward_type == 'coupon':
            # Check if loyalty module is installed
            loyalty_installed = self.env['ir.module.module'].sudo().search([
                ('name', '=', 'loyalty'),
                ('state', '=', 'installed')
            ], limit=1)

            if loyalty_installed:
                # Get the program based on the game reward's program_id
                program = self.env['loyalty.program'].sudo().search([
                    ('id', '=', self.game_reward_id.coupon_program_id)
                ], limit=1)

                if program:
                    # Create a new coupon based on the program type
                    if program.program_type == 'coupons':
                        # Create a new coupon
                        coupon = self.env['loyalty.card'].sudo().create({
                            'program_id': program.id,
                            'partner_id': self.user_id.partner_id.id,
                            'points': 1,  # For coupon programs, points represent number of coupons
                        })
                        result = {
                            'type': 'coupon',
                            'code': coupon.code,
                            'program_name': program.name,
                            'description': program.description,
                            'reward_ids': program.reward_ids.mapped('description'),
                        }
                    elif program.program_type == 'promotion':
                        # For promotion programs, create a new coupon with points
                        coupon = self.env['loyalty.card'].sudo().create({
                            'program_id': program.id,
                            'partner_id': self.user_id.partner_id.id,
                            'points': self.value,  # For promotions, points represent the value
                        })
                        result = {
                            'type': 'promotion',
                            'code': coupon.code,
                            'program_name': program.name,
                            'value': self.value,
                            'reward_ids': program.reward_ids.mapped('description'),
                        }
                    elif program.program_type == 'gift_card':
                        # For gift cards, create a new card with the specified value
                        coupon = self.env['loyalty.card'].sudo().create({
                            'program_id': program.id,
                            'partner_id': self.user_id.partner_id.id,
                            'points': self.value,  # For gift cards, points represent the value
                        })
                        result = {
                            'type': 'gift_card',
                            'code': coupon.code,
                            'program_name': program.name,
                            'value': self.value,
                            'description': program.description,
                        }
                    else:
                        result = {
                            'type': 'coupon',
                            'program_name': program.name,
                            'note': _('Unsupported program type: %s' % program.program_type)
                        }
                else:
                    result = {
                        'type': 'coupon',
                        'program_name': _('Program not found'),
                        'note': _('The specified program ID does not exist')
                    }
            else:
                result = {
                    'type': 'coupon',
                    'program_name': _('Loyalty module not installed'),
                    'note': _('Loyalty module is not installed. Cannot create coupon.')
                }

        elif self.reward_type == 'ewallet':
            # Check if loyalty module is installed
            loyalty_installed = self.env['ir.module.module'].sudo().search([
                ('name', '=', 'loyalty'),
                ('state', '=', 'installed')
            ], limit=1)

            if loyalty_installed:
                # Get or create an eWallet program
                ewallet_program = self.env['loyalty.program'].sudo().search([
                    ('program_type', '=', 'ewallet')
                ], limit=1)

                if not ewallet_program:
                    ewallet_program = self.env['loyalty.program'].sudo().create({
                        'name': 'eWallet Program',
                        'program_type': 'ewallet',
                        'active': True
                    })

                # Get or create an eWallet card for the user
                ewallet_card = self.env['loyalty.card'].sudo().search([
                    ('program_id', '=', ewallet_program.id),
                    ('partner_id', '=', self.user_id.partner_id.id)
                ], limit=1)

                if not ewallet_card:
                    ewallet_card = self.env['loyalty.card'].sudo().create({
                        'program_id': ewallet_program.id,
                        'partner_id': self.user_id.partner_id.id
                    })

                # Add eWallet credits
                ewallet_card.sudo().write({
                    'points': ewallet_card.points + self.value
                })

                result = {
                    'type': 'ewallet',
                    'value': self.value,
                    'current_balance': ewallet_card.points,
                    'program_name': ewallet_program.name
                }
            else:
                result = {
                    'type': 'ewallet',
                    'value': self.value,
                    'note': _('Loyalty module is not installed. Credits cannot be added.')
                }

        elif self.reward_type == 'external':
            result = {
                'type': 'external',
                'code': self.game_reward_id.external_code,
                'description': self.game_reward_id.external_description,
            }

        elif self.reward_type == 'no_reward':
            result = {
                'type': 'no_reward',
                'message': _("Better luck next time!"),
            }

        # Update reward state
        self.write({
            'state': 'claimed',
            'claim_date': fields.Datetime.now(),
        })

        return result

    def expire_rewards(self):
        """Expire rewards that have passed their expiry date"""
        expired_rewards = self.search([
            ('state', '=', 'available'),
            ('expiry_date', '<', fields.Datetime.now()),
        ])

        if expired_rewards:
            expired_rewards.write({'state': 'expired'})
            _logger.info("Expired %s rewards", len(expired_rewards))

        return True
