from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)

class GameReward(models.Model):
    _name = 'ai_game_ecommerce.game.reward'
    _description = 'Game Reward'
    _order = 'probability desc, name'

    name = fields.Char(string='Name', required=True, translate=True)
    game_instance_id = fields.Many2one('ai_game_ecommerce.game.instance', string='Game Instance', required=True, ondelete='cascade')
    active = fields.Boolean(string='Active', default=True)

    reward_type = fields.Selection([
        ('coupon', 'Coupon'),
        ('ewallet', 'eWallet Credit'),
        ('external', 'External Coupon'),
        ('no_reward', 'No Reward'),
    ], string='Reward Type', default='coupon', required=True)

    # For coupon rewards
    coupon_program_id = fields.Integer(string='Coupon Program ID',
                                      help="ID of the coupon program (only available if coupon module is installed)")
    coupon_program_name = fields.Char(string='Coupon Program',
                                     help="Name of the coupon program (only available if coupon module is installed)")

    @api.model
    def _is_coupon_installed(self):
        """Check if the coupon module is installed"""
        return self.env['ir.module.module'].sudo().search([
            ('name', '=', 'coupon'),
            ('state', '=', 'installed')
        ], limit=1)

    # For eWallet rewards
    value = fields.Float(string='Value', help="Value of the reward (amount for eWallet, etc.)")

    # For external coupon rewards
    external_code = fields.Char(string='External Code', help="Code for external coupon systems")
    external_description = fields.Text(string='External Description', translate=True)

    # Probability settings
    probability = fields.Float(string='Probability', default=10.0,
                              help="Relative probability of this reward being selected. Higher values mean higher chance.")

    # Statistics
    user_reward_ids = fields.One2many('ai_game_ecommerce.user.reward', 'game_reward_id', string='User Rewards')
    user_reward_count = fields.Integer(compute='_compute_user_reward_count', string='User Reward Count')

    @api.depends('user_reward_ids')
    def _compute_user_reward_count(self):
        for record in self:
            record.user_reward_count = len(record.user_reward_ids)

    @api.constrains('reward_type', 'value', 'external_code')
    def _check_reward_configuration(self):
        for record in self:
            if record.reward_type == 'ewallet' and record.value <= 0:
                raise ValidationError(_("Value must be positive for eWallet rewards"))
            elif record.reward_type == 'external' and not record.external_code:
                raise ValidationError(_("External Code is required for external coupon rewards"))

    def generate_reward(self, user_id):
        """Generate a reward for the given user"""
        self.ensure_one()

        if not user_id:
            _logger.error("Cannot generate reward: No user provided")
            return False

        user = self.env['res.users'].browse(user_id)
        if not user.exists():
            _logger.error("Cannot generate reward: User %s does not exist", user_id)
            return False

        # Create user reward record
        user_reward = self.env['ai_game_ecommerce.user.reward'].create({
            'user_id': user_id,
            'game_reward_id': self.id,
            'state': 'available',
        })

        # Process based on reward type
        if self.reward_type == 'coupon':
            self._generate_coupon_reward(user, user_reward)
        elif self.reward_type == 'ewallet':
            self._generate_ewallet_reward(user, user_reward)

        return user_reward

    def _generate_coupon_reward(self, user, user_reward):
        """Generate a coupon reward"""
        try:
            # Check if coupon module is installed
            if self._is_coupon_installed() and self.coupon_program_id:
                try:
                    # Try to use the coupon module
                    coupon_program = self.env['coupon.program'].browse(self.coupon_program_id)
                    if coupon_program.exists():
                        # Generate a coupon
                        coupon = self.env['coupon.coupon'].create({
                            'program_id': coupon_program.id,
                            'partner_id': user.partner_id.id,
                            'state': 'new',
                        })

                        # Link the coupon to the user reward
                        user_reward.write({
                            'reference': coupon.code,
                        })

                        _logger.info("Generated coupon %s for user %s", coupon.code, user.name)
                        return True
                except Exception as e:
                    _logger.warning("Failed to use coupon module: %s. Falling back to simple coupon code.", str(e))

            # Fallback to a simple coupon code
            coupon_code = f"COUPON-{user_reward.id}-{user.id}"

            # Link the coupon to the user reward
            user_reward.write({
                'reference': coupon_code,
            })

            _logger.info("Generated simple coupon code %s for user %s", coupon_code, user.name)
            return True
        except Exception as e:
            _logger.error("Failed to generate coupon reward: %s", str(e))
            return False

    def _generate_ewallet_reward(self, user, user_reward):
        """Generate an eWallet reward"""
        try:
            # TODO: Implement eWallet credit logic
            # This would depend on the specific eWallet implementation

            user_reward.write({
                'reference': f"WALLET-{user_reward.id}",
            })

            _logger.info("Generated eWallet credit of %s for user %s", self.value, user.name)
            return True
        except Exception as e:
            _logger.error("Failed to generate eWallet reward: %s", str(e))
            return False
