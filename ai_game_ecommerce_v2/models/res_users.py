from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class ResUsers(models.Model):
    _inherit = 'res.users'
    
    user_game_ids = fields.One2many('ai_game_ecommerce.user.game', 'user_id', string='Games')
    available_game_count = fields.Integer(compute='_compute_game_counts', string='Available Games')
    used_game_count = fields.Integer(compute='_compute_game_counts', string='Used Games')
    
    user_reward_ids = fields.One2many('ai_game_ecommerce.user.reward', 'user_id', string='Rewards')
    available_reward_count = fields.Integer(compute='_compute_reward_counts', string='Available Rewards')
    claimed_reward_count = fields.Integer(compute='_compute_reward_counts', string='Claimed Rewards')
    
    @api.depends('user_game_ids', 'user_game_ids.state')
    def _compute_game_counts(self):
        for user in self:
            user.available_game_count = len(user.user_game_ids.filtered(lambda g: g.state == 'available'))
            user.used_game_count = len(user.user_game_ids.filtered(lambda g: g.state == 'used'))
    
    @api.depends('user_reward_ids', 'user_reward_ids.state')
    def _compute_reward_counts(self):
        for user in self:
            user.available_reward_count = len(user.user_reward_ids.filtered(lambda r: r.state == 'available'))
            user.claimed_reward_count = len(user.user_reward_ids.filtered(lambda r: r.state == 'claimed'))
    
    def get_available_games(self):
        """Get available games for the user"""
        self.ensure_one()
        return self.user_game_ids.filtered(lambda g: g.state == 'available')
    
    def get_available_rewards(self):
        """Get available rewards for the user"""
        self.ensure_one()
        return self.user_reward_ids.filtered(lambda r: r.state == 'available')
    
    def process_game_event(self, event_type, event_data=None):
        """
        Process a game event for this user
        
        Args:
            event_type: Type of event (signup, login, purchase, etc.)
            event_data: Dict with event-specific data
            
        Returns:
            list: User games awarded
        """
        self.ensure_one()
        
        # Find applicable rules
        rules = self.env['ai_game_ecommerce.game.rule'].search([
            ('active', '=', True),
            ('event_type', '=', event_type),
        ])
        
        if not rules:
            return []
        
        # Apply rules and award games
        awarded_games = []
        for rule in rules:
            if rule.check_rule_eligibility(self, event_data):
                games = rule.award_games(self, event_data)
                awarded_games.extend(games)
        
        return awarded_games
