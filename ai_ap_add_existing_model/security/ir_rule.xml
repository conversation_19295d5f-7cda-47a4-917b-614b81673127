<odoo>
    <data>
       <!-- Rule for product.product visibility for warehouse users -->
        <record id="rule_warehouse_product_product_access" model="ir.rule">
            <field name="name">Warehouse Product Product Access</field>
            <field name="model_id" ref="product.model_product_product"/>
            <field name="domain_force">[('warehouse_ids', 'in', [user.property_warehouse_id.id])] </field>
        </record>
        <!-- Rule for product.product visibility for admins -->
        <record id="rule_admin_product_product_access" model="ir.rule">
            <field name="name">Admin Product Product Access</field>
            <field name="model_id" ref="product.model_product_product"/>
            <field name="domain_force">[]</field>
        </record>
        <!-- Rule for product.template visibility for warehouse users -->
        <record id="rule_warehouse_product_template_access" model="ir.rule">
            <field name="name">Warehouse Product Template Access</field>
            <field name="model_id" ref="product.model_product_template"/>
            <field name="domain_force">['product_variant_ids', 'in', [user.property_warehouse_id.product_ids.ids]]</field>
        </record>
        <!-- Rule for product.template visibility for admins -->
        <record id="rule_admin_product_template_access" model="ir.rule">
            <field name="name">Admin Product Template Access</field>
            <field name="model_id" ref="product.model_product_template"/>
            <field name="domain_force">[]</field>
        </record>
    </data>
</odoo>
