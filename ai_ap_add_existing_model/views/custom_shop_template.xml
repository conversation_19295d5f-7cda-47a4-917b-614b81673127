<?xml version="1.0"?>
<odoo>
    <template id="custom_shop_page" inherit_id="website_sale.products">
        <xpath expr="//div[@id='wrap']" position="replace">
            <div id="wrap" class="js_sale">
                <style>
                    #top_menu_container{
                        max-width:100%;
                    }
                </style>
                <div class="oe_structure oe_empty oe_structure_not_nearest" id="oe_structure_website_sale_products_1"/>
                <div class="container oe_website_sale pt-2" style="max-width:100%;">
                    <div class="row o_wsale_products_main_row">
                        <div t-if="enable_left_column" id="products_grid_before" class="col-lg-3 pb-2">
                            <div class="products_categories"/>
                            <div class="products_attributes_filters"/>
                        </div>
                        <div id="products_grid" t-attf-class="col #{'o_wsale_layout_list' if layout_mode == 'list' else ''}">
                            <t t-call="website_sale.products_breadcrumb">
                                <t t-set="_classes" t-valuef="w-100"/>
                            </t>
                            <div class="products_header form-inline flex-md-nowrap justify-content-end mb-4">
                                <t t-call="website_sale.search">
                                    <t t-set="_classes" t-valuef="w-100 w-md-auto mr-auto mb-2"/>
                                    <t t-set="search" t-value="original_search or search"/>
                                </t>
                                <t t-call="website_sale.pricelist_list">
                                    <t t-set="_classes" t-valuef="ml-3 mb-2"/>
                                </t>
                            </div>
                            <div t-if="original_search and bins" class="alert alert-warning mt8">
                                No results found for '<span t-esc="original_search"/>'. Showing results for '<span t-esc="search"/>'.
                            </div>
                            <t t-if="category">
                                <t t-set="editor_msg">Drag building blocks here to customize the header for "<t t-esc="category.name"/>" category.</t>
                                <div class="mb16" id="category_header" t-att-data-editor-message="editor_msg" t-field="category.website_description"/>
                            </t>
                            <div t-if="bins" class="o_wsale_products_grid_table_wrapper">
                                <table class="table table-borderless m-0" t-att-data-ppg="ppg" t-att-data-ppr="ppr">
                                    <colgroup t-ignore="true">
                                        <!-- Force the number of columns (useful when only one row of (x < ppr) products) -->
                                        <col t-foreach="ppr" t-as="p"/>
                                    </colgroup>
                                    <tbody>
                                        <tr t-foreach="bins" t-as="tr_product">
                                            <t t-foreach="tr_product" t-as="td_product">
                                                <t t-if="td_product">
                                                    <t t-set="product" t-value="td_product['product']"/>
                                                    <!-- We use t-attf-class here to allow easier customization -->
                                                    <td t-att-colspan="td_product['x'] != 1 and td_product['x']" t-att-rowspan="td_product['y'] != 1 and td_product['y']" t-attf-class="oe_product" t-att-data-ribbon-id="td_product['ribbon'].id">
                                                        <div t-attf-class="o_wsale_product_grid_wrapper o_wsale_product_grid_wrapper_#{td_product['x']}_#{td_product['y']}">
                                                            <t t-call="website_sale.products_item">
                                                                <t t-set="product_image_big" t-value="td_product['x'] + td_product['y'] &gt; 2"/>
                                                            </t>
                                                        </div>
                                                    </td>
                                                </t>
                                                <td t-else=""/>
                                            </t>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <t t-else="">
                                <div class="text-center text-muted mt128 mb256">
                                    <t t-if="not search">
                                        <h3 class="mt8">No product defined</h3>
                                        <p t-if="category">No product defined in category "<strong t-esc="category.display_name"/>".</p>
                                    </t>
                                    <t t-else="">
                                        <h3 class="mt8">No results</h3>
                                        <p>No results for "<strong t-esc="search"/>"<t t-if="category"> in category "<strong t-esc="category.display_name"/>"</t>.</p>
                                    </t>
                                    <p t-ignore="true" groups="sales_team.group_sale_manager">Click <i>'New'</i> in the top-right corner to create your first product.</p>
                                </div>
                            </t>
                            <div class="products_pager form-inline justify-content-center py-3">
                                <t t-call="website.pager"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="oe_structure oe_empty oe_structure_not_nearest" id="oe_structure_website_sale_products_2"/>
            </div>
        </xpath>
    </template>
</odoo>
