<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_product_template_form_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='product_tooltip']" position="replace">
                <field name="product_tooltip" string="" class="font-italic text-muted"/>
            </xpath>

            <xpath expr="//button[@name='action_open_label_layout']" position="replace">
                <button string="Print Labels" type="object" name="action_open_label_layout"/>
            </xpath>


            <xpath expr="//div[@name='options']" position="before">
                <style>
                    div[name="options"] .o_field_boolean {
                    margin-left: 10px;
                    margin-right: 0px;
                    }
                    div[name="Product Type"] .o_field_boolean {
                    margin-left: 10px;
                    margin-right: 0px;
                    }
                </style>
            </xpath>

            <xpath expr="//div[@name='options']" position="after">
                <span>
                    <h2>Internal Product Type</h2>
                </span>

                <div name="Product Type">
                    <span class="d-inline-block" invisible="x_asset_rentable or x_mg_rentable or x_non_rentable">
                        <field name="x_gift"/>
                        <label for="x_gift"/>
                    </span>
                    <span class="d-inline-block" invisible="x_gift or x_mg_rentable or x_non_rentable">
                        <field name="x_asset_rentable"/>
                        <label for="x_asset_rentable"/>
                    </span>
                    <span class="d-inline-block" invisible="x_asset_rentable or x_mg_rentable or x_gift">
                        <field name="x_non_rentable"/>
                        <label for="x_non_rentable"/>
                    </span>
                    <span class="d-inline-block" invisible="x_asset_rentable or x_gift or x_non_rentable">
                        <field name="x_mg_rentable"/>
                        <label for="x_mg_rentable"/>
                    </span>
                </div>
            </xpath>


            <xpath expr="//page[@name='general_information']" position="after">
                <page string="Pricing">
                    <group name="Pricing">
                        <field name="x_sub_type" invisible="not x_mg_rentable"/>
                    </group>
                    <group name="Rent Calculation">
                        <group>
                            <!--<field name="list_price" widget="monetary" readonly="1"/>-->
                            <field name="x_inventory_cost"/>
                            <field name="x_making_charges"/>
                        </group>
                        <group name="Silver group" invisible="x_sub_type != 'silver'">
                            <field name="weight"/>
                            <field name="x_weight_type"/>
                            <label for="x_silver_rate"/>
                            <div>
                                <field name="x_silver_rate" class="oe_inline"/>
                                <span>per Gram</span>
                            </div>
                        </group>
                    </group>
                </page>
            </xpath>


        </field>
    </record>
</odoo>
