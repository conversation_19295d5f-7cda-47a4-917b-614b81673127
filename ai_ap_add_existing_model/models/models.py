from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import datetime

class ResPartner(models.Model):
    _inherit = 'res.partner'

    x_rent_start_date = fields.Datetime(string='Rent Start Date', help="The date when the rental period starts.")
    x_rent_end_date = fields.Datetime(string='Rent End Date', help="The date when the rental period ends.")
    x_rental_start_date = fields.Datetime(string='Rental Start Date', help="The date when the rental contract starts.")
    x_rental_end_date = fields.Datetime(string='Rental End Date', help="The date when the rental contract ends.")
    x_duration = fields.Integer(string='Duration', help="The duration of the rental period in days.")
    x_walk_in_user = fields.Many2one('x_walk_in_user', string='Walk-in User', help="The walk-in user associated with this partner.")

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    x_weight_type = fields.Selection([('kg', 'Kilogram'), ('g', 'Gram'), ('mg', 'Milligram')], string='Weight Type', help="Select the type of weight: kg, g, or mg.")
    x_weight = fields.Float(string='Weight', help="Enter the weight of the product.")
    x_walk_in_user = fields.Many2one('x_walk_in_user', string='Walk-in User', help="The walk-in user associated with this product.")
    x_sub_type = fields.Selection([('silver', 'Silver'), ('non_silver', 'Non-Silver')], string='Sub Type', help="Specify the sub-type of the product.")
    x_silver_rate = fields.Float(string='Silver Rate', help="Enter the current rate of silver per unit weight.")
    x_procduct_code = fields.Char(string='Product Code', help="Unique code identifying the product.")
    x_non_rentable = fields.Boolean(string='Non Rentable', help="Check if this product cannot be rented.")
    x_mg_rentable = fields.Boolean(string='Mg Rentable', help="Check if this product can be rented in milligrams.")
    x_making_charges = fields.Monetary(string='Making Charges', help="Cost associated with the making of the product.")
    x_inventory_cost = fields.Monetary(string='Inventory Cost', help="Cost for maintaining inventory of this product.")
    x_gift = fields.Boolean(string='Gift', help="Check if this product is a gift item.")
    x_donation_percentage = fields.Float(string='Donation Percentage', help="Percentage of the sale amount to be donated.")
    x_asset_rentable = fields.Boolean(string='Asset Rentable', help="Check if this product is rentable as an asset.")

    # @api.depends('x_weight_type', 'x_weight', 'x_silver_rate', 'x_inventory_cost', 'x_making_charges')
    # def _compute_list_price(self):
    #     for record in self:
    #         if record.x_weight_type == 'kg':
    #             record.list_price = (record.x_silver_rate * 1000) * record.x_weight + record.x_inventory_cost + record.x_making_charges
    #         elif record.x_weight_type == 'g':
    #             record.list_price = record.x_silver_rate * record.x_weight + record.x_inventory_cost + record.x_making_charges
    #         elif record.x_weight_type == 'mg':
    #             record.list_price = (record.x_silver_rate / 1000) * record.x_weight + record.x_inventory_cost + record.x_making_charges


class ResUsers(models.Model):
    _inherit = 'res.users'

    x_rent_start_date = fields.Datetime(string='Rent Start Date', related='partner_id.x_rent_start_date', help="The date when the user's rental period starts.")
    x_rent_end_date = fields.Datetime(string='Rent End Date', related='partner_id.x_rent_end_date', help="The date when the user's rental period ends.")
    x_rental_start_date = fields.Datetime(string='Rental Start Date', related='partner_id.x_rental_start_date', help="The date when the user's rental contract starts.")
    x_rental_end_date = fields.Datetime(string='Rental End Date', related='partner_id.x_rental_end_date', help="The date when the user's rental contract ends.")
    x_duration = fields.Integer(string='Duration', related='partner_id.x_duration', help="The duration of the rental period in days.")
    property_warehouse_id = fields.Many2one('stock.warehouse', string='Warehouse', help="Select the warehouse associated with this user.")
    x_walk_in_user = fields.Many2one('x_walk_in_user', string='Walk-in User', help="The walk-in user associated with this user.")

class ProductProduct(models.Model):
    _inherit = 'product.product'

    x_weight_type = fields.Selection([('kg', 'Kilogram'), ('g', 'Gram'), ('mg', 'Milligram')], string='Weight Type', help="Select the type of weight: kg, g, or mg.")
    x_weight = fields.Float(string='Weight', related='product_tmpl_id.x_weight', help="Weight of the product.")
    x_walk_in_user = fields.Many2one('x_walk_in_user', string='Walk-in User', related='product_tmpl_id.x_walk_in_user', help="The walk-in user associated with this product.")
    x_sub_type = fields.Selection([('silver', 'Silver'), ('non_silver', 'Non-Silver')], string='Sub Type', help="Specify the sub-type of the product.")
    x_silver_rate = fields.Float(string='Silver Rate', related='product_tmpl_id.x_silver_rate', help="Current rate of silver per unit weight.")
    x_procduct_code = fields.Char(string='Product Code', related='product_tmpl_id.x_procduct_code', help="Unique code identifying the product.")
    x_non_rentable = fields.Boolean(string='Non Rentable', related='product_tmpl_id.x_non_rentable', help="Indicates if this product cannot be rented.")
    x_mg_rentable = fields.Boolean(string='Mg Rentable', related='product_tmpl_id.x_mg_rentable', help="Indicates if this product can be rented in milligrams.")
    x_making_charges = fields.Monetary(string='Making Charges', related='product_tmpl_id.x_making_charges', help="Cost associated with the making of the product.")
    x_inventory_cost = fields.Monetary(string='Inventory Cost', related='product_tmpl_id.x_inventory_cost', help="Cost for maintaining inventory of this product.")
    x_gift = fields.Boolean(string='Gift', related='product_tmpl_id.x_gift', help="Indicates if this product is a gift item.")
    x_donation_percentage = fields.Float(string='Donation Percentage', related='product_tmpl_id.x_donation_percentage', help="Percentage of the sale amount to be donated.")
    x_asset_rentable = fields.Boolean(string='Asset Rentable', related='product_tmpl_id.x_asset_rentable', help="Indicates if this product is rentable as an asset.")

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    x_return_order = fields.Boolean(string='Is Return Order', help="Check if this sale order is a return order.")
    x_rent_start_date = fields.Datetime(string='Rent Start Date', help="The date when the rental period starts for this order.")
    x_rent_end_date = fields.Datetime(string='Rent End Date', help="The date when the rental period ends for this order.")
    x_penalty_amount = fields.Float(string='Penalty Amount', help="Total penalty amount for this order, if applicable.")
    x_penalty = fields.Float(string='Penalty', help="Individual penalty amount associated with this order.")
    x_loss_value = fields.Float(string='Loss Value', help="Value of any loss associated with this order.")
    x_duration = fields.Integer(string='Duration', help="Duration of the rental period in days for this order.")
    x_deposit_amount = fields.Float(string='Deposit Amount', help="Deposit amount required for this order.")
    x_customer_confirm = fields.Boolean(string='Customer Confirm?', help="Indicates if the customer has confirmed this order.")
    