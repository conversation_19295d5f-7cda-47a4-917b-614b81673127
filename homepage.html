<!DOCTYPE html>
<html lang="en-US" data-website-id="1" data-main-object="ir.ui.view(2918,)" data-add2cart-redirect="1">
    <head>
        <meta charset="utf-8"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta name="generator" content="Arihant ERP"/>
            
        <meta property="og:type" content="website"/>
        <meta property="og:title" content="Homepage | oneclickgold.com"/>
        <meta property="og:site_name" content="oneclickgold.com"/>
        <meta property="og:url" content="https://oneclickgold.com/"/>
        <meta property="og:image" content="https://oneclickgold.com/web/image/website/1/logo?unique=e0c5ce3"/>
            
        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="twitter:title" content="Homepage | oneclickgold.com"/>
        <meta name="twitter:image" content="https://oneclickgold.com/web/image/website/1/logo/300x300?unique=e0c5ce3"/>
        
        
        <link rel="canonical" href="https://oneclickgold.com/"/>
        
        <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin=""/>
        <title> Homepage | oneclickgold.com </title>
        <link type="image/x-icon" rel="shortcut icon" href="/web/image/website/1/favicon?unique=e0c5ce3"/>
        <link rel="preload" href="/web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0" as="font" crossorigin=""/>
        <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.min.css"/>
        <link type="text/css" rel="stylesheet" href="/web/assets/1/433a8a0/web.assets_frontend.min.css"/>
        <script id="web.layout.odooscript" type="text/javascript">
            var odoo = {
                csrf_token: "c06a4ac6936ecceb6076c3c556349c6f82d79771o1779001071",
                debug: "",
            };
        </script>
        <script type="text/javascript">
            odoo.__session_info__ = {"is_admin": false, "is_system": false, "is_website_user": true, "user_id": false, "is_frontend": true, "profile_session": null, "profile_collectors": null, "profile_params": null, "show_effect": true, "currencies": {"20": {"symbol": "\u20b9", "position": "before", "digits": [69, 2]}, "1": {"symbol": "$", "position": "before", "digits": [69, 2]}}, "bundle_params": {"lang": "en_US", "website_id": 1}, "translationURL": "/website/translations", "cache_hashes": {"translations": "a6ed318370916e6b57afe5383c6fd4cd5f348856"}, "recaptcha_public_key": "6LcYvwwrAAAAAAOFYThgXTWnMjUjQZDr5oiG2fWW", "geoip_country_code": null, "geoip_phone_code": null, "lang_url_code": "en"};
            if (!/(^|;\s)tz=/.test(document.cookie)) {
                const userTZ = Intl.DateTimeFormat().resolvedOptions().timeZone;
                document.cookie = `tz=${userTZ}; path=/`;
            }
        </script>
        <script type="text/javascript" defer="defer" src="/web/assets/1/a9d1165/web.assets_frontend_minimal.min.js" onerror="__odooAssetError=1"></script>
        <script type="text/javascript" defer="defer" data-src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>
        <script type="text/javascript" defer="defer" data-src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js"></script>
        <script type="text/javascript" defer="defer" data-src="/web/assets/1/13bfe5d/web.assets_frontend_lazy.min.js" onerror="__odooAssetError=1"></script>
        
            <script type="text/javascript">
                // Simple helper to ensure cart operations don't break the page
                document.addEventListener('DOMContentLoaded', function() {
                    // Add a global error handler for cart operations
                    window.addEventListener('error', function(event) {
                        // Check if the error is related to cart operations or templates
                        if (event.message && (
                            event.message.includes('cart') ||
                            event.message.includes('notification') ||
                            event.message.includes('undefined') ||
                            event.message.includes('template') ||
                            event.message.includes('Missing') ||
                            event.message.includes('Dependencies')
                        )) {
                            console.log('Caught error in cart operation:', event.message);
                            // Prevent the error from breaking the page
                            event.preventDefault();
                        }
                    });
                });
            </script>
            <script type="text/javascript">
                // Simple helper to ensure cart notifications work properly
                document.addEventListener('DOMContentLoaded', function() {
                    // Add a global error handler for cart operations
                    window.addEventListener('error', function(event) {
                        // Check if the error is related to cart operations
                        if (event.message && (
                            event.message.includes('cart') ||
                            event.message.includes('notification') ||
                            event.message.includes('undefined')
                        )) {
                            console.log('Caught error in cart operation:', event.message);
                            // Prevent the error from breaking the page
                            event.preventDefault();
                        }
                    });
                });
            </script>
            <script type="text/javascript" src="/ai_metal_price_integration/static/src/js/timer_script.js"></script>
        
            
            <style>
                .price-lock-container {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 0.25rem;
                    padding: 1rem;
                    margin-bottom: 1rem;
                }
                .price-lock-info {
                    display: flex;
                    align-items: center;
                }
                .price-lock-icon {
                    font-size: 1.5rem;
                    color: #28a745;
                    margin-right: 1rem;
                }
                .price-lock-timer {
                    display: flex;
                    align-items: center;
                    font-size: 1.2rem;
                    font-weight: bold;
                }
                .price-lock-timer i {
                    margin-right: 0.5rem;
                    color: #dc3545;
                }
                #price-lock-timer-value {
                    font-family: monospace;
                    background-color: #f1f1f1;
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.25rem;
                }
                .price-updated {
                    animation: highlight 2s;
                }
                @keyframes highlight {
                    0% { background-color: #ffff99; }
                    100% { background-color: transparent; }
                }
            </style>
    </head>
    <body>



        <div id="wrapwrap" class="homepage   ">
                <header id="top" data-anchor="true" data-name="Header" data-extra-items-toggle-aria-label="Extra items button" class="   o_header_standard" style=" ">
                    
    <nav data-name="Navbar" aria-label="Main" class="navbar navbar-expand-lg navbar-light o_colored_level o_cc d-none d-lg-block shadow-sm ">
        

            <div id="o_main_nav" class="o_main_nav container">
                
    <a data-name="Navbar Logo" href="/" class="navbar-brand logo me-4">
            
            <span role="img" aria-label="Logo of oneclickgold.com" title="oneclickgold.com"><img src="/web/image/website/1/logo/oneclickgold.com?unique=e0c5ce3" class="img img-fluid" width="95" height="40" alt="oneclickgold.com" loading="lazy"/></span>
        </a>
    
                
    <ul id="top_menu" role="menu" class="nav navbar-nav top_menu o_menu_loading me-auto">
        

                    
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/" class="nav-link active">
            <span>Homepage</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/" class="nav-link active">
            <span>Home</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/sip/account" class="nav-link ">
            <span>Sip Account</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/ornament-price-calculator" class="nav-link ">
            <span>Ornament Price Calculator</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/sip/deliveries" class="nav-link ">
            <span>Sip Deliveries</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/my/wallet/transfer" class="nav-link ">
            <span>Wallet To Bank Acc</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/shop" class="nav-link ">
            <span>Shop</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/contactus" class="nav-link ">
            <span>Contact us</span>
        </a>
    </li>
                
    </ul>
                
                <ul class="navbar-nav align-items-center gap-2 flex-shrink-0 justify-content-end ps-3">
                    
        
            <li class=" divider d-none"></li> 
            <li class="o_wsale_my_cart  ">
                <a href="/shop/cart" aria-label="eCommerce cart" class="o_navlink_background btn position-relative rounded-circle p-1 text-center text-reset">
                    <div class="">
                        <i class="fa fa-shopping-cart fa-stack"></i>
                        <sup class="my_cart_quantity badge text-bg-primary position-absolute top-0 end-0 mt-n1 me-n1 rounded-pill d-none" data-order-id="">0</sup>
                    </div>
                </a>
            </li>
        
        <li class="">
                <div class="modal fade css_editable_mode_hidden" id="o_search_modal" aria-hidden="true" tabindex="-1">
                    <div class="modal-dialog modal-lg pt-5">
                        <div class="modal-content mt-5">
    <form method="get" class="o_searchbar_form o_wait_lazy_js s_searchbar_input " action="/website/search" data-snippet="s_searchbar_input">
            <div role="search" class="input-group input-group-lg">
        <input type="search" name="search" class="search-query form-control oe_search_box border-0 bg-light border border-end-0 p-3" placeholder="Search..." data-search-type="all" data-limit="5" data-display-image="true" data-display-description="true" data-display-extra-link="true" data-display-detail="true" data-order-by="name asc"/>
        <button type="submit" aria-label="Search" title="Search" class="btn oe_search_button border border-start-0 px-4 bg-o-color-4">
            <i class="oi oi-search"></i>
        </button>
    </div>

            <input name="order" type="hidden" class="o_search_order_by" value="name asc"/>
            
    
        </form>
                        </div>
                    </div>
                </div>
                <a data-bs-target="#o_search_modal" data-bs-toggle="modal" role="button" title="Search" href="#" class="btn rounded-circle p-1 lh-1 o_navlink_background text-reset o_not_editable">
                    <i class="oi oi-search fa-stack lh-lg"></i>
                </a>
        </li>
                    
        <li class="">
                <div data-name="Text" class="s_text_block ">
                    <a href="tel:+****************" class="nav-link o_nav-link_secondary p-2">
                        <i class="fa fa-1x fa-fw fa-phone me-1"></i>
                        <span class="o_force_ltr"><small>+****************</small></span>
                    </a>
                </div>
        </li>
                    
                    
        <li data-name="Language Selector" class="o_header_language_selector ">
        
        
        </li>
                    
            <li class=" o_no_autohide_item">
                <a href="/web/login" class="btn btn-outline-secondary">Sign in</a>
            </li>
                    
        
        
                    
        <li class="">
            <div class="oe_structure oe_structure_solo ">
                <section class="oe_unremovable oe_unmovable s_text_block" data-snippet="s_text_block" data-name="Text">
                    <div class="container">
                        <a href="/contactus" class="oe_unremovable btn btn-primary btn_cta">Contact Us</a>
                    </div>
                </section>
            </div>
        </li>
                </ul>
            </div>
        
    </nav>
    <nav data-name="Navbar" aria-label="Mobile" class="navbar  navbar-light o_colored_level o_cc o_header_mobile d-block d-lg-none shadow-sm px-0 ">
        

        <div class="o_main_nav container flex-wrap justify-content-between">
            
    <a data-name="Navbar Logo" href="/" class="navbar-brand logo ">
            
            <span role="img" aria-label="Logo of oneclickgold.com" title="oneclickgold.com"><img src="/web/image/website/1/logo/oneclickgold.com?unique=e0c5ce3" class="img img-fluid" width="95" height="40" alt="oneclickgold.com" loading="lazy"/></span>
        </a>
    
            <ul class="o_header_mobile_buttons_wrap navbar-nav flex-row align-items-center gap-2 mb-0">
        
            <li class=" divider d-none"></li> 
            <li class="o_wsale_my_cart  ">
                <a href="/shop/cart" aria-label="eCommerce cart" class="o_navlink_background_hover btn position-relative rounded-circle border-0 p-1 text-reset">
                    <div class="">
                        <i class="fa fa-shopping-cart fa-stack"></i>
                        <sup class="my_cart_quantity badge text-bg-primary position-absolute top-0 end-0 mt-n1 me-n1 rounded-pill d-none" data-order-id="">0</sup>
                    </div>
                </a>
            </li>
        
                <li>
                    <button class="nav-link btn me-auto p-2 o_not_editable" type="button" data-bs-toggle="offcanvas" data-bs-target="#top_menu_collapse_mobile" aria-controls="top_menu_collapse_mobile" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </li>
            </ul>
            <div id="top_menu_collapse_mobile" class="offcanvas offcanvas-end o_navbar_mobile">
                <div class="offcanvas-header justify-content-end o_not_editable">
                    <button type="button" class="nav-link btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                </div>
                <div class="offcanvas-body d-flex flex-column justify-content-between h-100 w-100">
                    <ul class="navbar-nav">
                        
        <li class="">
    <form method="get" class="o_searchbar_form o_wait_lazy_js s_searchbar_input " action="/website/search" data-snippet="s_searchbar_input">
            <div role="search" class="input-group mb-3">
        <input type="search" name="search" class="search-query form-control oe_search_box border-0 bg-light rounded-start-pill text-bg-light ps-3" placeholder="Search..." data-search-type="all" data-limit="0" data-display-image="true" data-display-description="true" data-display-extra-link="true" data-display-detail="true" data-order-by="name asc"/>
        <button type="submit" aria-label="Search" title="Search" class="btn oe_search_button rounded-end-pill bg-o-color-3 pe-3">
            <i class="oi oi-search"></i>
        </button>
    </div>

            <input name="order" type="hidden" class="o_search_order_by" value="name asc"/>
            
    
        </form>
        </li>
                        
    <ul role="menu" class="nav navbar-nav top_menu  ">
        

                            
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/" class="nav-link active">
            <span>Homepage</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/" class="nav-link active">
            <span>Home</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/sip/account" class="nav-link ">
            <span>Sip Account</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/ornament-price-calculator" class="nav-link ">
            <span>Ornament Price Calculator</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/sip/deliveries" class="nav-link ">
            <span>Sip Deliveries</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/my/wallet/transfer" class="nav-link ">
            <span>Wallet To Bank Acc</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/shop" class="nav-link ">
            <span>Shop</span>
        </a>
    </li>
    <li role="presentation" class="nav-item">
        <a role="menuitem" href="/contactus" class="nav-link ">
            <span>Contact us</span>
        </a>
    </li>
                        
    </ul>
                        
        <li class="">
                <div data-name="Text" class="s_text_block mt-2 border-top pt-2 o_border_contrast">
                    <a href="tel:+****************" class="nav-link o_nav-link_secondary p-2">
                        <i class="fa fa-1x fa-fw fa-phone me-1"></i>
                        <span class="o_force_ltr"><small>+****************</small></span>
                    </a>
                </div>
        </li>
                        
                    </ul>
                    <ul class="navbar-nav gap-2 mt-3 w-100">
                        
            <li class=" o_no_autohide_item">
                <a href="/web/login" class="btn btn-outline-secondary w-100">Sign in</a>
            </li>
                        
        
        
                        
        <li data-name="Language Selector" class="o_header_language_selector ">
        
        
        </li>
                        
        <li class="">
            <div class="oe_structure oe_structure_solo ">
                <section class="oe_unremovable oe_unmovable s_text_block" data-snippet="s_text_block" data-name="Text">
                    <div class="container">
                        <a href="/contactus" class="oe_unremovable btn btn-primary btn_cta w-100">Contact Us</a>
                    </div>
                </section>
            </div>
        </li>
                    </ul>
                </div>
            </div>
        </div>
    
    </nav>
    
        </header>
                <main>
                    
         <div id="wrap" class="oe_structure">
         <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"/>
         <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"/>
         <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
         <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.8.2/angular.min.js"></script>
         <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>   
         <section class="s_text_image o_cc o_cc2 pt72 pb72 o_colored_level" data-snippet="s_text_image" data-name="Text - Image" style="background-image: none;">
                       <div class="container">
                         <div>   <p style="font-family: inherit;font-size: 30px;"><b>Timeless <span style="color:#A37626;">Wealth</span> for Every Generation</b></p></div>
                       
                       
                       
                       
                       
                       <div class="text-center mb-4">
                           <button class="btn" style="color:#A37626;" onclick="showSection(&#39;gold&#39;)">Buy</button>
                           <button class="btn" style="color:#A37626;" onclick="scrollToSection(&#39;sellMetal&#39;)">Sell</button>
                       </div>
                       
                       
                       
                       
                       
                       
                       
                       
                       
                           <div class="row align-items-center buying">
                               <div class="col-lg-12 pt16 pb16 o_colored_level gold_section" id="gold_section" style="display:block">
                                   <div class="oe_structure">
                                       <section class="s_color_blocks_2 o_colored_level px-4" data-snippet="s_color_blocks_2" data-name="Big Boxes" style="background-image: none;">
                                           <div class="container-fluid">
                                               <div class="row">
                                                   <style>
                                                       .gold > .s_donation_btn,
                                                       .gold > #s_donation_amount_input,
                                                       .gold > #s_donation_gram_input {
                                                           color: #212529 !important;
                                                       }
                                                       .gold_section > .o_default_snippet_text,
                                                       .gold_section > .o_default_snippet_text font {
                                                           color: #212529 !important;
                                                       }
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                       
                                                        
                                                        
                                                        .s_donation_prefilled_buttons.gold .s_donation_btn,
                                                        .s_donation_prefilled_buttons.gold .s_donation_btn::after {
                                                            background: initial !important;
                                                            color: secondary !important;
                                                            box-shadow: initial !important;
                                                            border-color: initial !important;
                                                            transform: initial !important;
                                                            text-decoration: initial !important;
                                                            cursor: pointer !important; /* Change back to default cursor */
                                                        }
                                                        
                                                        #gold_gram_input:hover,#gold_gram_input::after{
                                                            color:secondary;
                                                        }
                                                            
                                                       
                                                      .s_donation_prefilled_buttons.silver .s_donation_btn:hover,
                                                        .s_donation_prefilled_buttons.silver .s_donation_btn:hover::after {
                                                            background: transparent !important;
                                                            color: white !important;
                                                            box-shadow: none !important;
                                                            border-color: white !important;
                                                            transform: none !important;
                                                            text-decoration: none !important;
                                                            cursor: default !important;
                                                        }
                                                        
                                                        
                                                        .s_donation_prefilled_buttons.silver .s_donation_btn,
                                                        .s_donation_prefilled_buttons.silver .s_donation_btn::after {
                                                            background: initial !important;
                                                            color: white !important;
                                                            box-shadow: initial !important;
                                                            border-color: initial !important;
                                                            transform: initial !important;
                                                            text-decoration: initial !important;
                                                            cursor: pointer !important; /* Change back to default cursor */
                                                        }
                                                        
                                                        #silver_gram_input:hover,#silver_gram_input::after{
                                                           background: transparent !important;
                                                            color: white 
                                                        }
                                                        
                                                   </style>
                                                   <div class="text-center o_colored_level col-lg-12 rounded border shadow pt32 pb40 o_cc o_cc4" data-oe-shape-data="{&#34;shape&#34;:&#34;web_editor/Wavy/11&#34;,&#34;flip&#34;:[&#34;y&#34;,&#34;x&#34;]}" style="border-radius: 8px !important; border-color: #FFD36B !important; box-shadow: rgba(0, 0, 0, 0.15) 0px 4px 4px 0px !important;background-color:#FFD36B;">
                                                       <div class="o_we_shape o_web_editor_Wavy_11" style="background-image: url(&#34;/web_editor/shape/web_editor/Wavy/11.svg?c1=o-color-5&amp;c4=o-color-5&amp;flip=xy&#34;); background-position: 50% 0%;"></div>
                                                           <div class="row">
                                                               <div class="col-6">
                                                           <h4 class="o_default_snippet_text" style="text-align: left;">
                                                               <font class="text-secondary">Gold Price</font>
                                                           </h4>
                                                           <p class="o_default_snippet_text" style="text-align: left;">
                                                               <font class="text-secondary">
                                                                   <span class="h5-fs">₹
                                                                       1500.0/gm
                                                                       <sub> +3% GST</sub>
                                                                   </span>
                                                               </font>
                                                               <br/>
                                                           </p>
                                                           <h6 class="o_default_snippet_text" style="text-align: left;">
                                                               <span style="font-weight: normal;">
                                                                   <font class="text-secondary">24k 99.9% Pure Gold</font>
                                                               </span>
                                                           </h6>
                                                           </div>
                                                           
                                                           
                                                           <div class="col-6">
                                                           <h4 class="o_default_snippet_text" style="text-align: right;">
                                                           <font class="text-secondary">Silver&nbsp;Price</font>
                                                       </h4>
                                                       <p class="o_default_snippet_text" style="text-align: right;">
                                                           <font class="text-secondary">
                                                               <span class="h5-fs">₹
                                                                   100.0/gm
                                                                   <sub> +3% GST</sub>
                                                               </span>
                                                           </font>
                                                           <br/>
                                                       </p>
                                                       <h6 class="o_default_snippet_text" style="text-align: right;">
                                                           <font class="text-secondary">
                                                               <span style="font-weight: normal;">24k 99.9% Pure Silver</span>
                                                           </font>
                                                       </h6>
                                                       </div>
                                                   </div>
                                                   <br/>
                                                    <div class="text-center mb-4">
                                                       <button class="btn btn-warning" onclick="showSection(&#39;gold&#39;)">Gold</button>
                                                       <button class="btn btn-secondary" onclick="showSection(&#39;silver&#39;)" style="background-color:grey; color:white;border:None;">Silver</button>
                                                   </div>                               
                                                  <div class="col-lg-12 pt16 pb16 o_colored_level">
                                                           <div class="s_donation" data-name="Donation Button" data-donation-email="<EMAIL>" data-minimum-amount="5" data-maximum-amount="1000000">
                                                               <form class="s_donation_form" action="/metal/payment" method="post" enctype="multipart/form-data">
                                                                
                                                                <div class="s_donation_prefilled_buttons mb-2 gold">                                                                     
                                                                       <input class="gold_id" type="hidden" id="gold_id" name="metal_id" value="1"/>                                                                   
                                                                       <span class="s_donation_btn s_donation_custom_btn w-100 btn btn-outline-secondary btn-lg mb-2 me-1">
                                                                           <input id="s_donation_amount_input" class="goldForm" type="number" placeholder="Custom Amount" aria-label="Amount" min="1" fdprocessedid="ltkj9b"/> <label id="gold_grams_label">| </label>
                                                                           <input id="gold_gram_input" type="number" placeholder="Custom Grams" aria-label="Grams" style="width:200px; border:none; background:transparent;"/>gm
                                                                           
                                                                       </span>
                                                                       <br/>
                                                                       <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1" type="button" data-donation-value="10">10</button>
                                                                       <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1" type="button" data-donation-value="25">25</button>
                                                                       <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1" type="button" data-donation-value="50">50</button>
                                                                       <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1" type="button" data-donation-value="100">100</button>
                                                                   </div>
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   
                                                                   <a id="submit_form" class="btn btn-secondary btn-lg mb-2" href="/web/login">
                                                                       Add Gold to Vault
                                                                   </a>
                                                                   
                                                                   
                                                                       
                                                                       
                                                                   
                                                                   <span id="metal_id" data-value="gold"></span>
   
                                                                   
                                                               </form>
                                                           </div>
                                                   </div>
                                                   </div>
                                                </div>
                                           </div>
                                       </section>
                                   </div>
                               </div>
                               
                               <div class="col-lg-12 pt16 pb16 o_colored_level silver_section " id="silver_section" style="display:none">
                                   <div class="oe_structure">
                                       <section class="s_color_blocks_2 o_colored_level px-4" data-snippet="s_color_blocks_2" data-name="Big Boxes" style="background-image: none;">
                                           <div class="container-fluid">
                                               <div class="row">
                                                   <div class="text-center o_colored_level col-lg-12 rounded border o_cc o_cc4 bg-900 shadow pt32 pb40" data-oe-shape-data="{&#34;shape&#34;:&#34;web_editor/Wavy/11&#34;,&#34;flip&#34;:[&#34;y&#34;,&#34;x&#34;]}" style="border-radius: 8px !important; border-color: rgb(106, 72, 57) !important; box-shadow: rgba(0, 0, 0, 0.15) 0px 4px 4px 0px !important; background-image: none;">
                                                       <div class="o_we_shape o_web_editor_Wavy_11" style="background-image: url(&#34;/web_editor/shape/web_editor/Wavy/11.svg?c1=o-color-5&amp;c4=o-color-5&amp;flip=xy&#34;); background-position: 50% 0%;"></div>
                                                       <div class="row">
                                                           <div class="col-6">
                                                               <h4 class="o_default_snippet_text" style="text-align: left;">
                                                               <font class="text-white">Gold Price</font>
                                                           </h4>
                                                           <p class="o_default_snippet_text" style="text-align: left;">
                                                               <font class="text-white">
                                                                   <span class="h5-fs">₹
                                                                       1500.0/gm
                                                                       <sub> +3% GST</sub>
                                                                   </span>
                                                               </font>
                                                               <br/>
                                                           </p>
                                                           <h6 class="o_default_snippet_text" style="text-align: left;">
                                                               <span style="font-weight: normal;">
                                                                   <font class="text-white">24k 99.9% Pure Gold</font>
                                                               </span>
                                                           </h6>
                                                           </div>
                                                           
                                                       <div class="col-6">    
                                                       <h4 class="o_default_snippet_text" style="text-align: right;">
                                                           <font class="text-white">Silver&nbsp;Price</font>
                                                       </h4>
                                                       <p class="o_default_snippet_text" style="text-align: right;">
                                                           <font class="text-white">
                                                               <span class="h5-fs">₹
                                                                   100.0/gm
                                                                   <sub> +3% GST</sub>
                                                               </span>
                                                           </font>
                                                           <br/>
                                                       </p>
                                                       <h6 class="o_default_snippet_text" style="text-align: right;">
                                                           <font class="text-white">
                                                               <span style="font-weight: normal;">24k 99.9% Pure Silver</span>
                                                           </font>
                                                       </h6>
                                                       </div>
                                                   </div>
                                                   <br/>
                                                   
                                                   <div class="text-center mb-4">
                                                                   <button class="btn btn-warning" onclick="showSection(&#39;gold&#39;)">Gold</button>
                                                                   <button class="btn btn-secondary" onclick="showSection(&#39;silver&#39;)" style="background-color:grey; color:white;border:None;">Silver</button>
                                                               </div>
                       
                                                   <div class="col-lg-12 pt16 pb16 o_colored_level" style="background-image: none;">
                                                       <div class="s_donation" data-name="Donation Button" data-donation-email="<EMAIL>" data-custom-amount="freeAmount" data-prefilled-options="true" data-descriptions="" data-minimum-amount="1" data-maximum-amount="1000000" data-slider-step="5" data-default-amount="250" data-snippet="s_donation_button" data-display-options="true" data-donation-amounts="[&#34;10&#34;,&#34;25&#34;,&#34;50&#34;,&#34;100&#34;]">
                                                           <form class="s_donation_form" action="/metal/payment" method="post" enctype="multipart/form-data">
                                                               <span id="s_donation_description_inputs"></span>
                                                               <div class="s_donation_prefilled_buttons mb-2 silver">
                                                                   <input class="silver_id" type="hidden" id="silver_id" name="metal_id" value="2"/>
                                                                   <span class="s_donation_btn s_donation_custom_btn w-100 btn btn-outline-secondary btn-lg mb-2 me-1">
                                                                       <input id="s_donation_amount_input" class="silverForm" type="number" placeholder="Custom Amount" aria-label="Amount" min="1" fdprocessedid="ltkj9b" style=""/>                    <label id="silver_grams_label">| </label>
                                                                       <input id="silver_grams_input" type="number" placeholder="Enter Grams" aria-label="Grams" style="width: 200px; display: inline-block;border:none; background:transparent;color:white"/>gm
                                                                   </span><br/>
                                                                   <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1 o_not_editable" type="button" data-donation-value="10">10</button>
                                                                   <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1 o_not_editable" type="button" data-donation-value="25">25</button>
                                                                   <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1 o_not_editable" type="button" data-donation-value="50">50</button>
                                                                   <button class="s_donation_btn btn btn-outline-secondary btn-lg mb-2 me-1 o_not_editable" type="button" data-donation-value="100">100</button>
                                                               </div>
                                                               
                                                               
                                                               
                                                               
                                                               <a class="btn btn-secondary btn-lg mb-2" id="add_silver_button" href="/web/login">
                                                                       Add Silver to Vault
                                                                   </a>
                                                                   <span id="metal_id" data-value="silver"></span>
                                                                   
                                                                       
                                                                       
                                                                       
                                                                   
                                                               
                                                           </form>
                                                       </div>
                                                   </div>
                                               </div>
                                               </div>
                                           </div>
                                       </section>
                                   </div>
                               </div>
                           </div>
                       </div>
   
   
                                                                <script>
                                                                    
                                                                    // Define showSection in global scope so it can be accessed from onclick attributes
    function showSection(metal) {
        if (metal === 'gold') {
            document.getElementById('gold_section').style.display = 'block';
            document.getElementById('silver_section').style.display = 'none';
        } else if (metal === 'silver') {
            document.getElementById('gold_section').style.display = 'none';
            document.getElementById('silver_section').style.display = 'block';
        }
    }
    document.addEventListener("DOMContentLoaded", function () {
        showSection('gold'); // Show gold section by default

        const goldButton = document.getElementById('submit_form');
        const silverButton = document.getElementById('add_silver_button');
        const metalSelect = document.getElementById('metal_id');

        const metalMap = {
            'gold': 1,
            'silver': 2
        };

        function debounce(func, delay) {
            let timer;
            return function (...args) {
                clearTimeout(timer);
                timer = setTimeout(() => func.apply(this, args), delay);
            };
        }

        function updateGramsFromAmount(amount, metalId, parentSection) {
            console.log('Converting amount to grams:', amount, 'Metal ID:', metalId);
            $.ajax({
                url: '/sip/get_grams/' + metalId,
                type: 'GET',
                data: { get_price: amount / 1.03 }, // Remove GST
                success: function (response) {
                    const result = JSON.parse(response);
                    console.log('Grams received:', result.grams);
                    if (result.grams) {
                        if (parentSection.hasClass('silver')) {
                            $('#silver_grams_input').val(result.grams.toFixed(6));
                        } else {
                            $('#gold_gram_input').val(result.grams.toFixed(6));
                        }
                    }
                }
            });
        }

        function updateAmountFromGrams(grams, metalId, parentSection) {
            console.log('Converting grams to amount:', grams, 'Metal ID:', metalId);
            $.ajax({
                url: '/sip/get_amount/' + metalId,
                type: 'GET',
                data: { grams: grams },
                success: function (response) {
                    const result = JSON.parse(response);
                    const amountWithGST = result.amount * 1.03;

                    if (parentSection.hasClass('silver')) {
                        $('.silverForm').val(amountWithGST.toFixed(2));
                        $('#silver_grams_input').text('₹ ' + amountWithGST.toFixed(2));
                    } else {
                        $('.goldForm').val(amountWithGST.toFixed(2));
                        $('#gold_gram_input').text('₹ ' + amountWithGST.toFixed(2));
                    }
                }
            });
        }

        
        
        
        
        
        
        
        
        
        

        // Handle donation button click
        document.querySelectorAll(".s_donation_btn").forEach(function (button) {
            button.addEventListener("click", function () {
                const donationValue = button.getAttribute("data-donation-value");
                const parentForm = button.closest(".s_donation_form");
                const amountInput = parentForm.querySelector("#s_donation_amount_input");

                if (amountInput) {
                    amountInput.value = donationValue;
                }

                const parentSection = $(parentForm).closest('.gold_section, .silver_section');
                const metalId = parentSection.find('.gold_id, .silver_id').val() || (parentSection.hasClass('gold_section') ? 1 : 2);

                if (amountInput.value) {
                    updateGramsFromAmount(amountInput.value, metalId, parentSection);
                }

                parentForm.querySelectorAll(".s_donation_btn").forEach(function (btn) {
                    btn.classList.remove("active");
                });
                button.classList.add("active");
            });
        });

        // Handle input changes in amount
        $('#s_donation_amount_input, .silver_text_input').on('input', debounce(function () {
            const amount = $(this).val();
            const parentSection = $(this).closest('.gold_section, .silver_section');
            const metalId = parentSection.find('.gold_id, .silver_id').val();

            if (amount) {
                updateGramsFromAmount(amount, metalId, parentSection);
            }
        }, 500));

        // Handle input changes in grams
        $('#gold_gram_input, #silver_grams_input').on('input', debounce(function () {
            const grams = $(this).val();
            const parentSection = $(this).closest('.gold_section, .silver_section');
            const metalId = parentSection.find('.gold_id, .silver_id').val();

            if (grams) {
                updateAmountFromGrams(grams, metalId, parentSection);
            }
        }, 500));

        // Handle gold/silver donate button click
        function handleMetalRedirect(button, metalName) {
            button.addEventListener('click', function (e) {
                e.preventDefault();

                let selectedMetal = metalSelect?.getAttribute('value') || metalSelect?.dataset.value || metalName;
                selectedMetal = metalMap[selectedMetal?.toLowerCase()];
                let targetUrl = button.getAttribute('href');

                if (targetUrl === '/web/login') {
                    window.location.href = targetUrl;
                    return;
                }

                if (!selectedMetal) {
                    alert('Please select a valid metal first');
                    return;
                }

                const form = button.closest('form');
                const amountInput = form.querySelector('#s_donation_amount_input');
                const amount = amountInput ? amountInput.value : '0';

                if (targetUrl === '#') {
                    targetUrl = '/metal/payment';
                }

                const separator = targetUrl.includes('?') ? '&' : '?';
                const newUrl = `${targetUrl}${separator}metal_id=${encodeURIComponent(selectedMetal)}&amount=${encodeURIComponent(amount)}`;

                console.log('Redirecting to:', newUrl);
                window.location.href = newUrl;
            });
        }

        if (goldButton) handleMetalRedirect(goldButton, 'gold');
        if (silverButton) handleMetalRedirect(silverButton, 'silver');
    });
</script>

                   </section>
                   
         <div class="oe_structure"></div>
         
   
               
               <div class="container mt-4">
                   <h1 class="text-center mb-4">Temples where we offer Online Chadava</h1>
                   
                   
                   <div class="row justify-content-center mb-4">
                       <div class="col-md-6">
                           <div class="input-group">
                               <input type="text" class="form-control" id="warehouseSearch" placeholder="Search temples..." autocomplete="off"/>
                               <div class="input-group-append">
                                   <span class="input-group-text">
                                       <i class="fa fa-search"></i>
                                   </span>
                               </div>
                           </div>
                       </div>
                   </div>
   
                  
                   <div id="loadingSpinner" class="text-center d-none">
                       <div class="spinner-border text-primary" role="status">
                           <span class="sr-only">Loading...</span>
                       </div>
                   </div>
   
                   
                   <div class="row" id="warehouseContainer">
                           <div class="col-md-4 col-sm-6 mb-3 warehouse-item">
                               <a class="warehouse-tag d-flex align-items-center p-3" href="/warehouse/1">
                                   <img src="/ai_religious_chadava/static/src/img/default_warehouse.png" class="warehouse-image mr-3" alt="Default Warehouse Image" loading="lazy"/>
                                   <span class="warehouse-name">My Company</span>
                               </a>
                           </div>
                           <div class="col-md-4 col-sm-6 mb-3 warehouse-item">
                               <a class="warehouse-tag d-flex align-items-center p-3" href="/warehouse/2">
                                   <img src="/ai_religious_chadava/static/src/img/default_warehouse.png" class="warehouse-image mr-3" alt="Default Warehouse Image" loading="lazy"/>
                                   <span class="warehouse-name">Ambaji Mandir</span>
                               </a>
                           </div>
                   </div>
               </div>
               
               <script type="text/javascript">
                   $(document).ready(function() {
                       var searchTimer;
                       var $search = $('#warehouseSearch');
                       var $container = $('#warehouseContainer');
                       var $spinner = $('#loadingSpinner');
   
                       function searchWarehouses() {
                           var searchTerm = $search.val().trim();
                           
                           $spinner.removeClass('d-none');
   
                           $.ajax({
                               url: '/warehouse/search',
                               method: 'POST',
                               contentType: 'application/json',
                               data: JSON.stringify({
                                   params: {
                                       search_term: searchTerm
                                   }
                               }),
                               success: function(response) {
                                   var warehouses = response.result || [];
                                   updateWarehouseDisplay(warehouses);
                               },
                               error: function(xhr, status, error) {
                                   console.error('Search failed:', error);
                                   $container.html('<div class="col-12 text-center">Error loading warehouses. Please try again.</div>');
                               },
                               complete: function() {
                                   $spinner.addClass('d-none');
                               }
                           });
                       }
   
                       function updateWarehouseDisplay(warehouses) {
                           $container.empty();
                           
                           if (warehouses.length === 0) {
                               $container.html('<div class="col-12 text-center">No warehouses found</div>');
                               return;
                           }
   
                           warehouses.forEach(function(warehouse) {
                               var warehouseHtml = `
                                   <div class="col-md-4 col-sm-6 mb-3 warehouse-item">
                                       <a href="/warehouse/${warehouse.id}" class="warehouse-tag d-flex align-items-center p-3">
                                           <img src="/${warehouse.image_url}" class="warehouse-image mr-3" onerror="this.src=&#39;/ai_religious_chadava/static/src/img/default_warehouse.png&#39;" alt="${warehouse.name}" loading="lazy"/>
                                           <span class="warehouse-name">${warehouse.name}</span>
                                       </a>
                                   </div>
                               `;
                               $container.append(warehouseHtml);
                           });
                       }
   
                       // Debounced search
                       $search.on('input', function() {
                           clearTimeout(searchTimer);
                           searchTimer = setTimeout(searchWarehouses, 300);
                       });
                   });
               </script>
   
   
         <section class="s_text_image o_cc pt72 pb72 o_colored_level">
            <div class="container">
               <div class="row">
                  <div id="products_grid" class="col-lg-12">
                     <style>
                        ul.o_wsale_filmstip > li.d-flex{
                        margin-left:5px;
                        }
                        .gold_category_wrapper{
                        background-color: #f5f5f5;
                        }
                        .gold_product_image_img_wrapper img{
                        transition: transform 0.5s ease-in-out;
                        mix-blend-mode: multiply;
                        }
                        .gold_product_image_img_wrapper img:hover{
                        transform: scale(1.1);
                        }
                        .o_wsale_product_information{
                        padding-left: 10px !important;
                        }
                        .owl-carousel-category-nav .owl-stage-outer .owl-stage .owl-item{
                        width: fit-content !important;
                        }
                     </style>
                     
                     <div ng-app="carouselApp" ng-controller="carouselController">
                        <div class="o_wsale_filmstip_container d-flex align-items-stretch mb-2 overflow-hidden">
                           <div class="o_wsale_filmstip_wrapper pb-1 overflow-auto w-100">
                           <h1 class="text-center mb-4"> Choose From Our Exquisite Categories </h1> 
                              <ul class="owl-carousel owl-carousel-category-nav o_wsale_filmstip d-flex align-items-stretch mb-0 list-unstyled overflow-visible owl-loaded owl-drag" id="categoryCarousel">
                              </ul>
                           </div>
                        </div>
                        <script>
                           $(document).ready(function() {
                               var owl = $('#categoryCarousel');
                           
                               owl.owlCarousel({
                                   loop: false,
                                   margin: 10,
                                   nav: false,  // Disable default nav buttons
                                   dots: false,
                                   autoplay: false,
                                   autoplayTimeout: 5000,
                                   responsive: {
                                       0: {
                                           items: 1,
                                           dots: false
                                       },
                                       425: {
                                           items: 2,
                                           dots: false
                                       },
                                       600: {
                                           items: 3
                                       },
                                       1200: {
                                           items: 4
                                       },
                                       1440: {
                                           items: 6
                                       }
                                   }
                               });
                           });
                        </script>
                        
                        <style>
                           @media (max-width: 991.98px) {
                           .oe_product_large_product{
                           width: 100% !important;
                           }
                           .oe_product_last_category{
                           width: 100% !important;
                           }
                           }
                           .pl-2{
                           padding-left: 1rem; 
                           }
                           .pr-2{
                           padding-right: 1rem; 
                           }
                           .animated-border {
                           position: relative;
                           cursor: pointer;
                           }
                           .animated-border::after {
                           content: '';
                           position: absolute;
                           bottom: 0;
                           left: 0;
                           width: 100%;
                           height: 2px;
                           background-color: #704c3c;
                           transform-origin: left;
                           }
                           /* Initial state - border visible */
                           .animated-border::after {
                           animation: showFromRight 0.5s ease forwards;
                           }
                           /* Hover state animations */
                           .animated-border:hover::after {
                           animation: 
                           hideFromLeft 0.5s ease forwards,
                           showFromLeft 0.5s ease forwards 0.5s;
                           }
                           /* No hover state animations */
                           .animated-border:not(:hover)::after {
                           animation: 
                           hideFromRight 0.5s ease forwards,
                           showFromRight 0.5s ease forwards 0.5s;
                           }
                           @keyframes hideFromLeft {
                           0% {
                           transform: scaleX(1);
                           transform-origin: left;
                           }
                           100% {
                           transform: scaleX(0);
                           transform-origin: left;
                           }
                           }
                           @keyframes showFromLeft {
                           0% {
                           transform: scaleX(0);
                           transform-origin: left;
                           }
                           100% {
                           transform: scaleX(1);
                           transform-origin: left;
                           }
                           }
                           @keyframes hideFromRight {
                           0% {
                           transform: scaleX(1);
                           transform-origin: right;
                           }
                           100% {
                           transform: scaleX(0);
                           transform-origin: right;
                           }
                           }
                           @keyframes showFromRight {
                           0% {
                           transform: scaleX(0);
                           transform-origin: right;
                           }
                           100% {
                           transform: scaleX(1);
                           transform-origin: right;
                           }
                           }
                        </style>
                        <div class="o_wsale_products_grid_table_wrapper pt-3 pt-lg-0">
                           <table class="table table-borderless h-100 m-0 o_wsale_context_thumb_cover" data-ppg="20" data-ppr="4" data-default-sort="website_sequence asc" data-name="Grid">
                              <tbody>
                                 <tr>
                                    <td ng-if="sub_categories_count &gt; 0" colspan="2" rowspan="2" class="oe_product oe_product_large_product" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_2_2">
                                          <form ng-action="{{sub_categories[0].url}}" method="post" class="oe_product_cart h-100 d-flex" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative" itemprop="url" contenteditable="false" ng-href="{{sub_categories[0].url}}">
                                                <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute" data-oe-xpath="/t[1]/form[1]/div[1]/a[1]/span[1]" data-oe-model="product.template" data-oe-id="5" data-oe-field="image_1920" data-oe-type="image" data-oe-expression="image_holder.image_1920">
                                                <img ng-src="{{sub_categories[0].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" ng-alt="{{sub_categories[0].name}}" loading="lazy" style=""/>
                                                </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" ng-href="{{sub_categories[0].url}}" ng-content="{{sub_categories[0].name}}">
                                                      {{sub_categories[0].name}}
                                                      </a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[0].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                    <td ng-if="sub_categories_count &gt; 1" class="oe_product" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_1_1">
                                          <form ng-action="{{sub_categories[1].url}}" method="post" class="oe_product_cart h-100 d-flex" itemscope="itemscope" itemtype="http://schema.org/Product" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative" itemprop="url" contenteditable="false" ng-href="{{sub_categories[1].url}}">
                                                <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute" data-oe-xpath="/t[1]/form[1]/div[1]/a[1]/span[1]" data-oe-model="product.template" data-oe-id="9" data-oe-field="image_1920" data-oe-type="image" data-oe-expression="image_holder.image_1920">
                                                <img ng-src="{{sub_categories[1].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" alt="Kalpruvksha Gold Coin 24kt (995)" loading="lazy" style=""/>
                                                </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" data-oe-xpath="/t[1]/form[1]/div[2]/div[1]/h6[1]/a[1]" ng-href="{{sub_categories[1].url}}" content="Kalpruvksha Gold Coin 24kt (995)" data-oe-model="product.template" data-oe-id="9" data-oe-field="name" data-oe-type="char" data-oe-expression="product.name">{{sub_categories[1].name}}</a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[1].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                    <td ng-if="sub_categories_count &gt; 2" class="oe_product" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_1_1">
                                          <form ng-action="{{sub_categories[2].url}}" method="post" class="oe_product_cart h-100 d-flex" itemscope="itemscope" itemtype="http://schema.org/Product" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative" itemprop="url" contenteditable="false" ng-href="{{sub_categories[2].url}}">
                                                <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute" data-oe-xpath="/t[1]/form[1]/div[1]/a[1]/span[1]" data-oe-model="product.template" data-oe-id="9" data-oe-field="image_1920" data-oe-type="image" data-oe-expression="image_holder.image_1920">
                                                <img ng-src="{{sub_categories[2].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" alt="Kalpruvksha Gold Coin 24kt (995)" loading="lazy" style=""/>
                                                </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" data-oe-xpath="/t[1]/form[1]/div[2]/div[1]/h6[1]/a[1]" ng-href="{{sub_categories[2].url}}" content="Kalpruvksha Gold Coin 24kt (995)" data-oe-model="product.template" data-oe-id="9" data-oe-field="name" data-oe-type="char" data-oe-expression="product.name">{{sub_categories[2].name}}</a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[2].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                 </tr>
                                 <tr>
                                    <td ng-if="sub_categories_count &gt; 3" class="oe_product" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_1_1">
                                          <form ng-href="{{sub_categories[3].url}}" method="post" class="oe_product_cart h-100 d-flex" itemscope="itemscope" itemtype="http://schema.org/Product" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative" itemprop="url" contenteditable="false" ng-href="{{sub_categories[3].url}}">
                                                <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute" data-oe-xpath="/t[1]/form[1]/div[1]/a[1]/span[1]" data-oe-model="product.template" data-oe-id="9" data-oe-field="image_1920" data-oe-type="image" data-oe-expression="image_holder.image_1920">
                                                <img ng-src="{{sub_categories[3].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" alt="Kalpruvksha Gold Coin 24kt (995)" loading="lazy" style=""/>
                                                </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" data-oe-xpath="/t[1]/form[1]/div[2]/div[1]/h6[1]/a[1]" ng-href="{{sub_categories[3].url}}" content="Kalpruvksha Gold Coin 24kt (995)" data-oe-model="product.template" data-oe-id="9" data-oe-field="name" data-oe-type="char" data-oe-expression="product.name">{{sub_categories[3].name}}</a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[3].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                    <td ng-if="sub_categories_count &gt; 4" class="oe_product" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_1_1">
                                          <form ng-action="{{sub_categories[4].url}}" method="post" class="oe_product_cart h-100 d-flex" itemscope="itemscope" itemtype="http://schema.org/Product" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative" itemprop="url" contenteditable="false" ng-href="{{sub_categories[4].url}}">
                                                <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center align-items-center position-absolute" data-oe-xpath="/t[1]/form[1]/div[1]/a[1]/span[1]" data-oe-model="product.template" data-oe-id="9" data-oe-field="image_1920" data-oe-type="image" data-oe-expression="image_holder.image_1920">
                                                <img ng-src="{{sub_categories[4].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" alt="Kalpruvksha Gold Coin 24kt (995)" loading="lazy" style=""/>
                                                </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" data-oe-xpath="/t[1]/form[1]/div[2]/div[1]/h6[1]/a[1]" ng-href="{{sub_categories[4].url}}" content="Kalpruvksha Gold Coin 24kt (995)" data-oe-model="product.template" data-oe-id="9" data-oe-field="name" data-oe-type="char" data-oe-expression="product.name">{{sub_categories[4].name}}</a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[4].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                 </tr>
                                 <tr>
                                    <td ng-if="sub_categories_count &gt; 5" class="oe_product oe_product_last_category" colspan="2" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_1_1" style="height: 294px !important;">
                                          <form ng-action="{{sub_categories[5].url}}" method="post" class="oe_product_cart h-100 d-flex" itemscope="itemscope" itemtype="http://schema.org/Product" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative p-0" itemprop="url" contenteditable="false" ng-href="{{sub_categories[5].url}}">
                                                <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center position-absolute" data-oe-xpath="/t[1]/form[1]/div[1]/a[1]/span[1]" data-oe-model="product.template" data-oe-id="9" data-oe-field="image_1920" data-oe-type="image" data-oe-expression="image_holder.image_1920">
                                                <img ng-src="{{sub_categories[5].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" alt="Kalpruvksha Gold Coin 24kt (995)" loading="lazy" style=""/>
                                                </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" data-oe-xpath="/t[1]/form[1]/div[2]/div[1]/h6[1]/a[1]" ng-href="{{sub_categories[5].url}}" content="Kalpruvksha Gold Coin 24kt (995)" data-oe-model="product.template" data-oe-id="9" data-oe-field="name" data-oe-type="char" data-oe-expression="product.name">{{sub_categories[5].name}}</a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[5].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                    <td ng-if="sub_categories_count &gt; 6" class="oe_product oe_product_last_category" colspan="2" rowspan="2" data-name="Product">
                                       <div class="gold_category_wrapper o_wsale_product_grid_wrapper position-relative h-100 o_wsale_product_grid_wrapper_1_1" style="height: 294px !important;">
                                          <form ng-action="{{sub_categories[6].url}}" method="post" class="oe_product_cart h-100 d-flex" itemscope="itemscope" itemtype="http://schema.org/Product" data-publish="on">
                                             <div class="oe_product_image position-relative h-100 flex-grow-0 overflow-hidden">
                                                <a class="oe_product_image_link d-block h-100 position-relative p-0" itemprop="url" contenteditable="false" ng-href="{{sub_categories[6].url}}">
                                                   
                                                   <span class="gold_product_image_img_wrapper oe_product_image_img_wrapper d-flex h-100 justify-content-center position-absolute">
                                                   <img ng-src="{{sub_categories[6].image_url}}" itemprop="image" class="img img-fluid h-100 w-100 position-absolute" alt="Kalpruvksha Gold Coin 24kt (995)" loading="lazy" style=""/>
                                                   </span>
                                                </a>
                                             </div>
                                             <div class="o_wsale_product_information position-relative d-flex flex-column flex-grow-1 flex-shrink-1">
                                                <div class="o_wsale_product_information_text flex-grow-1">
                                                   <h5 class="text-center pl-2 pr-2 o_wsale_products_item_title mb-2">
                                                      <a class="text-black text-decoration-none" itemprop="name" data-oe-xpath="/t[1]/form[1]/div[2]/div[1]/h6[1]/a[1]" ng-href="{{sub_categories[6].url}}" content="Kalpruvksha Gold Coin 24kt (995)" data-oe-model="product.template" data-oe-id="9" data-oe-field="name" data-oe-type="char" data-oe-expression="product.name">{{sub_categories[6].name}}</a>
                                                   </h5>
                                                   <br/>
                                                   <h6 class="text-center">
                                                      <a ng-href="{{sub_categories[6].url}}" target="_blank" class="animated-border gold_animated_button text-center button btn-style-link" style="text-decoration: none;">SHOP COLLECTION</a>
                                                   </h6>
                                                   <br/>
                                                </div>
                                             </div>
                                          </form>
                                       </div>
                                    </td>
                                 </tr>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>
         
         <script>
            var app = angular.module('carouselApp', []);
            app.controller('carouselController', function($scope, $http, $timeout) {
                $scope.categories = [];
                $scope.sub_categories = [];
            
                // Function to load main categories (used for "All" option)
                $scope.loadCategories = function() {
                    $http.get('/api/v3/categories', { withCredentials: true })
                        .then(function(response) {
                            if (response.data.status === 'success') {
                                $scope.categories = response.data.main_categories.data;
                                $scope.sub_categories = response.data.sub_categories.data;
                                $scope.sub_categories_count = response.data.sub_categories.count;
                                initializeCarousel();
                            }
                        })
                        .catch(function(error) {
                            console.error('Error fetching categories:', error);
                        });
                };
            
                // Function to load subcategories based on parent ID
                $scope.loadSubCategories = function(parentId) {
                    $http.get('/api/v3/categories?parent_id=' + parentId, { withCredentials: true })
                        .then(function(response) {
                            if (response.data.status === 'success') {
                                $scope.sub_categories = response.data.sub_categories.data;
                                $scope.sub_categories_count = response.data.sub_categories.count;
                                initializeCarousel();
                            }
                        })
                        .catch(function(error) {
                            console.error('Error fetching subcategories:', error);
                        });
                };
            
                // Initialize Owl Carousel after data is loaded
                function initializeCarousel() {
                    $timeout(function() {
                        $('#categoryCarousel').owlCarousel('destroy');  // Destroy previous carousel instance
                         var owl = $('#categoryCarousel');
                       owl.owlCarousel({
                                                loop: false,
                                                margin: 10,
                                                nav: false,  // Disable default nav buttons
                                                dots: false,
                                                autoplay: false,
                                                autoplayTimeout: 5000,
                                                responsive: {
                                                    0: {
                                                        items: 1,
                                                        dots: false
                                                    },
                                                    425: {
                                                        items: 2,
                                                        dots: false
                                                    },
                                                    600: {
                                                        items: 3
                                                    },
                                                    1200: {
                                                        items: 4
                                                    },
                                                    1440: {
                                                        items: 6
                                                    }
                                                }
                                            });
                    }, 0);
                }
            
                // Initial load of main categories
                $scope.loadCategories();
            });
         </script>
         
         <section class="s_text_image pt72 pb72 o_colored_level o_cc o_cc2" data-snippet="s_image_text" data-name="Image - Text" style="position: relative; background-color: rgb(255, 255, 255); background-image: none;">
            <div class="container">
               <div class="row align-items-center">
                  <div class="pt16 pb16 o_colored_level col-lg-7">
                     <div class="row">
                        <div class="col-lg-6 mb-4">
                           <div class="pt32 pb32 ml-2 mr-2 text-center o_colored_level rounded shadow o_animate o_anim_slide_in o_anim_from_left o_visible" style="background-color: rgb(252, 243, 230); --wanim-intensity: 8; border-radius: 8px !important; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px !important;">
                              <img src="/web/image/836-7b2a4175/AugmontCard.d5ec0e1a.webp" alt="" class="rounded m-3 img img-fluid o_we_custom_image" data-mimetype="image/webp" data-original-id="834" data-original-src="/web/image/834-cda08016/AugmontCard.d5ec0e1a.png" data-mimetype-before-conversion="image/png" data-resize-width="225" loading="eager"/>
                              <p class="o_default_snippet_text">
                                 <span class="h5-fs">
                                 <strong>Physical gold delivery</strong>
                                 </span>
                                 <br/>
                              </p>
                              <p class="o_default_snippet_text" style="margin-bottom: 0px;">We will deliver a gold coin to</p>
                              <p class="o_default_snippet_text" style="margin-bottom: 0px;">your doorstep</p>
                           </div>
                        </div>
                        <div class="col-lg-6">
                           <div class="pt32 pb32 ml-2 mr-2 text-center o_colored_level rounded offset-lg-1 shadow o_animate o_anim_slide_in o_anim_from_right o_visible" style="background-color: rgb(252, 243, 230); --wanim-intensity: 8; border-radius: 8px !important; box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 4px 0px !important;">
                              <img src="/web/image/835-6b3f8ebe/UPICard.f3561c9f.png" alt="" class="rounded m-3 img img-fluid o_we_custom_image" loading="eager"/>
                              <p class="o_default_snippet_text">
                                 <span class="h5-fs">
                                 <strong>Transfer to UPI</strong>
                                 </span>
                                 <br/>
                              </p>
                              <p class="o_default_snippet_text" style="margin-bottom: 0px;">We will sell gold &amp; transfer the</p>
                              <p class="o_default_snippet_text" style="margin-bottom: 0px;">money to your selected UPI ID</p>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="pt16 pb16 o_colored_level col-lg-5">
                     <h2>
                        <font style="color: rgb(59, 64, 63);">Withdraw Redeem Instantly</font>
                        <font class="text-gradient" style="background-image: linear-gradient(260deg, rgb(255, 250, 223) 0%, rgb(255, 237, 163) 100%);">​</font>
                        <br/>
                     </h2>
                     <h2>
                        <font style="font-family: Kalam; color: rgb(59, 64, 63);">in Gold or Silver</font>
                        <br/>
                     </h2>
                     <p class="o_default_snippet_text" style="margin-bottom: 0px;">
                        <span class="h5-fs">You're in control, pause/edit your automated&nbsp;savings or withdraw whenever you like</span>
                     </p>
                     <p class="o_default_snippet_text" style="margin-bottom: 0px;">
                        <br/>
                     </p>
                     <p>
                        <a class="btn btn-custom bg-o-color-2" style="border-width: 1px; border-style: solid;" href="" data-bs-original-title="" title="">
                        <span class="h5-fs" style="font-weight: normal;">&nbsp;Request for Gold/Silver Delivery</span>&nbsp;</a>
                        <br/>
                     </p>
                  </div>
               </div>
            </div>
         </section>
         <section class="s_text_image o_colored_level o_cc o_cc2 pt96 pb104" data-snippet="s_image_text" data-name="Text - Image" style="background-color: rgba(0, 0, 0, 0); background-image: linear-gradient(rgb(131, 157, 249) 0%, rgb(180, 133, 255) 100%);">
         <div class="container">
         <div class="row o_grid_mode" data-row-count="3">
         <div class="o_colored_level o_grid_item g-col-lg-12 g-height-3 col-lg-12" style="grid-area: 1 / 1 / 4 / 13; z-index: 1;">
         <h2 style="text-align: center;">
         <font class="text-white">&nbsp;Simplifying Finance</font>
         </h2>
         <p style="text-align: center;">
         <font class="text-white">
         <span class="h5-fs" style="font-family:Kalam;">with Gold+</span>
         </font>
         </p>
         </div>
         </div>
         <div class="row ml4 mr4" data-row-count="-Infinity">
         <div class="pt32 pb32 mb-4 text-center o_colored_level col-lg-5 o_cc o_cc1 rounded" style="border-radius: 12px !important;">
         <img src="/web/image/840-051e784d/Chart.935687e8.webp" alt="" class="m-3 img img-fluid o_we_custom_image o_we_image_cropped rounded" data-mimetype="image/webp" data-original-id="838" data-original-src="/web/image/838-fdf0ab5b/Chart.935687e8.png" data-mimetype-before-conversion="image/png" loading="lazy" style="width: 50% !important;" data-x="28.898387096774194" data-y="51.51451612903226" data-width="741.3064516129033" data-height="641.4854838709678" data-scale-x="1" data-scale-y="1" data-aspect-ratio="0/0"/>
         <h3 class="o_default_snippet_text">Everyday you delay,
         <br/>you lose money
         </h3>
         <p class="o_default_snippet_text">
         <br/>
         </p>
         <p class="o_default_snippet_text">
         <span class="h5-fs">
         <font class="text-o-color-5"></font>
         <a href="https://arihantai.com" data-bs-original-title="" title="">
         <font class="text-o-color-5">
         <u style="color:#D0B4A8">Explore Compound Calculator</u>
         </font>
         </a>
         <font class="text-o-color-5"></font>
         </span>
         </p>
         </div>
         <div class="pt32 pb32 mb-4 text-center o_colored_level col-lg-5 offset-lg-1 o_cc o_cc1 rounded" style="border-radius: 12px !important;">
         <img src="/web/image/842-a22f085f/GullakJar.0267e586.webp" alt="" class="m-3 img img-fluid o_we_custom_image o_we_image_cropped rounded" style="width: 50% !important;" data-mimetype="image/webp" data-original-id="841" data-original-src="/web/image/841-81f69982/GullakJar.0267e586.png" data-mimetype-before-conversion="image/png" data-resize-width="690" loading="lazy"/>
         <h3 class="o_default_snippet_text">It’s 2022, don’t save like&nbsp;
         <br/>the 1980s anymore
         </h3>
         <p class="o_default_snippet_text">
         <br/>
         </p>
         <p class="o_default_snippet_text">
         <span class="h5-fs">
         <font class="text-o-color-5"></font>
         <a href="https://arihantai.com" data-bs-original-title="" title="">
         <font class="text-o-color-5">
         <u style="color:#D0B4A8">Here is What We Mean</u>
         </font>
         </a>
         <font class="text-o-color-5"></font>
         </span>
         </p>
         </div>
         </div>
         </div>
         </section>
         <section class="s_title o_colored_level o_cc o_cc2 pt96 pb72" data-vcss="001" data-snippet="s_title" data-name="Title" style="background-image: none;">
         <div class="s_allow_columns container">
         <h4 class="o_default_snippet_text" style="text-align: center;">
         <font style="color: rgb(59, 64, 63);">
         <span class="h1-fs">Frequently Asked Questions</span>
         </font>
         <br/>
         </h4>
         </div>
         </section>
         <style>
         .accordion {
         display: flex;
         flex-direction: column;
         gap: 10px;
         width: 100%;
         }
         .accordion__item {
         border: 1px solid #f3eae2;
         border-radius: 10px;
         overflow: hidden;
         }
         .accordion__header {
         padding: 20px 25px 6px 25px;
         font-weight: 600;
         cursor: pointer;
         position: relative;
         }
         .accordion__header::after {
         content: '';
         background: url(https://www.svgrepo.com/show/357035/angle-down.svg) no-repeat center;
         width: 20px;
         height: 20px;
         transition: .4s;
         display: inline-block;
         position: absolute;
         right: 20px;
         top: 20px;
         z-index: 1;
         }
         .accordion__header.active {
         background: #f3eae2;
         padding: 20px 25px 20px 25px;
         }
         .accordion__header.active::after {
         transform: rotateX(180deg);
         }
         .accordion__item .accordion__content {
         padding: 0 25px;
         max-height: 0;
         transition: .5s;
         overflow: hidden;
         }
         </style>
         <section class="s_faq_collapse o_cc o_cc2 o_colored_level pb72" data-snippet="s_faq_collapse" data-name="Accordion" style="background-image: none;">
         <div class="container">
         <div class="accordion">
         
         
         
         
         
         
         
         
         
         
         
         
         
         </div>
         </div>
         </section>
         <script>
            document.addEventListener('DOMContentLoaded', () => {
              const togglers = document.querySelectorAll('[data-toggle]');
              
                togglers.forEach((btn) => {
                  btn.addEventListener('click', (e) => {
                     const selector = e.currentTarget.dataset.toggle
                     const block = document.querySelector(`${selector}`);
                    if (e.currentTarget.classList.contains('active')) {
                      block.style.maxHeight = '';
                    } else {
                      block.style.maxHeight = block.scrollHeight + 'px';
                    }
                      
                     e.currentTarget.classList.toggle('active')
                  })
                })
                })
         </script>
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         
         </div>
      
        <div id="o_shared_blocks" class="oe_unremovable"></div>
                </main>
                <footer id="bottom" data-anchor="true" data-name="Footer" class="o_footer o_colored_level o_cc ">
                    <div id="footer" class="oe_structure oe_structure_solo">
            <section class="s_text_block pt40 pb16" data-snippet="s_text_block" data-name="Text">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-2 pt24 pb24">
                            <h5 class="mb-3">Useful Links</h5>
                            <ul class="list-unstyled">
                                <li><a href="/">Home</a></li>
                                <li><a href="#">About us</a></li>
                                <li><a href="#">Products</a></li>
                                <li><a href="#">Services</a></li>
                                <li><a href="#">Legal</a></li>
                                
                                <li><a href="/contactus">Contact us</a></li>
                            </ul>
                        </div>
                        <div class="col-lg-5 pt24 pb24">
                            <h5 class="mb-3">About us</h5>
                            <p>We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.
                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance.</p>
                        </div>
                        <div id="connect" class="col-lg-4 offset-lg-1 pt24 pb24">
                            <h5 class="mb-3">Connect with us</h5>
                            <ul class="list-unstyled">
                                <li><i class="fa fa-comment fa-fw me-2"></i><span><a href="/contactus">Contact us</a></span></li>
                                <li><i class="fa fa-envelope fa-fw me-2"></i><span><a href="mailto:<EMAIL>"><EMAIL></a></span></li>
                                <li><i class="fa fa-phone fa-fw me-2"></i><span class="o_force_ltr"><a href="tel:+1(650)555-0111">+****************</a></span></li>
                            </ul>
                            <div class="s_social_media text-start o_not_editable" data-snippet="s_social_media" data-name="Social Media" contenteditable="false">
                                <h5 class="s_social_media_title d-none" contenteditable="true">Follow us</h5>
                                <a href="/website/social/facebook" class="s_social_media_facebook" target="_blank" aria-label="Facebook">
                                    <i class="fa fa-facebook rounded-circle shadow-sm o_editable_media"></i>
                                </a>
                                <a href="/website/social/twitter" class="s_social_media_twitter" target="_blank" aria-label="Twitter">
                                    <i class="fa fa-twitter rounded-circle shadow-sm o_editable_media"></i>
                                </a>
                                <a href="/website/social/linkedin" class="s_social_media_linkedin" target="_blank" aria-label="LinkedIn">
                                    <i class="fa fa-linkedin rounded-circle shadow-sm o_editable_media"></i>
                                </a>
                                <a href="/" class="text-800" aria-label="Extra page">
                                    <i class="fa fa-home rounded-circle shadow-sm o_editable_media"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    <div class="o_footer_copyright o_colored_level o_cc" data-name="Copyright">
                        <div class="container py-3">
                            <div class="row">
                                <div class="col-sm text-center text-sm-start text-muted">
                                    <span class="o_footer_copyright_name me-2">Copyright &copy; Company name</span>
        
        
    </div>
                                <div class="col-sm text-center text-sm-end o_not_editable">
        <div class="o_brand_promotion">
        Powered by 
            <a target="_blank" class="badge text-bg-light" href="http://www.arihantai.com?utm_source=db&amp;utm_medium=website">
                <img alt="Arihant ERP" src="/web/static/img/odoo_logo_tiny.png" width="62" height="20" style="width: auto; height: 1em; vertical-align: baseline;" loading="lazy"/>
            </a>
        - 
                    The #1 <a target="_blank" href="http://www.arihantai.com/app/ecommerce?utm_source=db&amp;utm_medium=website"> eCommerce</a>
                
        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        
        </body>
</html>