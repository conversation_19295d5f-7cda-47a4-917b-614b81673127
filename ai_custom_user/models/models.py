from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re

class ResUsers(models.Model):
    _inherit = 'res.users'

    # Custom fields
    x_first_name = fields.Char(string='First Name')
    x_middle_name = fields.Char(string='Father Name/ Husband Name')
    x_surname = fields.Char(string='Surname')
    x_district = fields.Many2one('location.district', string='District')
    x_taluka = fields.Many2one('location.taluka', string='Taluka/City')
    x_village = fields.Many2one('location.village', string='Village/Area')
    x_date_of_birth = fields.Date(string='Date of Birth')
    x_age = fields.Char(string='Age')
    x_whatsapp_no = fields.Char(string='WhatsApp Number')
    x_is_whatsapp_no = fields.Boolean(string='WhatsApp No. Same as Mobile No.')

    # Mobile login related fields
    login = fields.Char(string='Mobile Number')  # Override the login field
    country_code = fields.Char(string='Country Code', default='91')

    def _format_mobile_for_whatsapp(self, number, country_code):
        """Format mobile number to WhatsApp API compatible format (E.164)"""
        clean_number = re.sub(r'\D', '', number)
        clean_number = clean_number.lstrip('0')
        
        if not clean_number.startswith(country_code):
            clean_number = f"{country_code}{clean_number}"
            
        return clean_number

    @api.depends('x_first_name', 'x_middle_name', 'x_surname')
    def _compute_full_name(self):
        for record in self:
            full_name = ' '.join(filter(None, [record.x_first_name, record.x_middle_name, record.x_surname]))
            record.name = full_name

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            # Validate password if provided
            if vals.get('password'):
                self._validate_password_strength(vals['password'])

            # Format mobile number
            if vals.get('login'):
                country_code = vals.get('country_code', '91')
                formatted_mobile = self._format_mobile_for_whatsapp(vals['login'], country_code)
                vals['mobile'] = formatted_mobile
                vals['login'] = formatted_mobile
                
                # Ensure email is not overwritten by mobile number
                if not vals.get('email'):
                    vals['email'] = vals.get('email')  # Use a new field to store email

            # Create partner first
            partner_vals = {
                'name': vals.get('name'),
                'mobile': vals.get('mobile'),
                'email': vals.get('email') or vals.get('email_normalized'),
                'x_first_name': vals.get('x_first_name'),
                'x_middle_name': vals.get('x_middle_name'),
                'x_surname': vals.get('x_surname'),
                'x_district': vals.get('x_district'),
                'x_taluka': vals.get('x_taluka'),
                'x_village': vals.get('x_village'),
                'x_date_of_birth': vals.get('x_date_of_birth'),
                'x_age': vals.get('x_age'),
                'x_whatsapp_no': vals.get('x_whatsapp_no'),
                'x_is_whatsapp_no': vals.get('x_is_whatsapp_no'),
            }
            
            partner = self.env['res.partner'].sudo().create(partner_vals)
            vals['partner_id'] = partner.id

        return super(ResUsers, self).create(vals_list)

    def write(self, vals):
        # Validate password if being updated
        if vals.get('password'):
            self._validate_password_strength(vals['password'])

        # Existing write logic remains the same...
        return super(ResUsers, self).write(vals)

    def _validate_password_strength(self, password):
        if len(password) < 8:
            raise ValidationError(_('Password must be at least 8 characters long'))
        if not re.search(r'[@_!#$%^&*()<>?/\|}{~:]', password):
            raise ValidationError(_('Password must contain at least one special character'))

    @api.constrains('login')
    def _check_login(self):
        for user in self:
            if user.login:
                clean_number = re.sub(r'\D', '', user.login)
                
                if not clean_number.isdigit():
                    raise ValidationError(_('Login must contain only numbers as it represents a mobile number.'))
                
                country_code = user.country_code or '91'
                if clean_number.startswith(country_code):
                    number_without_code = clean_number[len(country_code):]
                else:
                    number_without_code = clean_number
                    
                if len(number_without_code) != 10:
                    raise ValidationError(_('Mobile number must be exactly 10 digits (excluding country code).'))

    @api.onchange('login')
    def on_change_login(self):
        # Skip email assignment since login is now used for mobile numbers
        pass

class ResPartner(models.Model):
    _inherit = 'res.partner'

    x_first_name = fields.Char(string='First Name')
    x_middle_name = fields.Char(string='Father Name/ Husband Name')
    x_surname = fields.Char(string='Surname')
    x_district = fields.Many2one('location.district', string='District')
    x_taluka = fields.Many2one('location.taluka', string='Taluka/City')
    x_village = fields.Many2one('location.village', string='Village/Area')
    x_date_of_birth = fields.Date(string='Date of Birth')
    x_age = fields.Char(string='Age')
    x_whatsapp_no = fields.Char(string='WhatsApp Number')
    x_is_whatsapp_no = fields.Boolean(string='WhatsApp No. Same as Mobile No.')

    @api.depends('x_first_name', 'x_middle_name', 'x_surname')
    def _compute_full_name(self):
        for record in self:
            full_name = ' '.join(filter(None, [record.x_first_name, record.x_middle_name, record.x_surname]))
            record.name = full_name
