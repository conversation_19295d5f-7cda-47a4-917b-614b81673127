<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Custom Login Template -->
    <template id="custom_login" inherit_id="web.login" name="Custom Login Form">
        <xpath expr="//form" position="attributes">
            <attribute name="class">oe_login_form o_custom_login_form</attribute>
        </xpath>

        <xpath expr="//div[hasclass('field-login')]" position="replace">
            <div class="form-group field-login">
                <label for="login" class="col-form-label">Mobile Number</label>
                <div class="input-group mb-2">
                    <div class="input-group-prepend">
                        <span class="input-group-text">+<field name="country_code" placeholder="91"/></span>
                    </div>
                    <input type="text" placeholder="Enter 10-digit mobile number" name="login" t-att-value="login" id="login" class="form-control" required="required" autofocus="autofocus"/>
                </div>
            </div>
        </xpath>

        <xpath expr="//div[hasclass('field-password')]" position="after">
            <div class="alert alert-info password-requirements" role="alert">
                <small>
                    <i class="fa fa-info-circle"/> Password Requirements:
                    <ul class="mb-0 mt-1">
                        <li>Minimum 8 characters</li>
                        <li>At least one special character (@, #, $, etc.)</li>
                    </ul>
                </small>
            </div>
        </xpath>
    </template>

    <!-- Custom Signup Template -->
    <template id="custom_signup" inherit_id="auth_signup.signup" name="Custom Signup Form">
        <xpath expr="//div[hasclass('field-login')]" position="replace">
            <div class="form-group field-login">
                <label for="login" class="col-form-label">Mobile Number</label>
                <div class="input-group mb-2">
                    <div class="input-group-prepend">
                        <span class="input-group-text">+<field name="country_code" placeholder="91"/></span>
                    </div>
                    <input type="text" placeholder="Enter 10-digit mobile number" name="login" t-att-value="login" id="login" class="form-control" required="required"/>
                </div>
            </div>
        </xpath>

        <xpath expr="//div[hasclass('field-name')]" position="after">
            <div class="row">
                <div class="col-md-4 form-group">
                    <label for="x_first_name">First Name</label>
                    <input type="text" name="x_first_name" class="form-control" required="required"/>
                </div>
                <div class="col-md-4 form-group">
                    <label for="x_middle_name">Middle Name</label>
                    <input type="text" name="x_middle_name" class="form-control"/>
                </div>
                <div class="col-md-4 form-group">
                    <label for="x_surname">Surname</label>
                    <input type="text" name="x_surname" class="form-control" required="required"/>
                </div>
            </div>
            <div class="form-group">
                <label for="x_whatsapp_no">WhatsApp Number</label>
                <input type="text" name="x_whatsapp_no" class="form-control"/>
                <div class="custom-control custom-checkbox mt-2">
                    <input type="checkbox" class="custom-control-input" id="x_is_whatsapp_no" name="x_is_whatsapp_no"/>
                    <label class="custom-control-label" for="x_is_whatsapp_no">Same as Mobile Number</label>
                </div>
            </div>
        </xpath>
    </template>

    <!-- Assets for Custom Login/Signup -->
    <template id="custom_auth_assets" inherit_id="web.assets_frontend" name="Custom Auth Assets">
        <xpath expr="." position="inside">
            <script type="text/javascript">
                odoo.define('ai_custom_user.auth_customizations', function (require) {
                    'use strict';

                    var publicWidget = require('web.public.widget');

                    publicWidget.registry.CustomAuth = publicWidget.Widget.extend({
                        selector: '.o_custom_login_form, .oe_signup_form',
                        events: {
                            'change #x_is_whatsapp_no': '_onWhatsAppCheckbox',
                            'input input[name="login"]': '_onMobileInput'
                        },

                        _onWhatsAppCheckbox: function (ev) {
                            var $whatsappInput = this.$('input[name="x_whatsapp_no"]');
                            var $mobileInput = this.$('input[name="login"]');
                            if (ev.target.checked) {
                                $whatsappInput.val($mobileInput.val());
                                $whatsappInput.prop('readonly', true);
                            } else {
                                $whatsappInput.prop('readonly', false);
                            }
                        },

                        _onMobileInput: function (ev) {
                            var $input = $(ev.target);
                            var value = $input.val().replace(/\D/g, '');
                            if (value.length > 10) {
                                value = value.substr(0, 10);
                            }
                            $input.val(value);

                            if (this.$('#x_is_whatsapp_no').prop('checked')) {
                                this.$('input[name="x_whatsapp_no"]').val(value);
                            }
                        }
                    });

                    return publicWidget.registry.CustomAuth;
                });
            </script>
            <style>
                .o_custom_login_form .password-requirements {
                    font-size: 0.875rem;
                    background-color: #e9ecef;
                    border-color: #dee2e6;
                }
                .o_custom_login_form .input-group-text {
                    font-size: 0.875rem;
                }
            </style>
        </xpath>
    </template>
</odoo>
