#!/usr/bin/env python3
"""
Fix Portal Template Issue
Resolves the missing portal edit template issue
"""

import os
import sys

def check_template_structure():
    """Check if the portal template structure is correct"""
    
    print("🔍 Checking Portal Template Structure")
    print("=" * 50)
    
    portal_file = '/mnt/extra-addons/ovakil_customer_feedback/views/portal/customer_feedback_main_portal.xml'
    
    if not os.path.exists(portal_file):
        print("❌ Portal file not found!")
        return False
    
    with open(portal_file, 'r') as f:
        content = f.read()
    
    # Check for required templates
    required_templates = [
        'portal_customer_feedback_main_list',
        'portal_customer_feedback_main_detail', 
        'portal_customer_feedback_main_edit'
    ]
    
    missing_templates = []
    for template in required_templates:
        if f'id="{template}"' in content:
            print(f"✅ Template found: {template}")
        else:
            print(f"❌ Template missing: {template}")
            missing_templates.append(template)
    
    if missing_templates:
        print(f"\n❌ Missing templates: {missing_templates}")
        return False
    else:
        print(f"\n✅ All required portal templates found!")
        return True

def check_controller_routes():
    """Check if controller routes are properly defined"""
    
    print("\n🔍 Checking Controller Routes")
    print("=" * 40)
    
    controller_file = '/mnt/extra-addons/ovakil_customer_feedback/controllers/customer_feedback_main_portal.py'
    
    if not os.path.exists(controller_file):
        print("❌ Controller file not found!")
        return False
    
    with open(controller_file, 'r') as f:
        content = f.read()
    
    # Check for required routes
    required_routes = [
        '/my/customer-feedback-main',
        '/my/customer-feedback-main/<int:record_id>',
        '/my/customer-feedback-main/<int:record_id>/edit'
    ]
    
    missing_routes = []
    for route in required_routes:
        if route in content:
            print(f"✅ Route found: {route}")
        else:
            print(f"❌ Route missing: {route}")
            missing_routes.append(route)
    
    if missing_routes:
        print(f"\n❌ Missing routes: {missing_routes}")
        return False
    else:
        print(f"\n✅ All required routes found!")
        return True

def create_module_update_instructions():
    """Create instructions for updating the module"""
    
    instructions = """
🔧 MODULE UPDATE INSTRUCTIONS
=============================

The portal template exists but Odoo can't find it. This is likely a caching issue.

SOLUTION STEPS:

1. **Update the Module:**
   - Go to Apps → Search "ovakil_customer_feedback"
   - Click the module → Click "Upgrade"
   - Wait for the upgrade to complete

2. **Clear Browser Cache:**
   - Press Ctrl+F5 (or Cmd+Shift+R on Mac)
   - Or clear browser cache manually

3. **Alternative - Restart Odoo:**
   - If upgrade doesn't work, restart Odoo service
   - This will clear all caches

4. **Test the Edit Function:**
   - Go to /my/customer-feedback-main
   - Click on a record
   - Click "Edit" button
   - Should now work without errors

TECHNICAL DETAILS:
- Template ID: ovakil_customer_feedback.portal_customer_feedback_main_edit
- Controller: portal_customer_feedback_main_edit
- Route: /my/customer-feedback-main/<int:record_id>/edit
- All components exist, just need cache refresh

If the issue persists after these steps, there might be a deeper 
template inheritance or XML structure issue.
"""
    
    print(instructions)
    
    # Save instructions to file
    with open('/mnt/extra-addons/module_update_instructions.txt', 'w') as f:
        f.write(instructions)
    
    print("📝 Instructions saved to: /mnt/extra-addons/module_update_instructions.txt")

def main():
    """Main function"""
    
    print("🔧 Portal Template Issue Diagnostic")
    print("=" * 60)
    
    # Check template structure
    templates_ok = check_template_structure()
    
    # Check controller routes
    routes_ok = check_controller_routes()
    
    # Create update instructions
    create_module_update_instructions()
    
    if templates_ok and routes_ok:
        print("\n✅ DIAGNOSIS: All components exist!")
        print("🎯 ISSUE: Likely a caching problem")
        print("💡 SOLUTION: Update/upgrade the module in Odoo")
        return True
    else:
        print("\n❌ DIAGNOSIS: Missing components found!")
        print("🎯 ISSUE: Template or controller problems")
        print("💡 SOLUTION: Fix missing components first")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
