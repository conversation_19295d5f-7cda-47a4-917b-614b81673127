<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cash Management Dashboard Action -->
        <record id="action_cash_management_dashboard" model="ir.actions.act_window">
            <field name="name">Cash Management Dashboard</field>
            <field name="res_model">ai.cash.management.cash.collection.simple</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="domain">[]</field>
            <field name="context">{
                'search_default_today': 1,
                'search_default_group_state': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Welcome to Cash Management Dashboard!
                </p>
                <p>
                    Here you can see an overview of all cash collections.
                    Start by creating your first cash collection.
                </p>
            </field>
        </record>

        <!-- Cash Collection Kanban View -->
        <record id="view_cash_collection_simple_kanban" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.simple.kanban</field>
            <field name="model">ai.cash.management.cash.collection.simple</field>
            <field name="arch" type="xml">
                <kanban default_group_by="state" class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="collector_id"/>
                    <field name="customer_name"/>
                    <field name="amount"/>
                    <field name="collection_date"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                        </div>
                                        <div class="o_kanban_record_body">
                                            <div>Customer: <field name="customer_name"/></div>
                                            <div>Collector: <field name="collector_id"/></div>
                                            <div>Amount: <field name="amount" widget="monetary"/></div>
                                            <div>Date: <field name="collection_date" widget="date"/></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Reports Action -->
        <record id="action_cash_management_reports" model="ir.actions.act_window">
            <field name="name">Cash Management Reports</field>
            <field name="res_model">ai.cash.management.cash.collection.simple</field>
            <field name="view_mode">graph,pivot</field>
            <field name="context">{
                'search_default_this_week': 1,
                'search_default_group_collector': 1
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Cash Management Reports
                </p>
                <p>
                    Analyze your cash collection data with graphs and pivot tables.
                </p>
            </field>
        </record>

        <!-- Cash Collection Graph View -->
        <record id="view_cash_collection_simple_graph" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.simple.graph</field>
            <field name="model">ai.cash.management.cash.collection.simple</field>
            <field name="arch" type="xml">
                <graph string="Cash Collections Analysis" type="bar">
                    <field name="collector_id" type="row"/>
                    <field name="amount" type="measure"/>
                </graph>
            </field>
        </record>

        <!-- Cash Collection Pivot View -->
        <record id="view_cash_collection_simple_pivot" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.simple.pivot</field>
            <field name="model">ai.cash.management.cash.collection.simple</field>
            <field name="arch" type="xml">
                <pivot string="Cash Collections Analysis">
                    <field name="collector_id" type="row"/>
                    <field name="collection_date" interval="day" type="col"/>
                    <field name="amount" type="measure"/>
                </pivot>
            </field>
        </record>
    </data>
</odoo>
