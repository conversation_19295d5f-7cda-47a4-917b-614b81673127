<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Branch Cash Submission Tree View -->
        <record id="view_branch_cash_submission_tree" model="ir.ui.view">
            <field name="name">ai.cash.management.branch.cash.submission.tree</field>
            <field name="model">ai.cash.management.branch.cash.submission</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="submission_date"/>
                    <field name="collector_id"/>
                    <field name="branch_id"/>
                    <field name="amount"/>
                    <field name="collection_count"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <!-- Branch Cash Submission Form View -->
        <record id="view_branch_cash_submission_form" model="ir.ui.view">
            <field name="name">ai.cash.management.branch.cash.submission.form</field>
            <field name="model">ai.cash.management.branch.cash.submission</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_submit" type="object" string="Submit to Manager" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_approve" type="object" string="Approve" 
                                class="btn-success" invisible="state != 'submitted'"
                                groups="group_branch_manager"/>
                        <button name="action_reject" type="object" string="Reject" 
                                class="btn-danger" invisible="state != 'submitted'"
                                groups="group_branch_manager"/>
                        <button name="action_reset_to_draft" type="object" string="Reset to Draft" 
                                class="btn-secondary" invisible="state not in ['submitted', 'rejected']"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="collector_id"/>
                                <field name="branch_id"/>
                                <field name="manager_id"/>
                            </group>
                            <group>
                                <field name="submission_date"/>
                                <field name="amount"/>
                                <field name="collection_count"/>
                            </group>
                        </group>
                        <group string="Collection Period">
                            <group>
                                <field name="period_from"/>
                                <field name="period_to"/>
                            </group>
                        </group>
                        <group string="Approval Details" invisible="state in ['draft', 'submitted']">
                            <group>
                                <field name="approved_by"/>
                                <field name="approval_date"/>
                            </group>
                            <group>
                                <field name="rejection_reason" invisible="state != 'rejected'"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Collections">
                                <field name="collection_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="collection_date"/>
                                        <field name="customer_name"/>
                                        <field name="amount"/>
                                        <field name="payment_method"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Notes">
                                <group>
                                    <field name="notes" nolabel="1"/>
                                    <field name="manager_notes" nolabel="1" readonly="1"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Branch Cash Submission Action -->
        <record id="action_branch_cash_submission" model="ir.actions.act_window">
            <field name="name">Branch Cash Submissions</field>
            <field name="res_model">ai.cash.management.branch.cash.submission</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No cash submissions yet!
                </p>
                <p>
                    Create daily cash submissions to submit collected cash to branch managers.
                </p>
            </field>
        </record>
    </data>
</odoo>
