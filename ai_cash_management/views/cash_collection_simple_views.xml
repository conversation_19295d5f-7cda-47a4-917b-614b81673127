<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cash Collection Simple Tree View -->
        <record id="view_cash_collection_simple_tree" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.simple.tree</field>
            <field name="model">ai.cash.management.cash.collection.simple</field>
            <field name="arch" type="xml">
                <tree decoration-info="state == 'draft'" decoration-success="state == 'approved'" decoration-warning="state == 'submitted'">
                    <field name="name"/>
                    <field name="collection_date"/>
                    <field name="collector_id"/>
                    <field name="customer_name"/>
                    <field name="amount"/>
                    <field name="payment_method"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <!-- Cash Collection Simple Form View -->
        <record id="view_cash_collection_simple_form" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.simple.form</field>
            <field name="model">ai.cash.management.cash.collection.simple</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_confirm" type="object" string="Confirm" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_submit" type="object" string="Submit to Manager" 
                                class="btn-info" invisible="state != 'confirmed'"/>
                        <button name="action_approve" type="object" string="Approve" 
                                class="btn-success" invisible="state != 'submitted'"/>
                        <button name="action_cancel" type="object" string="Cancel" 
                                class="btn-secondary" invisible="state in ['approved', 'cancelled']"/>
                        <button name="action_reset_to_draft" type="object" string="Reset to Draft" 
                                class="btn-warning" invisible="state not in ['cancelled', 'submitted']"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="collector_id"/>
                                <field name="collection_date"/>
                            </group>
                            <group>
                                <field name="amount"/>
                                <field name="payment_method"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                        </group>
                        <group string="Customer Information">
                            <group>
                                <field name="customer_name"/>
                                <field name="customer_phone"/>
                            </group>
                            <group>
                                <field name="customer_email"/>
                                <field name="source_reference"/>
                            </group>
                        </group>
                        <group string="Payment Details" invisible="payment_method == 'cash'">
                            <group>
                                <field name="check_number"/>
                                <field name="check_date"/>
                            </group>
                            <group>
                                <field name="bank_name"/>
                            </group>
                        </group>
                        <group string="Additional Information">
                            <field name="description" nolabel="1" placeholder="Description of the collection..."/>
                            <field name="notes" nolabel="1" placeholder="Internal notes..."/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Cash Collection Simple Search View -->
        <record id="view_cash_collection_simple_search" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.simple.search</field>
            <field name="model">ai.cash.management.cash.collection.simple</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="collector_id"/>
                    <field name="customer_name"/>
                    <field name="source_reference"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Confirmed" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter string="Submitted" name="submitted" domain="[('state', '=', 'submitted')]"/>
                    <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                    <separator/>
                    <filter string="Today" name="today" domain="[('collection_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('collection_date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                    <filter string="This Week" name="this_week" domain="[('collection_date', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Collector" name="group_collector" context="{'group_by': 'collector_id'}"/>
                        <filter string="Payment Method" name="group_payment_method" context="{'group_by': 'payment_method'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Collection Date" name="group_date" context="{'group_by': 'collection_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Cash Collection Simple Action -->
        <record id="action_cash_collection_simple" model="ir.actions.act_window">
            <field name="name">Cash Collections</field>
            <field name="res_model">ai.cash.management.cash.collection.simple</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_cash_collection_simple_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Record your first cash collection!
                </p>
                <p>
                    Cash collections track money received from customers by collectors.
                    Create a new collection to get started.
                </p>
            </field>
        </record>
    </data>
</odoo>
