<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cash Collector Simple Tree View -->
        <record id="view_cash_collector_simple_tree" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collector.simple.tree</field>
            <field name="model">ai.cash.management.cash.collector.simple</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="user_id"/>
                    <field name="phone"/>
                    <field name="current_cash_balance"/>
                    <field name="state"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Cash Collector Simple Form View -->
        <record id="view_cash_collector_simple_form" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collector.simple.form</field>
            <field name="model">ai.cash.management.cash.collector.simple</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_activate" type="object" string="Activate" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_suspend" type="object" string="Suspend" 
                                class="btn-warning" invisible="state != 'active'"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="user_id"/>
                            </group>
                            <group>
                                <field name="phone"/>
                                <field name="email"/>
                                <field name="active"/>
                            </group>
                        </group>
                        <group>
                            <group string="Cash Information">
                                <field name="current_cash_balance"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                            <group string="Dates">
                                <field name="start_date"/>
                                <field name="company_id"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Cash Collector Simple Action -->
        <record id="action_cash_collector_simple" model="ir.actions.act_window">
            <field name="name">Cash Collectors</field>
            <field name="res_model">ai.cash.management.cash.collector.simple</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first cash collector!
                </p>
                <p>
                    Cash collectors are responsible for collecting cash payments from customers.
                </p>
            </field>
        </record>
    </data>
</odoo>
