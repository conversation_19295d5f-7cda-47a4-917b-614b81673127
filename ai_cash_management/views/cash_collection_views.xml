<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cash Collection Tree View -->
        <record id="view_cash_collection_tree" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.tree</field>
            <field name="model">ai.cash.management.cash.collection</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="collection_date"/>
                    <field name="collector_id"/>
                    <field name="customer_name"/>
                    <field name="amount"/>
                    <field name="payment_method"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <!-- Cash Collection Form View -->
        <record id="view_cash_collection_form" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collection.form</field>
            <field name="model">ai.cash.management.cash.collection</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_confirm" type="object" string="Confirm" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_cancel" type="object" string="Cancel" 
                                class="btn-secondary" invisible="state not in ['draft', 'confirmed']"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="collector_id"/>
                                <field name="collection_date"/>
                                <field name="branch_id"/>
                            </group>
                            <group>
                                <field name="amount"/>
                                <field name="payment_method"/>
                                <field name="source_model"/>
                                <field name="source_reference"/>
                            </group>
                        </group>
                        <group string="Customer Information">
                            <group>
                                <field name="customer_id"/>
                                <field name="customer_name"/>
                            </group>
                            <group>
                                <field name="customer_phone"/>
                            </group>
                        </group>
                        <group string="Payment Details" invisible="payment_method == 'cash'">
                            <group>
                                <field name="check_number"/>
                                <field name="check_date"/>
                            </group>
                            <group>
                                <field name="bank_name"/>
                            </group>
                        </group>
                        <group string="Notes">
                            <field name="description" nolabel="1"/>
                            <field name="notes" nolabel="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Cash Collection Action -->
        <record id="action_cash_collection" model="ir.actions.act_window">
            <field name="name">Cash Collections</field>
            <field name="res_model">ai.cash.management.cash.collection</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No cash collections yet!
                </p>
                <p>
                    Cash collections are automatically created when cash payments are recorded.
                </p>
            </field>
        </record>
    </data>
</odoo>
