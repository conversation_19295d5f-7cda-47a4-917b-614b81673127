<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cash Collector Tree View -->
        <record id="view_cash_collector_tree" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collector.tree</field>
            <field name="model">ai.cash.management.cash.collector</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="user_id"/>
                    <field name="branch_id"/>
                    <field name="current_cash_balance"/>
                    <field name="state"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Cash Collector Form View -->
        <record id="view_cash_collector_form" model="ir.ui.view">
            <field name="name">ai.cash.management.cash.collector.form</field>
            <field name="model">ai.cash.management.cash.collector</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_activate" type="object" string="Activate" 
                                class="btn-primary" invisible="state != 'draft'"/>
                        <button name="action_suspend" type="object" string="Suspend" 
                                class="btn-warning" invisible="state != 'active'"/>
                        <button name="action_terminate" type="object" string="Terminate" 
                                class="btn-danger" invisible="state in ['terminated', 'draft']"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="user_id"/>
                                <field name="employee_id"/>
                            </group>
                            <group>
                                <field name="branch_id"/>
                                <field name="manager_id"/>
                                <field name="phone"/>
                                <field name="email"/>
                            </group>
                        </group>
                        <group>
                            <group string="Cash Balance">
                                <field name="current_cash_balance"/>
                                <field name="total_collected"/>
                                <field name="total_submitted"/>
                            </group>
                            <group string="Dates">
                                <field name="start_date"/>
                                <field name="end_date"/>
                                <field name="active"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Cash Collector Action -->
        <record id="action_cash_management_collector" model="ir.actions.act_window">
            <field name="name">Cash Collectors</field>
            <field name="res_model">ai.cash.management.cash.collector</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first cash collector!
                </p>
                <p>
                    Cash collectors are responsible for collecting cash payments from customers.
                </p>
            </field>
        </record>

        <!-- Branch Tree View -->
        <record id="view_branch_tree" model="ir.ui.view">
            <field name="name">ai.cash.management.branch.tree</field>
            <field name="model">ai.cash.management.branch</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="manager_id"/>
                    <field name="city"/>
                    <field name="current_cash_balance"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Branch Form View -->
        <record id="view_branch_form" model="ir.ui.view">
            <field name="name">ai.cash.management.branch.form</field>
            <field name="model">ai.cash.management.branch</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="code"/>
                                <field name="manager_id"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="phone"/>
                                <field name="email"/>
                                <field name="current_cash_balance"/>
                            </group>
                        </group>
                        <group string="Address">
                            <group>
                                <field name="street"/>
                                <field name="street2"/>
                                <field name="city"/>
                            </group>
                            <group>
                                <field name="state_id"/>
                                <field name="zip"/>
                                <field name="country_id"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Branch Action -->
        <record id="action_cash_management_branch" model="ir.actions.act_window">
            <field name="name">Branches</field>
            <field name="res_model">ai.cash.management.branch</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first branch!
                </p>
                <p>
                    Branches are locations where cash collectors submit their daily collections.
                </p>
            </field>
        </record>
    </data>
</odoo>
