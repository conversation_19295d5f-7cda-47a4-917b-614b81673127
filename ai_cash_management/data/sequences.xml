<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Cash Collector Sequence -->
        <record id="seq_cash_collector_code" model="ir.sequence">
            <field name="name">Cash Collector Code</field>
            <field name="code">cash.collector.code</field>
            <field name="prefix">COL</field>
            <field name="padding">3</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
        </record>

        <!-- Branch Code Sequence -->
        <record id="seq_cash_branch_code" model="ir.sequence">
            <field name="name">Cash Branch Code</field>
            <field name="code">cash.branch.code</field>
            <field name="prefix">BR</field>
            <field name="padding">3</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
        </record>

        <!-- Cash Collection Sequence -->
        <record id="seq_cash_collection" model="ir.sequence">
            <field name="name">Cash Collection</field>
            <field name="code">cash.collection</field>
            <field name="prefix">CC</field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
        </record>

        <!-- Branch Cash Submission Sequence -->
        <record id="seq_branch_cash_submission" model="ir.sequence">
            <field name="name">Branch Cash Submission</field>
            <field name="code">branch.cash.submission</field>
            <field name="prefix">BCS</field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
        </record>
    </data>
</odoo>
