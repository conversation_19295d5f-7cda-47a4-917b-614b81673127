from odoo import api, fields, models, _


class HeadOfficeCashTransfer(models.Model):
    _name = 'ai.cash.management.head.office.cash.transfer'
    _description = 'Head Office Cash Transfer'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'transfer_date desc, name desc'

    name = fields.Char(string='Transfer Reference', required=True, copy=False, default='New', tracking=True)
    branch_id = fields.Many2one('ai.cash.management.branch', string='Branch', required=True, tracking=True)
    transfer_date = fields.Datetime(string='Transfer Date', default=fields.Datetime.now, required=True, tracking=True)
    amount = fields.Monetary(string='Amount', required=True, tracking=True, currency_field='currency_id')
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('head.office.cash.transfer') or 'HOT000'
        return super().create(vals)
