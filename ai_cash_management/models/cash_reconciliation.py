from odoo import api, fields, models, _


class CashReconciliation(models.Model):
    _name = 'ai.cash.management.cash.reconciliation'
    _description = 'Cash Reconciliation'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Reconciliation Reference', required=True, copy=False, default='New', tracking=True)
    reconciliation_date = fields.Date(string='Reconciliation Date', default=fields.Date.context_today, required=True, tracking=True)
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('reconciled', 'Reconciled'),
    ], string='Status', default='draft', tracking=True)

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('cash.reconciliation') or 'CR000'
        return super().create(vals)
