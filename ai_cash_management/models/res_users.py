from odoo import api, fields, models


class ResUsers(models.Model):
    _inherit = 'res.users'

    cash_collector_id = fields.Many2one('ai.cash.management.cash.collector', string='Cash Collector Record')
    is_cash_collector = fields.Boolean(string='Is Cash Collector', compute='_compute_is_cash_collector')

    @api.depends('cash_collector_id')
    def _compute_is_cash_collector(self):
        for user in self:
            user.is_cash_collector = bool(user.cash_collector_id)
