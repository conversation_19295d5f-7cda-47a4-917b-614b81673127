from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class CashCollectorSimple(models.Model):
    _name = 'ai.cash.management.cash.collector.simple'
    _description = 'Cash Collector (Simple)'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(string='Collector Name', required=True, tracking=True)
    code = fields.Char(string='Collector Code', required=True, copy=False, tracking=True)
    user_id = fields.Many2one('res.users', string='User', required=True, tracking=True)
    
    # Contact Information
    phone = fields.Char(string='Phone', tracking=True)
    email = fields.Char(string='Email', related='user_id.email', store=True)
    
    # Status
    active = fields.Boolean(string='Active', default=True, tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
    ], string='Status', default='draft', tracking=True)
    
    # Cash Balance (simplified)
    current_cash_balance = fields.Monetary(
        string='Current Cash Balance', 
        default=0.0,
        currency_field='currency_id'
    )
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)
    
    # Dates
    start_date = fields.Date(string='Start Date', default=fields.Date.context_today, tracking=True)

    @api.model
    def create(self, vals):
        if not vals.get('code'):
            vals['code'] = self.env['ir.sequence'].next_by_code('cash.collector.simple') or f"COL{self.env['ai.cash.management.cash.collector.simple'].search_count([]) + 1:03d}"
        return super().create(vals)

    def action_activate(self):
        self.write({'state': 'active'})
        self.message_post(body=_("Collector activated"))

    def action_suspend(self):
        self.write({'state': 'suspended'})
        self.message_post(body=_("Collector suspended"))

    @api.constrains('user_id')
    def _check_unique_user(self):
        for collector in self:
            if self.search_count([('user_id', '=', collector.user_id.id), ('id', '!=', collector.id)]) > 0:
                raise ValidationError(_("A collector already exists for this user."))
