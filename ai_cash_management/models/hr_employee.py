from odoo import api, fields, models


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    cash_collector_id = fields.Many2one('ai.cash.management.cash.collector', string='Cash Collector Record')
    is_cash_collector = fields.Boolean(string='Is Cash Collector', compute='_compute_is_cash_collector')

    @api.depends('cash_collector_id')
    def _compute_is_cash_collector(self):
        for employee in self:
            employee.is_cash_collector = bool(employee.cash_collector_id)
