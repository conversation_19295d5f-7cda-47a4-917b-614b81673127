from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError


class CashCollection(models.Model):
    _name = 'ai.cash.management.cash.collection'
    _description = 'Cash Collection Record'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'collection_date desc, name desc'

    name = fields.Char(string='Collection Reference', required=True, copy=False, default='New', tracking=True)
    collector_id = fields.Many2one('ai.cash.management.cash.collector', string='Collector', required=True, tracking=True)
    collection_date = fields.Datetime(string='Collection Date', default=fields.Datetime.now, required=True, tracking=True)
    
    # Source Information
    source_model = fields.Char(string='Source Model', required=True, help='Model from which cash was collected')
    source_record_id = fields.Integer(string='Source Record ID', required=True, help='ID of the source record')
    source_reference = fields.Char(string='Source Reference', help='Reference from source record')
    
    # Customer Information
    customer_id = fields.Many2one('res.partner', string='Customer', tracking=True)
    customer_name = fields.Char(string='Customer Name', tracking=True)
    customer_phone = fields.Char(string='Customer Phone')
    
    # Amount and Payment Details
    amount = fields.Monetary(string='Amount Collected', required=True, tracking=True, currency_field='currency_id')
    payment_method = fields.Selection([
        ('cash', 'Cash'),
        ('check', 'Check'),
        ('demand_draft', 'Demand Draft'),
    ], string='Payment Method', default='cash', required=True, tracking=True)
    
    # Check/DD Details
    check_number = fields.Char(string='Check/DD Number')
    check_date = fields.Date(string='Check/DD Date')
    bank_name = fields.Char(string='Bank Name')
    
    # Status and Workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('submitted', 'Submitted to Branch'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Notes and Description
    description = fields.Text(string='Description')
    notes = fields.Text(string='Internal Notes')
    
    # Relations
    branch_id = fields.Many2one('ai.cash.management.branch', string='Branch', related='collector_id.branch_id', store=True)
    submission_id = fields.Many2one('ai.cash.management.branch.cash.submission', string='Branch Submission')
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)
    
    # Accounting Integration
    payment_id = fields.Many2one('account.payment', string='Related Payment', readonly=True)
    journal_entry_id = fields.Many2one('account.move', string='Journal Entry', readonly=True)

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('cash.collection') or 'CC000'
        return super().create(vals)

    def action_confirm(self):
        """Confirm the cash collection"""
        if self.amount <= 0:
            raise ValidationError(_("Amount must be greater than zero."))
        
        self.write({'state': 'confirmed'})
        self.message_post(body=_("Cash collection confirmed"))
        
        # Create accounting entry
        self._create_accounting_entry()

    def action_cancel(self):
        """Cancel the cash collection"""
        if self.state == 'submitted':
            raise ValidationError(_("Cannot cancel a collection that has been submitted to branch."))
        
        self.write({'state': 'cancelled'})
        self.message_post(body=_("Cash collection cancelled"))
        
        # Reverse accounting entry if exists
        if self.journal_entry_id:
            self.journal_entry_id.button_cancel()

    def action_submit_to_branch(self):
        """Submit cash collection to branch (part of daily submission)"""
        if self.state != 'confirmed':
            raise ValidationError(_("Only confirmed collections can be submitted to branch."))
        
        self.write({'state': 'submitted'})
        self.message_post(body=_("Cash collection submitted to branch"))

    def _create_accounting_entry(self):
        """Create accounting entry for cash collection"""
        if self.journal_entry_id:
            return  # Already created
        
        # Get cash journal
        cash_journal = self.env['account.journal'].search([
            ('type', '=', 'cash'),
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        
        if not cash_journal:
            raise UserError(_("No cash journal found. Please configure a cash journal first."))
        
        # Get accounts
        cash_account = cash_journal.default_account_id
        if not cash_account:
            raise UserError(_("Cash journal must have a default account configured."))
        
        # Get income account (or use a default one)
        income_account = self._get_income_account()
        
        # Create journal entry
        move_vals = {
            'journal_id': cash_journal.id,
            'date': self.collection_date.date(),
            'ref': f"Cash Collection: {self.name}",
            'line_ids': [
                (0, 0, {
                    'name': f"Cash collected from {self.customer_name or 'Customer'}",
                    'account_id': cash_account.id,
                    'debit': self.amount,
                    'credit': 0,
                    'partner_id': self.customer_id.id if self.customer_id else False,
                }),
                (0, 0, {
                    'name': f"Income from {self.source_reference or 'Service'}",
                    'account_id': income_account.id,
                    'debit': 0,
                    'credit': self.amount,
                    'partner_id': self.customer_id.id if self.customer_id else False,
                }),
            ],
        }
        
        journal_entry = self.env['account.move'].create(move_vals)
        journal_entry.action_post()
        
        self.journal_entry_id = journal_entry.id

    def _get_income_account(self):
        """Get income account for the collection"""
        # Try to get income account from company
        income_account = self.env['account.account'].search([
            ('account_type', '=', 'income'),
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        
        if not income_account:
            # Create a basic income account if none exists
            income_account = self.env['account.account'].create({
                'name': 'Cash Collection Income',
                'code': '400002',
                'account_type': 'income',
                'company_id': self.company_id.id,
            })
        
        return income_account

    @api.model
    def create_from_payment(self, payment_data):
        """Create cash collection from external payment (called by other modules)"""
        vals = {
            'collector_id': payment_data.get('collector_id'),
            'source_model': payment_data.get('source_model'),
            'source_record_id': payment_data.get('source_record_id'),
            'source_reference': payment_data.get('source_reference'),
            'customer_id': payment_data.get('customer_id'),
            'customer_name': payment_data.get('customer_name'),
            'customer_phone': payment_data.get('customer_phone'),
            'amount': payment_data.get('amount'),
            'payment_method': payment_data.get('payment_method', 'cash'),
            'description': payment_data.get('description'),
            'payment_id': payment_data.get('payment_id'),
        }
        
        collection = self.create(vals)
        collection.action_confirm()
        
        return collection
