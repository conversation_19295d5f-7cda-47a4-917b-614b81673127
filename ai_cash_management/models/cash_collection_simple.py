from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class CashCollectionSimple(models.Model):
    _name = 'ai.cash.management.cash.collection.simple'
    _description = 'Cash Collection Record (Simple)'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'collection_date desc, name desc'

    name = fields.Char(string='Collection Reference', required=True, copy=False, default='New', tracking=True)
    collector_id = fields.Many2one('ai.cash.management.cash.collector.simple', string='Collector', required=True, tracking=True)
    collection_date = fields.Datetime(string='Collection Date', default=fields.Datetime.now, required=True, tracking=True)
    
    # Customer Information
    customer_name = fields.Char(string='Customer Name', required=True, tracking=True)
    customer_phone = fields.Char(string='Customer Phone')
    customer_email = fields.Char(string='Customer Email')
    
    # Amount and Payment Details
    amount = fields.Monetary(string='Amount Collected', required=True, tracking=True, currency_field='currency_id')
    payment_method = fields.Selection([
        ('cash', 'Cash'),
        ('check', 'Check'),
        ('bank_transfer', 'Bank Transfer'),
    ], string='Payment Method', default='cash', required=True, tracking=True)
    
    # Check Details (for non-cash payments)
    check_number = fields.Char(string='Check Number')
    check_date = fields.Date(string='Check Date')
    bank_name = fields.Char(string='Bank Name')
    
    # Status and Workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('submitted', 'Submitted to Manager'),
        ('approved', 'Approved'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Source Information
    source_reference = fields.Char(string='Source Reference', help='Reference from source document')
    description = fields.Text(string='Description')
    notes = fields.Text(string='Internal Notes')
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('cash.collection.simple') or f"CC{self.env['ai.cash.management.cash.collection.simple'].search_count([]) + 1:04d}"
        return super().create(vals)

    def action_confirm(self):
        """Confirm the cash collection"""
        if self.amount <= 0:
            raise ValidationError(_("Amount must be greater than zero."))
        
        self.write({'state': 'confirmed'})
        self.message_post(body=_("Cash collection confirmed"))

    def action_submit(self):
        """Submit cash collection to manager"""
        if self.state != 'confirmed':
            raise ValidationError(_("Only confirmed collections can be submitted."))
        
        self.write({'state': 'submitted'})
        self.message_post(body=_("Cash collection submitted to manager"))

    def action_approve(self):
        """Approve the cash collection"""
        if self.state != 'submitted':
            raise ValidationError(_("Only submitted collections can be approved."))
        
        self.write({'state': 'approved'})
        self.message_post(body=_("Cash collection approved"))

    def action_cancel(self):
        """Cancel the cash collection"""
        if self.state in ['approved']:
            raise ValidationError(_("Cannot cancel approved collections."))
        
        self.write({'state': 'cancelled'})
        self.message_post(body=_("Cash collection cancelled"))

    def action_reset_to_draft(self):
        """Reset to draft state"""
        self.write({'state': 'draft'})
        self.message_post(body=_("Cash collection reset to draft"))

    @api.constrains('amount')
    def _check_amount(self):
        for record in self:
            if record.amount <= 0:
                raise ValidationError(_("Collection amount must be greater than zero."))
