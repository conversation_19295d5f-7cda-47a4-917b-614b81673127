from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError


class BranchCashSubmission(models.Model):
    _name = 'ai.cash.management.branch.cash.submission'
    _description = 'Branch Cash Submission'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'submission_date desc, name desc'

    name = fields.Char(string='Submission Reference', required=True, copy=False, default='New', tracking=True)
    collector_id = fields.Many2one('ai.cash.management.cash.collector', string='Collector', required=True, tracking=True)
    branch_id = fields.Many2one('ai.cash.management.branch', string='Branch', related='collector_id.branch_id', store=True)
    manager_id = fields.Many2one('res.users', string='Branch Manager', related='branch_id.manager_id', store=True)
    
    # Submission Details
    submission_date = fields.Datetime(string='Submission Date', default=fields.Datetime.now, required=True, tracking=True)
    amount = fields.Monetary(string='Total Amount', required=True, tracking=True, currency_field='currency_id')
    
    # Collection Period
    period_from = fields.Date(string='Period From', required=True, tracking=True)
    period_to = fields.Date(string='Period To', required=True, tracking=True)
    
    # Status and Workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved by Manager'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)
    
    # Approval Details
    approved_by = fields.Many2one('res.users', string='Approved By', readonly=True, tracking=True)
    approval_date = fields.Datetime(string='Approval Date', readonly=True, tracking=True)
    rejection_reason = fields.Text(string='Rejection Reason', tracking=True)
    
    # Collections
    collection_ids = fields.One2many('ai.cash.management.cash.collection', 'submission_id', string='Collections')
    collection_count = fields.Integer(string='Number of Collections', compute='_compute_collection_count')
    
    # Notes
    notes = fields.Text(string='Notes')
    manager_notes = fields.Text(string='Manager Notes', tracking=True)
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)
    
    # Accounting
    journal_entry_id = fields.Many2one('account.move', string='Journal Entry', readonly=True)

    @api.depends('collection_ids')
    def _compute_collection_count(self):
        for submission in self:
            submission.collection_count = len(submission.collection_ids)

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('branch.cash.submission') or 'BCS000'
        return super().create(vals)

    def action_submit(self):
        """Submit cash to branch manager for approval"""
        if not self.collection_ids:
            raise ValidationError(_("Cannot submit without any collections."))
        
        # Validate all collections are confirmed
        unconfirmed = self.collection_ids.filtered(lambda c: c.state != 'confirmed')
        if unconfirmed:
            raise ValidationError(_("All collections must be confirmed before submission."))
        
        # Calculate total amount
        total_amount = sum(self.collection_ids.mapped('amount'))
        if abs(self.amount - total_amount) > 0.01:  # Allow small rounding differences
            raise ValidationError(_("Submission amount does not match total of collections."))
        
        # Update collections state
        self.collection_ids.write({'state': 'submitted', 'submission_id': self.id})
        
        self.write({'state': 'submitted'})
        self.message_post(body=_("Cash submission sent for manager approval"))
        
        # Notify branch manager
        self._notify_branch_manager()

    def action_approve(self):
        """Approve cash submission (Branch Manager action)"""
        if self.env.user != self.manager_id:
            raise ValidationError(_("Only the branch manager can approve this submission."))
        
        self.write({
            'state': 'approved',
            'approved_by': self.env.user.id,
            'approval_date': fields.Datetime.now(),
        })
        
        self.message_post(body=_("Cash submission approved by branch manager"))
        
        # Create accounting entry for branch cash
        self._create_branch_accounting_entry()

    def action_reject(self):
        """Reject cash submission (Branch Manager action)"""
        if self.env.user != self.manager_id:
            raise ValidationError(_("Only the branch manager can reject this submission."))
        
        if not self.rejection_reason:
            raise ValidationError(_("Please provide a rejection reason."))
        
        # Reset collections to confirmed state
        self.collection_ids.write({'state': 'confirmed', 'submission_id': False})
        
        self.write({'state': 'rejected'})
        self.message_post(body=_("Cash submission rejected: %s") % self.rejection_reason)

    def action_reset_to_draft(self):
        """Reset submission to draft"""
        if self.state == 'approved':
            raise ValidationError(_("Cannot reset approved submissions."))
        
        # Reset collections
        self.collection_ids.write({'state': 'confirmed', 'submission_id': False})
        
        self.write({'state': 'draft'})
        self.message_post(body=_("Cash submission reset to draft"))

    def _notify_branch_manager(self):
        """Notify branch manager about pending approval"""
        if self.manager_id:
            self.activity_schedule(
                'mail.mail_activity_data_todo',
                user_id=self.manager_id.id,
                summary=_("Cash Submission Approval Required"),
                note=_("Cash submission %s from %s requires your approval. Amount: %s") % (
                    self.name, self.collector_id.name, self.amount
                )
            )

    def _create_branch_accounting_entry(self):
        """Create accounting entry when cash is approved at branch level"""
        if self.journal_entry_id:
            return  # Already created
        
        # Get cash journal
        cash_journal = self.env['account.journal'].search([
            ('type', '=', 'cash'),
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        
        if not cash_journal:
            raise UserError(_("No cash journal found."))
        
        # Get or create branch cash account
        branch_cash_account = self._get_branch_cash_account()
        collector_cash_account = cash_journal.default_account_id
        
        # Create journal entry to transfer from collector to branch
        move_vals = {
            'journal_id': cash_journal.id,
            'date': self.approval_date.date(),
            'ref': f"Branch Cash Submission: {self.name}",
            'line_ids': [
                (0, 0, {
                    'name': f"Cash received from {self.collector_id.name}",
                    'account_id': branch_cash_account.id,
                    'debit': self.amount,
                    'credit': 0,
                }),
                (0, 0, {
                    'name': f"Cash transferred by {self.collector_id.name}",
                    'account_id': collector_cash_account.id,
                    'debit': 0,
                    'credit': self.amount,
                }),
            ],
        }
        
        journal_entry = self.env['account.move'].create(move_vals)
        journal_entry.action_post()
        
        self.journal_entry_id = journal_entry.id

    def _get_branch_cash_account(self):
        """Get or create branch cash account"""
        account_code = f"101{self.branch_id.code}"
        branch_cash_account = self.env['account.account'].search([
            ('code', '=', account_code),
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        
        if not branch_cash_account:
            branch_cash_account = self.env['account.account'].create({
                'name': f'Branch Cash - {self.branch_id.name}',
                'code': account_code,
                'account_type': 'asset_cash',
                'company_id': self.company_id.id,
            })
        
        return branch_cash_account

    @api.model
    def create_daily_submission(self, collector_id, period_from, period_to):
        """Create daily submission for a collector"""
        collector = self.env['ai.cash.management.cash.collector'].browse(collector_id)
        
        # Get confirmed collections for the period
        collections = self.env['ai.cash.management.cash.collection'].search([
            ('collector_id', '=', collector_id),
            ('state', '=', 'confirmed'),
            ('collection_date', '>=', period_from),
            ('collection_date', '<=', period_to),
            ('submission_id', '=', False),
        ])
        
        if not collections:
            raise ValidationError(_("No confirmed collections found for the specified period."))
        
        total_amount = sum(collections.mapped('amount'))
        
        submission = self.create({
            'collector_id': collector_id,
            'amount': total_amount,
            'period_from': period_from,
            'period_to': period_to,
            'collection_ids': [(6, 0, collections.ids)],
        })
        
        return submission
