from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class CashCollector(models.Model):
    _name = 'ai.cash.management.cash.collector'
    _description = 'Cash Collector'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(string='Collector Name', required=True, tracking=True)
    code = fields.Char(string='Collector Code', required=True, copy=False, tracking=True)
    user_id = fields.Many2one('res.users', string='User', required=True, tracking=True)
    employee_id = fields.Many2one('hr.employee', string='Employee', tracking=True)
    branch_id = fields.Many2one('ai.cash.management.branch', string='Branch', required=True, tracking=True)
    manager_id = fields.Many2one('res.users', string='Branch Manager', related='branch_id.manager_id', store=True)
    
    # Contact Information
    phone = fields.Char(string='Phone', tracking=True)
    email = fields.Char(string='Email', related='user_id.email', store=True)
    
    # Status and Configuration
    active = fields.Boolean(string='Active', default=True, tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('terminated', 'Terminated'),
    ], string='Status', default='draft', tracking=True)
    
    # Cash Balance Tracking
    current_cash_balance = fields.Monetary(
        string='Current Cash Balance', 
        compute='_compute_cash_balance', 
        store=True,
        currency_field='currency_id'
    )
    total_collected = fields.Monetary(
        string='Total Collected', 
        compute='_compute_cash_totals', 
        store=True,
        currency_field='currency_id'
    )
    total_submitted = fields.Monetary(
        string='Total Submitted', 
        compute='_compute_cash_totals', 
        store=True,
        currency_field='currency_id'
    )
    
    # Relations
    collection_ids = fields.One2many('ai.cash.management.cash.collection', 'collector_id', string='Collections')
    submission_ids = fields.One2many('ai.cash.management.branch.cash.submission', 'collector_id', string='Submissions')
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)
    
    # Dates
    start_date = fields.Date(string='Start Date', default=fields.Date.context_today, tracking=True)
    end_date = fields.Date(string='End Date', tracking=True)

    @api.depends('collection_ids.amount', 'collection_ids.state')
    def _compute_cash_totals(self):
        for collector in self:
            confirmed_collections = collector.collection_ids.filtered(lambda c: c.state == 'confirmed')
            collector.total_collected = sum(confirmed_collections.mapped('amount'))
            
            approved_submissions = collector.submission_ids.filtered(lambda s: s.state == 'approved')
            collector.total_submitted = sum(approved_submissions.mapped('amount'))

    @api.depends('total_collected', 'total_submitted')
    def _compute_cash_balance(self):
        for collector in self:
            collector.current_cash_balance = collector.total_collected - collector.total_submitted

    @api.model
    def create(self, vals):
        if not vals.get('code'):
            vals['code'] = self.env['ir.sequence'].next_by_code('cash.collector.code') or 'COL000'
        return super().create(vals)

    def action_activate(self):
        self.write({'state': 'active'})
        self.message_post(body=_("Collector activated"))

    def action_suspend(self):
        self.write({'state': 'suspended'})
        self.message_post(body=_("Collector suspended"))

    def action_terminate(self):
        if self.current_cash_balance > 0:
            raise ValidationError(_("Cannot terminate collector with pending cash balance. Please submit all cash first."))
        self.write({'state': 'terminated', 'active': False})
        self.message_post(body=_("Collector terminated"))

    @api.constrains('user_id')
    def _check_unique_user(self):
        for collector in self:
            if self.search_count([('user_id', '=', collector.user_id.id), ('id', '!=', collector.id)]) > 0:
                raise ValidationError(_("A collector already exists for this user."))


class Branch(models.Model):
    _name = 'ai.cash.management.branch'
    _description = 'Cash Management Branch'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(string='Branch Name', required=True, tracking=True)
    code = fields.Char(string='Branch Code', required=True, copy=False, tracking=True)
    manager_id = fields.Many2one('res.users', string='Branch Manager', required=True, tracking=True)
    
    # Address Information
    street = fields.Char(string='Street')
    street2 = fields.Char(string='Street2')
    city = fields.Char(string='City')
    state_id = fields.Many2one('res.country.state', string='State')
    zip = fields.Char(string='ZIP')
    country_id = fields.Many2one('res.country', string='Country')
    
    # Contact Information
    phone = fields.Char(string='Phone')
    email = fields.Char(string='Email')
    
    # Status
    active = fields.Boolean(string='Active', default=True, tracking=True)
    
    # Relations
    collector_ids = fields.One2many('ai.cash.management.cash.collector', 'branch_id', string='Collectors')
    cash_submission_ids = fields.One2many('ai.cash.management.branch.cash.submission', 'branch_id', string='Cash Submissions')
    
    # Cash Balance
    current_cash_balance = fields.Monetary(
        string='Current Cash Balance', 
        compute='_compute_branch_cash_balance', 
        store=True,
        currency_field='currency_id'
    )
    
    # Company and Currency
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency', related='company_id.currency_id', store=True)

    @api.depends('cash_submission_ids.amount', 'cash_submission_ids.state')
    def _compute_branch_cash_balance(self):
        for branch in self:
            approved_submissions = branch.cash_submission_ids.filtered(lambda s: s.state == 'approved')
            total_received = sum(approved_submissions.mapped('amount'))
            
            # Subtract cash transferred to head office
            head_office_transfers = self.env['ai.cash.management.head.office.cash.transfer'].search([
                ('branch_id', '=', branch.id),
                ('state', '=', 'confirmed')
            ])
            total_transferred = sum(head_office_transfers.mapped('amount'))
            
            branch.current_cash_balance = total_received - total_transferred

    @api.model
    def create(self, vals):
        if not vals.get('code'):
            vals['code'] = self.env['ir.sequence'].next_by_code('cash.branch.code') or 'BR000'
        return super().create(vals)
