<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Cash Management Categories -->
        <record id="module_category_cash_management" model="ir.module.category">
            <field name="name">Cash Management</field>
            <field name="description">Manage cash collections, branches, and reconciliation</field>
            <field name="sequence">10</field>
        </record>

        <!-- Cash Collector Group -->
        <record id="group_cash_collector" model="res.groups">
            <field name="name">Cash Collector</field>
            <field name="category_id" ref="module_category_cash_management"/>
            <field name="comment">Can record cash collections and view own collections</field>
        </record>

        <!-- Branch Manager Group -->
        <record id="group_branch_manager" model="res.groups">
            <field name="name">Branch Manager</field>
            <field name="category_id" ref="module_category_cash_management"/>
            <field name="implied_ids" eval="[(4, ref('group_cash_collector'))]"/>
            <field name="comment">Can approve cash submissions and manage branch cash</field>
        </record>

        <!-- Head Office Manager Group -->
        <record id="group_head_office_manager" model="res.groups">
            <field name="name">Head Office Manager</field>
            <field name="category_id" ref="module_category_cash_management"/>
            <field name="implied_ids" eval="[(4, ref('group_branch_manager'))]"/>
            <field name="comment">Can manage all cash operations and reconciliation</field>
        </record>

        <!-- Cash Management Admin Group -->
        <record id="group_cash_management_admin" model="res.groups">
            <field name="name">Cash Management Administrator</field>
            <field name="category_id" ref="module_category_cash_management"/>
            <field name="implied_ids" eval="[(4, ref('group_head_office_manager'))]"/>
            <field name="comment">Full access to cash management system</field>
        </record>


    </data>
</odoo>
