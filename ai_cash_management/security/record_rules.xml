<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Record Rules -->
        
        <!-- Cash Collectors can only see their own collections -->
        <record id="rule_cash_collection_collector" model="ir.rule">
            <field name="name">Cash Collection: Collector Access</field>
            <field name="model_id" ref="model_ai_cash_management_cash_collection"/>
            <field name="domain_force">[('collector_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_cash_collector'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- Branch Managers can see collections in their branch -->
        <record id="rule_cash_collection_branch_manager" model="ir.rule">
            <field name="name">Cash Collection: Branch Manager Access</field>
            <field name="model_id" ref="model_ai_cash_management_cash_collection"/>
            <field name="domain_force">[('collector_id.branch_id.manager_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_branch_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Head Office Managers can see all collections -->
        <record id="rule_cash_collection_head_office" model="ir.rule">
            <field name="name">Cash Collection: Head Office Access</field>
            <field name="model_id" ref="model_ai_cash_management_cash_collection"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('group_head_office_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Cash Collector Rules -->
        <record id="rule_cash_collector_own" model="ir.rule">
            <field name="name">Cash Collector: Own Record</field>
            <field name="model_id" ref="model_ai_cash_management_cash_collector"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_cash_collector'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- Branch Cash Submission Rules -->
        <record id="rule_branch_submission_collector" model="ir.rule">
            <field name="name">Branch Submission: Collector Access</field>
            <field name="model_id" ref="model_ai_cash_management_branch_cash_submission"/>
            <field name="domain_force">[('collector_id.user_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_cash_collector'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="rule_branch_submission_manager" model="ir.rule">
            <field name="name">Branch Submission: Manager Access</field>
            <field name="model_id" ref="model_ai_cash_management_branch_cash_submission"/>
            <field name="domain_force">[('branch_id.manager_id', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('group_branch_manager'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
    </data>
</odoo>
