#!/usr/bin/env python3
"""
Odoo XML-RPC Configuration
=========================

Configuration file for Odoo Module Manager.
Replace the values below with your actual Odoo server credentials.
"""

# Odoo Server Configuration
ODOO_CONFIG = {
    # Server URL (without trailing slash)
    'url': 'https://oneclickvakil.com',
    
    # Database name
    'db': 'oneclickvakil.com',
    
    # Authentication credentials
    'username': 'admin',  # Replace with your username
    'password': 'your_password_here',  # Replace with your password
}

# Alternative configurations for different environments
CONFIGS = {
    'production': {
        'url': 'https://oneclickvakil.com',
        'db': 'oneclickvakil.com',
        'username': 'admin',
        'password': 'your_production_password',
    },
    
    'staging': {
        'url': 'https://staging.oneclickvakil.com',
        'db': 'staging_db',
        'username': 'admin',
        'password': 'your_staging_password',
    },
    
    'local': {
        'url': 'http://localhost:8069',
        'db': 'local_db',
        'username': 'admin',
        'password': 'admin',
    }
}

def get_config(environment='production'):
    """
    Get configuration for specific environment
    
    Args:
        environment (str): Environment name ('production', 'staging', 'local')
    
    Returns:
        dict: Configuration dictionary
    """
    return CONFIGS.get(environment, CONFIGS['production'])
