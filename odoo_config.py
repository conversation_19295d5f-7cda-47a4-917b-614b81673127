#!/usr/bin/env python3
"""
Odoo XML-RPC Configuration
=========================

Configuration file for Odoo Module Manager.
Replace the values below with your actual Odoo server credentials.
"""

# Odoo Server Configuration
ODOO_CONFIG = {
    # Server URL (without trailing slash)
    'url': 'https://oneclickvakil.com',
    
    # Database name
    'db': 'oneclickvakil.com',
    
    # Authentication credentials
    'username': '<EMAIL>',  # Replace with your username
    'password': 'tY5hd!28',  # Replace with your password
}

# Alternative configurations for different environments
CONFIGS = {
    'production': {
        'url': 'https://oneclickvakil.com',
        'db': 'oneclickvakil.com',
        'username': '<EMAIL>',
        'password': 'tY5hd!28',
    },
    
    'staging': {
        'url': 'https://oneclickvakil.com',
        'db': 'oneclickvakil.com',
        'username': '<EMAIL>',
        'password': 'tY5hd!28',
    },
    
    'local': {
        'url': 'https://oneclickvakil.com',
        'db': 'oneclickvakil.com',
        'username': '<EMAIL>',
        'password': 'tY5hd!28',
    }
}

def get_config(environment='production'):
    """
    Get configuration for specific environment
    
    Args:
        environment (str): Environment name ('production', 'staging', 'local')
    
    Returns:
        dict: Configuration dictionary
    """
    return CONFIGS.get(environment, CONFIGS['production'])
