# AI Legal Document System - Quick Reference Guide

## Module Overview

| Module | Purpose | Key Models | Dependencies |
|--------|---------|------------|--------------|
| **ai_legal_document_core** | Core service management | LegalDocumentService, LegalDocumentRequest | base, mail, sale, portal, legal_case_management |
| **ai_chatbot_integration** | AI conversation handling | AIChatbotConfig, AIChatbotConversation | ai_legal_document_core |
| **ai_dynamic_forms** | Dynamic form generation | AISurveyBridge | ai_legal_document_core, ai_chatbot_integration, survey |
| **ai_document_generation** | Document creation & review | DocumentGeneration, DocumentReview | ai_legal_document_core, ai_chatbot_integration, ai_dynamic_forms |

## Critical Methods Reference

### Payment Processing
```python
# Create sales order and request payment
LegalDocumentRequest.action_request_payment()

# Mark request as paid and create legal case
LegalDocumentRequest.action_mark_paid()

# Handle payment confirmation
PaymentTransaction._reconcile_after_done()
```

### AI Conversation
```python
# Send message to AI and get response
AIChatbotConversation.send_message(message, user_id)

# Call Groq API
AIChatbotConversation._call_groq_api(config, messages)

# Test API connection
AIChatbotConfig.test_connection()
```

### Dynamic Forms
```python
# Create survey from AI response
AISurveyBridge.create_from_ai_response(ai_response, document_request_id, conversation_id)

# Parse AI response for form structure
AISurveyBridge._extract_form_data_from_ai_response(ai_response)

# Generate Odoo survey
AISurveyBridge._create_survey_from_form_data(form_data)
```

### Document Generation
```python
# Generate document from AI
DocumentGeneration.action_generate_document()

# Start expert review
DocumentReview.action_start_review()

# Complete review and update document
DocumentReview.action_complete_review()
```

## State Flows

### Document Request States
```
draft → in_progress → pending_payment → paid → processing → completed
                                    ↓
                                cancelled
```

### Document Generation States
```
draft → generated → review → approved → delivered
                         ↓
                    cancelled
```

## Security Groups

| Group | Access Level | Permissions |
|-------|-------------|-------------|
| Legal Document Manager | Full Access | CRUD on all models |
| Legal Document Expert | Review Access | Review and modify documents |
| Legal Document User | Basic Access | Create requests, view own data |
| Portal Users | Customer Access | View own requests only |

## API Integration

### Groq API Configuration
- **Endpoint**: `https://api.groq.com/openai/v1/chat/completions`
- **Model**: `llama3-70b-8192`
- **Authentication**: Bearer token
- **Temperature**: 0.7 (default)

### Key Controllers

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/legal-documents/conversation/send` | JSON | Send message to AI |
| `/chatbot/start-conversation` | JSON | Initialize conversation |
| `/ai-survey/<int:bridge_id>` | HTTP | Render AI-generated survey |
| `/document/preview/<int:document_id>` | HTTP | Preview generated document |

## Database Relationships

### Core Relationships
```
LegalDocumentService → LegalDocumentRequest (One2many)
LegalDocumentRequest → LegalDocumentConversation (One2many)
LegalDocumentRequest → SaleOrder (Many2one)
AIChatbotConversation → LegalDocumentRequest (Many2one)
AISurveyBridge → AIChatbotConversation (Many2one)
DocumentGeneration → LegalDocumentRequest (Many2one)
DocumentReview → DocumentGeneration (Many2one)
```

## Critical Files to Monitor

### Configuration Files
- `ai_legal_document_core/security/security.xml` - Security groups and rules
- `ai_chatbot_integration/data/ai_chatbot_data.xml` - Default AI configurations
- `ai_dynamic_forms/data/dynamic_form_template_data.xml` - Form templates

### Core Model Files
- `ai_legal_document_core/models/legal_document_request.py` - Main request handling
- `ai_chatbot_integration/models/ai_chatbot_conversation.py` - AI integration
- `ai_dynamic_forms/models/ai_survey_bridge.py` - Form generation
- `ai_document_generation/models/document_generation.py` - Document creation

## Common Issues and Solutions

### API Connection Issues
```python
# Test Groq API connection
config = env['ai.chatbot.config'].search([('active', '=', True)], limit=1)
config.test_connection()
```

### Form Generation Failures
- Check AI response format for form markers
- Validate JSON structure in form_data fields
- Ensure survey module is installed and accessible

### Payment Processing Issues
- Verify sales order creation in `action_request_payment()`
- Check payment transaction reconciliation
- Ensure legal case management module is available

## Backup Critical Data

### Before Modifications
```sql
-- Backup conversation history
SELECT id, conversation_history FROM ai_chatbot_conversation;

-- Backup form data
SELECT id, form_json FROM ai_survey_bridge;

-- Backup document content
SELECT id, html_content FROM document_generation;
```

## Performance Monitoring

### Key Metrics
- AI API response times
- Conversation history size
- Document generation duration
- Form submission rates

### Database Indexes
- `legal_document_request.partner_id`
- `ai_chatbot_conversation.document_request_id`
- `document_generation.document_request_id`

## Emergency Procedures

### API Downtime
1. Check API status and configuration
2. Implement fallback messaging
3. Queue requests for retry
4. Notify users of temporary unavailability

### Data Corruption
1. Stop affected processes
2. Restore from backup
3. Validate data integrity
4. Resume operations

This quick reference provides immediate access to critical information for maintaining and enhancing the AI Legal Document System.
