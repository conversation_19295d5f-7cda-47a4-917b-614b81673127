<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="action_pdf_upload_list" model="ir.actions.act_window">
            <field name="name">PDF Upload List</field>
            <field name="res_model">pdf.upload</field>
            <field name="view_mode">tree,form</field>
        </record>
   
        <record id="view_pdf_upload_tree" model="ir.ui.view">
            <field name="name">pdf.upload.tree</field>
            <field name="model">pdf.upload</field>
            <field name="arch" type="xml">
                <tree>
		    <field name="id" string="Folder Name"/>
                    <field name="name" />
                    <field name="x_file"/>
                    <field name="x_file_link" widget="url"/>
                </tree>    
            </field>
        </record>

        <record id="view_pdf_upload_form" model="ir.ui.view">
            <field name="name">pdf.upload.form</field>
            <field name="model">pdf.upload</field>
            <field name="arch" type="xml">
                <form string="PDF Upload">
                    <group>
                        <field name="id" string="Folder Name"/>
                    <field name="name" />
                        <p><span style="color:red"> File will be deleted from here once is link is generated please do not upload file again</span></p>
                        <field name="x_file" filename="name"/>
                        <field name="x_file_link" widget="url"/>

                    </group>
                    <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"
                           options="{'post_refresh': 'recipients'}"/>
                </div>
                </form>
            </field>
        </record>
        
        <menuitem id="menu_pdf_upload" name="PDF Upload" sequence="10"/>
        <menuitem id="menu_pdf_upload_list" name="PDF Upload" parent="menu_pdf_upload" action="action_pdf_upload_list"/>

        
          
    </data>
</odoo>
