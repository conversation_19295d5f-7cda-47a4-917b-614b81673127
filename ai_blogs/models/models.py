from odoo import models, fields, api
import base64
import os

import re

class PdfUpload(models.Model):
    _name = 'pdf.upload'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'PDF Upload'

    name = fields.Char(string='Name')
    x_file = fields.Binary(string='File')
    x_file_link = fields.Char(string='File Link', compute='_compute_file_link', store=True)

    @api.model
    def create(self, vals):
        record = super(PdfUpload, self).create(vals)
        record.transfer_to_static_folder()
        return record

    def transfer_to_static_folder(self):
        for record in self:
            # Create the directory if it doesn't exist
            directory = '/usr/lib/python3/dist-packages/odoo/addons/website/static/assets/legaldocs/'+ str(record.id) +'/'
            if not os.path.exists(directory):
                os.makedirs(directory)

            # Get the data of the file
            file_data = base64.b64decode(record.x_file)

            # Specify the path where the file will be stored
            file_path = directory + record.name

            # Write the file data to the specified path
            with open(file_path, 'wb') as file:
                file.write(file_data)

            # Update the computed file link
            # record.x_file_link = attachment.url
            record.x_file_link = 'https://oneclickvakil.com/website/static/assets/legaldocs/' + str(record.id) + '/' + str(record.name)
            record.x_file = None

    def _compute_file_link(self):
        for record in self:
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            record.x_file_link = f"{base_url}/ai_blogs/static/legal/{record.name}"



