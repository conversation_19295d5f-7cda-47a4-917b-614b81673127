odoo.define('ai_blogs.custom_events', function (require) {
    'use strict';

    var core = require('web.core');
    var BlogViewer = require('ai_blogs.blog_viewer');

    var _t = core._t;

    BlogViewer.include({
        events: _.extend({}, BlogViewer.prototype.events, {
            // Define the event handler for sharing articles
            'click .o_share_article': '_onShareArticle',
            // Define event handlers for WhatsApp and email sharing buttons
            'click .o_whatsapp': '_onShareViaWhatsApp',
            'click .o_email': '_onShareViaEmail',
        }),

        _onShareArticle: function (ev) {
            ev.preventDefault();
            var url = '';
            var $element = $(ev.currentTarget);
            var blogPostTitle = $('#o_wblog_post_name').html() || '';
            var articleURL = window.location.href;

            if ($element.hasClass('o_twitter')) {
                var twitterText = _t("Amazing blog article: %s! Check it live: %s");
                var tweetText = _.str.sprintf(twitterText, blogPostTitle, articleURL);
                url = 'https://twitter.com/intent/tweet?tw_p=tweetbutton&text=' + encodeURIComponent(tweetText);
            } else if ($element.hasClass('o_facebook')) {
                url = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(articleURL);
            } else if ($element.hasClass('o_linkedin')) {
                url = 'https://www.linkedin.com/sharing/share-offsite/?url=' + encodeURIComponent(articleURL);
            } else if ($element.hasClass('o_whatsapp')) {
                url = 'whatsapp://send?text=' + encodeURIComponent(articleURL);
            } else if ($element.hasClass('o_email')) {
                url = 'mailto:?body=' + encodeURIComponent(articleURL);
            }
            
            // Open the sharing URL in a new window
            window.open(url, '', 'menubar=no, width=500, height=400');
        },

        
    });

});
