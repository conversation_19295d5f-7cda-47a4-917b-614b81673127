<odoo>
    <data noupdate="1">
        <!-- Create a new group for blog users -->
        <record id="group_blog_user" model="res.groups">
            <field name="name">Blog User</field>
            <field name="category_id" ref="base.module_category_website"/>
        </record>

        <!-- Create a new group for blog administrators -->
        <record id="group_blog_admin" model="res.groups">
            <field name="name">Blog Administrator</field>
            <field name="category_id" ref="base.module_category_website"/>
        </record>

        <!-- Blog Post Rule for Reading All Blog Posts for Blog Users -->
        <record id="blog_post_user_rule_read" model="ir.rule">
            <field name="name">Blog post read rule for users</field>
            <field name="model_id" ref="website_blog.model_blog_post"/>
            <field name="groups" eval="[(4, ref('group_blog_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <!-- Blog Post Rule for Creating/Updating/Deleting Own Blog Posts for Blog Users -->
        <record id="blog_post_user_rule_own" model="ir.rule">
            <field name="name">Blog post own rule for users</field>
            <field name="model_id" ref="website_blog.model_blog_post"/>
            <field name="groups" eval="[(4, ref('group_blog_user'))]"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- Blog Post Rule for CRUD Operations by Blog Administrators -->
        <record id="blog_post_admin_rule" model="ir.rule">
            <field name="name">Blog post admin rule</field>
            <field name="model_id" ref="website_blog.model_blog_post"/>
            <field name="groups" eval="[(4, ref('group_blog_admin'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- PDF Upload Rule for Blog Users -->
        <record id="pdf_upload_user_rule" model="ir.rule">
            <field name="name">PDF upload user rule</field>
            <field name="model_id" ref="model_pdf_upload"/>
            <field name="groups" eval="[(4, ref('group_blog_user'))]"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
        </record>
    </data>
</odoo>
