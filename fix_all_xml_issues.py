#!/usr/bin/env python3
"""
Comprehensive XML Fix Script
Fixes all XML attribute duplication issues in generated modules
"""

import os
import re
import sys

def fix_xml_file(file_path):
    """Fix all XML attribute issues in a file"""
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        original_content = content
        
        # Fix duplicated attributes with regex patterns
        fixes = [
            # Fix multiple duplicated required attributes
            (r'required="required="required="required"""', 'required="required"'),
            (r'required="required="required""', 'required="required"'),
            (r'required="required="required"', 'required="required"'),
            
            # Fix multiple duplicated class attributes
            (r'class="required="required="required"""', 'class="required"'),
            (r'class="required="required=""', 'class="required"'),
            (r'class="required="required"', 'class="required"'),
            
            # Fix multiple duplicated hidden attributes
            (r'hidden="hidden="hidden"""', 'hidden="hidden"'),
            (r'hidden="hidden=""', 'hidden="hidden"'),
            (r'type="hidden="hidden""', 'type="hidden"'),
            
            # Fix multiple duplicated disabled attributes
            (r'disabled="disabled="disabled"""', 'disabled="disabled"'),
            (r'disabled="disabled=""', 'disabled="disabled"'),
            
            # Fix multiple duplicated multiple attributes
            (r'multiple="multiple="multiple"""', 'multiple="multiple"'),
            (r'multiple="multiple=""', 'multiple="multiple"'),
            
            # Fix multiple duplicated checked attributes
            (r'checked="checked="checked"""', 'checked="checked"'),
            (r'checked="checked=""', 'checked="checked"'),
            
            # Fix multiple duplicated selected attributes
            (r'selected="selected="selected"""', 'selected="selected"'),
            (r'selected="selected=""', 'selected="selected"'),
            
            # Fix multiple duplicated readonly attributes
            (r'readonly="readonly="readonly"""', 'readonly="readonly"'),
            (r'readonly="readonly=""', 'readonly="readonly"'),
            
            # Fix multiple duplicated novalidate attributes
            (r'novalidate="novalidate="novalidate"""', 'novalidate="novalidate"'),
            (r'novalidate="novalidate=""', 'novalidate="novalidate"'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # Additional cleanup for any remaining triple quotes
        content = re.sub(r'"""', '"', content)
        
        if content != original_content:
            with open(file_path, 'w') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def validate_xml_file(file_path):
    """Validate XML syntax"""
    
    try:
        import xml.etree.ElementTree as ET
        ET.parse(file_path)
        return True, None
    except ET.ParseError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def main():
    """Main function to fix all XML issues"""
    
    print("🔧 Comprehensive XML Fix Tool")
    print("=" * 40)
    
    # Target the specific module
    module_path = '/mnt/extra-addons/ovakil_customer_feedback'
    
    if not os.path.exists(module_path):
        print(f"❌ Module not found: {module_path}")
        return False
    
    print(f"📁 Fixing module: {os.path.basename(module_path)}")
    
    # Find and fix all XML files
    xml_files = []
    for root, dirs, files in os.walk(module_path):
        for file in files:
            if file.endswith('.xml'):
                xml_files.append(os.path.join(root, file))
    
    print(f"📄 Found {len(xml_files)} XML files")
    
    fixed_files = 0
    for xml_file in xml_files:
        print(f"  Processing: {os.path.relpath(xml_file, module_path)}")
        
        # Fix the file
        if fix_xml_file(xml_file):
            fixed_files += 1
            print(f"    ✅ Fixed")
        else:
            print(f"    ✓ No changes needed")
        
        # Validate the file
        is_valid, error = validate_xml_file(xml_file)
        if is_valid:
            print(f"    ✅ Valid XML")
        else:
            print(f"    ❌ XML Error: {error}")
    
    print(f"\n📊 Summary:")
    print(f"  • Files processed: {len(xml_files)}")
    print(f"  • Files fixed: {fixed_files}")
    
    # Final validation of the main problematic file
    main_template = os.path.join(module_path, 'views', 'website_templates.xml')
    if os.path.exists(main_template):
        is_valid, error = validate_xml_file(main_template)
        if is_valid:
            print(f"\n✅ Main template file is valid!")
            print(f"🎉 Module should now install successfully!")
            return True
        else:
            print(f"\n❌ Main template still has issues: {error}")
            return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
