<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Survey Forms for Dynamic Forms -->
        <record id="survey_employment_details" model="survey.survey">
            <field name="title">Employment Contract Details</field>
            <field name="access_mode">public</field>
            <field name="users_login_required" eval="False"/>
            <field name="scoring_type">no_scoring</field>
            <field name="questions_layout">page_per_section</field>
            <field name="progression_mode">number</field>
            <field name="description">Collect detailed information for employment contract generation</field>
        </record>
        
        <!-- Employment Contract Questions -->
        <record id="page_company_info" model="survey.question">
            <field name="title">Company Information</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="sequence">1</field>
            <field name="is_page" eval="True"/>
            <field name="question_type">text_box</field>
        </record>
        
        <record id="question_company_name" model="survey.question">
            <field name="title">Company Name</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="page_id" ref="page_company_info"/>
            <field name="sequence">2</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="question_company_address" model="survey.question">
            <field name="title">Company Address</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="page_id" ref="page_company_info"/>
            <field name="sequence">3</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="page_employee_info" model="survey.question">
            <field name="title">Employee Information</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="sequence">4</field>
            <field name="is_page" eval="True"/>
            <field name="question_type">text_box</field>
        </record>
        
        <record id="question_employee_name" model="survey.question">
            <field name="title">Employee Full Name</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="page_id" ref="page_employee_info"/>
            <field name="sequence">5</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="question_job_title" model="survey.question">
            <field name="title">Job Title</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="page_id" ref="page_employee_info"/>
            <field name="sequence">6</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="question_annual_salary" model="survey.question">
            <field name="title">Annual Salary</field>
            <field name="survey_id" ref="survey_employment_details"/>
            <field name="page_id" ref="page_employee_info"/>
            <field name="sequence">7</field>
            <field name="question_type">numerical_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <!-- NDA Survey -->
        <record id="survey_nda_details" model="survey.survey">
            <field name="title">NDA Information</field>
            <field name="access_mode">public</field>
            <field name="users_login_required" eval="False"/>
            <field name="scoring_type">no_scoring</field>
            <field name="questions_layout">page_per_section</field>
            <field name="progression_mode">number</field>
            <field name="description">Collect information for Non-Disclosure Agreement</field>
        </record>
        
        <record id="nda_page_parties" model="survey.question">
            <field name="title">Parties Information</field>
            <field name="survey_id" ref="survey_nda_details"/>
            <field name="sequence">1</field>
            <field name="is_page" eval="True"/>
            <field name="question_type">text_box</field>
        </record>
        
        <record id="nda_question_disclosing_party" model="survey.question">
            <field name="title">Disclosing Party (Company/Individual sharing information)</field>
            <field name="survey_id" ref="survey_nda_details"/>
            <field name="page_id" ref="nda_page_parties"/>
            <field name="sequence">2</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="nda_question_receiving_party" model="survey.question">
            <field name="title">Receiving Party (Company/Individual receiving information)</field>
            <field name="survey_id" ref="survey_nda_details"/>
            <field name="page_id" ref="nda_page_parties"/>
            <field name="sequence">3</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="nda_question_purpose" model="survey.question">
            <field name="title">Purpose of Information Sharing</field>
            <field name="survey_id" ref="survey_nda_details"/>
            <field name="page_id" ref="nda_page_parties"/>
            <field name="sequence">4</field>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <record id="nda_question_duration" model="survey.question">
            <field name="title">Duration of Agreement</field>
            <field name="survey_id" ref="survey_nda_details"/>
            <field name="page_id" ref="nda_page_parties"/>
            <field name="sequence">5</field>
            <field name="question_type">simple_choice</field>
            <field name="constr_mandatory" eval="True"/>
        </record>
        
        <!-- NDA Duration Options -->
        <record id="nda_duration_1_year" model="survey.question.answer">
            <field name="question_id" ref="nda_question_duration"/>
            <field name="value">1 year</field>
            <field name="sequence">1</field>
        </record>
        
        <record id="nda_duration_2_years" model="survey.question.answer">
            <field name="question_id" ref="nda_question_duration"/>
            <field name="value">2 years</field>
            <field name="sequence">2</field>
        </record>
        
        <record id="nda_duration_5_years" model="survey.question.answer">
            <field name="question_id" ref="nda_question_duration"/>
            <field name="value">5 years</field>
            <field name="sequence">3</field>
        </record>
        
        <record id="nda_duration_indefinite" model="survey.question.answer">
            <field name="question_id" ref="nda_question_duration"/>
            <field name="value">Indefinite</field>
            <field name="sequence">4</field>
        </record>
    </data>
</odoo>
