#!/usr/bin/env python3
"""
Comprehensive Fix for Customer Feedback Module Issues
Addresses: Portal template, static files, and cache issues
"""

import os
import sys
import subprocess
import time

def run_command(cmd, description=""):
    """Run a command and return the result"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ Success: {description}")
            return True
        else:
            print(f"❌ Failed: {description}")
            print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout: {description}")
        return False
    except Exception as e:
        print(f"❌ Exception: {description} - {e}")
        return False

def check_module_files():
    """Check if all required module files exist"""
    print("\n🔍 Checking Module Files")
    print("=" * 40)
    
    base_path = "/mnt/extra-addons/ovakil_customer_feedback"
    required_files = [
        "__manifest__.py",
        "views/website_templates.xml",
        "views/portal/customer_feedback_main_portal.xml",
        "static/src/css/enhanced_forms.css",
        "static/src/js/enhanced_forms.js"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = os.path.join(base_path, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING!")
            all_exist = False
    
    return all_exist

def fix_portal_template_issue():
    """Fix the portal template registration issue"""
    print("\n🔧 Fixing Portal Template Issue")
    print("=" * 45)
    
    # Check if portal template exists in the right location
    portal_file = "/mnt/extra-addons/ovakil_customer_feedback/views/portal/customer_feedback_main_portal.xml"
    
    if not os.path.exists(portal_file):
        print("❌ Portal template file missing!")
        return False
    
    # Check if template is properly defined
    with open(portal_file, 'r') as f:
        content = f.read()
    
    if 'portal_customer_feedback_main_edit' in content:
        print("✅ Portal edit template found in file")
    else:
        print("❌ Portal edit template not found in file!")
        return False
    
    # Check manifest includes the portal file
    manifest_file = "/mnt/extra-addons/ovakil_customer_feedback/__manifest__.py"
    with open(manifest_file, 'r') as f:
        manifest_content = f.read()
    
    if 'customer_feedback_main_portal.xml' in manifest_content:
        print("✅ Portal template included in manifest")
        return True
    else:
        print("❌ Portal template NOT included in manifest!")
        return False

def restart_odoo_service():
    """Restart Odoo service to clear all caches"""
    print("\n🔄 Restarting Odoo Service")
    print("=" * 35)
    
    return run_command("sudo systemctl restart odoo", "Restarting Odoo service")

def wait_for_odoo():
    """Wait for Odoo to be ready"""
    print("\n⏳ Waiting for Odoo to be ready...")
    time.sleep(10)  # Give Odoo time to start
    
    # Try to check if Odoo is responding
    for i in range(6):  # Try for 60 seconds
        try:
            result = subprocess.run("curl -s -o /dev/null -w '%{http_code}' http://localhost:8069", 
                                  shell=True, capture_output=True, text=True, timeout=5)
            if result.stdout.strip() == "200":
                print("✅ Odoo is ready!")
                return True
        except:
            pass
        
        print(f"⏳ Waiting... ({i+1}/6)")
        time.sleep(10)
    
    print("⚠️ Odoo may still be starting up")
    return True

def create_update_instructions():
    """Create instructions for manual module update"""
    instructions = """
🔧 MANUAL MODULE UPDATE INSTRUCTIONS
====================================

After running this script, you MUST update the module manually:

1. **Access Odoo:**
   - Go to http://oneclickvakil.com
   - Login as administrator

2. **Update Module:**
   - Go to Apps menu
   - Search for "ovakil_customer_feedback"
   - Click on the module
   - Click "Upgrade" button
   - Wait for upgrade to complete

3. **Clear Browser Cache:**
   - Press Ctrl+F5 (or Cmd+Shift+R on Mac)
   - Or manually clear browser cache

4. **Test the Fixes:**
   - Website Form: https://oneclickvakil.com/customer-feedback-main
     ✓ Should load CSS/JS properly
     ✓ Should show all fields including assigned_user and feedback_tags
   
   - Portal Edit: https://oneclickvakil.com/my/customer-feedback-main/15/edit
     ✓ Should work without template errors

5. **If Issues Persist:**
   - Try uninstalling and reinstalling the module
   - Check Odoo logs for any errors
   - Ensure all static files are properly served

IMPORTANT: The module MUST be upgraded for changes to take effect!
"""
    
    print(instructions)
    
    # Save to file
    with open('/mnt/extra-addons/module_update_instructions.txt', 'w') as f:
        f.write(instructions)
    
    print("📝 Instructions saved to: /mnt/extra-addons/module_update_instructions.txt")

def main():
    """Main function to run all fixes"""
    print("🔧 Customer Feedback Module Comprehensive Fix")
    print("=" * 60)
    
    # Check if all files exist
    if not check_module_files():
        print("\n❌ Some required files are missing!")
        print("Please ensure all module files are properly generated.")
        return False
    
    # Check portal template issue
    if not fix_portal_template_issue():
        print("\n❌ Portal template issues detected!")
        print("Please check the portal template configuration.")
        return False
    
    # Restart Odoo to clear caches
    if not restart_odoo_service():
        print("\n❌ Failed to restart Odoo!")
        print("Please restart Odoo manually: sudo systemctl restart odoo")
        return False
    
    # Wait for Odoo to be ready
    wait_for_odoo()
    
    # Create manual update instructions
    create_update_instructions()
    
    print("\n✅ FIXES APPLIED SUCCESSFULLY!")
    print("🎯 NEXT STEP: Update the module manually in Odoo Apps")
    print("📋 See instructions above for detailed steps")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
