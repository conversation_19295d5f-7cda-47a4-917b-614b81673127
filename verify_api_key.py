# Verify the API key is correctly set
print("=== VERIFYING API KEY ===")

# Check the Default Groq Configuration
config = env['ai.chatbot.config'].browse(1)
if config:
    print(f"Config: {config.name}")
    print(f"API Key: {config.api_key}")
    print(f"API Type: {config.api_type}")
    print(f"Active: {config.active}")
    
    # Test if the key looks correct
    if config.api_key.startswith('gsk_nsWkfoYbKxvgBcB5'):
        print("✅ API key looks correct!")
    else:
        print("❌ API key is wrong!")
        print("Expected to start with: gsk_nsWkfoYbKxvgBcB5")
        print(f"Actual: {config.api_key[:30]}...")
else:
    print("❌ Config not found")

print("Done!")
