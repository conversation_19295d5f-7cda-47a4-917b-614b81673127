{"timestamp": "2025-06-13 10:59:28", "tests": {"ai_module_generator": {"status": "PASS"}, "ovakil_customer_feedback": {"module_path": "/mnt/extra-addons/ovakil_customer_feedback", "tests": [{"test": "File exists: __manifest__.py", "status": "PASS"}, {"test": "File exists: __init__.py", "status": "PASS"}, {"test": "File exists: models/__init__.py", "status": "PASS"}, {"test": "File exists: security/ir.model.access.csv", "status": "PASS"}, {"test": "File exists: views/views.xml", "status": "PASS"}, {"test": "Manifest validity", "status": "PASS", "error": null}, {"test": "XML validity: reports/customer_feedback_main_report.xml", "status": "PASS", "error": null}, {"test": "XML validity: data/data.xml", "status": "PASS", "error": null}, {"test": "XML validity: data/cron_jobs.xml", "status": "PASS", "error": null}, {"test": "XML validity: views/views.xml", "status": "PASS", "error": null}, {"test": "XML validity: views/website_templates.xml", "status": "PASS", "error": null}, {"test": "XML validity: views/portal/customer_feedback_main_portal.xml", "status": "PASS", "error": null}, {"test": "XML validity: views/portal/customer_feedback_main_portal_menu.xml", "status": "PASS", "error": null}, {"test": "XML validity: demo/demo.xml", "status": "PASS", "error": null}, {"test": "Python syntax: __init__.py", "status": "PASS", "error": null}, {"test": "Python syntax: __manifest__.py", "status": "PASS", "error": null}, {"test": "Python syntax: tests/test_models.py", "status": "PASS", "error": null}, {"test": "Python syntax: tests/__init__.py", "status": "PASS", "error": null}, {"test": "Python syntax: controllers/customer_feedback_main_portal.py", "status": "PASS", "error": null}, {"test": "Python syntax: controllers/main.py", "status": "PASS", "error": null}, {"test": "Python syntax: controllers/__init__.py", "status": "PASS", "error": null}, {"test": "Python syntax: controllers/api.py", "status": "PASS", "error": null}, {"test": "Python syntax: models/__init__.py", "status": "PASS", "error": null}, {"test": "Python syntax: models/customer_feedback_main.py", "status": "PASS", "error": null}, {"test": "Portal templates exist", "status": "PASS", "details": "Found 2 portal template files"}], "overall_status": "PASS"}}}