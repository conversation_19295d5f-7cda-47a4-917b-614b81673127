#!/usr/bin/env python3
"""
Odoo Module Manager - Command Line Interface
===========================================

Simple CLI for quick module operations via XML-RPC.

Usage:
    python odoo_cli.py install module_name
    python odoo_cli.py upgrade module_name
    python odoo_cli.py uninstall module_name
    python odoo_cli.py status module_name
    python odoo_cli.py list [installed|uninstalled|to_upgrade]
    python odoo_cli.py batch-upgrade module1,module2,module3
"""

import sys
import argparse
from odoo_module_manager import OdooModuleManager
from odoo_config import get_config

def setup_parser():
    """Setup command line argument parser"""
    parser = argparse.ArgumentParser(
        description='Odoo Module Manager CLI',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s install ai_module_generator
  %(prog)s upgrade ai_module_generator
  %(prog)s uninstall ovakil_customer_feedback
  %(prog)s status ai_module_generator
  %(prog)s list installed
  %(prog)s batch-upgrade ai_module_generator,ai_chatbot_integration
  %(prog)s export-list --state installed --output modules.json
        """
    )
    
    parser.add_argument(
        '--env', 
        choices=['production', 'staging', 'local'],
        default='production',
        help='Environment to connect to (default: production)'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Install command
    install_parser = subparsers.add_parser('install', help='Install a module')
    install_parser.add_argument('module', help='Module name to install')
    install_parser.add_argument('--no-update-list', action='store_true', 
                               help='Skip updating module list')
    
    # Upgrade command
    upgrade_parser = subparsers.add_parser('upgrade', help='Upgrade a module')
    upgrade_parser.add_argument('module', help='Module name to upgrade')
    
    # Uninstall command
    uninstall_parser = subparsers.add_parser('uninstall', help='Uninstall a module')
    uninstall_parser.add_argument('module', help='Module name to uninstall')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Check module status')
    status_parser.add_argument('modules', nargs='+', help='Module name(s) to check')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List modules')
    list_parser.add_argument('state', nargs='?', 
                            choices=['installed', 'uninstalled', 'to_upgrade'],
                            help='Filter by state')
    list_parser.add_argument('--limit', type=int, default=20,
                            help='Limit number of results (default: 20)')
    
    # Batch upgrade command
    batch_upgrade_parser = subparsers.add_parser('batch-upgrade', 
                                                help='Upgrade multiple modules')
    batch_upgrade_parser.add_argument('modules', 
                                     help='Comma-separated list of module names')
    
    # Batch install command
    batch_install_parser = subparsers.add_parser('batch-install', 
                                                help='Install multiple modules')
    batch_install_parser.add_argument('modules', 
                                     help='Comma-separated list of module names')
    
    # Export list command
    export_parser = subparsers.add_parser('export-list', help='Export module list')
    export_parser.add_argument('--state', 
                              choices=['installed', 'uninstalled', 'to_upgrade'],
                              help='Filter by state')
    export_parser.add_argument('--output', help='Output filename')
    
    # Update list command
    update_parser = subparsers.add_parser('update-list', help='Update module list')
    
    return parser

def cmd_install(manager, args):
    """Handle install command"""
    print(f"📦 Installing module: {args.module}")
    success = manager.install_module(args.module, update_list=not args.no_update_list)
    
    if success:
        print(f"✅ Module '{args.module}' installed successfully")
        return 0
    else:
        print(f"❌ Failed to install module '{args.module}'")
        return 1

def cmd_upgrade(manager, args):
    """Handle upgrade command"""
    print(f"⬆️ Upgrading module: {args.module}")
    success = manager.upgrade_module(args.module)
    
    if success:
        print(f"✅ Module '{args.module}' upgraded successfully")
        return 0
    else:
        print(f"❌ Failed to upgrade module '{args.module}'")
        return 1

def cmd_uninstall(manager, args):
    """Handle uninstall command"""
    print(f"🗑️ Uninstalling module: {args.module}")
    success = manager.uninstall_module(args.module)
    
    if success:
        print(f"✅ Module '{args.module}' uninstalled successfully")
        return 0
    else:
        print(f"❌ Failed to uninstall module '{args.module}'")
        return 1

def cmd_status(manager, args):
    """Handle status command"""
    print(f"🔍 Checking status of {len(args.modules)} module(s)")
    manager.print_module_status(args.modules)
    return 0

def cmd_list(manager, args):
    """Handle list command"""
    print(f"📋 Listing modules" + (f" with state '{args.state}'" if args.state else ""))
    
    modules = manager.list_modules(args.state)
    
    if not modules:
        print("No modules found")
        return 0
    
    # Limit results
    limited_modules = modules[:args.limit]
    
    print(f"\nFound {len(modules)} modules (showing first {len(limited_modules)}):")
    print("-" * 80)
    
    for module in limited_modules:
        name = module['name'][:30]
        state = module['state'][:15]
        summary = (module.get('summary') or 'N/A')[:30]
        print(f"{name:<32} {state:<17} {summary}")
    
    if len(modules) > args.limit:
        print(f"\n... and {len(modules) - args.limit} more modules")
    
    return 0

def cmd_batch_upgrade(manager, args):
    """Handle batch upgrade command"""
    module_names = [name.strip() for name in args.modules.split(',')]
    print(f"⬆️ Batch upgrading {len(module_names)} modules")
    
    results = manager.batch_upgrade(module_names)
    
    print("\n📊 Upgrade Results:")
    success_count = 0
    for module, success in results.items():
        status = "✅ Success" if success else "❌ Failed"
        print(f"  {module}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📈 Summary: {success_count}/{len(module_names)} modules upgraded successfully")
    return 0 if success_count == len(module_names) else 1

def cmd_batch_install(manager, args):
    """Handle batch install command"""
    module_names = [name.strip() for name in args.modules.split(',')]
    print(f"📦 Batch installing {len(module_names)} modules")
    
    results = manager.batch_install(module_names)
    
    print("\n📊 Installation Results:")
    success_count = 0
    for module, success in results.items():
        status = "✅ Success" if success else "❌ Failed"
        print(f"  {module}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📈 Summary: {success_count}/{len(module_names)} modules installed successfully")
    return 0 if success_count == len(module_names) else 1

def cmd_export_list(manager, args):
    """Handle export list command"""
    print(f"💾 Exporting module list" + (f" (state: {args.state})" if args.state else ""))
    
    filename = manager.export_module_list(args.output, args.state)
    print(f"✅ Module list exported to: {filename}")
    return 0

def cmd_update_list(manager, args):
    """Handle update list command"""
    print("📋 Updating module list...")
    success = manager.update_module_list()
    
    if success:
        print("✅ Module list updated successfully")
        return 0
    else:
        print("❌ Failed to update module list")
        return 1

def main():
    """Main CLI function"""
    parser = setup_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        # Get configuration
        config = get_config(args.env)
        print(f"🔗 Connecting to {config['url']} (env: {args.env})")
        
        # Initialize manager
        manager = OdooModuleManager(**config)
        
        # Execute command
        command_handlers = {
            'install': cmd_install,
            'upgrade': cmd_upgrade,
            'uninstall': cmd_uninstall,
            'status': cmd_status,
            'list': cmd_list,
            'batch-upgrade': cmd_batch_upgrade,
            'batch-install': cmd_batch_install,
            'export-list': cmd_export_list,
            'update-list': cmd_update_list,
        }
        
        handler = command_handlers.get(args.command)
        if handler:
            return handler(manager, args)
        else:
            print(f"❌ Unknown command: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        return 1
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
