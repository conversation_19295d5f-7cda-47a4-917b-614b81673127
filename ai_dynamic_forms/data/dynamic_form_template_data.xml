<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default Survey Templates -->
        <record id="survey_template_personal_information" model="survey.survey">
            <field name="title">Personal Information</field>
            <field name="access_mode">public</field>
            <field name="users_login_required" eval="False"/>
            <field name="scoring_type">no_scoring</field>
            <field name="questions_layout">page_per_section</field>
            <field name="progression_mode">number</field>
            <field name="description">Basic personal information form template</field>
        </record>

        <!-- Personal Information Section -->
        <record id="survey_page_personal_information" model="survey.question">
            <field name="title">Personal Information</field>
            <field name="survey_id" ref="survey_template_personal_information"/>
            <field name="sequence">1</field>
            <field name="is_page" eval="True"/>
        </record>

        <!-- Personal Information Questions -->
        <record id="survey_question_full_name" model="survey.question">
            <field name="title">Full Name</field>
            <field name="survey_id" ref="survey_template_personal_information"/>
            <field name="sequence">10</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
            <field name="save_as_nickname" eval="True"/>
        </record>

        <record id="survey_question_email" model="survey.question">
            <field name="title">Email Address</field>
            <field name="survey_id" ref="survey_template_personal_information"/>
            <field name="sequence">20</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
            <field name="save_as_email" eval="True"/>
            <field name="validation_required" eval="True"/>
            <field name="validation_email" eval="True"/>
        </record>

        <record id="survey_question_phone" model="survey.question">
            <field name="title">Phone Number</field>
            <field name="survey_id" ref="survey_template_personal_information"/>
            <field name="sequence">30</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>

        <record id="survey_question_address" model="survey.question">
            <field name="title">Address</field>
            <field name="survey_id" ref="survey_template_personal_information"/>
            <field name="sequence">40</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>

        <record id="survey_question_dob" model="survey.question">
            <field name="title">Date of Birth</field>
            <field name="survey_id" ref="survey_template_personal_information"/>
            <field name="sequence">50</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">date</field>
            <field name="constr_mandatory" eval="False"/>
        </record>

        <!-- RTI Information Template -->
        <record id="survey_template_rti_information" model="survey.survey">
            <field name="title">RTI Information</field>
            <field name="access_mode">public</field>
            <field name="users_login_required" eval="False"/>
            <field name="scoring_type">no_scoring</field>
            <field name="questions_layout">page_per_section</field>
            <field name="progression_mode">number</field>
            <field name="description">Information required for RTI applications</field>
        </record>

        <!-- RTI Information Section -->
        <record id="survey_page_rti_information" model="survey.question">
            <field name="title">RTI Information</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">1</field>
            <field name="is_page" eval="True"/>
        </record>

        <!-- RTI Information Questions -->
        <record id="survey_question_authority_name" model="survey.question">
            <field name="title">Public Authority Name</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">10</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>

        <record id="survey_question_information_requested" model="survey.question">
            <field name="title">Information Requested</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">20</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">text_box</field>
            <field name="constr_mandatory" eval="True"/>
        </record>

        <record id="survey_question_request_type" model="survey.question">
            <field name="title">Request Type</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">30</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">simple_choice</field>
            <field name="constr_mandatory" eval="True"/>
        </record>

        <record id="survey_answer_general_info" model="survey.question.answer">
            <field name="value">General Information</field>
            <field name="question_id" ref="survey_question_request_type"/>
            <field name="sequence">1</field>
        </record>

        <record id="survey_answer_personal_info" model="survey.question.answer">
            <field name="value">Personal Information</field>
            <field name="question_id" ref="survey_question_request_type"/>
            <field name="sequence">2</field>
        </record>

        <record id="survey_answer_third_party_info" model="survey.question.answer">
            <field name="value">Third Party Information</field>
            <field name="question_id" ref="survey_question_request_type"/>
            <field name="sequence">3</field>
        </record>

        <record id="survey_question_period_from" model="survey.question">
            <field name="title">Period From</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">40</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">date</field>
            <field name="constr_mandatory" eval="False"/>
        </record>

        <record id="survey_question_period_to" model="survey.question">
            <field name="title">Period To</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">50</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">date</field>
            <field name="constr_mandatory" eval="False"/>
        </record>

        <record id="survey_question_bpl_category" model="survey.question">
            <field name="title">Below Poverty Line Category</field>
            <field name="survey_id" ref="survey_template_rti_information"/>
            <field name="sequence">60</field>
            <field name="is_page" eval="False"/>
            <field name="question_type">simple_choice</field>
            <field name="constr_mandatory" eval="False"/>
        </record>

        <record id="survey_answer_yes" model="survey.question.answer">
            <field name="value">Yes</field>
            <field name="question_id" ref="survey_question_bpl_category"/>
            <field name="sequence">1</field>
        </record>

        <record id="survey_answer_no" model="survey.question.answer">
            <field name="value">No</field>
            <field name="question_id" ref="survey_question_bpl_category"/>
            <field name="sequence">2</field>
        </record>
    </data>
</odoo>
