<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Embedded Survey Form Template -->
    <template id="embedded_survey_form" name="Embedded Survey Form">
        <div class="o_survey_form_embedded">
            <form t-att-action="'/survey/%s/%s' % (survey.access_token, user_input.access_token)" method="post" class="o_survey_form" t-att-data-survey-id="survey.id" t-att-data-user-input-id="user_input.id">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                <input type="hidden" name="token" t-att-value="user_input.access_token"/>
                <input type="hidden" name="page_id" t-att-value="page.id"/>
                <input type="hidden" name="embedded_in_chat" value="1"/>

                <!-- We're skipping the survey title and description to make it more chat-like -->

                <!-- Questions -->
                <div class="o_survey_questions">
                    <t t-foreach="survey.question_ids.filtered(lambda q: not q.is_page and q.page_id.id == page.id)" t-as="question">
                        <div class="o_survey_question mb-4" t-att-data-question-id="question.id">
                            <!-- Question Title -->
                            <div class="o_survey_question_title">
                                <span t-field="question.title"/>
                                <span t-if="question.constr_mandatory" class="o_survey_question_required">*</span>
                            </div>

                            <!-- Question Description (if any) -->
                            <div t-if="question.description" class="o_survey_question_description mb-2">
                                <div t-field="question.description"/>
                            </div>

                            <!-- Question Content -->
                            <div class="o_survey_question_content">
                                <!-- Text Box -->
                                <t t-if="question.question_type == 'text_box'">
                                    <input t-if="question.save_as_email" type="email" class="form-control" t-att-name="'question_%s' % question.id" t-att-required="question.constr_mandatory" t-att-placeholder="question.placeholder"/>
                                    <textarea t-elif="question.question_type_id.code == 'textbox'" class="form-control" t-att-name="'question_%s' % question.id" t-att-required="question.constr_mandatory" t-att-placeholder="question.placeholder" rows="3"/>
                                    <input t-else="" type="text" class="form-control" t-att-name="'question_%s' % question.id" t-att-required="question.constr_mandatory" t-att-placeholder="question.placeholder"/>
                                </t>

                                <!-- Numerical Box -->
                                <t t-if="question.question_type == 'numerical_box'">
                                    <input type="number" class="form-control" t-att-name="'question_%s' % question.id" t-att-required="question.constr_mandatory" t-att-placeholder="question.placeholder" t-att-min="question.validation_min_float_value" t-att-max="question.validation_max_float_value" step="any"/>
                                </t>

                                <!-- Date -->
                                <t t-if="question.question_type == 'date'">
                                    <input type="date" class="form-control" t-att-name="'question_%s' % question.id" t-att-required="question.constr_mandatory" t-att-min="question.validation_min_date" t-att-max="question.validation_max_date"/>
                                </t>

                                <!-- Datetime -->
                                <t t-if="question.question_type == 'datetime'">
                                    <input type="datetime-local" class="form-control" t-att-name="'question_%s' % question.id" t-att-required="question.constr_mandatory"/>
                                </t>

                                <!-- Simple Choice -->
                                <t t-if="question.question_type == 'simple_choice'">
                                    <div class="o_survey_form_choice">
                                        <t t-foreach="question.suggested_answer_ids" t-as="answer">
                                            <div class="form-check">
                                                <input type="radio" class="form-check-input" t-att-id="'answer_%s_%s' % (question.id, answer.id)" t-att-name="'question_%s' % question.id" t-att-value="answer.id" t-att-required="question.constr_mandatory"/>
                                                <label class="form-check-label" t-att-for="'answer_%s_%s' % (question.id, answer.id)" t-field="answer.value"/>
                                            </div>
                                        </t>
                                    </div>
                                </t>

                                <!-- Multiple Choice -->
                                <t t-if="question.question_type == 'multiple_choice'">
                                    <div class="o_survey_form_choice">
                                        <t t-foreach="question.suggested_answer_ids" t-as="answer">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" t-att-id="'answer_%s_%s' % (question.id, answer.id)" t-att-name="'question_%s_%s' % (question.id, answer.id)" t-att-value="answer.id"/>
                                                <label class="form-check-label" t-att-for="'answer_%s_%s' % (question.id, answer.id)" t-field="answer.value"/>
                                            </div>
                                        </t>
                                    </div>
                                </t>

                                <!-- Matrix -->
                                <t t-if="question.question_type == 'matrix'">
                                    <table class="table table-bordered o_survey_matrix_table">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <t t-foreach="question.suggested_answer_ids" t-as="col_answer">
                                                    <th t-field="col_answer.value"/>
                                                </t>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="question.matrix_row_ids" t-as="row_answer">
                                                <tr>
                                                    <td t-field="row_answer.value"/>
                                                    <t t-foreach="question.suggested_answer_ids" t-as="col_answer">
                                                        <td>
                                                            <input t-if="question.matrix_subtype == 'simple'" type="radio" t-att-name="'matrix_%s_%s' % (question.id, row_answer.id)" t-att-value="col_answer.id" t-att-required="question.constr_mandatory"/>
                                                            <input t-else="" type="checkbox" t-att-name="'matrix_%s_%s_%s' % (question.id, row_answer.id, col_answer.id)" t-att-value="1"/>
                                                        </td>
                                                    </t>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </t>
                            </div>
                        </div>
                    </t>
                </div>

                <!-- Submit Button -->
                <div class="o_survey_navigation mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-paper-plane me-1"></i> Submit Information
                    </button>
                </div>
            </form>
        </div>
    </template>
</odoo>
