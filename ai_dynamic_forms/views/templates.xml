<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Survey Success Template -->
    <template id="ai_survey_success" name="AI Survey Success">
        <t t-call="website.layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12 text-center">
                        <div class="alert alert-success">
                            <h2><i class="fa fa-check-circle"></i> Form Submitted Successfully</h2>
                            <p>Thank you for your submission. You can now return to your conversation.</p>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <a href="/" class="btn btn-primary">Return to Home</a>
                        <t t-if="document_request_id">
                            <a t-att-href="'/legal-documents/conversation/%s' % document_request_id" class="btn btn-secondary ml-2">
                                Return to Conversation
                            </a>
                        </t>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Standalone Dynamic Forms JavaScript -->
    <template id="dynamic_forms_scripts" name="Dynamic Forms Scripts">
        <script type="text/javascript">
            // Function to process survey submission
            function processSurveySubmission(bridgeId, requestId, conversationId) {
                // Show success message in the survey container
                $(`#survey-container-${bridgeId}`).html(`
                    <div class="text-center p-2">
                        <div class="mb-2">
                            <i class="fa fa-check-circle text-success" style="font-size: 2rem;"></i>
                        </div>
                        <p class="mb-0">Thanks! I've received your information.</p>
                    </div>
                `);

                // Show loading indicator for AI response
                var loadingHtml = `
                    <div id="ai-loading" class="message mb-3 ai-message">
                        <div class="message-bubble p-3 rounded bg-light">
                            <div class="message-header mb-2">
                                <strong>AI Assistant</strong>
                                <small class="text-muted ms-2">Thinking...</small>
                            </div>
                            <div class="message-content">
                                <div class="d-flex align-items-center">
                                    <span class="me-2">Processing your information</span>
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                $('#conversation-messages').append(loadingHtml);

                // Get survey results
                $.ajax({
                    url: '/dynamic-forms/get-survey-results/' + bridgeId,
                    type: 'POST',
                    dataType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'call',
                        params: {}
                    }),
                    success: function(data) {
                        // Remove loading indicator
                        $('#ai-loading').remove();

                        if (data.result &amp;&amp; data.result.success) {
                            // Submit survey results to AI
                            $.ajax({
                                url: '/dynamic-forms/submit-survey-to-ai/' + bridgeId,
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {}
                                }),
                                success: function(data) {
                                    if (data.result &amp;&amp; data.result.success) {
                                        // Add AI response
                                        var aiMessage = data.result.message;
                                        var hasForm = data.result.has_form;
                                        var formData = data.result.form_data;

                                        var aiMessageHtml = `
                                            <div class="message mb-3 ai-message">
                                                <div class="message-bubble p-3 rounded bg-light">
                                                    <div class="message-header mb-2">
                                                        <strong>AI Assistant</strong>
                                                        <small class="text-muted ms-2">Just now</small>
                                                    </div>
                                                    <div class="message-content">
                                                        ${window.AIMessageFormatter ? window.AIMessageFormatter.formatAIMessage(aiMessage) : aiMessage.replace(/\n/g, '&lt;br/&gt;')}
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                        $('#conversation-messages').append(aiMessageHtml);

                                        // If there's form data, render the form
                                        if (hasForm &amp;&amp; formData) {
                                            renderAISurvey(formData, requestId, conversationId);
                                        }

                                        // Scroll to bottom
                                        var conversationDiv = document.getElementById('conversation-messages');
                                        if (conversationDiv) {
                                            conversationDiv.scrollTop = conversationDiv.scrollHeight;
                                        }
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('AJAX error:', error);

                                    // Show error message
                                    var errorHtml = `
                                        <div class="message mb-3 ai-message">
                                            <div class="message-bubble p-3 rounded bg-danger text-white">
                                                <div class="message-header mb-2">
                                                    <strong>Error</strong>
                                                    <small class="text-muted ms-2">Just now</small>
                                                </div>
                                                <div class="message-content">
                                                    Sorry, there was an error processing your form submission. Please try again.
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                    $('#conversation-messages').append(errorHtml);
                                }
                            });
                        } else {
                            // Show error message
                            var errorHtml = `
                                <div class="message mb-3 ai-message">
                                    <div class="message-bubble p-3 rounded bg-warning text-dark">
                                        <div class="message-header mb-2">
                                            <strong>Form Not Completed</strong>
                                            <small class="text-muted ms-2">Just now</small>
                                        </div>
                                        <div class="message-content">
                                            It looks like you haven't completed the form yet. Please click the "Open Form" button to fill out the form.
                                        </div>
                                    </div>
                                </div>
                            `;
                            $('#conversation-messages').append(errorHtml);
                        }
                    },
                    error: function(xhr, status, error) {
                        // Remove loading indicator
                        $('#ai-loading').remove();

                        console.error('AJAX error:', error);

                        // Show error message
                        var errorHtml = `
                            <div class="message mb-3 ai-message">
                                <div class="message-bubble p-3 rounded bg-warning text-dark">
                                    <div class="message-header mb-2">
                                        <strong>Form Not Completed</strong>
                                        <small class="text-muted ms-2">Just now</small>
                                    </div>
                                    <div class="message-content">
                                        It looks like you haven't completed the form yet. Please click the "Open Form" button to fill out the form.
                                    </div>
                                </div>
                            </div>
                        `;
                        $('#conversation-messages').append(errorHtml);
                    }
                });
            }

            // Function to render survey from AI response
            function renderAISurvey(formData, requestId, conversationId) {
                if (!formData || !formData.form) return;

                // Parse AI response to extract form data and create a survey
                $.ajax({
                    url: '/dynamic-forms/parse-ai-response',
                    type: 'POST',
                    dataType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'call',
                        params: {
                            ai_response: JSON.stringify(formData),
                            document_request_id: requestId,
                            conversation_id: conversationId
                        }
                    }),
                    success: function(data) {
                        if (data.result &amp;&amp; data.result.success) {
                            var bridgeId = data.result.bridge_id;
                            var surveyUrl = data.result.survey_url;

                            // Add embedded survey to conversation
                            var surveyContainerHtml = `
                                <div class="message mb-3 ai-message">
                                    <div class="message-bubble p-3 rounded bg-light">
                                        <div class="message-header mb-2">
                                            <strong>AI Assistant</strong>
                                            <small class="text-muted ms-2">I need some information from you</small>
                                        </div>
                                        <div class="dynamic-form p-3 bg-white border rounded">
                                            <div class="embedded-survey-container" id="survey-container-${bridgeId}">
                                                <div class="text-center p-3">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <p class="mt-2">Loading questions...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;

                            $('#conversation-messages').append(surveyContainerHtml);

                            // Scroll to bottom
                            var conversationDiv = document.getElementById('conversation-messages');
                            if (conversationDiv) {
                                conversationDiv.scrollTop = conversationDiv.scrollHeight;
                            }

                            // Load the survey in the embedded container
                            $.ajax({
                                url: '/ai-survey/embed/' + bridgeId,
                                type: 'GET',
                                success: function(data) {
                                    $(`#survey-container-${bridgeId}`).html(data);

                                    // Initialize survey form submission
                                    $(`#survey-container-${bridgeId} form`).on('submit', function(e) {
                                        e.preventDefault();

                                        var $form = $(this);
                                        var formData = new FormData($form[0]);

                                        // Show loading indicator
                                        $(`#survey-container-${bridgeId}`).html(`
                                            <div class="text-center p-3">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <p class="mt-2">Submitting form...</p>
                                            </div>
                                        `);

                                        // Submit the form
                                        $.ajax({
                                            url: $form.attr('action'),
                                            type: 'POST',
                                            data: formData,
                                            processData: false,
                                            contentType: false,
                                            success: function(response) {
                                                // Process the survey submission
                                                processSurveySubmission(bridgeId, requestId, conversationId);
                                            },
                                            error: function() {
                                                $(`#survey-container-${bridgeId}`).html(`
                                                    <div class="alert alert-danger">
                                                        <p>There was an error submitting the form. Please try again.</p>
                                                        <button class="btn btn-outline-danger btn-sm mt-2" onclick="location.reload()">Reload</button>
                                                    </div>
                                                `);
                                            }
                                        });
                                    });
                                },
                                error: function() {
                                    $(`#survey-container-${bridgeId}`).html(`
                                        <div class="alert alert-danger">
                                            <p>There was an error loading the form. Please try again.</p>
                                            <button class="btn btn-outline-danger btn-sm mt-2" onclick="location.reload()">Reload</button>
                                        </div>
                                    `);
                                }
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', error);
                    }
                });
            }

            // Override the renderDynamicForm function
            function renderDynamicForm(formFields, requestId, conversationId) {
                // Create form data object
                var formData = {
                    form: formFields
                };

                // Render the form
                renderAISurvey(formData, requestId, conversationId);
            }
        </script>
    </template>
</odoo>
