<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Survey Bridge Views -->
    <record id="view_ai_survey_bridge_form" model="ir.ui.view">
        <field name="name">ai.survey.bridge.form</field>
        <field name="model">ai.survey.bridge</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_view_survey" string="View Survey" type="object" class="oe_highlight"
                            invisible="not survey_id"/>
                    <button name="action_get_share_url" string="Get Share URL" type="object"
                            invisible="not survey_id"/>
                    <button name="action_activate" string="Activate" type="object" class="oe_highlight"
                            invisible="state != 'draft'"/>
                    <button name="action_archive" string="Archive" type="object"
                            invisible="state == 'archived'"/>
                    <button name="action_draft" string="Set to Draft" type="object"
                            invisible="state == 'draft'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,archived"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_survey" type="object" class="oe_stat_button" icon="fa-list-alt"
                                invisible="not survey_id">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Survey</span>
                            </div>
                        </button>
                        <button name="action_view_responses" type="object" class="oe_stat_button" icon="fa-check-square-o"
                                invisible="not survey_id">
                            <field name="user_input_count" widget="statinfo" string="Responses"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Form Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="description"/>
                            <field name="source"/>
                            <field name="survey_id"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="document_request_id"/>
                            <field name="conversation_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="AI Response" name="ai_response">
                            <field name="ai_response" widget="text_markdown"/>
                        </page>
                        <page string="Form JSON" name="form_json">
                            <field name="form_json" widget="text"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_ai_survey_bridge_tree" model="ir.ui.view">
        <field name="name">ai.survey.bridge.tree</field>
        <field name="model">ai.survey.bridge</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" decoration-muted="state == 'archived'">
                <field name="name"/>
                <field name="source"/>
                <field name="survey_id"/>
                <field name="user_input_count"/>
                <field name="document_request_id"/>
                <field name="state"/>
                <field name="active" invisible="1"/>
            </tree>
        </field>
    </record>

    <record id="view_ai_survey_bridge_search" model="ir.ui.view">
        <field name="name">ai.survey.bridge.search</field>
        <field name="model">ai.survey.bridge</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="survey_id"/>
                <field name="document_request_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Archived" name="archived" domain="[('state', '=', 'archived')]"/>
                <separator/>
                <filter string="AI Generated" name="ai_generated" domain="[('source', '=', 'ai')]"/>
                <filter string="Manual" name="manual" domain="[('source', '=', 'manual')]"/>
                <filter string="From Template" name="from_template" domain="[('source', '=', 'template')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Source" name="groupby_source" context="{'group_by': 'source'}"/>
                    <filter string="Document Request" name="groupby_document_request" context="{'group_by': 'document_request_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_ai_survey_bridge" model="ir.actions.act_window">
        <field name="name">AI Survey Forms</field>
        <field name="res_model">ai.survey.bridge</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_ai_survey_bridge"
              name="AI Survey Forms"
              parent="survey.menu_surveys"
              action="action_ai_survey_bridge"
              sequence="20"/>
</odoo>
