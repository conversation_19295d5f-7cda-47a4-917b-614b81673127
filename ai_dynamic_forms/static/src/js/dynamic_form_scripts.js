/**
 * AI Dynamic Forms Scripts
 * Simple implementation for Odoo 17 Community Edition
 */
$(document).ready(function() {
    'use strict';

    var AISurveyWidget = {
        /**
         * Initialize the survey widget
         */
        init: function() {
            this.bindEvents();
            this.initializeSurvey();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;
            $(document).on('submit', '.o_survey_form', function(e) {
                return self.onSubmitForm(e);
            });
            $(document).on('change', '.o_survey_form input, .o_survey_form select, .o_survey_form textarea', function(e) {
                self.onFieldChange(e);
            });
        },

        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------

        /**
         * Initialize the survey
         */
        initializeSurvey: function () {
            // Add any additional initialization for AI surveys
            this.setupValidation();
        },

        /**
         * Setup form validation
         */
        setupValidation: function () {
            var self = this;

            // Add validation classes
            $('.o_survey_form input, .o_survey_form select, .o_survey_form textarea').each(function () {
                var $field = $(this);
                if ($field.prop('required')) {
                    $field.addClass('required');
                }
            });

            // Add validation event listeners
            $(document).on('blur', '.o_survey_form input, .o_survey_form select, .o_survey_form textarea', function () {
                self.validateField($(this));
            });
        },

        /**
         * Validate a field
         * @param {jQuery} $field - The field to validate
         * @returns {Boolean} - Whether the field is valid
         */
        validateField: function ($field) {
            var isValid = true;
            var value = $field.val();

            // Clear previous validation
            $field.removeClass('is-invalid');
            $field.siblings('.invalid-feedback').remove();

            // Required validation
            if ($field.prop('required') && !value) {
                isValid = false;
                this.addValidationError($field, 'This field is required');
            }

            // Email validation
            if ($field.attr('type') === 'email' && value) {
                var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(value)) {
                    isValid = false;
                    this.addValidationError($field, 'Please enter a valid email address');
                }
            }

            // Number validation
            if ($field.attr('type') === 'number' && value) {
                if (isNaN(parseFloat(value))) {
                    isValid = false;
                    this.addValidationError($field, 'Please enter a valid number');
                }
            }

            return isValid;
        },

        /**
         * Add validation error to a field
         * @param {jQuery} $field - The field to add the error to
         * @param {String} message - The error message
         */
        addValidationError: function ($field, message) {
            $field.addClass('is-invalid');
            $field.after('<div class="invalid-feedback">' + message + '</div>');
        },

        /**
         * Validate the entire form
         * @param {jQuery} $form - The form to validate
         * @returns {Boolean} - Whether the form is valid
         */
        validateForm: function ($form) {
            var self = this;
            var isValid = true;

            $form.find('input, select, textarea').each(function () {
                if (!self.validateField($(this))) {
                    isValid = false;
                }
            });

            return isValid;
        },

        //--------------------------------------------------------------------------
        // Handlers
        //--------------------------------------------------------------------------

        /**
         * Handle form submission
         * @param {Event} ev - The submit event
         */
        onSubmitForm: function (ev) {
            var $form = $(ev.currentTarget);
            if (!this.validateForm($form)) {
                ev.preventDefault();
                ev.stopPropagation();

                // Scroll to first error
                var $firstError = $form.find('.is-invalid').first();
                if ($firstError.length) {
                    $('html, body').animate({
                        scrollTop: $firstError.offset().top - 100
                    }, 500);
                }
            }
        },

        /**
         * Handle field change
         * @param {Event} ev - The change event
         */
        onFieldChange: function (ev) {
            var $field = $(ev.currentTarget);
            this.validateField($field);
        }
    };

    // Initialize the survey widget
    AISurveyWidget.init();

    // Export for global access
    window.AISurveyWidget = AISurveyWidget;
});
