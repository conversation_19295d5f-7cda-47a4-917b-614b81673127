/* AI Survey Styles */

/* Embedded Survey Styles */
.o_survey_form_embedded {
    max-width: 100%;
    margin: 0;
    padding: 0;
    font-size: 0.95rem;
}

.o_survey_form_embedded .o_survey_form {
    box-shadow: none;
    padding: 0;
    margin: 0;
    background-color: transparent;
    border: none;
}

/* Hide survey title and description in chat */
.o_survey_form_embedded .o_survey_title,
.o_survey_form_embedded .o_survey_description {
    display: none;
}

.o_survey_form_embedded .o_survey_question {
    margin-bottom: 1.25rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    background-color: rgba(248, 249, 250, 0.5);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.o_survey_form_embedded .o_survey_question:hover {
    background-color: rgba(248, 249, 250, 0.8);
    border-color: rgba(0, 0, 0, 0.1);
}

.o_survey_form_embedded .o_survey_question_title {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #333;
}

.o_survey_form_embedded .o_survey_question_description {
    font-size: 0.8125rem;
    color: #666;
    font-style: italic;
}

.o_survey_form_embedded .form-control,
.o_survey_form_embedded .form-select {
    font-size: 0.9375rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0.75rem;
    background-color: white;
    transition: all 0.2s ease;
}

.o_survey_form_embedded .form-control:focus,
.o_survey_form_embedded .form-select:focus {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 0.2rem rgba(74, 108, 247, 0.25);
}

.o_survey_form_embedded .form-check-input {
    margin-top: 0.25rem;
    cursor: pointer;
}

.o_survey_form_embedded .form-check-label {
    cursor: pointer;
    padding-left: 0.25rem;
}

.o_survey_form_embedded .o_survey_navigation {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
}

.o_survey_form_embedded .btn-primary {
    padding: 0.5rem 1.25rem;
    background-color: #4a6cf7;
    border-color: #4a6cf7;
    font-weight: 500;
    font-size: 0.9rem;
    border-radius: 1.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.o_survey_form_embedded .btn-primary:hover {
    background-color: #3a5ce4;
    border-color: #3a5ce4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Survey Container */
.o_survey_form {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

/* Survey Title */
.o_survey_form .o_survey_title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Survey Description */
.o_survey_form .o_survey_description {
    font-size: 1rem;
    color: #666;
    margin-bottom: 2rem;
    text-align: center;
}

/* Question Container */
.o_survey_form .o_survey_question {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.o_survey_form .o_survey_question:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

/* Question Title */
.o_survey_form .o_survey_question_title {
    font-size: 1.25rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 1rem;
}

/* Required Question */
.o_survey_form .o_survey_question_required {
    color: #dc3545;
    margin-left: 0.25rem;
}

/* Question Description */
.o_survey_form .o_survey_question_description {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 1rem;
}

/* Form Controls */
.o_survey_form .form-control,
.o_survey_form .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.o_survey_form .form-control:focus,
.o_survey_form .form-select:focus {
    border-color: #4a6cf7;
    box-shadow: 0 0 0 0.25rem rgba(74, 108, 247, 0.25);
}

/* Radio and Checkbox */
.o_survey_form .o_survey_form_choice {
    margin-bottom: 0.5rem;
}

.o_survey_form .o_survey_form_choice input[type="radio"],
.o_survey_form .o_survey_form_choice input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Matrix Questions */
.o_survey_form .o_survey_matrix_table {
    width: 100%;
    border-collapse: collapse;
}

.o_survey_form .o_survey_matrix_table th,
.o_survey_form .o_survey_matrix_table td {
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #dee2e6;
}

.o_survey_form .o_survey_matrix_table th {
    background-color: #f8f9fa;
    font-weight: 500;
}

/* Navigation Buttons */
.o_survey_form .o_survey_navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
}

.o_survey_form .btn-primary {
    background-color: #4a6cf7;
    border-color: #4a6cf7;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.o_survey_form .btn-primary:hover {
    background-color: #3a5ce4;
    border-color: #3a5ce4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.o_survey_form .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.o_survey_form .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Progress Bar */
.o_survey_form .o_survey_progress {
    margin-bottom: 2rem;
}

.o_survey_form .progress {
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #e9ecef;
}

.o_survey_form .progress-bar {
    background-color: #4a6cf7;
    border-radius: 0.25rem;
}

/* Validation */
.o_survey_form .is-invalid {
    border-color: #dc3545;
}

.o_survey_form .invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.o_survey_form {
    animation: fadeIn 0.5s ease;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .o_survey_form {
        padding: 1rem;
    }

    .o_survey_form .o_survey_navigation {
        flex-direction: column;
        gap: 0.5rem;
    }

    .o_survey_form .btn {
        width: 100%;
    }
}
