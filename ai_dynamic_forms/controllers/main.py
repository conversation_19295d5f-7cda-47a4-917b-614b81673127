from odoo import http, _
from odoo.http import request
import logging
import json

_logger = logging.getLogger(__name__)


class DynamicFormsController(http.Controller):

    @http.route(['/ai-survey/<int:bridge_id>'], type='http', auth='public', website=True)
    def render_ai_survey(self, bridge_id, **kw):
        """Render an AI-generated survey"""
        bridge = request.env['ai.survey.bridge'].sudo().browse(bridge_id)

        if not bridge.exists() or not bridge.survey_id:
            return request.render('website.404')

        # Redirect to the survey
        return request.redirect(f'/survey/start/{bridge.survey_id.access_token}')

    @http.route(['/ai-survey/embed/<int:bridge_id>'], type='http', auth='public', website=True)
    def embed_ai_survey(self, bridge_id, **kw):
        """Embed an AI-generated survey in the chat"""
        bridge = request.env['ai.survey.bridge'].sudo().browse(bridge_id)

        if not bridge.exists() or not bridge.survey_id:
            return "<div class='alert alert-danger'>Survey not found</div>"

        survey = bridge.survey_id

        # Create a user_input (survey response) if not exists
        user_input = request.env['survey.user_input'].sudo().search([
            ('survey_id', '=', survey.id),
            ('partner_id', '=', request.env.user.partner_id.id),
            ('state', '!=', 'done')
        ], limit=1)

        if not user_input:
            user_input = request.env['survey.user_input'].sudo().create({
                'survey_id': survey.id,
                'partner_id': request.env.user.partner_id.id,
            })

        # Get the first page/question
        first_page = request.env['survey.question'].sudo().search([
            ('survey_id', '=', survey.id),
            ('is_page', '=', True)
        ], limit=1, order='sequence')

        # Render the survey form
        values = {
            'survey': survey,
            'user_input': user_input,
            'page': first_page,
            'embedded_in_chat': True,
        }

        return request.render('ai_dynamic_forms.embedded_survey_form', values)

    @http.route(['/survey/<string:survey_token>/<string:input_token>'], type='http', auth='public', website=True, methods=['POST'])
    def survey_submit(self, survey_token, input_token, **post):
        """Handle survey submission from embedded form"""
        # Check if this is an embedded survey submission
        if post.get('embedded_in_chat'):
            # Process the survey submission
            survey_controller = request.env['survey.survey'].get_survey_controller()
            result = survey_controller.submit(survey_token, input_token, **post)

            # Return a simple success message for embedded forms
            return """
                <div class="text-center p-2">
                    <div class="mb-2">
                        <i class="fa fa-check-circle text-success" style="font-size: 2rem;"></i>
                    </div>
                    <p class="mb-0">Thanks! I've received your information.</p>
                </div>
            """

        # If not embedded, let the standard survey controller handle it
        return request.env['survey.survey'].get_survey_controller().submit(survey_token, input_token, **post)

    @http.route(['/dynamic-forms/parse-ai-response'], type='json', auth='user')
    def parse_ai_response(self, **post):
        """Parse AI response to extract form data and create a survey"""
        ai_response = post.get('ai_response')
        if not ai_response:
            return {'error': 'No AI response provided'}

        document_request_id = post.get('document_request_id')
        conversation_id = post.get('conversation_id')

        bridge = request.env['ai.survey.bridge'].sudo().create_from_ai_response(
            ai_response, document_request_id, conversation_id
        )

        if not bridge:
            return {'error': 'No form data found in AI response or failed to create survey'}

        # Get the survey public URL
        survey_url = f'/survey/start/{bridge.survey_id.access_token}'

        return {
            'success': True,
            'bridge_id': bridge.id,
            'survey_id': bridge.survey_id.id,
            'survey_url': survey_url,
            'form_json': bridge.form_json,
        }

    @http.route(['/dynamic-forms/get-survey-results/<int:bridge_id>'], type='json', auth='user')
    def get_survey_results(self, bridge_id, **post):
        """Get the results of a survey"""
        bridge = request.env['ai.survey.bridge'].sudo().browse(bridge_id)

        if not bridge.exists() or not bridge.survey_id:
            return {'error': 'Survey not found'}

        # Get the latest user input (response)
        user_input = request.env['survey.user_input'].sudo().search([
            ('survey_id', '=', bridge.survey_id.id),
            ('state', '=', 'done')
        ], limit=1, order='create_date desc')

        if not user_input:
            return {'error': 'No completed responses found'}

        # Get the answers
        answers = request.env['survey.user_input.line'].sudo().search([
            ('user_input_id', '=', user_input.id)
        ])

        # Format the answers
        formatted_answers = {}
        for answer in answers:
            question = answer.question_id

            # Skip page/section questions
            if question.is_page:
                continue

            # Get the answer value based on question type
            if question.question_type == 'text_box':
                value = answer.value_text_box
            elif question.question_type == 'numerical_box':
                value = answer.value_numerical_box
            elif question.question_type == 'date':
                value = answer.value_date
            elif question.question_type == 'datetime':
                value = answer.value_datetime
            elif question.question_type in ['simple_choice', 'multiple_choice']:
                if answer.suggested_answer_id:
                    value = answer.suggested_answer_id.value
                else:
                    value = None
            elif question.question_type == 'matrix':
                value = {
                    'row': answer.matrix_row_id.value,
                    'col': answer.suggested_answer_id.value
                }
            else:
                value = None

            formatted_answers[question.title] = value

        return {
            'success': True,
            'user_input_id': user_input.id,
            'answers': formatted_answers,
        }

    @http.route(['/dynamic-forms/submit-survey-to-ai/<int:bridge_id>'], type='json', auth='user')
    def submit_survey_to_ai(self, bridge_id, **post):
        """Submit survey results to AI"""
        bridge = request.env['ai.survey.bridge'].sudo().browse(bridge_id)

        if not bridge.exists() or not bridge.survey_id:
            return {'error': 'Survey not found'}

        # Get the document request
        document_request_id = bridge.document_request_id.id
        if not document_request_id:
            return {'error': 'No document request associated with this survey'}

        # Get the survey results
        results = self.get_survey_results(bridge_id)
        if 'error' in results:
            return results

        # Format the message for AI
        message = "Form submitted with the following information:\n\n"
        for question, answer in results['answers'].items():
            if answer is not None:
                message += f"- {question}: {answer}\n"

        # Send the message to AI
        ai_controller = request.env['ai.chatbot.integration.controller'].sudo()
        return ai_controller.send_message_to_ai(document_request_id, message)
