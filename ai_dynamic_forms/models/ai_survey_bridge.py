from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import logging
import json
import re

_logger = logging.getLogger(__name__)


class AISurveyBridge(models.Model):
    _name = 'ai.survey.bridge'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'AI Survey Bridge'
    _order = 'create_date desc'

    name = fields.Char(string='Name', required=True, tracking=True)
    description = fields.Text(string='Description', tracking=True)

    # Source information
    source = fields.Selection([
        ('ai', 'AI Generated'),
        ('manual', 'Manual'),
        ('template', 'From Template'),
    ], string='Source', default='ai', required=True, tracking=True)

    # AI Response data
    ai_response = fields.Text(string='AI Response', tracking=True)
    form_json = fields.Text(string='Form JSON', tracking=True)

    # Survey integration
    survey_id = fields.Many2one('survey.survey', string='Survey', tracking=True)
    user_input_count = fields.Integer(string='Responses', compute='_compute_user_input_count')

    # Related records
    document_request_id = fields.Many2one('legal.document.request', string='Document Request', tracking=True)
    conversation_id = fields.Many2one('ai.chatbot.conversation', string='Conversation', tracking=True)

    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('archived', 'Archived'),
    ], string='Status', default='draft', tracking=True)

    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    # Partner for portal access
    partner_id = fields.Many2one('res.partner', string='Partner',
                                default=lambda self: self.env.user.partner_id)

    @api.depends('survey_id')
    def _compute_user_input_count(self):
        for record in self:
            if record.survey_id:
                record.user_input_count = self.env['survey.user_input'].search_count([
                    ('survey_id', '=', record.survey_id.id)
                ])
            else:
                record.user_input_count = 0

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get('name'):
                vals['name'] = self.env['ir.sequence'].next_by_code('ai.survey.bridge') or 'New'
        return super().create(vals_list)

    def action_view_survey(self):
        """Open the survey"""
        self.ensure_one()
        if not self.survey_id:
            raise UserError(_("No survey is associated with this record."))

        return {
            'name': _('Survey'),
            'type': 'ir.actions.act_window',
            'res_model': 'survey.survey',
            'res_id': self.survey_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_responses(self):
        """View survey responses"""
        self.ensure_one()
        if not self.survey_id:
            raise UserError(_("No survey is associated with this record."))

        return {
            'name': _('Survey Responses'),
            'type': 'ir.actions.act_window',
            'res_model': 'survey.user_input',
            'view_mode': 'tree,form',
            'domain': [('survey_id', '=', self.survey_id.id)],
            'context': {'default_survey_id': self.survey_id.id},
        }

    def action_get_share_url(self):
        """Get the survey share URL"""
        self.ensure_one()
        if not self.survey_id:
            raise UserError(_("No survey is associated with this record."))

        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        share_url = f"{base_url}/survey/start/{self.survey_id.access_token}"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Survey URL'),
                'message': _('Survey URL: %s') % share_url,
                'sticky': True,
                'type': 'info',
            }
        }

    def action_activate(self):
        """Activate the survey bridge"""
        self.ensure_one()
        self.write({'state': 'active'})

    def action_archive(self):
        """Archive the survey bridge"""
        self.ensure_one()
        self.write({'state': 'archived'})

    def action_draft(self):
        """Set to draft"""
        self.ensure_one()
        self.write({'state': 'draft'})

    @api.model
    def create_from_ai_response(self, ai_response, document_request_id=None, conversation_id=None):
        """Create a survey bridge from AI response"""
        if not ai_response:
            return False

        # Extract form data from AI response
        form_data = self._extract_form_data_from_ai_response(ai_response)
        if not form_data:
            _logger.warning("No form data found in AI response")
            return False

        # Create the bridge record
        bridge_vals = {
            'name': form_data.get('title', 'AI Generated Form'),
            'description': form_data.get('description', ''),
            'source': 'ai',
            'ai_response': ai_response,
            'form_json': json.dumps(form_data),
            'document_request_id': document_request_id,
            'conversation_id': conversation_id,
        }

        bridge = self.create(bridge_vals)

        # Create the survey
        survey = bridge._create_survey_from_form_data(form_data)
        if survey:
            bridge.survey_id = survey.id
            bridge.action_activate()

        return bridge

    def _extract_form_data_from_ai_response(self, ai_response):
        """Extract form data from AI response"""
        try:
            # Look for JSON form data in the AI response
            # This is a simplified extraction - you may need to adjust based on your AI's response format

            # Try to find JSON blocks in the response
            json_pattern = r'```json\s*(.*?)\s*```'
            json_matches = re.findall(json_pattern, ai_response, re.DOTALL | re.IGNORECASE)

            for json_match in json_matches:
                try:
                    data = json.loads(json_match)
                    if self._is_valid_form_data(data):
                        return data
                except json.JSONDecodeError:
                    continue

            # If no JSON blocks found, try to parse the entire response as JSON
            try:
                data = json.loads(ai_response)
                if self._is_valid_form_data(data):
                    return data
            except json.JSONDecodeError:
                pass

            # If no JSON found, try to extract form-like content using patterns
            return self._extract_form_from_text(ai_response)

        except Exception as e:
            _logger.error(f"Error extracting form data from AI response: {e}")
            return False

    def _is_valid_form_data(self, data):
        """Check if the data contains valid form structure"""
        if not isinstance(data, dict):
            return False

        # Check for required form fields
        required_keys = ['questions', 'title']
        if not any(key in data for key in required_keys):
            return False

        return True

    def _extract_form_from_text(self, text):
        """Extract form structure from plain text"""
        # This is a basic implementation - you may want to enhance this
        # based on your specific AI response patterns

        form_data = {
            'title': 'AI Generated Form',
            'description': 'Form generated from AI response',
            'questions': []
        }

        # Look for question patterns
        question_patterns = [
            r'(?:Question|Q)\s*\d*[:\-]?\s*(.+?)(?=\n|$)',
            r'^\d+\.\s*(.+?)(?=\n|$)',
            r'^\*\s*(.+?)(?=\n|$)',
        ]

        for pattern in question_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 5:  # Avoid very short matches
                    form_data['questions'].append({
                        'title': match.strip(),
                        'question_type': 'text_box',
                        'is_required': True
                    })

        return form_data if form_data['questions'] else False

    def _create_survey_from_form_data(self, form_data):
        """Create a survey from form data"""
        try:
            # Create the survey
            survey_vals = {
                'title': form_data.get('title', 'AI Generated Survey'),
                'description': form_data.get('description', ''),
                'access_mode': 'public',
                'users_login_required': False,
                'is_attempts_limited': False,
                'is_time_limited': False,
            }

            survey = self.env['survey.survey'].create(survey_vals)

            # Create survey page
            page_vals = {
                'title': 'Questions',
                'survey_id': survey.id,
                'sequence': 1,
                'is_page': True,
            }
            page = self.env['survey.question'].create(page_vals)

            # Create questions
            questions = form_data.get('questions', [])
            for i, question_data in enumerate(questions):
                question_vals = {
                    'title': question_data.get('title', f'Question {i+1}'),
                    'survey_id': survey.id,
                    'page_id': page.id,
                    'sequence': i + 1,
                    'question_type': self._map_question_type(question_data.get('question_type', 'text_box')),
                    'is_required': question_data.get('is_required', True),
                }

                # Handle multiple choice questions
                if question_vals['question_type'] in ['simple_choice', 'multiple_choice']:
                    question = self.env['survey.question'].create(question_vals)

                    # Create answer choices
                    choices = question_data.get('choices', [])
                    for j, choice in enumerate(choices):
                        choice_vals = {
                            'value': choice if isinstance(choice, str) else choice.get('value', f'Choice {j+1}'),
                            'question_id': question.id,
                            'sequence': j + 1,
                        }
                        self.env['survey.question.answer'].create(choice_vals)
                else:
                    self.env['survey.question'].create(question_vals)

            return survey

        except Exception as e:
            _logger.error(f"Error creating survey from form data: {e}")
            return False

    def _map_question_type(self, ai_question_type):
        """Map AI question types to Odoo survey question types"""
        mapping = {
            'text': 'text_box',
            'text_box': 'text_box',
            'textarea': 'text_box',
            'number': 'numerical_box',
            'date': 'date',
            'datetime': 'datetime',
            'single_choice': 'simple_choice',
            'multiple_choice': 'multiple_choice',
            'radio': 'simple_choice',
            'checkbox': 'multiple_choice',
            'select': 'simple_choice',
            'email': 'text_box',
            'phone': 'text_box',
        }

        return mapping.get(ai_question_type, 'text_box')
