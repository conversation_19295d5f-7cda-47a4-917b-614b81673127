<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- AI Dynamic Forms Security Groups -->
        <record id="group_ai_dynamic_forms_user" model="res.groups">
            <field name="name">AI Dynamic Forms User</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <record id="group_ai_dynamic_forms_manager" model="res.groups">
            <field name="name">AI Dynamic Forms Manager</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('group_ai_dynamic_forms_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
    
    <data noupdate="1">
        <!-- Multi-company rules -->
        <record id="ai_survey_bridge_comp_rule" model="ir.rule">
            <field name="name">AI Survey Bridge: multi-company</field>
            <field name="model_id" ref="model_ai_survey_bridge"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <!-- Portal access rules -->
        <record id="ai_survey_bridge_portal_rule" model="ir.rule">
            <field name="name">Portal: AI Survey Bridge</field>
            <field name="model_id" ref="model_ai_survey_bridge"/>
            <field name="domain_force">[('partner_id', '=', user.partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        </record>
    </data>
</odoo>
