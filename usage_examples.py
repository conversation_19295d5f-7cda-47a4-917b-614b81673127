#!/usr/bin/env python3
"""
Odoo Module Manager - Usage Examples
===================================

Practical examples of how to use the Odoo Module Manager for common tasks.
"""

from odoo_module_manager import OdooModuleManager
from odoo_config import get_config
import sys

def example_basic_operations():
    """Example: Basic module operations"""
    print("🔧 Example: Basic Module Operations")
    print("="*50)
    
    # Get configuration
    config = get_config('production')  # or 'staging', 'local'
    
    # Initialize manager
    manager = OdooModuleManager(**config)
    
    # 1. Update module list
    print("📋 Updating module list...")
    manager.update_module_list()
    
    # 2. Check module status
    print("\n🔍 Checking module status...")
    module_info = manager.get_module_info('ai_module_generator')
    if module_info:
        print(f"Module: {module_info['name']}")
        print(f"State: {module_info['state']}")
        print(f"Version: {module_info.get('version', 'N/A')}")
    
    # 3. Install a module
    print("\n📦 Installing module...")
    success = manager.install_module('ovakil_customer_feedback')
    print(f"Installation: {'✅ Success' if success else '❌ Failed'}")
    
    # 4. Upgrade a module
    print("\n⬆️ Upgrading module...")
    success = manager.upgrade_module('ai_module_generator')
    print(f"Upgrade: {'✅ Success' if success else '❌ Failed'}")

def example_batch_operations():
    """Example: Batch operations on multiple modules"""
    print("\n🔄 Example: Batch Operations")
    print("="*50)
    
    config = get_config('production')
    manager = OdooModuleManager(**config)
    
    # List of modules to manage
    modules_to_upgrade = [
        'ai_module_generator',
        'ai_chatbot_integration',
        'ai_cash_management',
        'ovakil_customer_feedback'
    ]
    
    # Batch upgrade
    print("⬆️ Batch upgrading modules...")
    results = manager.batch_upgrade(modules_to_upgrade)
    
    print("\n📊 Upgrade Results:")
    for module, success in results.items():
        status = "✅ Success" if success else "❌ Failed"
        print(f"  {module}: {status}")

def example_module_analysis():
    """Example: Module analysis and reporting"""
    print("\n📊 Example: Module Analysis")
    print("="*50)
    
    config = get_config('production')
    manager = OdooModuleManager(**config)
    
    # List all installed modules
    installed_modules = manager.list_modules('installed')
    print(f"📦 Total installed modules: {len(installed_modules)}")
    
    # List modules that need upgrade
    upgrade_modules = manager.list_modules('to upgrade')
    print(f"⬆️ Modules needing upgrade: {len(upgrade_modules)}")
    
    # Show module status table
    ai_modules = [
        'ai_module_generator',
        'ai_chatbot_integration', 
        'ai_cash_management',
        'ai_dynamic_forms',
        'ovakil_customer_feedback'
    ]
    
    print("\n🔍 AI Modules Status:")
    manager.print_module_status(ai_modules)
    
    # Export module list
    filename = manager.export_module_list(state='installed')
    print(f"\n💾 Installed modules exported to: {filename}")

def example_dependency_management():
    """Example: Managing module dependencies"""
    print("\n🔗 Example: Dependency Management")
    print("="*50)
    
    config = get_config('production')
    manager = OdooModuleManager(**config)
    
    module_name = 'ai_module_generator'
    
    # Get dependencies
    dependencies = manager.get_module_dependencies(module_name)
    print(f"📋 Dependencies for '{module_name}':")
    for dep in dependencies:
        print(f"  - {dep}")
    
    # Check if all dependencies are installed
    print(f"\n🔍 Checking dependency status:")
    for dep in dependencies:
        dep_info = manager.get_module_info(dep)
        if dep_info:
            state = dep_info['state']
            status = "✅" if state == 'installed' else "❌"
            print(f"  {dep}: {state} {status}")

def example_safe_upgrade_workflow():
    """Example: Safe upgrade workflow with checks"""
    print("\n🛡️ Example: Safe Upgrade Workflow")
    print("="*50)
    
    config = get_config('production')
    manager = OdooModuleManager(**config)
    
    module_name = 'ai_module_generator'
    
    # Step 1: Check current status
    print(f"🔍 Checking current status of '{module_name}'...")
    module_info = manager.get_module_info(module_name)
    
    if not module_info:
        print(f"❌ Module '{module_name}' not found")
        return
    
    current_state = module_info['state']
    print(f"Current state: {current_state}")
    
    # Step 2: Check if upgrade is needed
    if current_state != 'installed':
        print(f"⚠️ Module is not installed (state: {current_state})")
        return
    
    # Step 3: Check dependencies
    print("🔗 Checking dependencies...")
    dependencies = manager.get_module_dependencies(module_name)
    
    for dep in dependencies:
        dep_info = manager.get_module_info(dep)
        if dep_info and dep_info['state'] != 'installed':
            print(f"⚠️ Dependency '{dep}' is not installed")
            return
    
    # Step 4: Perform upgrade
    print(f"⬆️ Upgrading '{module_name}'...")
    success = manager.upgrade_module(module_name)
    
    if success:
        print(f"✅ Module '{module_name}' upgraded successfully")
    else:
        print(f"❌ Failed to upgrade module '{module_name}'")

def example_custom_module_management():
    """Example: Managing custom AI modules"""
    print("\n🤖 Example: Custom AI Module Management")
    print("="*50)
    
    config = get_config('production')
    manager = OdooModuleManager(**config)
    
    # List of custom AI modules
    ai_modules = [
        'ai_module_generator',
        'ai_chatbot_integration',
        'ai_cash_management',
        'ai_dynamic_forms',
        'ai_metal_price_integration',
        'ai_dynamic_cart_expiry',
        'ovakil_customer_feedback'
    ]
    
    print("🔍 Checking AI modules status...")
    manager.print_module_status(ai_modules)
    
    # Find modules that need upgrade
    modules_to_upgrade = []
    for module_name in ai_modules:
        module_info = manager.get_module_info(module_name)
        if module_info and module_info['state'] == 'to upgrade':
            modules_to_upgrade.append(module_name)
    
    if modules_to_upgrade:
        print(f"\n⬆️ Found {len(modules_to_upgrade)} modules needing upgrade:")
        for module in modules_to_upgrade:
            print(f"  - {module}")
        
        # Batch upgrade
        print("\n🔄 Performing batch upgrade...")
        results = manager.batch_upgrade(modules_to_upgrade)
        
        # Show results
        print("\n📊 Upgrade Results:")
        for module, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            print(f"  {module}: {status}")
    else:
        print("✅ All AI modules are up to date")

def main():
    """Run all examples"""
    print("🚀 Odoo Module Manager - Usage Examples")
    print("="*60)
    
    try:
        # Run examples
        example_basic_operations()
        example_batch_operations()
        example_module_analysis()
        example_dependency_management()
        example_safe_upgrade_workflow()
        example_custom_module_management()
        
        print("\n🎉 All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Error running examples: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
