from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import json
import logging

_logger = logging.getLogger(__name__)


class FieldDefinition(models.Model):
    """
    Field Definition model for defining individual fields that will be included
    in the generated forms and models.
    """
    _name = 'field.definition'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Field Definition'
    _order = 'sequence, name'

    # Basic Information
    name = fields.Char(string='Field Name', required=True, tracking=True,
                      help="Technical name of the field")
    field_description = fields.Char(string='Field Label', required=True, tracking=True,
                                   help="Human-readable label for the field")
    help_text = fields.Text(string='Help Text',
                           help="Help text to display for the field")
    sequence = fields.Integer(string='Sequence', default=10,
                             help="Sequence for ordering fields")
    
    # Relationships
    template_id = fields.Many2one('module.template', string='Module Template', 
                                 required=True, ondelete='cascade')
    form_builder_id = fields.Many2one('form.builder', string='Form Builder',
                                     ondelete='cascade',
                                     help="Form that this field belongs to")
    
    # Field Type Configuration
    field_type = fields.Selection([
        ('char', 'Text (Single Line)'),
        ('text', 'Text (Multi Line)'),
        ('html', 'HTML'),
        ('integer', 'Integer'),
        ('float', 'Float'),
        ('monetary', 'Monetary'),
        ('boolean', 'Boolean (Checkbox)'),
        ('date', 'Date'),
        ('datetime', 'Date & Time'),
        ('selection', 'Selection (Dropdown)'),
        ('many2one', 'Many2one (Relation)'),
        ('many2many', 'Many2many (Multiple Relations)'),
        ('one2many', 'One2many (Sub-records)'),
        ('binary', 'File Upload'),
        ('image', 'Image'),
        ('email', 'Email'),
        ('phone', 'Phone'),
        ('url', 'URL'),
    ], string='Field Type', required=True, tracking=True)
    
    # Field Properties
    required = fields.Boolean(string='Required', default=False, tracking=True)
    readonly = fields.Boolean(string='Readonly', default=False, tracking=True)
    invisible = fields.Boolean(string='Invisible', default=False, tracking=True)
    store = fields.Boolean(string='Store in Database', default=True, tracking=True)
    index = fields.Boolean(string='Database Index', default=False, tracking=True)
    copy = fields.Boolean(string='Copy on Duplicate', default=True, tracking=True)
    translate = fields.Boolean(string='Translatable', default=False, tracking=True)
    
    # Default Value
    default_value = fields.Text(string='Default Value',
                               help="Default value for the field")
    
    # Selection Field Configuration
    selection_options = fields.Text(string='Selection Options',
                                   help="JSON array of selection options [['key', 'Label'], ...]")
    
    # Relational Field Configuration
    relation_model = fields.Char(string='Related Model',
                                help="Model name for relational fields (e.g., 'res.partner')")
    relation_field = fields.Char(string='Related Field',
                                help="Field name in the related model")
    domain = fields.Text(string='Domain',
                        help="Domain filter for relational fields")
    
    # Widget Configuration
    widget = fields.Selection([
        ('', 'Default'),
        ('email', 'Email'),
        ('phone', 'Phone'),
        ('url', 'URL'),
        ('color', 'Color Picker'),
        ('date', 'Date Picker'),
        ('datetime', 'DateTime Picker'),
        ('monetary', 'Monetary'),
        ('percentage', 'Percentage'),
        ('priority', 'Priority Stars'),
        ('selection', 'Selection'),
        ('radio', 'Radio Buttons'),
        ('image', 'Image'),
        ('binary', 'File'),
        ('html', 'HTML Editor'),
        ('text', 'Text Area'),
        # Many2one Widget Options
        ('many2one_dropdown', 'Many2one: Simple Dropdown'),
        ('many2one_searchable', 'Many2one: Searchable Dropdown'),
        ('many2one_radio', 'Many2one: Radio Button List'),
        ('many2one_autocomplete', 'Many2one: Autocomplete Input'),
        # Many2many Widget Options
        ('many2many_tags', 'Many2many: Tags'),
        ('many2many_checkboxes', 'Many2many: Checkbox List'),
        ('many2many_multiselect', 'Many2many: Multi-select Dropdown'),
        ('many2many_list', 'Many2many: List View'),
        # One2many Widget Options
        ('one2many_list', 'One2many: List View'),
        ('one2many_checkboxes', 'One2many: Checkbox List'),
        ('one2many_tags', 'One2many: Tag-based Entries'),
        ('one2many_inline', 'One2many: Inline Form'),
    ], string='Widget', help="Widget to use for displaying the field")
    
    # Validation Configuration
    validation_rules = fields.Text(string='Validation Rules',
                                  help="JSON configuration for field validation")
    
    # Form Display Configuration
    form_view_position = fields.Selection([
        ('top', 'Top Section'),
        ('middle', 'Middle Section'),
        ('bottom', 'Bottom Section'),
        ('sidebar', 'Sidebar'),
        ('tab', 'Separate Tab'),
    ], string='Form Position', default='middle')
    
    form_view_colspan = fields.Integer(string='Column Span', default=1,
                                      help="Number of columns this field should span")
    
    # Tree View Configuration
    tree_view_visible = fields.Boolean(string='Visible in List View', default=True)
    tree_view_optional = fields.Selection([
        ('show', 'Always Show'),
        ('hide', 'Always Hide'),
        ('optional', 'Optional (User Choice)'),
    ], string='List View Display', default='optional')
    
    # Search View Configuration
    search_view_filter = fields.Boolean(string='Add Search Filter', default=False)
    search_view_group_by = fields.Boolean(string='Add Group By', default=False)
    
    # Website Form Configuration
    website_form_visible = fields.Boolean(string='Visible on Website Form', default=True)
    website_form_required = fields.Boolean(string='Required on Website', default=False)
    website_form_placeholder = fields.Char(string='Placeholder Text',
                                          help="Placeholder text for website form")
    
    # Portal Configuration
    portal_visible = fields.Boolean(string='Visible in Portal', default=True)
    portal_readonly = fields.Boolean(string='Readonly in Portal', default=True)
    
    # API Configuration
    api_visible = fields.Boolean(string='Visible in API', default=True)
    api_readonly = fields.Boolean(string='Readonly in API', default=False)
    
    # Status
    active = fields.Boolean(string='Active', default=True, tracking=True)
    
    # Constraints
    _sql_constraints = [
        ('name_form_uniq', 'unique(name, form_builder_id)', 
         'Field name must be unique within the form!'),
    ]

    @api.constrains('name')
    def _check_name(self):
        """Validate field name format"""
        for record in self:
            if record.name:
                if not record.name.replace('_', '').isalnum():
                    raise ValidationError(_("Field name must contain only letters, numbers, and underscores"))
                if not record.name.islower():
                    raise ValidationError(_("Field name must be lowercase"))
                if record.name.startswith('x_'):
                    raise ValidationError(_("Field name cannot start with 'x_' (reserved for custom fields)"))

    @api.constrains('selection_options')
    def _check_selection_options(self):
        """Validate selection options JSON"""
        for record in self:
            if record.selection_options and record.field_type == 'selection':
                try:
                    options = json.loads(record.selection_options)
                    if not isinstance(options, list):
                        raise ValidationError(_("Selection options must be a JSON array"))
                    for option in options:
                        if not isinstance(option, list) or len(option) != 2:
                            raise ValidationError(_("Each selection option must be a [key, label] pair"))
                except json.JSONDecodeError as e:
                    raise ValidationError(_("Invalid selection options JSON: %s") % str(e))

    @api.constrains('validation_rules')
    def _check_validation_rules(self):
        """Validate validation rules JSON"""
        for record in self:
            if record.validation_rules:
                try:
                    json.loads(record.validation_rules)
                except json.JSONDecodeError as e:
                    raise ValidationError(_("Invalid validation rules JSON: %s") % str(e))

    @api.constrains('domain')
    def _check_domain(self):
        """Validate domain format"""
        for record in self:
            if record.domain:
                try:
                    # Try to evaluate the domain
                    eval(record.domain)
                except Exception as e:
                    raise ValidationError(_("Invalid domain format: %s") % str(e))

    @api.onchange('field_type')
    def _onchange_field_type(self):
        """Set default configurations based on field type"""
        if self.field_type == 'email':
            self.widget = 'email'
        elif self.field_type == 'phone':
            self.widget = 'phone'
        elif self.field_type == 'url':
            self.widget = 'url'
        elif self.field_type == 'date':
            self.widget = 'date'
        elif self.field_type == 'datetime':
            self.widget = 'datetime'
        elif self.field_type == 'monetary':
            self.widget = 'monetary'
        elif self.field_type == 'html':
            self.widget = 'html'
        elif self.field_type == 'text':
            self.widget = 'text'
        elif self.field_type == 'binary':
            self.widget = 'binary'
        elif self.field_type == 'image':
            self.widget = 'image'
        elif self.field_type == 'selection':
            self.widget = 'selection'
        elif self.field_type == 'many2one':
            self.widget = 'many2one_searchable'  # Default to searchable dropdown
        elif self.field_type == 'many2many':
            self.widget = 'many2many_tags'  # Default to tags
        elif self.field_type == 'one2many':
            self.widget = 'one2many_list'  # Default to list view

    @api.onchange('field_description')
    def _onchange_field_description(self):
        """Auto-generate field name from description"""
        if self.field_description and not self.name:
            # Convert description to field name format
            name = self.field_description.lower().replace(' ', '_')
            # Remove special characters
            name = ''.join(c for c in name if c.isalnum() or c == '_')
            self.name = name

    def get_selection_options(self):
        """Get parsed selection options"""
        self.ensure_one()
        if self.selection_options:
            try:
                return json.loads(self.selection_options)
            except json.JSONDecodeError:
                return []
        return []

    def set_selection_options(self, options_list):
        """Set selection options from list"""
        self.ensure_one()
        self.selection_options = json.dumps(options_list, indent=2)

    def get_validation_rules(self):
        """Get parsed validation rules"""
        self.ensure_one()
        if self.validation_rules:
            try:
                return json.loads(self.validation_rules)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_validation_rules(self, rules_dict):
        """Set validation rules from dictionary"""
        self.ensure_one()
        self.validation_rules = json.dumps(rules_dict, indent=2)

    def get_domain(self):
        """Get parsed domain"""
        self.ensure_one()
        if self.domain:
            try:
                return eval(self.domain)
            except:
                return []
        return []

    def generate_field_definition(self):
        """Generate the field definition for the Odoo model"""
        self.ensure_one()
        
        field_def = {
            'name': self.name,
            'string': self.field_description,
            'type': self.field_type,
            'required': self.required,
            'readonly': self.readonly,
            'store': self.store,
            'index': self.index,
            'copy': self.copy,
            'translate': self.translate,
        }
        
        if self.help_text:
            field_def['help'] = self.help_text
        
        if self.default_value:
            field_def['default'] = self.default_value
        
        if self.field_type == 'selection' and self.selection_options:
            field_def['selection'] = self.get_selection_options()
        
        if self.field_type in ['many2one', 'many2many', 'one2many'] and self.relation_model:
            field_def['comodel_name'] = self.relation_model
        
        if self.domain:
            field_def['domain'] = self.domain
        
        return field_def
