from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import json
import os
import shutil
import logging
from datetime import timedelta

_logger = logging.getLogger(__name__)


class GeneratedModule(models.Model):
    """
    Model to track and manage modules that have been generated
    from templates using the AI Module Generator.
    """
    _name = 'generated.module'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Generated Module'
    _order = 'create_date desc'

    # Basic Information
    name = fields.Char(string='Module Title', required=True, tracking=True,
                      help="Human-readable title of the generated module")
    code = fields.Char(string='Module Code', required=True, tracking=True,
                      help="Technical name of the generated module")
    description = fields.Text(string='Description', tracking=True,
                             help="Description of the generated module")

    # Template Relationship
    template_id = fields.Many2one('module.template', string='Source Template',
                                 required=True, ondelete='restrict', tracking=True)

    # Generation Information
    generation_date = fields.Datetime(string='Generation Date', default=fields.Datetime.now,
                                     readonly=True, tracking=True)
    generation_config = fields.Text(string='Generation Configuration',
                                   help="JSON configuration used for generation")
    module_path = fields.Char(string='Module Path', readonly=True,
                             help="File system path where the module was generated")

    # Module Status
    state = fields.Selection([
        ('generated', 'Generated'),
        ('installed', 'Installed'),
        ('active', 'Active'),
        ('error', 'Error'),
        ('archived', 'Archived'),
    ], string='Status', default='generated', tracking=True)

    # Website URLs
    website_url = fields.Char(string='Website URL', compute='_compute_website_url', store=False,
                             help="Main website URL for the generated module")
    form_urls = fields.Text(string='Form URLs', compute='_compute_form_urls', store=False,
                           help="All form URLs for the generated module")

    # Installation Information
    installation_date = fields.Datetime(string='Installation Date', readonly=True, tracking=True)
    installation_log = fields.Text(string='Installation Log', readonly=True)

    # Module Statistics
    form_count = fields.Integer(string='Number of Forms', compute='_compute_form_count')
    field_count = fields.Integer(string='Number of Fields', compute='_compute_field_count')
    submission_count = fields.Integer(string='Total Submissions', compute='_compute_submission_count')

    # Version Information
    version = fields.Char(string='Module Version', default='17.0.1.0.0', tracking=True)
    last_update_date = fields.Datetime(string='Last Update', readonly=True)

    # Error Information
    error_message = fields.Text(string='Error Message', readonly=True)
    error_traceback = fields.Text(string='Error Traceback', readonly=True)

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    # Active
    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Constraints
    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Module code must be unique!'),
    ]

    @api.depends('template_id')
    def _compute_form_count(self):
        """Compute the number of forms in the generated module"""
        for record in self:
            if record.template_id:
                record.form_count = len(record.template_id.form_builder_ids)
            else:
                record.form_count = 0

    @api.depends('code')
    def _compute_website_url(self):
        """Compute the main website URL for the module"""
        for record in self:
            if record.code:
                # Main module URL follows pattern: /{module_name}
                # Extract module name without prefix (e.g., ovakil_customer_feedback -> customer_feedback)
                module_name = record.code
                if '_' in module_name:
                    # Remove prefix (e.g., ovakil_)
                    parts = module_name.split('_', 1)
                    if len(parts) > 1:
                        module_name = parts[1]  # Use part after first underscore

                base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url', 'http://localhost:8069')
                record.website_url = f"{base_url}/{module_name}"
            else:
                record.website_url = False

    @api.depends('template_id', 'code')
    def _compute_form_urls(self):
        """Compute all form URLs for the module"""
        for record in self:
            if record.template_id and record.code:
                base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url', 'http://localhost:8069')
                urls = []

                # Extract module name without prefix
                module_name = record.code
                if '_' in module_name:
                    parts = module_name.split('_', 1)
                    if len(parts) > 1:
                        module_name = parts[1]

                # Main module URL
                urls.append(f"Main Module: {base_url}/{module_name}")

                # Individual form URLs
                for form in record.template_id.form_builder_ids:
                    if form.website_published:
                        # Form URLs use form.code with underscores replaced by hyphens
                        form_url = form.code.replace('_', '-')
                        urls.append(f"{form.name}: {base_url}/{form_url}")

                record.form_urls = '\n'.join(urls)
            else:
                record.form_urls = False

    @api.depends('template_id')
    def _compute_field_count(self):
        """Compute the total number of fields in the generated module"""
        for record in self:
            if record.template_id:
                total_fields = 0
                for form in record.template_id.form_builder_ids:
                    total_fields += len(form.field_definition_ids)
                record.field_count = total_fields
            else:
                record.field_count = 0

    def _compute_submission_count(self):
        """Compute the total number of submissions across all forms"""
        for record in self:
            # This will be implemented when the generated modules are active
            # and we can query their submission models
            record.submission_count = 0

    def get_generation_config(self):
        """Get parsed generation configuration"""
        self.ensure_one()
        if self.generation_config:
            try:
                return json.loads(self.generation_config)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_generation_config(self, config_dict):
        """Set generation configuration from dictionary"""
        self.ensure_one()
        self.generation_config = json.dumps(config_dict, indent=2)

    def action_open_website(self):
        """Action to open the main website URL"""
        self.ensure_one()
        if not self.website_url:
            raise UserError(_("No website URL available"))

        return {
            'type': 'ir.actions.act_url',
            'url': self.website_url,
            'target': 'new',
        }

    def action_view_backend_module(self):
        """Action to view the module in backend"""
        self.ensure_one()
        if not self.template_id or not self.template_id.form_builder_ids:
            raise UserError(_("No forms available in this module"))

        # Get the first form to open its backend view
        first_form = self.template_id.form_builder_ids[0]
        model_name = first_form.model_name

        return {
            'type': 'ir.actions.act_window',
            'name': f'{self.name} - Backend',
            'res_model': model_name,
            'view_mode': 'tree,form',
            'target': 'current',
        }

    def action_install_module(self):
        """Install the generated module in the Odoo instance"""
        self.ensure_one()

        if self.state not in ['generated', 'error']:
            raise ValidationError(_("Module can only be installed from 'Generated' or 'Error' state"))

        if not self.module_path or not os.path.exists(self.module_path):
            raise ValidationError(_("Module path does not exist: %s") % self.module_path)

        try:
            # Copy module to addons directory
            addons_path = self._get_addons_path()
            target_path = os.path.join(addons_path, self.code)

            if os.path.exists(target_path):
                # Remove existing module
                shutil.rmtree(target_path)

            # Copy the generated module
            shutil.copytree(self.module_path, target_path)

            # Update module list
            self.env['ir.module.module'].update_list()

            # Find and install the module
            module = self.env['ir.module.module'].search([('name', '=', self.code)])
            if module:
                module.button_immediate_install()

                self.write({
                    'state': 'installed',
                    'installation_date': fields.Datetime.now(),
                    'installation_log': _('Module installed successfully'),
                })

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Module Installed'),
                        'message': _('Module %s has been installed successfully') % self.name,
                        'type': 'success',
                    }
                }
            else:
                raise UserError(_("Module not found in module list after copying"))

        except Exception as e:
            error_msg = str(e)
            _logger.error("Error installing module %s: %s", self.code, error_msg)

            self.write({
                'state': 'error',
                'error_message': error_msg,
                'error_traceback': str(e),
            })

            raise UserError(_("Module installation failed: %s") % error_msg)

    def action_uninstall_module(self):
        """Uninstall the module from the Odoo instance"""
        self.ensure_one()

        if self.state != 'installed':
            raise ValidationError(_("Module must be installed to uninstall"))

        try:
            # Find and uninstall the module
            module = self.env['ir.module.module'].search([('name', '=', self.code)])
            if module and module.state == 'installed':
                module.button_immediate_uninstall()

            self.write({
                'state': 'generated',
                'installation_date': False,
                'installation_log': _('Module uninstalled'),
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Module Uninstalled'),
                    'message': _('Module %s has been uninstalled') % self.name,
                    'type': 'success',
                }
            }

        except Exception as e:
            error_msg = str(e)
            _logger.error("Error uninstalling module %s: %s", self.code, error_msg)
            raise UserError(_("Module uninstallation failed: %s") % error_msg)

    def action_regenerate_module(self):
        """Regenerate the module with current template configuration"""
        self.ensure_one()

        if not self.template_id:
            raise ValidationError(_("Source template is required for regeneration"))

        if self.template_id.state != 'active':
            raise ValidationError(_("Source template must be active for regeneration"))

        # Launch regeneration wizard
        return {
            'name': _('Regenerate Module'),
            'type': 'ir.actions.act_window',
            'res_model': 'module.generator.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.template_id.id,
                'default_regenerate_module_id': self.id,
                'default_module_name': self.code,
                'default_module_title': self.name,
            }
        }

    def action_view_template(self):
        """View the source template"""
        self.ensure_one()

        return {
            'name': _('Source Template'),
            'type': 'ir.actions.act_window',
            'res_model': 'module.template',
            'res_id': self.template_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_open_module_directory(self):
        """Open the module directory in file manager (if possible)"""
        self.ensure_one()

        if not self.module_path or not os.path.exists(self.module_path):
            raise ValidationError(_("Module path does not exist: %s") % self.module_path)

        # This would typically open the file manager
        # Implementation depends on the server environment
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Module Path'),
                'message': _('Module is located at: %s') % self.module_path,
                'type': 'info',
            }
        }

    def action_download_module(self):
        """Download the generated module as a ZIP file"""
        self.ensure_one()

        if not self.module_path or not os.path.exists(self.module_path):
            raise ValidationError(_("Module path does not exist: %s") % self.module_path)

        # This would create a ZIP file and return it for download
        # Implementation would involve creating a temporary ZIP file
        return {
            'type': 'ir.actions.act_url',
            'url': f'/module_generator/download/{self.id}',
            'target': 'self',
        }

    def _get_addons_path(self):
        """Get the addons path for module installation"""
        # This should return the appropriate addons path
        # In a real implementation, this would be configurable
        return '/mnt/extra-addons'

    @api.model
    def cleanup_old_modules(self, days=30):
        """Cleanup old generated modules that are not installed"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days)

        old_modules = self.search([
            ('state', '=', 'generated'),
            ('create_date', '<', cutoff_date),
        ])

        for module in old_modules:
            try:
                if module.module_path and os.path.exists(module.module_path):
                    shutil.rmtree(module.module_path, ignore_errors=True)
                module.unlink()
            except Exception as e:
                _logger.warning("Error cleaning up module %s: %s", module.code, str(e))
