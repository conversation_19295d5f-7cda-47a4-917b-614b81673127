from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import json
import logging
import os
import shutil
import tempfile
from datetime import datetime

_logger = logging.getLogger(__name__)


class ModuleTemplate(models.Model):
    """
    Core model for defining module templates that will be used to generate
    complete Odoo modules with all necessary components.
    """
    _name = 'module.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Module Template'
    _order = 'name'

    # Basic Information
    name = fields.Char(string='Template Name', required=True, tracking=True,
                      help="Name of the module template")
    code = fields.Char(string='Template Code', required=True, tracking=True,
                      help="Unique code for the template (will be used in module name)")
    description = fields.Text(string='Description', tracking=True,
                             help="Description of what this module template does")

    # Module Configuration
    module_prefix = fields.Char(string='Module Prefix', default='ovakil_', required=True,
                               help="Prefix for generated module names")
    module_author = fields.Char(string='Module Author', default='Oneclickvakil', required=True,
                               help="Author name for generated modules")
    module_website = fields.Char(string='Module Website', default='https://www.oneclickvakil.com',
                                help="Website URL for generated modules")
    module_category = fields.Char(string='Module Category', default='Tools',
                                 help="Category for generated modules")
    module_version = fields.Char(string='Module Version', default='********.0',
                                help="Version for generated modules")

    # Template Configuration
    template_type = fields.Selection([
        ('form_based', 'Form-Based Module'),
        ('service_based', 'Service-Based Module'),
        ('document_based', 'Document-Based Module'),
        ('custom', 'Custom Module'),
    ], string='Template Type', default='form_based', required=True, tracking=True)

    # Payment Configuration
    default_product_id = fields.Many2one('product.product', string='Default Payment Product',
                                        help='Default product for payment sales orders. Sales price will be used as default rate.')
    default_rate = fields.Float(string='Default Rate', compute='_compute_default_rate', store=True,
                               help='Default rate from selected product sales price')
    allow_rate_override = fields.Boolean(string='Allow Rate Override', default=True,
                                        help='Allow users to override the default rate on individual records')

    # JSON Configuration
    json_config = fields.Text(string='JSON Configuration',
                             help="JSON configuration for the module template")

    # Form Builder Configuration
    form_builder_ids = fields.One2many('form.builder', 'template_id', string='Form Builders',
                                      help="Form builders associated with this template")

    # Field Definitions
    field_definition_ids = fields.One2many('field.definition', 'template_id', string='Field Definitions',
                                          help="Field definitions for the module")

    # Workflow Configuration
    workflow_state_ids = fields.One2many('workflow.state', 'template_id', string='Workflow States',
                                        help="Workflow states for the module")

    # AI Configuration (to be implemented)
    # ai_prompt_config_ids = fields.One2many('ai.prompt.config', 'template_id', string='AI Prompt Configurations',
    #                                       help="AI prompt configurations for document generation")

    # Payment Configuration (to be implemented)
    # payment_config_ids = fields.One2many('payment.config', 'template_id', string='Payment Configurations',
    #                                     help="Payment configurations for the module")

    # Generated Modules
    generated_module_ids = fields.One2many('generated.module', 'template_id', string='Generated Modules',
                                          help="Modules generated from this template")

    # Status and Control
    state = fields.Selection([
        ('draft', 'Draft'),
        ('validated', 'Validated'),
        ('active', 'Active'),
        ('archived', 'Archived'),
    ], string='Status', default='draft', tracking=True)

    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Statistics
    generation_count = fields.Integer(string='Generation Count', compute='_compute_generation_count',
                                     help="Number of modules generated from this template")

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    # Constraints
    _sql_constraints = [
        ('code_uniq', 'unique(code)', 'Template code must be unique!'),
        ('name_uniq', 'unique(name)', 'Template name must be unique!'),
    ]

    @api.depends('generated_module_ids')
    def _compute_generation_count(self):
        """Compute the number of modules generated from this template"""
        for record in self:
            record.generation_count = len(record.generated_module_ids)

    @api.depends('default_product_id')
    def _compute_default_rate(self):
        """Compute default rate from product sales price"""
        for record in self:
            if record.default_product_id:
                record.default_rate = record.default_product_id.list_price
            else:
                record.default_rate = 0.0

    @api.constrains('code')
    def _check_code(self):
        """Validate template code format"""
        for record in self:
            if record.code:
                if not record.code.replace('_', '').isalnum():
                    raise ValidationError(_("Template code must contain only letters, numbers, and underscores"))
                if not record.code.islower():
                    raise ValidationError(_("Template code must be lowercase"))

    @api.constrains('json_config')
    def _check_json_config(self):
        """Validate JSON configuration"""
        for record in self:
            if record.json_config:
                try:
                    json.loads(record.json_config)
                except json.JSONDecodeError as e:
                    raise ValidationError(_("Invalid JSON configuration: %s") % str(e))

    def action_validate_template(self):
        """Validate the template configuration"""
        self.ensure_one()

        # Validate basic configuration
        if not self.name or not self.code:
            raise ValidationError(_("Template name and code are required"))

        # Validate form builders
        if not self.form_builder_ids:
            raise ValidationError(_("At least one form builder is required"))

        # Validate field definitions
        if not self.field_definition_ids:
            raise ValidationError(_("At least one field definition is required"))

        # Validate workflow states
        if not self.workflow_state_ids:
            raise ValidationError(_("At least one workflow state is required"))

        # Check for required workflow states
        required_states = ['draft', 'submitted']
        existing_states = self.workflow_state_ids.mapped('code')
        missing_states = [state for state in required_states if state not in existing_states]

        if missing_states:
            raise ValidationError(_("Missing required workflow states: %s") % ', '.join(missing_states))

        self.write({'state': 'validated'})

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Template Validated'),
                'message': _('Template has been successfully validated and is ready for module generation.'),
                'type': 'success',
            }
        }

    def action_activate_template(self):
        """Activate the template for module generation"""
        self.ensure_one()

        if self.state != 'validated':
            raise ValidationError(_("Template must be validated before activation"))

        self.write({'state': 'active'})

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Template Activated'),
                'message': _('Template is now active and available for module generation.'),
                'type': 'success',
            }
        }

    def action_generate_module(self):
        """Launch the module generation wizard"""
        self.ensure_one()

        if self.state != 'active':
            raise ValidationError(_("Template must be active to generate modules"))

        return {
            'name': _('Generate Module'),
            'type': 'ir.actions.act_window',
            'res_model': 'module.generator.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
            }
        }

    def action_view_generated_modules(self):
        """View modules generated from this template"""
        self.ensure_one()

        return {
            'name': _('Generated Modules'),
            'type': 'ir.actions.act_window',
            'res_model': 'generated.module',
            'view_mode': 'tree,form',
            'domain': [('template_id', '=', self.id)],
            'context': {'default_template_id': self.id},
        }

    def action_duplicate_template(self):
        """Create a copy of this template"""
        self.ensure_one()

        copy_name = _("%s (Copy)") % self.name
        copy_code = "%s_copy" % self.code

        # Ensure unique code
        counter = 1
        while self.search([('code', '=', copy_code)]):
            copy_code = "%s_copy_%d" % (self.code, counter)
            counter += 1

        new_template = self.copy({
            'name': copy_name,
            'code': copy_code,
            'state': 'draft',
        })

        return {
            'name': _('Template Copy'),
            'type': 'ir.actions.act_window',
            'res_model': 'module.template',
            'res_id': new_template.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def get_module_name(self):
        """Generate the full module name"""
        self.ensure_one()
        return f"{self.module_prefix}{self.code}"

    def get_json_config(self):
        """Get parsed JSON configuration"""
        self.ensure_one()
        if self.json_config:
            try:
                return json.loads(self.json_config)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_json_config(self, config_dict):
        """Set JSON configuration from dictionary"""
        self.ensure_one()
        self.json_config = json.dumps(config_dict, indent=2)
