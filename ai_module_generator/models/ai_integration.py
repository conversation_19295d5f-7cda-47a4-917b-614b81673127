# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import json
import logging
import requests

_logger = logging.getLogger(__name__)


class AIPromptConfig(models.Model):
    """AI Prompt Configuration for Form Builders"""
    _name = 'ai.prompt.config'
    _description = 'AI Prompt Configuration'
    _order = 'sequence, name'

    name = fields.Char(string='Prompt Name', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    form_builder_id = fields.Many2one('form.builder', string='Form Builder', ondelete='cascade')
    # Simplified - removed template_id to avoid relationship issues

    # Prompt Configuration
    prompt_type = fields.Selection([
        ('document_generation', 'Document Generation'),
        ('data_validation', 'Data Validation'),
        ('auto_response', 'Auto Response'),
        ('content_analysis', 'Content Analysis'),
        ('recommendation', 'Recommendation'),
        ('system_prompt', 'System Prompt for Records'),  # ✨ NEW
    ], string='Prompt Type', required=True, default='document_generation')

    # ✨ NEW: Chatbot Integration
    chatbot_config_id = fields.Many2one('ai.chatbot.config', string='Chatbot Configuration',
                                       help="Select chatbot configuration for AI responses")

    # ✨ NEW: Button Configuration
    button_label = fields.Char(string='Button Label', default='Get AI Response',
                              help="Label for the AI button in generated forms")
    button_icon = fields.Char(string='Button Icon', default='fa-magic',
                             help="Font Awesome icon for the AI button")

    # ✨ NEW: PDF Generation
    enable_pdf_generation = fields.Boolean(string='Enable PDF Generation', default=True,
                                          help="Allow generating PDF from AI responses")
    pdf_template_name = fields.Char(string='PDF Template Name', default='AI Response Report',
                                   help="Name for the generated PDF template")

    # ✨ NEW: Portal Access
    portal_access = fields.Selection([
        ('none', 'No Portal Access'),
        ('paid_only', 'Paid Customers Only'),
        ('all_customers', 'All Customers'),
    ], string='Portal PDF Access', default='paid_only',
       help="Who can download PDF from customer portal")

    prompt_template = fields.Text(string='Prompt Template', required=True,
                                 help="Use {field_name} placeholders for dynamic content")
    system_prompt = fields.Text(string='System Prompt',
                               help="System instructions for the AI model")

    # AI Model Configuration
    ai_model = fields.Selection([
        ('gpt-3.5-turbo', 'GPT-3.5 Turbo'),
        ('gpt-4', 'GPT-4'),
        ('gpt-4-turbo', 'GPT-4 Turbo'),
        ('claude-3-sonnet', 'Claude 3 Sonnet'),
        ('claude-3-opus', 'Claude 3 Opus'),
        ('gemini-pro', 'Gemini Pro'),
        ('groq-llama3-70b', 'Groq Llama 3 70B'),
        ('groq-llama3-8b', 'Groq Llama 3 8B'),
        ('groq-mixtral-8x7b', 'Groq Mixtral 8x7B'),
        ('custom-openai-compatible', 'Custom OpenAI Compatible API'),
    ], string='AI Model', default='gpt-3.5-turbo')

    # ✨ NEW: Custom API Configuration
    use_custom_api = fields.Boolean(string='Use Custom API', default=False,
                                   help="Enable to use custom OpenAI-compatible API (like Groq, LocalAI, etc.)")
    custom_api_url = fields.Char(string='Custom API URL',
                                help="Custom API endpoint URL (e.g., https://api.groq.com/openai/v1/chat/completions)")
    custom_api_key = fields.Char(string='Custom API Key',
                                help="API key for the custom service")
    custom_model_name = fields.Char(string='Custom Model Name',
                                   help="Model name to use with custom API (e.g., llama3-70b-8192)")

    max_tokens = fields.Integer(string='Max Tokens', default=1000)
    temperature = fields.Float(string='Temperature', default=0.7,
                              help="Controls randomness (0.0 to 1.0)")

    # Trigger Configuration
    trigger_event = fields.Selection([
        ('on_create', 'On Record Creation'),
        ('on_submit', 'On Form Submission'),
        ('on_approve', 'On Approval'),
        ('manual', 'Manual Trigger'),
    ], string='Trigger Event', default='on_submit')

    auto_execute = fields.Boolean(string='Auto Execute', default=True,
                                 help="Automatically execute when triggered")

    # Output Configuration
    output_field = fields.Many2one('field.definition', string='Output Field',
                                  domain="[('form_builder_id', '=', form_builder_id)]",
                                  help="Field to store the AI response")
    output_format = fields.Selection([
        ('text', 'Plain Text'),
        ('html', 'HTML'),
        ('json', 'JSON'),
        ('markdown', 'Markdown'),
    ], string='Output Format', default='text')

    # Status and Logging
    active = fields.Boolean(string='Active', default=True)
    execution_count = fields.Integer(string='Execution Count', default=0, readonly=True)
    last_execution = fields.Datetime(string='Last Execution', readonly=True)

    @api.depends('form_builder_id.template_id')
    def _compute_template_id(self):
        """Compute template_id from form_builder_id"""
        for record in self:
            if record.form_builder_id and record.form_builder_id.template_id:
                record.template_id = record.form_builder_id.template_id
            else:
                record.template_id = False

    @api.constrains('prompt_template')
    def _check_prompt_template(self):
        """Validate prompt template"""
        for record in self:
            if record.prompt_template:
                # Check for valid placeholder format
                import re
                placeholders = re.findall(r'\{(\w+)\}', record.prompt_template)
                if placeholders:
                    # Validate that placeholders match available fields
                    available_fields = record.form_builder_id.field_definition_ids.mapped('name')
                    invalid_fields = [p for p in placeholders if p not in available_fields + ['name', 'state', 'create_date']]
                    if invalid_fields:
                        raise ValidationError(_("Invalid placeholders in prompt template: %s") % ', '.join(invalid_fields))

    def execute_prompt(self, record_data):
        """Execute AI prompt with given record data"""
        self.ensure_one()

        if not self.active:
            return False

        try:
            # Prepare prompt with data
            formatted_prompt = self._format_prompt(record_data)

            # Call AI service
            response = self._call_ai_service(formatted_prompt)

            # Update execution stats
            self.execution_count += 1
            self.last_execution = fields.Datetime.now()

            return response

        except Exception as e:
            _logger.error(f"AI prompt execution failed: {str(e)}")
            raise UserError(_("AI prompt execution failed: %s") % str(e))

    def _format_prompt(self, record_data):
        """Format prompt template with record data"""
        self.ensure_one()

        prompt = self.prompt_template

        # Replace placeholders with actual data
        for field_name, value in record_data.items():
            placeholder = '{' + field_name + '}'
            if placeholder in prompt:
                prompt = prompt.replace(placeholder, str(value or ''))

        return prompt

    def _call_ai_service(self, prompt):
        """Call AI service API"""
        self.ensure_one()

        # ✨ NEW: Use custom API if configured
        if self.use_custom_api:
            return self._call_custom_api(prompt)

        # ✨ Use chatbot configuration if available
        if self.chatbot_config_id:
            return self._call_chatbot_api(prompt)

        # Handle Groq models with built-in configuration
        if self.ai_model.startswith('groq-'):
            return self._call_groq_builtin_api(prompt)

        # Fallback to original AI service configuration
        ai_config = self.env['ir.config_parameter'].sudo()
        api_key = ai_config.get_param('ai_module_generator.openai_api_key')

        if not api_key:
            raise UserError(_("OpenAI API key not configured. Please configure it in Settings."))

        # Prepare API request based on model
        if self.ai_model.startswith('gpt'):
            return self._call_openai_api(prompt, api_key)
        elif self.ai_model.startswith('claude'):
            return self._call_anthropic_api(prompt, api_key)
        elif self.ai_model.startswith('gemini'):
            return self._call_google_api(prompt, api_key)
        else:
            raise UserError(_("Unsupported AI model: %s") % self.ai_model)

    def _call_chatbot_api(self, prompt):
        """Call AI service using chatbot configuration"""
        self.ensure_one()

        if not self.chatbot_config_id:
            raise UserError(_("No chatbot configuration selected"))

        chatbot = self.chatbot_config_id

        try:
            if chatbot.api_type == 'groq':
                return self._call_groq_api(prompt, chatbot)
            elif chatbot.api_type == 'openai':
                return self._call_openai_chatbot_api(prompt, chatbot)
            else:
                raise UserError(_("Unsupported chatbot API type: %s") % chatbot.api_type)
        except Exception as e:
            _logger.error(f"Chatbot API call failed: {str(e)}")
            raise UserError(_("AI service call failed: %s") % str(e))

    def _call_groq_api(self, prompt, chatbot):
        """Call Groq API using chatbot configuration"""
        headers = {
            'Authorization': f'Bearer {chatbot.api_key}',
            'Content-Type': 'application/json'
        }

        messages = []
        if self.system_prompt or chatbot.system_prompt:
            system_content = self.system_prompt or chatbot.system_prompt
            messages.append({'role': 'system', 'content': system_content})
        messages.append({'role': 'user', 'content': prompt})

        data = {
            'model': chatbot.api_model or 'llama3-70b-8192',
            'messages': messages,
            'temperature': chatbot.temperature,
            'max_tokens': chatbot.max_tokens,
            'top_p': chatbot.top_p,
        }

        url = chatbot.api_url or 'https://api.groq.com/openai/v1/chat/completions'
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()
        return result['choices'][0]['message']['content']

    def _call_openai_chatbot_api(self, prompt, chatbot):
        """Call OpenAI API using chatbot configuration"""
        headers = {
            'Authorization': f'Bearer {chatbot.api_key}',
            'Content-Type': 'application/json'
        }

        messages = []
        if self.system_prompt or chatbot.system_prompt:
            system_content = self.system_prompt or chatbot.system_prompt
            messages.append({'role': 'system', 'content': system_content})
        messages.append({'role': 'user', 'content': prompt})

        data = {
            'model': chatbot.api_model or 'gpt-3.5-turbo',
            'messages': messages,
            'temperature': chatbot.temperature,
            'max_tokens': chatbot.max_tokens,
        }

        url = chatbot.api_url or 'https://api.openai.com/v1/chat/completions'
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()
        return result['choices'][0]['message']['content']

    def _call_openai_api(self, prompt, api_key):
        """Call OpenAI API"""
        url = "https://api.openai.com/v1/chat/completions"

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
        }

        messages = []
        if self.system_prompt:
            messages.append({"role": "system", "content": self.system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            'model': self.ai_model,
            'messages': messages,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
        }

        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()
        return result['choices'][0]['message']['content']

    def _call_anthropic_api(self, prompt, api_key):
        """Call Anthropic Claude API"""
        # Implementation for Claude API
        raise UserError(_("Claude API integration not yet implemented"))

    def _call_google_api(self, prompt, api_key):
        """Call Google Gemini API"""
        # Implementation for Gemini API
        raise UserError(_("Gemini API integration not yet implemented"))

    def _call_custom_api(self, prompt):
        """Call custom OpenAI-compatible API"""
        self.ensure_one()

        if not self.custom_api_url or not self.custom_api_key:
            raise UserError(_("Custom API URL and API key are required when using custom API"))

        headers = {
            'Authorization': f'Bearer {self.custom_api_key}',
            'Content-Type': 'application/json'
        }

        messages = []
        if self.system_prompt:
            messages.append({'role': 'system', 'content': self.system_prompt})
        messages.append({'role': 'user', 'content': prompt})

        # Use custom model name if provided, otherwise use the selected model
        model_name = self.custom_model_name or self.ai_model

        data = {
            'model': model_name,
            'messages': messages,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
        }

        try:
            response = requests.post(self.custom_api_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            _logger.error(f"Custom API call failed: {str(e)}")
            raise UserError(_("Custom API call failed: %s") % str(e))

    def _call_groq_builtin_api(self, prompt):
        """Call Groq API with built-in configuration"""
        self.ensure_one()

        # Get Groq API key from system parameters
        ai_config = self.env['ir.config_parameter'].sudo()
        api_key = ai_config.get_param('ai_module_generator.groq_api_key')

        if not api_key:
            raise UserError(_("Groq API key not configured. Please configure it in Settings or use custom API configuration."))

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        messages = []
        if self.system_prompt:
            messages.append({'role': 'system', 'content': self.system_prompt})
        messages.append({'role': 'user', 'content': prompt})

        # Map model names to Groq model names
        model_mapping = {
            'groq-llama3-70b': 'llama3-70b-8192',
            'groq-llama3-8b': 'llama3-8b-8192',
            'groq-mixtral-8x7b': 'mixtral-8x7b-32768',
        }

        model_name = model_mapping.get(self.ai_model, 'llama3-70b-8192')

        data = {
            'model': model_name,
            'messages': messages,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
        }

        try:
            url = 'https://api.groq.com/openai/v1/chat/completions'
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result['choices'][0]['message']['content']
        except Exception as e:
            _logger.error(f"Groq API call failed: {str(e)}")
            raise UserError(_("Groq API call failed: %s") % str(e))

    def test_prompt(self):
        """Test the prompt with sample data"""
        self.ensure_one()

        # Create sample data from form fields
        sample_data = {}
        for field in self.form_builder_id.field_definition_ids:
            if field.field_type == 'char':
                sample_data[field.name] = f"Sample {field.field_description}"
            elif field.field_type == 'text':
                sample_data[field.name] = f"Sample {field.field_description} content"
            elif field.field_type == 'integer':
                sample_data[field.name] = 123
            elif field.field_type == 'float':
                sample_data[field.name] = 123.45
            elif field.field_type == 'boolean':
                sample_data[field.name] = True
            elif field.field_type == 'selection':
                options = field.get_selection_options()
                sample_data[field.name] = options[0][0] if options else 'option1'
            else:
                sample_data[field.name] = f"Sample {field.field_type}"

        # Add system fields
        sample_data.update({
            'name': 'TEST-001',
            'state': 'draft',
            'create_date': fields.Datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        })

        try:
            result = self.execute_prompt(sample_data)

            return {
                'type': 'ir.actions.act_window',
                'name': _('AI Prompt Test Result'),
                'res_model': 'ai.prompt.test.wizard',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_prompt_config_id': self.id,
                    'default_test_result': result,
                    'default_sample_data': json.dumps(sample_data, indent=2),
                }
            }
        except Exception as e:
            raise UserError(_("Test failed: %s") % str(e))

    def execute_prompt(self, record_data):
        """Execute AI prompt with record data"""
        self.ensure_one()

        # Format prompt with record data
        formatted_prompt = self._format_prompt(record_data)

        # Call AI service
        response = self._call_ai_service(formatted_prompt)

        # Update execution count
        self.execution_count += 1

        return response

    def _format_prompt(self, record_data):
        """Format prompt template with record data"""
        self.ensure_one()

        prompt = self.prompt_template or ''

        # Replace field variables with actual values
        for field_name, field_value in record_data.items():
            placeholder = f'{{{field_name}}}'
            prompt = prompt.replace(placeholder, str(field_value))

        return prompt


class AIPromptTestWizard(models.TransientModel):
    """Wizard to display AI prompt test results"""
    _name = 'ai.prompt.test.wizard'
    _description = 'AI Prompt Test Wizard'

    prompt_config_id = fields.Many2one('ai.prompt.config', string='Prompt Configuration')
    sample_data = fields.Text(string='Sample Data', readonly=True)
    test_result = fields.Text(string='AI Response', readonly=True)

    def action_close(self):
        """Close the wizard"""
        return {'type': 'ir.actions.act_window_close'}


class AIExecutionLog(models.Model):
    """Log AI prompt executions"""
    _name = 'ai.execution.log'
    _description = 'AI Execution Log'
    _order = 'create_date desc'

    prompt_config_id = fields.Many2one('ai.prompt.config', string='Prompt Configuration', required=True)
    record_model = fields.Char(string='Record Model')
    record_id = fields.Integer(string='Record ID')
    input_data = fields.Text(string='Input Data')
    output_data = fields.Text(string='Output Data')
    execution_time = fields.Float(string='Execution Time (seconds)')
    status = fields.Selection([
        ('success', 'Success'),
        ('error', 'Error'),
    ], string='Status', default='success')
    error_message = fields.Text(string='Error Message')
