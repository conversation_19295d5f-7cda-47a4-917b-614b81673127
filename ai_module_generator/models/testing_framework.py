# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import json
import logging
import time
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class ModuleTestSuite(models.Model):
    """Test Suite for Generated Modules"""
    _name = 'module.test.suite'
    _description = 'Module Test Suite'
    _order = 'name'

    name = fields.Char(string='Test Suite Name', required=True)
    template_id = fields.Many2one('module.template', string='Module Template', required=True)
    generated_module_id = fields.Many2one('generated.module', string='Generated Module')
    
    # Test Configuration
    test_type = fields.Selection([
        ('unit', 'Unit Tests'),
        ('integration', 'Integration Tests'),
        ('functional', 'Functional Tests'),
        ('performance', 'Performance Tests'),
        ('security', 'Security Tests'),
    ], string='Test Type', required=True, default='functional')
    
    # Test Cases
    test_case_ids = fields.One2many('module.test.case', 'test_suite_id', string='Test Cases')
    
    # Execution Results
    last_execution = fields.Datetime(string='Last Execution', readonly=True)
    execution_count = fields.Integer(string='Execution Count', default=0, readonly=True)
    success_rate = fields.Float(string='Success Rate (%)', compute='_compute_success_rate', store=True)
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('ready', 'Ready'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='draft', tracking=True)
    
    active = fields.Boolean(string='Active', default=True)
    
    @api.depends('test_case_ids.result')
    def _compute_success_rate(self):
        """Compute success rate based on test case results"""
        for suite in self:
            total_cases = len(suite.test_case_ids)
            if total_cases == 0:
                suite.success_rate = 0.0
            else:
                passed_cases = len(suite.test_case_ids.filtered(lambda tc: tc.result == 'passed'))
                suite.success_rate = (passed_cases / total_cases) * 100

    def action_run_tests(self):
        """Run all test cases in the suite"""
        self.ensure_one()
        
        if self.state == 'running':
            raise UserError(_("Test suite is already running"))
        
        self.state = 'running'
        
        try:
            # Run each test case
            for test_case in self.test_case_ids:
                test_case.execute_test()
            
            # Update execution stats
            self.execution_count += 1
            self.last_execution = fields.Datetime.now()
            self.state = 'completed'
            
            # Check if any tests failed
            failed_tests = self.test_case_ids.filtered(lambda tc: tc.result == 'failed')
            if failed_tests:
                self.state = 'failed'
            
        except Exception as e:
            self.state = 'failed'
            _logger.error(f"Test suite execution failed: {str(e)}")
            raise UserError(_("Test suite execution failed: %s") % str(e))

    def action_generate_test_cases(self):
        """Auto-generate test cases based on template configuration"""
        self.ensure_one()
        
        # Clear existing test cases
        self.test_case_ids.unlink()
        
        # Generate test cases for each form
        for form in self.template_id.form_builder_ids:
            self._generate_form_test_cases(form)
        
        self.state = 'ready'

    def _generate_form_test_cases(self, form):
        """Generate test cases for a specific form"""
        test_cases = []
        
        # Basic CRUD tests
        test_cases.extend([
            {
                'name': f'Test {form.name} - Create Record',
                'test_type': 'crud',
                'test_method': 'test_create',
                'form_builder_id': form.id,
                'test_data': self._generate_test_data(form),
            },
            {
                'name': f'Test {form.name} - Read Record',
                'test_type': 'crud',
                'test_method': 'test_read',
                'form_builder_id': form.id,
            },
            {
                'name': f'Test {form.name} - Update Record',
                'test_type': 'crud',
                'test_method': 'test_update',
                'form_builder_id': form.id,
                'test_data': self._generate_test_data(form, update=True),
            },
            {
                'name': f'Test {form.name} - Delete Record',
                'test_type': 'crud',
                'test_method': 'test_delete',
                'form_builder_id': form.id,
            },
        ])
        
        # Validation tests
        if form.field_definition_ids.filtered('required'):
            test_cases.append({
                'name': f'Test {form.name} - Required Field Validation',
                'test_type': 'validation',
                'test_method': 'test_required_fields',
                'form_builder_id': form.id,
                'test_data': self._generate_invalid_test_data(form),
            })
        
        # Workflow tests
        if self.template_id.workflow_state_ids:
            test_cases.append({
                'name': f'Test {form.name} - Workflow Transitions',
                'test_type': 'workflow',
                'test_method': 'test_workflow',
                'form_builder_id': form.id,
            })
        
        # Website form tests
        if form.website_published:
            test_cases.append({
                'name': f'Test {form.name} - Website Form Submission',
                'test_type': 'website',
                'test_method': 'test_website_form',
                'form_builder_id': form.id,
                'test_data': self._generate_test_data(form),
            })
        
        # API tests
        if form.api_enabled:
            test_cases.append({
                'name': f'Test {form.name} - API Endpoints',
                'test_type': 'api',
                'test_method': 'test_api_endpoints',
                'form_builder_id': form.id,
                'test_data': self._generate_test_data(form),
            })
        
        # Create test case records
        for test_case_data in test_cases:
            test_case_data['test_suite_id'] = self.id
            self.env['module.test.case'].create(test_case_data)

    def _generate_test_data(self, form, update=False):
        """Generate test data for a form"""
        test_data = {}
        
        for field in form.field_definition_ids:
            if field.field_type == 'char':
                test_data[field.name] = f"Test {field.field_description}" + (" Updated" if update else "")
            elif field.field_type == 'text':
                test_data[field.name] = f"Test {field.field_description} content" + (" updated" if update else "")
            elif field.field_type == 'integer':
                test_data[field.name] = 456 if update else 123
            elif field.field_type == 'float':
                test_data[field.name] = 456.78 if update else 123.45
            elif field.field_type == 'boolean':
                test_data[field.name] = not update
            elif field.field_type == 'selection':
                options = field.get_selection_options()
                if options:
                    test_data[field.name] = options[1][0] if update and len(options) > 1 else options[0][0]
            elif field.field_type == 'date':
                test_data[field.name] = fields.Date.today() + timedelta(days=1 if update else 0)
            elif field.field_type == 'datetime':
                test_data[field.name] = fields.Datetime.now() + timedelta(hours=1 if update else 0)
        
        return json.dumps(test_data)

    def _generate_invalid_test_data(self, form):
        """Generate invalid test data for validation testing"""
        test_data = {}
        
        # Leave required fields empty
        for field in form.field_definition_ids.filtered('required'):
            if field.field_type in ['char', 'text']:
                test_data[field.name] = ''
            elif field.field_type in ['integer', 'float']:
                test_data[field.name] = None
            elif field.field_type == 'boolean':
                test_data[field.name] = False
        
        return json.dumps(test_data)


class ModuleTestCase(models.Model):
    """Individual Test Case"""
    _name = 'module.test.case'
    _description = 'Module Test Case'
    _order = 'sequence, name'

    name = fields.Char(string='Test Case Name', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    test_suite_id = fields.Many2one('module.test.suite', string='Test Suite', required=True, ondelete='cascade')
    form_builder_id = fields.Many2one('form.builder', string='Form Builder')
    
    # Test Configuration
    test_type = fields.Selection([
        ('crud', 'CRUD Operations'),
        ('validation', 'Data Validation'),
        ('workflow', 'Workflow'),
        ('website', 'Website Form'),
        ('api', 'API Endpoints'),
        ('performance', 'Performance'),
        ('security', 'Security'),
    ], string='Test Type', required=True)
    
    test_method = fields.Char(string='Test Method', required=True)
    test_data = fields.Text(string='Test Data', help="JSON data for test execution")
    expected_result = fields.Text(string='Expected Result')
    
    # Execution Results
    result = fields.Selection([
        ('not_run', 'Not Run'),
        ('passed', 'Passed'),
        ('failed', 'Failed'),
        ('error', 'Error'),
    ], string='Result', default='not_run', readonly=True)
    
    execution_time = fields.Float(string='Execution Time (seconds)', readonly=True)
    error_message = fields.Text(string='Error Message', readonly=True)
    actual_result = fields.Text(string='Actual Result', readonly=True)
    last_execution = fields.Datetime(string='Last Execution', readonly=True)
    
    # Status
    active = fields.Boolean(string='Active', default=True)

    def execute_test(self):
        """Execute the test case"""
        self.ensure_one()
        
        start_time = time.time()
        
        try:
            # Parse test data
            test_data = json.loads(self.test_data) if self.test_data else {}
            
            # Execute test based on type
            if self.test_type == 'crud':
                result = self._execute_crud_test(test_data)
            elif self.test_type == 'validation':
                result = self._execute_validation_test(test_data)
            elif self.test_type == 'workflow':
                result = self._execute_workflow_test(test_data)
            elif self.test_type == 'website':
                result = self._execute_website_test(test_data)
            elif self.test_type == 'api':
                result = self._execute_api_test(test_data)
            else:
                result = {'success': False, 'message': 'Test type not implemented'}
            
            # Update results
            execution_time = time.time() - start_time
            self.execution_time = execution_time
            self.last_execution = fields.Datetime.now()
            self.actual_result = json.dumps(result, indent=2)
            
            if result.get('success', False):
                self.result = 'passed'
                self.error_message = False
            else:
                self.result = 'failed'
                self.error_message = result.get('message', 'Test failed')
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.execution_time = execution_time
            self.last_execution = fields.Datetime.now()
            self.result = 'error'
            self.error_message = str(e)
            self.actual_result = f"Error: {str(e)}"
            _logger.error(f"Test case execution error: {str(e)}")

    def _execute_crud_test(self, test_data):
        """Execute CRUD test"""
        try:
            if not self.form_builder_id:
                return {'success': False, 'message': 'Form builder not specified'}
            
            model_name = self.form_builder_id.model_name
            if not model_name:
                return {'success': False, 'message': 'Model name not found'}
            
            # Check if model exists
            if model_name not in self.env:
                return {'success': False, 'message': f'Model {model_name} not found'}
            
            model = self.env[model_name]
            
            if self.test_method == 'test_create':
                # Test record creation
                record = model.create(test_data)
                return {'success': True, 'record_id': record.id, 'message': 'Record created successfully'}
            
            elif self.test_method == 'test_read':
                # Test record reading
                records = model.search([], limit=1)
                if records:
                    data = records.read()
                    return {'success': True, 'data': data, 'message': 'Record read successfully'}
                else:
                    return {'success': False, 'message': 'No records found to read'}
            
            elif self.test_method == 'test_update':
                # Test record update
                records = model.search([], limit=1)
                if records:
                    records.write(test_data)
                    return {'success': True, 'message': 'Record updated successfully'}
                else:
                    return {'success': False, 'message': 'No records found to update'}
            
            elif self.test_method == 'test_delete':
                # Test record deletion
                records = model.search([], limit=1)
                if records:
                    records.unlink()
                    return {'success': True, 'message': 'Record deleted successfully'}
                else:
                    return {'success': False, 'message': 'No records found to delete'}
            
            return {'success': False, 'message': f'Unknown test method: {self.test_method}'}
            
        except Exception as e:
            return {'success': False, 'message': f'CRUD test error: {str(e)}'}

    def _execute_validation_test(self, test_data):
        """Execute validation test"""
        try:
            if not self.form_builder_id:
                return {'success': False, 'message': 'Form builder not specified'}
            
            model_name = self.form_builder_id.model_name
            if model_name not in self.env:
                return {'success': False, 'message': f'Model {model_name} not found'}
            
            model = self.env[model_name]
            
            # Try to create record with invalid data - should fail
            try:
                record = model.create(test_data)
                return {'success': False, 'message': 'Validation should have failed but record was created'}
            except ValidationError:
                return {'success': True, 'message': 'Validation correctly prevented invalid data'}
            except Exception as e:
                return {'success': True, 'message': f'Validation triggered: {str(e)}'}
                
        except Exception as e:
            return {'success': False, 'message': f'Validation test error: {str(e)}'}

    def _execute_workflow_test(self, test_data):
        """Execute workflow test"""
        # Implementation for workflow testing
        return {'success': True, 'message': 'Workflow test not yet implemented'}

    def _execute_website_test(self, test_data):
        """Execute website form test"""
        # Implementation for website form testing
        return {'success': True, 'message': 'Website test not yet implemented'}

    def _execute_api_test(self, test_data):
        """Execute API test"""
        # Implementation for API testing
        return {'success': True, 'message': 'API test not yet implemented'}
