from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import json
import logging

_logger = logging.getLogger(__name__)


class FormBuilder(models.Model):
    """
    Form Builder model for defining forms that will be generated in the modules.
    Each form represents a specific business process or data collection requirement.
    """
    _name = 'form.builder'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Form Builder'
    _order = 'sequence, name'

    # Basic Information
    name = fields.Char(string='Form Name', required=True, tracking=True,
                      help="Name of the form")
    code = fields.Char(string='Form Code', required=True, tracking=True,
                      help="Unique code for the form (will be used in model name)")
    description = fields.Text(string='Description', tracking=True,
                             help="Description of the form purpose")
    sequence = fields.Integer(string='Sequence', default=10,
                             help="Sequence for ordering forms")

    # Template Relationship
    template_id = fields.Many2one('module.template', string='Module Template',
                                 required=True, ondelete='cascade')

    # Form Configuration
    form_type = fields.Selection([
        ('data_collection', 'Data Collection Form'),
        ('application', 'Application Form'),
        ('registration', 'Registration Form'),
        ('request', 'Request Form'),
        ('survey', 'Survey Form'),
        ('custom', 'Custom Form'),
    ], string='Form Type', default='data_collection', required=True, tracking=True)

    # Model Configuration
    model_name = fields.Char(string='Model Name', compute='_compute_model_name', store=True,
                            help="Generated model name")
    model_description = fields.Char(string='Model Description',
                                   help="Description for the generated model")

    # Form Layout
    layout_type = fields.Selection([
        ('single_page', 'Single Page'),
        ('multi_step', 'Multi-Step'),
        ('tabbed', 'Tabbed'),
        ('accordion', 'Accordion'),
    ], string='Layout Type', default='single_page', tracking=True)

    # Website Configuration
    website_published = fields.Boolean(string='Published on Website', default=True,
                                      help="Make this form available on the website")
    website_url = fields.Char(string='Website URL', compute='_compute_website_url', store=True,
                             help="URL for accessing the form on website")
    website_menu_name = fields.Char(string='Website Menu Name',
                                   help="Name for the website menu item")
    website_page_title = fields.Char(string='Website Page Title',
                                    help="Title for the website page")
    website_meta_description = fields.Text(string='Meta Description',
                                          help="Meta description for SEO")

    # Portal Configuration
    portal_access = fields.Boolean(string='Portal Access', default=True,
                                  help="Allow customers to access their submissions via portal")
    portal_menu_name = fields.Char(string='Portal Menu Name',
                                  help="Name for the portal menu item")

    # API Configuration
    api_enabled = fields.Boolean(string='API Enabled', default=True,
                                help="Enable REST API endpoints for this form")
    api_endpoint = fields.Char(string='API Endpoint', compute='_compute_api_endpoint', store=True,
                              help="REST API endpoint for this form")

    # Field Definitions
    field_definition_ids = fields.One2many('field.definition', 'form_builder_id',
                                          string='Field Definitions',
                                          help="Fields that will be included in this form")

    # AI Integration
    ai_prompt_config_ids = fields.One2many('ai.prompt.config', 'form_builder_id',
                                          string='AI Prompt Configurations',
                                          help="AI prompt configurations for this form")

    # Validation Rules
    validation_rules = fields.Text(string='Validation Rules',
                                  help="JSON configuration for form validation rules")

    # Submission Configuration
    submission_email_template_id = fields.Many2one('mail.template', string='Submission Email Template',
                                                  help="Email template to send on form submission")
    auto_confirm_submission = fields.Boolean(string='Auto Confirm Submission', default=False,
                                           help="Automatically confirm submissions without admin review")

    # Document Generation
    enable_document_generation = fields.Boolean(string='Enable Document Generation', default=False,
                                               help="Enable AI document generation for this form")
    document_template_id = fields.Many2one('document.template', string='Document Template',
                                          help="Template for document generation")

    # Sales Order Integration
    create_sales_order = fields.Boolean(string='Create Sales Order', default=True,
                                       help="Create a sales order for each form submission")
    product_id = fields.Many2one('product.product', string='Service Product',
                                help="Product to use in the sales order for this form")
    product_price = fields.Float(string='Service Price',
                                help="Price for the service (0 for variable pricing)")
    payment_currency_id = fields.Many2one('res.currency', string='Currency',
                                         default=lambda self: self.env.company.currency_id)

    # Payment Configuration
    enable_payment = fields.Boolean(string='Enable Payment', default=True,
                                   help="Enable payment processing for this form")
    payment_required = fields.Boolean(string='Payment Required', default=True,
                                     help="Payment is required before processing the form")
    payment_method_ids = fields.Many2many('payment.provider', string='Payment Methods',
                                         help="Available payment methods for this form")

    # Notification Configuration
    enable_whatsapp_notifications = fields.Boolean(string='Enable WhatsApp Notifications', default=False,
                                                   help="Send WhatsApp notifications for status updates")
    enable_email_notifications = fields.Boolean(string='Enable Email Notifications', default=True,
                                               help="Send email notifications for status updates")

    # Status
    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Statistics
    submission_count = fields.Integer(string='Submission Count', compute='_compute_submission_count',
                                     help="Number of submissions for this form")

    # Constraints
    _sql_constraints = [
        ('code_template_uniq', 'unique(code, template_id)',
         'Form code must be unique within the template!'),
    ]

    @api.depends('template_id', 'code')
    def _compute_model_name(self):
        """Compute the model name based on template and form code"""
        for record in self:
            if record.template_id and record.code:
                module_name = record.template_id.get_module_name()
                record.model_name = f"{module_name}.{record.code}"
            else:
                record.model_name = False

    @api.depends('template_id', 'code')
    def _compute_website_url(self):
        """Compute the website URL for the form"""
        for record in self:
            if record.template_id and record.code:
                module_name = record.template_id.get_module_name()
                record.website_url = f"/{module_name}/{record.code}"
            else:
                record.website_url = False

    @api.depends('template_id', 'code')
    def _compute_api_endpoint(self):
        """Compute the API endpoint for the form"""
        for record in self:
            if record.template_id and record.code:
                module_name = record.template_id.get_module_name()
                record.api_endpoint = f"/api/{module_name}/{record.code}"
            else:
                record.api_endpoint = False

    def _compute_submission_count(self):
        """Compute the number of submissions for this form"""
        for record in self:
            # This will be implemented when the generated modules are created
            record.submission_count = 0

    @api.constrains('code')
    def _check_code(self):
        """Validate form code format"""
        for record in self:
            if record.code:
                if not record.code.replace('_', '').isalnum():
                    raise ValidationError(_("Form code must contain only letters, numbers, and underscores"))
                if not record.code.islower():
                    raise ValidationError(_("Form code must be lowercase"))

    @api.constrains('validation_rules')
    def _check_validation_rules(self):
        """Validate JSON validation rules"""
        for record in self:
            if record.validation_rules:
                try:
                    json.loads(record.validation_rules)
                except json.JSONDecodeError as e:
                    raise ValidationError(_("Invalid validation rules JSON: %s") % str(e))

    @api.onchange('name')
    def _onchange_name(self):
        """Auto-generate code from name"""
        if self.name and not self.code:
            # Convert name to code format
            code = self.name.lower().replace(' ', '_')
            # Remove special characters
            code = ''.join(c for c in code if c.isalnum() or c == '_')
            self.code = code

    @api.onchange('form_type')
    def _onchange_form_type(self):
        """Set default configurations based on form type"""
        if self.form_type == 'application':
            self.enable_payment = True
            self.enable_document_generation = True
            self.portal_access = True
        elif self.form_type == 'registration':
            self.auto_confirm_submission = True
            self.enable_email_notifications = True
        elif self.form_type == 'survey':
            self.auto_confirm_submission = True
            self.enable_payment = False

    def action_configure_fields(self):
        """Open field configuration wizard"""
        self.ensure_one()

        return {
            'name': _('Configure Fields'),
            'type': 'ir.actions.act_window',
            'res_model': 'field.definition',
            'view_mode': 'tree,form',
            'domain': [('form_builder_id', '=', self.id)],
            'context': {
                'default_form_builder_id': self.id,
                'default_template_id': self.template_id.id,
            },
        }

    def action_preview_form(self):
        """Preview the form layout"""
        self.ensure_one()

        # This will open a preview of how the form will look
        return {
            'name': _('Form Preview'),
            'type': 'ir.actions.act_window',
            'res_model': 'form.builder.preview',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_form_builder_id': self.id,
            },
        }

    def get_validation_rules(self):
        """Get parsed validation rules"""
        self.ensure_one()
        if self.validation_rules:
            try:
                return json.loads(self.validation_rules)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_validation_rules(self, rules_dict):
        """Set validation rules from dictionary"""
        self.ensure_one()
        self.validation_rules = json.dumps(rules_dict, indent=2)

    def get_field_definitions_ordered(self):
        """Get field definitions ordered by sequence"""
        self.ensure_one()
        return self.field_definition_ids.sorted('sequence')
