# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json


class FormBuilderPreview(models.TransientModel):
    """
    Transient model for previewing form builders
    This model allows users to preview how their forms will look and behave
    """
    _name = 'form.builder.preview'
    _description = 'Form Builder Preview'

    # Basic Information
    form_builder_id = fields.Many2one('form.builder', string='Form Builder',
                                     required=True, ondelete='cascade')
    name = fields.Char(related='form_builder_id.name', string='Form Name', readonly=True)
    description = fields.Text(related='form_builder_id.description', string='Description', readonly=True)
    
    # Form Configuration
    form_type = fields.Selection(related='form_builder_id.form_type', readonly=True)
    layout_type = fields.Selection(related='form_builder_id.layout_type', readonly=True)
    enable_step_form = fields.Boolean(related='form_builder_id.enable_step_form', readonly=True)
    
    # Step Configuration
    current_step = fields.Integer(string='Current Step', default=1)
    total_steps = fields.Integer(string='Total Steps', compute='_compute_total_steps')
    step_data = fields.Text(string='Step Data', default='{}',
                           help="JSON data for step-by-step form progress")
    
    # Preview Data
    preview_data = fields.Text(string='Preview Data', default='{}',
                              help="JSON data for form preview values")
    
    # Payment Preview
    enable_payment = fields.Boolean(related='form_builder_id.enable_payment', readonly=True)
    payment_type = fields.Selection(related='form_builder_id.payment_type', readonly=True)
    default_payment_rate = fields.Float(related='form_builder_id.default_payment_rate', readonly=True)
    product_price = fields.Float(related='form_builder_id.product_price', readonly=True)
    
    # Add-ons Preview
    addon_ids = fields.Many2many('form.builder.addon', string='Selected Add-ons',
                                domain="[('form_builder_id', '=', form_builder_id)]")
    total_addon_price = fields.Float(string='Add-ons Total', compute='_compute_addon_price')
    total_price = fields.Float(string='Total Price', compute='_compute_total_price')
    
    # FAQ Preview
    faq_ids = fields.One2many(related='form_builder_id.faq_ids', readonly=True)

    @api.depends('form_builder_id.field_definition_ids', 'enable_step_form')
    def _compute_total_steps(self):
        """Compute total steps for step-by-step forms"""
        for record in self:
            if record.enable_step_form and record.form_builder_id:
                # Group fields by step (assuming step field exists in field definitions)
                steps = set()
                for field_def in record.form_builder_id.field_definition_ids:
                    step = getattr(field_def, 'step', 1)  # Default to step 1
                    steps.add(step)
                record.total_steps = len(steps) if steps else 1
            else:
                record.total_steps = 1

    @api.depends('addon_ids')
    def _compute_addon_price(self):
        """Compute total price of selected add-ons"""
        for record in self:
            record.total_addon_price = sum(record.addon_ids.mapped('price'))

    @api.depends('default_payment_rate', 'product_price', 'total_addon_price')
    def _compute_total_price(self):
        """Compute total price including base price and add-ons"""
        for record in self:
            base_price = record.product_price or record.default_payment_rate or 0.0
            record.total_price = base_price + record.total_addon_price

    def action_next_step(self):
        """Move to next step in step-by-step form"""
        self.ensure_one()
        if self.current_step < self.total_steps:
            self.current_step += 1
        return self._reload_preview()

    def action_previous_step(self):
        """Move to previous step in step-by-step form"""
        self.ensure_one()
        if self.current_step > 1:
            self.current_step -= 1
        return self._reload_preview()

    def action_go_to_step(self, step_number):
        """Go to specific step"""
        self.ensure_one()
        if 1 <= step_number <= self.total_steps:
            self.current_step = step_number
        return self._reload_preview()

    def _reload_preview(self):
        """Reload the preview form"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Form Preview'),
            'res_model': 'form.builder.preview',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }

    def action_simulate_submission(self):
        """Simulate form submission"""
        self.ensure_one()
        
        # Validate required fields (simplified)
        preview_data = json.loads(self.preview_data or '{}')
        required_fields = self.form_builder_id.field_definition_ids.filtered('required')
        
        missing_fields = []
        for field_def in required_fields:
            if not preview_data.get(field_def.name):
                missing_fields.append(field_def.label or field_def.name)
        
        if missing_fields:
            raise ValidationError(
                _("Please fill in the following required fields: %s") % 
                ', '.join(missing_fields)
            )
        
        # Show success message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Form Submission Simulated'),
                'message': _('Your form would be submitted successfully with the current data.'),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_reset_preview(self):
        """Reset preview data"""
        self.ensure_one()
        self.write({
            'current_step': 1,
            'preview_data': '{}',
            'step_data': '{}',
            'addon_ids': [(5, 0, 0)],  # Clear all add-ons
        })
        return self._reload_preview()

    def get_current_step_fields(self):
        """Get fields for current step"""
        self.ensure_one()
        if not self.enable_step_form:
            return self.form_builder_id.field_definition_ids
        
        # Filter fields by current step
        return self.form_builder_id.field_definition_ids.filtered(
            lambda f: getattr(f, 'step', 1) == self.current_step
        )

    def get_step_progress_percentage(self):
        """Get progress percentage for step-by-step forms"""
        self.ensure_one()
        if self.total_steps <= 1:
            return 100
        return int((self.current_step / self.total_steps) * 100)

    @api.model
    def create_preview_from_form_builder(self, form_builder_id):
        """Create a preview instance from form builder"""
        form_builder = self.env['form.builder'].browse(form_builder_id)
        if not form_builder.exists():
            raise ValidationError(_("Form builder not found"))
        
        preview = self.create({
            'form_builder_id': form_builder_id,
        })
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Form Preview: %s') % form_builder.name,
            'res_model': 'form.builder.preview',
            'res_id': preview.id,
            'view_mode': 'form',
            'target': 'new',
            'context': self.env.context,
        }
