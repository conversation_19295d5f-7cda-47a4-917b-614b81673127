from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class WorkflowState(models.Model):
    """
    Workflow State model for defining the states that generated modules
    will use in their workflow processes.
    """
    _name = 'workflow.state'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Workflow State'
    _order = 'sequence, name'

    # Basic Information
    name = fields.Char(string='State Name', required=True, tracking=True,
                      help="Human-readable name of the state")
    code = fields.Char(string='State Code', required=True, tracking=True,
                      help="Technical code for the state")
    description = fields.Text(string='Description', tracking=True,
                             help="Description of what this state represents")
    sequence = fields.Integer(string='Sequence', default=10,
                             help="Sequence for ordering states")

    # Template Relationship
    template_id = fields.Many2one('module.template', string='Module Template',
                                 required=True, ondelete='cascade')

    # State Configuration
    state_type = fields.Selection([
        ('initial', 'Initial State'),
        ('intermediate', 'Intermediate State'),
        ('payment_request', 'Payment Request State'),
        ('final', 'Final State'),
        ('error', 'Error State'),
    ], string='State Type', default='intermediate', required=True, tracking=True)

    # Workflow Behavior
    is_default = fields.Boolean(string='Default State', default=False, tracking=True,
                               help="This is the default state for new records")
    is_readonly = fields.Boolean(string='Readonly State', default=False, tracking=True,
                                help="Records in this state are readonly")
    is_portal_visible = fields.Boolean(string='Visible in Portal', default=True, tracking=True,
                                      help="Records in this state are visible in customer portal")

    # Transition Configuration
    allowed_transition_ids = fields.Many2many('workflow.state',
                                             'workflow_state_transition_rel',
                                             'from_state_id', 'to_state_id',
                                             string='Allowed Transitions',
                                             help="States that can be reached from this state")

    # Action Configuration
    entry_action = fields.Selection([
        ('none', 'No Action'),
        ('send_email', 'Send Email'),
        ('send_whatsapp', 'Send WhatsApp'),
        ('generate_document', 'Generate Document'),
        ('create_payment', 'Create Payment'),
        ('request_payment', 'Request Payment'),
        ('send_payment_link', 'Send Payment Link'),
        ('custom', 'Custom Action'),
    ], string='Entry Action', default='none', tracking=True,
       help="Action to perform when entering this state")

    exit_action = fields.Selection([
        ('none', 'No Action'),
        ('send_email', 'Send Email'),
        ('send_whatsapp', 'Send WhatsApp'),
        ('archive_record', 'Archive Record'),
        ('process_payment', 'Process Payment'),
        ('generate_invoice', 'Generate Invoice'),
        ('custom', 'Custom Action'),
    ], string='Exit Action', default='none', tracking=True,
       help="Action to perform when leaving this state")

    # Payment Configuration
    is_payment_stage = fields.Boolean(string='Payment Stage', default=False, tracking=True,
                                     help="This stage requires customer payment")
    payment_product_id = fields.Many2one('product.product', string='Payment Product',
                                        help="Default product to use for payment sales orders")
    payment_description = fields.Text(string='Payment Description',
                                     help="Description for the payment request")
    auto_create_sales_order = fields.Boolean(string='Auto Create Sales Order', default=True,
                                            help="Automatically create sales order when entering this stage")
    auto_send_payment_link = fields.Boolean(string='Auto Send Payment Link', default=True,
                                           help="Automatically send payment link via email and WhatsApp")

    # Notification Configuration
    send_email_notification = fields.Boolean(string='Send Email Notification', default=False,
                                            help="Send email notification when entering this stage")
    send_whatsapp_notification = fields.Boolean(string='Send WhatsApp Notification', default=False,
                                               help="Send WhatsApp notification when entering this stage")
    notification_message = fields.Text(string='Notification Message',
                                      help="Custom message for notifications")

    # Email Configuration
    email_template_id = fields.Many2one('mail.template', string='Email Template',
                                       help="Email template to use for notifications")

    # WhatsApp Configuration
    whatsapp_template = fields.Text(string='WhatsApp Template',
                                   help="WhatsApp message template")

    # UI Configuration
    color = fields.Char(string='Color', default='#007bff',
                       help="Color to use for this state in UI")
    icon = fields.Char(string='Icon', default='fa-circle',
                      help="Font Awesome icon for this state")

    # Permissions
    required_group_ids = fields.Many2many('res.groups', string='Required Groups',
                                         help="Groups required to transition to this state")

    # Status
    active = fields.Boolean(string='Active', default=True, tracking=True)

    # Constraints
    _sql_constraints = [
        ('code_template_uniq', 'unique(code, template_id)',
         'State code must be unique within the template!'),
    ]

    @api.constrains('code')
    def _check_code(self):
        """Validate state code format"""
        for record in self:
            if record.code:
                if not record.code.replace('_', '').isalnum():
                    raise ValidationError(_("State code must contain only letters, numbers, and underscores"))
                if not record.code.islower():
                    raise ValidationError(_("State code must be lowercase"))

    @api.constrains('is_default')
    def _check_default_state(self):
        """Ensure only one default state per template"""
        for record in self:
            if record.is_default:
                other_defaults = self.search([
                    ('template_id', '=', record.template_id.id),
                    ('is_default', '=', True),
                    ('id', '!=', record.id)
                ])
                if other_defaults:
                    raise ValidationError(_("Only one default state is allowed per template"))

    @api.onchange('name')
    def _onchange_name(self):
        """Auto-generate code from name"""
        if self.name and not self.code:
            # Convert name to code format
            code = self.name.lower().replace(' ', '_')
            # Remove special characters
            code = ''.join(c for c in code if c.isalnum() or c == '_')
            self.code = code

    @api.onchange('state_type')
    def _onchange_state_type(self):
        """Set default configurations based on state type"""
        if self.state_type == 'initial':
            self.is_default = True
            self.is_readonly = False
            self.color = '#28a745'  # Green
            self.icon = 'fa-play'
        elif self.state_type == 'payment_request':
            self.is_payment_stage = True
            self.entry_action = 'request_payment'
            self.send_email_notification = True
            self.send_whatsapp_notification = True
            self.color = '#fd7e14'  # Orange
            self.icon = 'fa-credit-card'
        elif self.state_type == 'final':
            self.is_readonly = True
            self.color = '#007bff'  # Blue
            self.icon = 'fa-check'
        elif self.state_type == 'error':
            self.is_readonly = True
            self.color = '#dc3545'  # Red
            self.icon = 'fa-exclamation-triangle'
        else:  # intermediate
            self.color = '#ffc107'  # Yellow
            self.icon = 'fa-clock'

    def action_configure_transitions(self):
        """Configure allowed transitions for this state"""
        self.ensure_one()

        return {
            'name': _('Configure Transitions'),
            'type': 'ir.actions.act_window',
            'res_model': 'workflow.state.transition.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_state_id': self.id,
                'default_template_id': self.template_id.id,
            }
        }

    def action_test_transition(self):
        """Test the transition logic for this state"""
        self.ensure_one()

        # This would test the transition logic
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Transition Test'),
                'message': _('Transition logic for state %s is working correctly') % self.name,
                'type': 'success',
            }
        }

    def get_allowed_transitions(self, user=None):
        """Get allowed transitions for the current user"""
        self.ensure_one()

        if not user:
            user = self.env.user

        allowed_states = self.allowed_transition_ids

        # Filter by required groups
        if self.required_group_ids:
            user_groups = user.groups_id
            allowed_states = allowed_states.filtered(
                lambda s: not s.required_group_ids or
                any(group in user_groups for group in s.required_group_ids)
            )

        return allowed_states

    def can_transition_to(self, target_state, user=None):
        """Check if transition to target state is allowed"""
        self.ensure_one()

        if not user:
            user = self.env.user

        allowed_transitions = self.get_allowed_transitions(user)
        return target_state in allowed_transitions

    def execute_entry_action(self, record):
        """Execute the entry action for this state"""
        self.ensure_one()

        if self.entry_action == 'send_email' and self.email_template_id:
            self.email_template_id.send_mail(record.id, force_send=True)
        elif self.entry_action == 'send_whatsapp' and self.whatsapp_template:
            self._send_whatsapp_message(record)
        elif self.entry_action == 'generate_document':
            self._generate_document(record)
        elif self.entry_action == 'create_payment':
            self._create_payment(record)
        elif self.entry_action == 'request_payment':
            self._request_payment(record)
        elif self.entry_action == 'send_payment_link':
            self._send_payment_link(record)
        elif self.entry_action == 'custom':
            self._execute_custom_entry_action(record)

    def execute_exit_action(self, record):
        """Execute the exit action for this state"""
        self.ensure_one()

        if self.exit_action == 'send_email' and self.email_template_id:
            self.email_template_id.send_mail(record.id, force_send=True)
        elif self.exit_action == 'send_whatsapp' and self.whatsapp_template:
            self._send_whatsapp_message(record)
        elif self.exit_action == 'archive_record':
            record.write({'active': False})
        elif self.exit_action == 'custom':
            self._execute_custom_exit_action(record)

    def _send_whatsapp_message(self, record):
        """Send WhatsApp message"""
        # Implementation for WhatsApp integration
        _logger.info("Sending WhatsApp message for record %s in state %s", record.id, self.code)

    def _generate_document(self, record):
        """Generate document for the record"""
        # Implementation for document generation
        _logger.info("Generating document for record %s in state %s", record.id, self.code)

    def _create_payment(self, record):
        """Create payment for the record"""
        # Implementation for payment creation
        _logger.info("Creating payment for record %s in state %s", record.id, self.code)

    def _request_payment(self, record):
        """Request payment from customer"""
        self.ensure_one()

        if self.is_payment_stage and self.auto_create_sales_order:
            # Create sales order for payment
            sales_order = self._create_sales_order_for_payment(record)

            if sales_order and self.auto_send_payment_link:
                # Send payment link via email and WhatsApp
                self._send_payment_notifications(record, sales_order)

        _logger.info("Payment requested for record %s in state %s", record.id, self.code)

    def _send_payment_link(self, record):
        """Send payment link to customer"""
        self.ensure_one()

        # Find existing sales order or create new one
        sales_order = self._get_or_create_sales_order(record)

        if sales_order:
            self._send_payment_notifications(record, sales_order)

        _logger.info("Payment link sent for record %s in state %s", record.id, self.code)

    def _create_sales_order_for_payment(self, record):
        """Create sales order for payment processing using template or state product"""
        self.ensure_one()

        # Get product from template first, then state configuration
        product = self.template_id.default_product_id if self.template_id and self.template_id.default_product_id else self.payment_product_id

        if not product:
            _logger.warning("No payment product configured in template or state %s", self.code)
            return False

        # Get customer from record
        customer = self._get_customer_from_record(record)
        if not customer:
            _logger.warning("No customer found for record %s", record.id)
            return False

        # Get price from record's payment_rate field (can be 0)
        price = self._get_payment_price(record)

        # Create sales order
        SaleOrder = self.env['sale.order']
        order_vals = {
            'partner_id': customer.id,
            'origin': f"{record._name}:{record.id}",
            'state': 'draft',
            'order_line': [(0, 0, {
                'product_id': product.id,
                'name': f"{product.name} - {getattr(record, 'name', 'Payment')}",
                'product_uom_qty': 1,
                'price_unit': price,
            })]
        }

        sales_order = SaleOrder.create(order_vals)

        # Link sales order to record
        if hasattr(record, 'sales_order_id'):
            record.sales_order_id = sales_order.id

        _logger.info("Sales order %s created for record %s", sales_order.name, record.id)
        return sales_order

    def _get_or_create_sales_order(self, record):
        """Get existing sales order or create new one"""
        # Check if record has linked sales order
        if hasattr(record, 'sales_order_id') and record.sales_order_id:
            return record.sales_order_id

        # Create new sales order
        return self._create_sales_order_for_payment(record)

    def _get_customer_from_record(self, record):
        """Extract customer from record"""
        # Try common customer field names
        customer_fields = ['partner_id', 'customer_id', 'customer_partner_id']

        for field_name in customer_fields:
            if hasattr(record, field_name):
                customer = getattr(record, field_name)
                if customer:
                    return customer

        # Try to find customer by email
        if hasattr(record, 'customer_email') and record.customer_email:
            Partner = self.env['res.partner']
            customer = Partner.search([('email', '=', record.customer_email)], limit=1)
            if customer:
                return customer

            # Create customer if not found
            customer_vals = {
                'name': getattr(record, 'customer_name', record.customer_email),
                'email': record.customer_email,
                'is_company': False,
            }
            if hasattr(record, 'customer_phone') and record.customer_phone:
                customer_vals['phone'] = record.customer_phone

            return Partner.create(customer_vals)

        return False

    def _get_payment_price(self, record):
        """Get payment price from record's payment_rate field or template default"""
        # First priority: record's payment_rate field (can be 0)
        if hasattr(record, 'payment_rate'):
            return record.payment_rate

        # Second priority: record's payment_amount field
        if hasattr(record, 'payment_amount') and record.payment_amount > 0:
            return record.payment_amount

        # Third priority: template's default product price
        if self.template_id and self.template_id.default_product_id:
            return self.template_id.default_product_id.list_price

        # Fourth priority: state's payment product price
        if self.payment_product_id:
            return self.payment_product_id.list_price

        return 0.0

    def _send_payment_notifications(self, record, sales_order):
        """Send payment notifications via email and WhatsApp"""
        self.ensure_one()

        # Generate payment link
        payment_url = f"/my/orders/{sales_order.id}"

        # Send email notification
        if self.send_email_notification:
            self._send_payment_email(record, sales_order, payment_url)

        # Send WhatsApp notification
        if self.send_whatsapp_notification:
            self._send_payment_whatsapp(record, sales_order, payment_url)

    def _send_payment_email(self, record, sales_order, payment_url):
        """Send payment email notification with audit logging"""
        self.ensure_one()

        # Get customer email
        customer_email = None
        if hasattr(record, 'customer_email') and record.customer_email:
            customer_email = record.customer_email
        elif sales_order.partner_id.email:
            customer_email = sales_order.partner_id.email

        if not customer_email:
            _logger.warning("No email found for payment notification - record %s", record.id)
            return

        # Create email content
        subject = f"Payment Request - {sales_order.name}"
        body = f"""
        Dear {sales_order.partner_id.name or 'Customer'},

        We have created a payment request for your order.

        Order Details:
        - Order Number: {sales_order.name}
        - Amount: {sales_order.amount_total} {sales_order.currency_id.name}
        - Description: {self.payment_description or 'Payment for services'}

        You can make the payment by clicking the link below:
        {payment_url}

        Thank you for your business!

        Best regards,
        {self.env.company.name}
        """

        # Send email using Odoo's mail system
        try:
            mail_values = {
                'subject': subject,
                'body_html': body.replace('\n', '<br/>'),
                'email_to': customer_email,
                'email_from': self.env.company.email or self.env.user.email,
                'auto_delete': False,
            }

            mail = self.env['mail.mail'].create(mail_values)
            mail.send()

            # Log the email in record's chatter
            record.message_post(
                body=f"Payment email sent to {customer_email}<br/>Subject: {subject}<br/>Payment URL: {payment_url}",
                subject="Payment Email Sent",
                message_type='notification'
            )

            _logger.info("Payment email sent to %s for order %s", customer_email, sales_order.name)

        except Exception as e:
            _logger.error("Failed to send payment email: %s", str(e))
            record.message_post(
                body=f"Failed to send payment email to {customer_email}: {str(e)}",
                subject="Payment Email Failed",
                message_type='notification'
            )

    def _send_payment_whatsapp(self, record, sales_order, payment_url):
        """Send payment WhatsApp notification with audit logging"""
        self.ensure_one()

        # Get customer phone
        customer_phone = None
        if hasattr(record, 'customer_phone') and record.customer_phone:
            customer_phone = record.customer_phone
        elif sales_order.partner_id.phone:
            customer_phone = sales_order.partner_id.phone
        elif sales_order.partner_id.mobile:
            customer_phone = sales_order.partner_id.mobile

        if not customer_phone:
            _logger.warning("No phone found for WhatsApp notification - record %s", record.id)
            return

        # Create WhatsApp message content
        message = f"""
🔔 *Payment Request*

Dear {sales_order.partner_id.name or 'Customer'},

We have created a payment request for your order.

📋 *Order Details:*
• Order Number: {sales_order.name}
• Amount: {sales_order.amount_total} {sales_order.currency_id.name}
• Description: {self.payment_description or 'Payment for services'}

💳 *Make Payment:*
{payment_url}

Thank you for your business! 🙏

Best regards,
{self.env.company.name}
        """.strip()

        try:
            # Log the WhatsApp message in record's chatter (actual sending would require WhatsApp API integration)
            record.message_post(
                body=f"WhatsApp message prepared for {customer_phone}<br/><br/><strong>Message:</strong><br/>{message.replace(chr(10), '<br/>')}",
                subject="WhatsApp Message Prepared",
                message_type='notification'
            )

            # Here you would integrate with your WhatsApp API
            # For now, we'll just log it
            _logger.info("WhatsApp message prepared for %s for order %s", customer_phone, sales_order.name)

        except Exception as e:
            _logger.error("Failed to prepare WhatsApp message: %s", str(e))
            record.message_post(
                body=f"Failed to prepare WhatsApp message for {customer_phone}: {str(e)}",
                subject="WhatsApp Message Failed",
                message_type='notification'
            )

    def _execute_custom_entry_action(self, record):
        """Execute custom entry action"""
        # Implementation for custom actions
        _logger.info("Executing custom entry action for record %s in state %s", record.id, self.code)

    def _execute_custom_exit_action(self, record):
        """Execute custom exit action"""
        # Implementation for custom actions
        _logger.info("Executing custom exit action for record %s in state %s", record.id, self.code)
