# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import json
import logging

_logger = logging.getLogger(__name__)


class PaymentConfig(models.Model):
    """Payment Configuration for Form Builders"""
    _name = 'payment.config'
    _description = 'Payment Configuration'
    _order = 'sequence, name'

    name = fields.Char(string='Configuration Name', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    form_builder_id = fields.Many2one('form.builder', string='Form Builder', required=True, ondelete='cascade')
    template_id = fields.Many2one('module.template', string='Template', compute='_compute_template_id', store=True)

    # Pricing Configuration
    pricing_type = fields.Selection([
        ('fixed', 'Fixed Price'),
        ('variable', 'Variable Price'),
        ('tier_based', 'Tier Based'),
        ('field_based', 'Field Based'),
    ], string='Pricing Type', required=True, default='fixed')

    fixed_amount = fields.Float(string='Fixed Amount', default=0.0)
    currency_id = fields.Many2one('res.currency', string='Currency',
                                 default=lambda self: self.env.company.currency_id)

    # Variable Pricing Rules
    pricing_rules = fields.Text(string='Pricing Rules',
                               help="JSON configuration for variable pricing")

    # Payment Methods
    payment_provider_ids = fields.Many2many('payment.provider', string='Payment Providers',
                                           help="Available payment providers for this form")

    # Payment Flow Configuration
    payment_required = fields.Boolean(string='Payment Required', default=True)
    payment_timing = fields.Selection([
        ('immediate', 'Immediate Payment'),
        ('on_approval', 'Payment on Approval'),
        ('on_delivery', 'Payment on Delivery'),
    ], string='Payment Timing', default='immediate')

    # Notification Configuration
    send_payment_link = fields.Boolean(string='Send Payment Link', default=True)
    payment_email_template_id = fields.Many2one('mail.template', string='Payment Email Template')
    payment_reminder_days = fields.Integer(string='Payment Reminder (Days)', default=3)

    # Status
    active = fields.Boolean(string='Active', default=True)

    @api.depends('form_builder_id.template_id')
    def _compute_template_id(self):
        """Compute template_id from form_builder_id"""
        for record in self:
            if record.form_builder_id and record.form_builder_id.template_id:
                record.template_id = record.form_builder_id.template_id
            else:
                record.template_id = False

    @api.constrains('pricing_rules')
    def _check_pricing_rules(self):
        """Validate pricing rules JSON"""
        for record in self:
            if record.pricing_rules:
                try:
                    json.loads(record.pricing_rules)
                except json.JSONDecodeError as e:
                    raise ValidationError(_("Invalid pricing rules JSON: %s") % str(e))

    def calculate_amount(self, form_data):
        """Calculate payment amount based on configuration"""
        self.ensure_one()

        if self.pricing_type == 'fixed':
            return self.fixed_amount

        elif self.pricing_type == 'variable':
            return self._calculate_variable_amount(form_data)

        elif self.pricing_type == 'tier_based':
            return self._calculate_tier_amount(form_data)

        elif self.pricing_type == 'field_based':
            return self._calculate_field_amount(form_data)

        return 0.0

    def _calculate_variable_amount(self, form_data):
        """Calculate variable amount based on rules"""
        if not self.pricing_rules:
            return 0.0

        try:
            rules = json.loads(self.pricing_rules)
            base_amount = rules.get('base_amount', 0.0)

            # Apply multipliers based on form data
            for rule in rules.get('multipliers', []):
                field_name = rule.get('field')
                multiplier = rule.get('multiplier', 1.0)
                condition = rule.get('condition')

                if field_name in form_data:
                    field_value = form_data[field_name]

                    if self._evaluate_condition(field_value, condition):
                        base_amount *= multiplier

            return base_amount

        except Exception as e:
            _logger.error(f"Error calculating variable amount: {str(e)}")
            return 0.0

    def _calculate_tier_amount(self, form_data):
        """Calculate tier-based amount"""
        if not self.pricing_rules:
            return 0.0

        try:
            rules = json.loads(self.pricing_rules)
            tiers = rules.get('tiers', [])

            # Find applicable tier based on criteria
            for tier in tiers:
                criteria = tier.get('criteria', {})
                if self._check_tier_criteria(form_data, criteria):
                    return tier.get('amount', 0.0)

            # Return default amount if no tier matches
            return rules.get('default_amount', 0.0)

        except Exception as e:
            _logger.error(f"Error calculating tier amount: {str(e)}")
            return 0.0

    def _calculate_field_amount(self, form_data):
        """Calculate amount based on specific field values"""
        if not self.pricing_rules:
            return 0.0

        try:
            rules = json.loads(self.pricing_rules)
            total_amount = 0.0

            for field_rule in rules.get('field_rules', []):
                field_name = field_rule.get('field')
                amount_per_unit = field_rule.get('amount_per_unit', 0.0)

                if field_name in form_data:
                    field_value = form_data[field_name]

                    # Handle numeric fields
                    if isinstance(field_value, (int, float)):
                        total_amount += field_value * amount_per_unit

                    # Handle selection fields with specific pricing
                    elif isinstance(field_value, str):
                        selection_pricing = field_rule.get('selection_pricing', {})
                        total_amount += selection_pricing.get(field_value, 0.0)

            return total_amount

        except Exception as e:
            _logger.error(f"Error calculating field amount: {str(e)}")
            return 0.0

    def _evaluate_condition(self, value, condition):
        """Evaluate a condition against a value"""
        if not condition:
            return True

        operator = condition.get('operator', '==')
        expected = condition.get('value')

        if operator == '==':
            return value == expected
        elif operator == '!=':
            return value != expected
        elif operator == '>':
            return value > expected
        elif operator == '>=':
            return value >= expected
        elif operator == '<':
            return value < expected
        elif operator == '<=':
            return value <= expected
        elif operator == 'in':
            return value in expected
        elif operator == 'not_in':
            return value not in expected

        return True

    def _check_tier_criteria(self, form_data, criteria):
        """Check if form data meets tier criteria"""
        for field_name, condition in criteria.items():
            if field_name not in form_data:
                return False

            if not self._evaluate_condition(form_data[field_name], condition):
                return False

        return True

    def create_payment_transaction(self, record, amount=None):
        """Create payment transaction for a record"""
        self.ensure_one()

        if amount is None:
            # Get form data from record
            form_data = self._extract_form_data(record)
            amount = self.calculate_amount(form_data)

        if amount <= 0:
            raise UserError(_("Payment amount must be greater than zero"))

        # Create payment transaction
        payment_vals = {
            'reference': f"{record._name}-{record.id}",
            'amount': amount,
            'currency_id': self.currency_id.id,
            'partner_id': record.partner_id.id if hasattr(record, 'partner_id') else False,
            'provider_ids': [(6, 0, self.payment_provider_ids.ids)],
            'operation': 'online_direct',
            'tokenization_requested': False,
        }

        transaction = self.env['payment.transaction'].create(payment_vals)

        # Link transaction to record if field exists
        if hasattr(record, 'payment_transaction_id'):
            record.payment_transaction_id = transaction.id

        return transaction

    def _extract_form_data(self, record):
        """Extract form data from record for pricing calculation"""
        form_data = {}

        # Get all fields from the form builder
        for field_def in self.form_builder_id.field_definition_ids:
            if hasattr(record, field_def.name):
                form_data[field_def.name] = getattr(record, field_def.name)

        # Add system fields
        form_data.update({
            'name': getattr(record, 'name', ''),
            'state': getattr(record, 'state', ''),
            'create_date': getattr(record, 'create_date', False),
        })

        return form_data

    def send_payment_link(self, record, transaction):
        """Send payment link to customer"""
        self.ensure_one()

        if not self.send_payment_link:
            return False

        if not hasattr(record, 'partner_id') or not record.partner_id:
            raise UserError(_("Customer information is required to send payment link"))

        # Generate payment link
        payment_link = transaction.get_portal_url()

        # Send email with payment link
        if self.payment_email_template_id:
            self.payment_email_template_id.with_context(
                payment_link=payment_link,
                transaction=transaction,
                record=record,
            ).send_mail(record.id, force_send=True)

        return True

    def get_pricing_preview(self, sample_data=None):
        """Get pricing preview for configuration"""
        self.ensure_one()

        if sample_data is None:
            # Create sample data
            sample_data = {}
            for field_def in self.form_builder_id.field_definition_ids:
                if field_def.field_type == 'integer':
                    sample_data[field_def.name] = 1
                elif field_def.field_type == 'float':
                    sample_data[field_def.name] = 1.0
                elif field_def.field_type == 'selection':
                    options = field_def.get_selection_options()
                    sample_data[field_def.name] = options[0][0] if options else 'option1'
                else:
                    sample_data[field_def.name] = f"Sample {field_def.field_description}"

        amount = self.calculate_amount(sample_data)

        return {
            'amount': amount,
            'currency': self.currency_id.name,
            'sample_data': sample_data,
            'pricing_type': self.pricing_type,
        }


class PaymentWorkflow(models.Model):
    """Payment Workflow Management"""
    _name = 'payment.workflow'
    _description = 'Payment Workflow'

    name = fields.Char(string='Workflow Name', required=True)
    payment_config_id = fields.Many2one('payment.config', string='Payment Configuration', required=True)

    # Workflow Steps
    step_ids = fields.One2many('payment.workflow.step', 'workflow_id', string='Workflow Steps')

    # Status
    active = fields.Boolean(string='Active', default=True)


class PaymentWorkflowStep(models.Model):
    """Payment Workflow Steps"""
    _name = 'payment.workflow.step'
    _description = 'Payment Workflow Step'
    _order = 'sequence'

    name = fields.Char(string='Step Name', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    workflow_id = fields.Many2one('payment.workflow', string='Workflow', required=True, ondelete='cascade')

    # Step Configuration
    step_type = fields.Selection([
        ('send_link', 'Send Payment Link'),
        ('wait_payment', 'Wait for Payment'),
        ('send_reminder', 'Send Reminder'),
        ('escalate', 'Escalate'),
        ('complete', 'Complete'),
    ], string='Step Type', required=True)

    delay_days = fields.Integer(string='Delay (Days)', default=0)
    condition = fields.Text(string='Condition', help="Python expression for step execution")

    # Actions
    email_template_id = fields.Many2one('mail.template', string='Email Template')
    notification_message = fields.Text(string='Notification Message')

    # Status
    active = fields.Boolean(string='Active', default=True)
