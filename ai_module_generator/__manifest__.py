{
    'name': 'AI Module Generator',
    'version': '********.0',
    'category': 'Tools',
    'summary': 'Dynamic Module Generator with AI Integration for Odoo 17',
    'description': """
        AI Module Generator
        ==================

        A comprehensive Odoo 17 module that enables users to dynamically create complete
        business modules through JSON configuration or UI forms, with integrated AI
        document generation, customer portal functionality, and payment processing.

        Key Features:
        - Dynamic module generation with prefix 'ovakil_' and author 'Oneclickvakil'
        - JSON-based or UI-based module configuration interface
        - Complete module components: models, fields, views, actions, menus, security
        - Website form pages with public access
        - Custom REST API endpoints for each form type
        - Configurable workflow states management
        - Customer portal integration with "My Account" section
        - AI document generation workflow with prompt configuration
        - Payment integration with configurable pricing
        - Multi-channel notifications (Email, WhatsApp, Portal)
        - Form builder with validation rules
        - PDF generation and delivery system

        Generated Module Structure:
        - Backend: Complete models with CRUD views and menu structure
        - Frontend: Website forms with submission handling
        - API: REST endpoints for form interactions
        - Portal: Customer dashboard with status tracking
        - AI: Document generation with admin review workflow
        - Payment: Integrated payment processing and tracking

        Technical Specifications:
        - Odoo 17 compatibility
        - RESTful API design
        - Responsive web design
        - PDF generation using wkhtmltopdf
        - WhatsApp Business API integration
        - Secure file handling and storage
        - Multi-language support
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'web',
        'mail',
        'portal',
        'website',
        'payment',
        'website_payment',
        'survey',
        'ai_legal_document_core',
        'ai_chatbot_integration',
        'ai_dynamic_forms',
        'ai_document_generation',
        'all_in_one_dynamic_custom_fields',
    ],
    'data': [
        # Security
        'security/module_generator_security.xml',
        'security/ir.model.access.csv',

        # Views - Core Module Generator (actions must be loaded before menus)
        'views/module_template_views.xml',
        'views/form_builder_views.xml',
        'views/form_builder_preview_views.xml',
        'views/workflow_state_views.xml',
        'views/generated_module_views.xml',
        'views/field_definition_views.xml',
        'views/ai_integration_views.xml',
        'views/file_viewer_templates.xml',
        'views/menu_views.xml',
        'views/menu_actions.xml',  # Update menus with action references

        # Wizard Views
        'wizards/views/module_generator_wizard_views.xml',

        # Demo Data Loader
        'data/demo_data_loader.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'ai_module_generator/static/src/css/module_generator.css',
            'ai_module_generator/static/src/js/form_builder.js',
            'ai_module_generator/static/src/js/module_generator.js',
            'ai_module_generator/static/src/js/field_configurator.js',
        ],
        'website.assets_frontend': [
            'ai_module_generator/static/src/css/generated_forms.css',
            'ai_module_generator/static/src/js/form_validation.js',
            'ai_module_generator/static/src/js/dynamic_forms.js',
        ],
        'web.assets_qweb': [
            'ai_module_generator/static/src/xml/form_builder_templates.xml',
        ],
    },
    'demo': [
        # Demo Data Files - Now enabled!
        'demo/module_template_demo.xml',
        'demo/form_builder_demo.xml',
        'demo/field_definition_demo.xml',
        'demo/workflow_state_demo.xml',
        'demo/ai_integration_demo.xml',
        'demo/payment_integration_demo.xml',
        'demo/testing_framework_demo.xml',
    ],

    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
