# AI Module Generator - Complete User Guide

## 🚀 Overview

The AI Module Generator is a powerful Odoo 17 module that allows you to create complete, production-ready Odoo modules with advanced features including:

- **AI Integration** (GP<PERSON>, <PERSON>, <PERSON>)
- **Payment Processing** with multiple pricing models
- **Automated Testing Framework**
- **Website & Portal Integration**
- **WhatsApp & Email Notifications**
- **Sales Order Automation**

## 📋 Table of Contents

1. [Installation & Setup](#installation--setup)
2. [Getting Started](#getting-started)
3. [Creating Your First Module](#creating-your-first-module)
4. [Advanced Features](#advanced-features)
5. [Demo Data Examples](#demo-data-examples)
6. [API Documentation](#api-documentation)
7. [Troubleshooting](#troubleshooting)

## 🔧 Installation & Setup

### Prerequisites
- Odoo 17.0 or later
- Python 3.8+
- PostgreSQL database
- Internet connection for AI features

### Installation Steps

1. **Install the Module**
   ```bash
   # Copy the module to your addons directory
   cp -r ai_module_generator /mnt/extra-addons/

   # Restart Odoo
   sudo systemctl restart odoo

   # Install via web interface or command line
   sudo -u odoo odoo -c /etc/odoo/odoo.conf -d your_database -i ai_module_generator
   ```

2. **Access the Module**
   - Go to Apps → Search "AI Module Generator"
   - Click Install
   - Navigate to AI Module Generator menu

## 🎯 Getting Started

### Understanding the Module Structure

The AI Module Generator works with these core components:

1. **Module Templates** - Define the overall structure and type of module
2. **Form Builders** - Create forms for data collection
3. **Field Definitions** - Define individual form fields with validation
4. **Workflow States** - Set up automated workflows and notifications
5. **AI Integration** - Add AI-powered features and automation
6. **Payment Integration** - Configure payment processing
7. **Testing Framework** - Automated testing and validation

### Demo Data Overview

The module comes with comprehensive demo data including:

- **5 Module Templates** (Customer Feedback, Service Booking, Product Survey, E-commerce Orders, Custom CRM)
- **7 Form Builders** with different layouts and configurations
- **20+ Field Definitions** covering all field types
- **15 Workflow States** with automated transitions
- **6 AI Prompt Configurations** for different use cases
- **4 Payment Integration** setups with various pricing models
- **3 Test Suites** with 8 comprehensive test cases

## 🏗️ Creating Your First Module

### Step 1: Create a Module Template

1. Go to **AI Module Generator → Module Templates**
2. Click **Create**
3. Fill in the basic information:
   ```
   Name: My Customer Portal
   Code: ovakil_customer_portal
   Description: Customer portal for service requests
   Template Type: Service Based
   Author: Oneclickvakil
   Category: Customer Service
   ```

4. Enable integrations:
   - ✅ Website Integration
   - ✅ Portal Integration
   - ✅ Email Notifications
   - ✅ Auto Sales Order

5. Click **Save**

### Step 2: Create Form Builders

1. In your template, go to **Form Builders** tab
2. Click **Add a line**
3. Configure your form:
   ```
   Name: Service Request Form
   Code: service_request_main
   Form Type: Standard
   Layout Type: Single Column
   Website Published: Yes
   Portal Access: Yes
   Requires Login: Yes
   ```

### Step 3: Add Field Definitions

1. In your form builder, go to **Field Definitions** tab
2. Add fields one by one:

   **Customer Name Field:**
   ```
   Field Name: customer_name
   Description: Customer Name
   Type: Text (Short)
   Required: Yes
   Help Text: Please enter your full name
   ```

   **Service Type Field:**
   ```
   Field Name: service_type
   Description: Service Type
   Type: Dropdown
   Required: Yes
   Options: [
     {"key": "support", "value": "Technical Support"},
     {"key": "billing", "value": "Billing Inquiry"},
     {"key": "general", "value": "General Question"}
   ]
   ```

   **Description Field:**
   ```
   Field Name: description
   Description: Request Description
   Type: Text (Long)
   Required: Yes
   Max Length: 1000
   ```

### Step 4: Configure Workflow States

1. Go to **Workflow States** tab
2. Create workflow progression:

   **Initial State:**
   ```
   Name: Request Submitted
   Code: request_submitted
   Type: Initial
   Color: Blue (#17a2b8)
   Icon: fa-paper-plane
   WhatsApp Message: Your request has been received. Reference: {reference_number}
   ```

   **Processing State:**
   ```
   Name: Under Review
   Code: under_review
   Type: Intermediate
   Color: Orange (#fd7e14)
   Icon: fa-search
   Auto Transition: Yes (after 2 hours)
   ```

   **Final State:**
   ```
   Name: Completed
   Code: completed
   Type: Final
   Color: Green (#28a745)
   Icon: fa-check-circle
   ```

### Step 5: Generate Your Module

1. Go back to your **Module Template**
2. Click **Generate Module** button
3. Wait for the generation process to complete
4. Your module will be created with prefix `ovakil_`

### Step 6: Install Your Generated Module

1. Go to **Apps** in Odoo
2. Update Apps List
3. Search for your module name
4. Click **Install**
5. Your module is now ready to use!

## 🤖 Advanced Features

### AI Integration

Add AI-powered features to your modules:

1. **Go to AI Integration tab** in your form builder
2. **Create AI Prompt Configuration:**
   ```
   Name: Auto Response Generator
   AI Model: GPT-4
   Trigger: On Submit
   Prompt Template:
   "Generate a professional response to this service request:
   Customer: {customer_name}
   Service Type: {service_type}
   Description: {description}

   Create a helpful, professional response addressing their needs."
   ```

3. **Available AI Models:**
   - GPT-4 (Best for complex reasoning)
   - GPT-3.5-Turbo (Fast and cost-effective)
   - Claude-3 (Great for analysis)
   - Gemini-Pro (Google's latest model)

### Payment Integration

Configure payment processing:

1. **Go to Payment Integration tab**
2. **Set up pricing:**
   - **Fixed Amount:** Set a single price for all submissions
   - **Variable Pricing:** Different prices based on field values
   - **Tier-Based:** Multiple service levels
   - **Field-Based:** Calculate based on form data

3. **Example Variable Pricing:**
   ```json
   [
     {"service_type": "support", "amount": 25.00},
     {"service_type": "billing", "amount": 15.00},
     {"service_type": "general", "amount": 10.00}
   ]
   ```

### Testing Framework

Ensure your module works correctly:

1. **Go to Testing Framework tab**
2. **Create Test Suite:**
   ```
   Name: Service Request Tests
   Auto Run: Yes
   Run on Generation: Yes
   ```

3. **Add Test Cases:**
   - **Validation Tests:** Check form validation rules
   - **Workflow Tests:** Verify state transitions
   - **API Tests:** Test REST endpoints
   - **Performance Tests:** Load testing

## 📊 Demo Data Examples

### Customer Feedback System
- **Purpose:** Collect and analyze customer feedback
- **Features:** AI sentiment analysis, automated responses
- **Payment:** Premium analysis service ($25)
- **Workflow:** Submit → Review → Respond

### Service Booking System
- **Purpose:** Professional service appointment booking
- **Features:** Calendar integration, payment processing
- **Payment:** Variable pricing by service type ($100-$500)
- **Workflow:** Request → Confirm → In Progress → Complete

### E-commerce Orders
- **Purpose:** Online order management
- **Features:** Inventory tracking, shipping integration
- **Payment:** Complex field-based calculations
- **Workflow:** Place → Confirm → Ship → Deliver

## 🔌 API Documentation

Generated modules include REST API endpoints:

### Standard Endpoints
```
GET    /api/v1/{module_name}/records          # List all records
POST   /api/v1/{module_name}/records          # Create new record
GET    /api/v1/{module_name}/records/{id}     # Get specific record
PUT    /api/v1/{module_name}/records/{id}     # Update record
DELETE /api/v1/{module_name}/records/{id}     # Delete record
```

### Authentication
```python
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}
```

### Example Usage
```python
import requests

# Create a new service request
data = {
    'customer_name': 'John Doe',
    'service_type': 'support',
    'description': 'Need help with setup'
}

response = requests.post(
    'https://yourdomain.com/api/v1/ovakil_customer_portal/records',
    json=data,
    headers=headers
)
```

## 🛠️ Troubleshooting

### Common Issues

1. **Module Generation Fails**
   - Check template configuration is complete
   - Ensure all required fields are filled
   - Verify workflow states are properly configured

2. **AI Integration Not Working**
   - Check API keys are configured
   - Verify internet connection
   - Test with simple prompts first

3. **Payment Processing Issues**
   - Ensure payment providers are configured in Odoo
   - Check currency settings
   - Verify pricing rules are valid JSON

4. **Website Forms Not Showing**
   - Check "Website Published" is enabled
   - Verify website integration is active
   - Clear browser cache

### Getting Help

1. **Check Logs:** Look at Odoo server logs for detailed error messages
2. **Demo Data:** Use demo data as reference for proper configuration
3. **Test Framework:** Run tests to identify specific issues
4. **Documentation:** Refer to generated module documentation

## 🎉 Success Tips

1. **Start Simple:** Begin with basic forms and add complexity gradually
2. **Use Demo Data:** Study the demo examples for best practices
3. **Test Early:** Use the testing framework throughout development
4. **Iterate:** Generate, test, refine, and regenerate as needed
5. **Document:** Keep track of your configurations for future reference

## ⚡ Quick Start (5 Minutes)

### Option 1: Use Demo Data (Recommended for Learning)

1. **Install with Demo Data:**
   ```bash
   sudo -u odoo odoo -c /etc/odoo/odoo.conf -d your_database -i ai_module_generator --load-language=en_US
   ```

2. **Explore Demo Templates:**
   - Go to AI Module Generator → Module Templates
   - Open "Customer Feedback System"
   - Click "Generate Module"
   - Install the generated module

3. **Test the Generated Module:**
   - Go to your website
   - Find the customer feedback form
   - Submit a test feedback
   - Check the workflow progression

### Option 2: Create from Scratch

1. **Create Template:** AI Module Generator → Module Templates → Create
2. **Add Form:** Form Builders tab → Add form with 3-5 fields
3. **Set Workflow:** Workflow States tab → Add Initial → Intermediate → Final states
4. **Generate:** Click "Generate Module" button
5. **Install:** Apps → Search your module → Install

## 🎯 Module Activation Instructions

### After Generation:

1. **Update Apps List:**
   ```
   Apps → Update Apps List
   ```

2. **Find Your Module:**
   ```
   Apps → Search: "ovakil_" → Find your module
   ```

3. **Install:**
   ```
   Click "Install" button
   ```

4. **Access Your Module:**
   ```
   Main Menu → Your Module Name
   ```

### Generated Module Features:

✅ **Backend Models** - Complete CRUD operations
✅ **Website Forms** - Public forms at `/your-module-name`
✅ **Customer Portal** - Portal access for logged-in users
✅ **API Endpoints** - REST API at `/api/v1/your-module-name/`
✅ **Email Notifications** - Automated email workflows
✅ **WhatsApp Integration** - Automated WhatsApp messages
✅ **Sales Orders** - Automatic sales order creation
✅ **Payment Processing** - Integrated payment workflows
✅ **AI Features** - AI-powered automation (if configured)
✅ **Testing Suite** - Automated testing framework

---

**Generated modules follow the naming convention:** `ovakil_{your_module_code}`
**Author:** Oneclickvakil
**Support:** Use the testing framework and demo data for guidance
