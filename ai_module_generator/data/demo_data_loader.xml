<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Demo Module Template: Customer Feedback System -->
        <record id="demo_template_customer_feedback" model="module.template">
            <field name="name">Customer Feedback System</field>
            <field name="code">customer_feedback</field>
            <field name="description">Complete customer feedback collection and management system with AI analysis and payment integration</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">Customer Relationship</field>
        </record>

        <!-- Demo Module Template: Service Booking System -->
        <record id="demo_template_service_booking" model="module.template">
            <field name="name">Service Booking System</field>
            <field name="code">service_booking</field>
            <field name="description">Professional service booking system with payment processing, calendar integration, and automated workflows</field>
            <field name="template_type">service_based</field>
            <field name="state">active</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">Services</field>
        </record>

        <!-- Demo Module Template: E-commerce Orders -->
        <record id="demo_template_ecommerce_orders" model="module.template">
            <field name="name">E-commerce Order Management</field>
            <field name="code">ecommerce_orders</field>
            <field name="description">Advanced e-commerce order management with payment processing, inventory tracking, and customer portal</field>
            <field name="template_type">custom</field>
            <field name="state">validated</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">E-commerce</field>
        </record>

        <!-- Demo Form Builder for Customer Feedback with Enhanced Features -->
        <record id="demo_form_customer_feedback_main" model="form.builder">
            <field name="name">Customer Feedback Form</field>
            <field name="code">customer_feedback_main</field>
            <field name="description">Advanced customer feedback collection form with step-by-step interface, payment integration, and comprehensive features</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="sequence">10</field>

            <!-- Enhanced Form Configuration -->
            <field name="form_type">data_collection</field>
            <field name="layout_type">step_by_step</field>
            <field name="enable_step_form">True</field>
            <field name="require_login_before_form">True</field>
            <field name="enable_review_step">True</field>
            <field name="enable_edit_in_review">True</field>

            <!-- Payment Configuration -->
            <field name="enable_payment">True</field>
            <field name="payment_type">admin_approval</field>
            <field name="default_payment_rate">299.00</field>
            <field name="create_sales_order">True</field>

            <!-- Publishing & Access -->
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_enabled">True</field>
            <field name="active">True</field>
        </record>

        <!-- Demo Field Definitions for Customer Feedback -->
        <record id="demo_field_customer_name" model="field.definition">
            <field name="name">customer_name</field>
            <field name="field_description">Customer Name</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_field_customer_email" model="field.definition">
            <field name="name">customer_email</field>
            <field name="field_description">Email Address</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_field_overall_rating" model="field.definition">
            <field name="name">overall_rating</field>
            <field name="field_description">Overall Rating</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="selection_options">[["1", "1 Star"], ["2", "2 Stars"], ["3", "3 Stars"], ["4", "4 Stars"], ["5", "5 Stars"]]</field>
        </record>

        <!-- Advanced Field Examples: Many2one Field -->
        <record id="demo_field_assigned_user" model="field.definition">
            <field name="name">assigned_user</field>
            <field name="field_description">Assigned User</field>
            <field name="field_type">many2one</field>
            <field name="widget">many2one_dropdown</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">35</field>
            <field name="relation_model">res.users</field>
            <field name="domain">[('active', '=', True)]</field>
        </record>

        <!-- Advanced Field Examples: Selection Field with Priority -->
        <record id="demo_field_priority" model="field.definition">
            <field name="name">priority</field>
            <field name="field_description">Priority Level</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">36</field>
            <field name="selection_options">[["low", "Low Priority"], ["medium", "Medium Priority"], ["high", "High Priority"], ["urgent", "Urgent"]]</field>
        </record>

        <!-- Advanced Field Examples: Many2many Field -->
        <record id="demo_field_feedback_tags" model="field.definition">
            <field name="name">feedback_tags</field>
            <field name="field_description">Feedback Tags</field>
            <field name="field_type">many2many</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">37</field>
            <field name="relation_model">project.tags</field>
            <field name="widget">many2many_tags</field>
            <field name="domain">[]</field>
        </record>

        <record id="demo_field_feedback_comments" model="field.definition">
            <field name="name">feedback_comments</field>
            <field name="field_description">Comments &amp; Suggestions</field>
            <field name="field_type">text</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
        </record>

        <!-- Demo Add-ons for Customer Feedback Form -->
        <record id="demo_addon_priority_support" model="form.builder.addon">
            <field name="name">Priority Support</field>
            <field name="description">Get priority support and faster response times for your feedback</field>
            <field name="price">99.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_detailed_analysis" model="form.builder.addon">
            <field name="name">Detailed Analysis Report</field>
            <field name="description">Receive a comprehensive analysis report of your feedback with actionable insights</field>
            <field name="price">199.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_followup_service" model="form.builder.addon">
            <field name="name">Follow-up Service</field>
            <field name="description">Mandatory follow-up service to ensure your concerns are addressed</field>
            <field name="price">149.00</field>
            <field name="is_required">True</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <!-- Demo FAQs for Customer Feedback Form -->
        <record id="demo_faq_response_time" model="form.builder.faq">
            <field name="question">How long does it take to get a response to my feedback?</field>
            <field name="answer">We typically respond to all feedback within 24-48 hours during business days. Priority support customers receive responses within 4-6 hours.</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_feedback_anonymous" model="form.builder.faq">
            <field name="question">Can I submit feedback anonymously?</field>
            <field name="answer">While we require basic contact information for follow-up purposes, you can request that your feedback be treated confidentially. We respect your privacy and will not share your information without consent.</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_payment_required" model="form.builder.faq">
            <field name="question">Why is there a fee for submitting feedback?</field>
            <field name="answer">The base fee covers our comprehensive feedback analysis and response service. This ensures that every piece of feedback receives proper attention and a detailed response from our team.</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_refund_policy" model="form.builder.faq">
            <field name="question">What is your refund policy?</field>
            <field name="answer">If you're not satisfied with our response to your feedback, we offer a full refund within 30 days of submission. Simply contact our support team with your feedback reference number.</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="sequence">40</field>
            <field name="active">True</field>
        </record>

        <!-- Demo Form Builder for Service Booking with Enhanced Features -->
        <record id="demo_form_service_booking_main" model="form.builder">
            <field name="name">Professional Service Booking Form</field>
            <field name="code">service_booking_main</field>
            <field name="description">Professional service booking form with step-by-step interface, automatic payment processing, and service add-ons</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="sequence">10</field>

            <!-- Enhanced Form Configuration -->
            <field name="form_type">request</field>
            <field name="layout_type">step_by_step</field>
            <field name="enable_step_form">True</field>
            <field name="require_login_before_form">True</field>
            <field name="enable_review_step">True</field>
            <field name="enable_edit_in_review">True</field>

            <!-- Payment Configuration -->
            <field name="enable_payment">True</field>
            <field name="payment_type">auto</field>
            <field name="default_payment_rate">1500.00</field>
            <field name="create_sales_order">True</field>

            <!-- Publishing & Access -->
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_enabled">True</field>
            <field name="active">True</field>
        </record>

        <!-- Service Booking Advanced Fields -->
        <record id="demo_field_service_type" model="field.definition">
            <field name="name">service_type</field>
            <field name="field_description">Service Type</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="selection_options">[["consultation", "Legal Consultation"], ["documentation", "Document Preparation"], ["representation", "Court Representation"], ["contract", "Contract Review"]]</field>
        </record>

        <record id="demo_field_customer" model="field.definition">
            <field name="name">customer</field>
            <field name="field_description">Customer</field>
            <field name="field_type">many2one</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('is_company', '=', False), ('customer_rank', '>', 0)]</field>
        </record>

        <record id="demo_field_booking_status" model="field.definition">
            <field name="name">booking_status</field>
            <field name="field_description">Booking Status</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="selection_options">[["draft", "Draft"], ["confirmed", "Confirmed"], ["in_progress", "In Progress"], ["completed", "Completed"], ["cancelled", "Cancelled"]]</field>
        </record>

        <record id="demo_field_service_lines" model="field.definition">
            <field name="name">service_lines</field>
            <field name="field_description">Service Lines</field>
            <field name="field_type">one2many</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
            <field name="relation_model">sale.order.line</field>
            <field name="relation_field">order_id</field>
        </record>

        <!-- Demo Workflow States for Service Booking Template -->
        <record id="demo_workflow_booking_draft" model="workflow.state">
            <field name="name">Draft</field>
            <field name="code">draft</field>
            <field name="description">Initial draft state for new booking requests</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">initial</field>
            <field name="is_default">True</field>
            <field name="is_readonly">False</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#6c757d</field>
            <field name="icon">fa-edit</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_workflow_booking_submitted" model="workflow.state">
            <field name="name">Submitted</field>
            <field name="code">submitted</field>
            <field name="description">Booking request has been submitted for processing</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#007bff</field>
            <field name="icon">fa-paper-plane</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_workflow_booking_payment_pending" model="workflow.state">
            <field name="name">Payment Pending</field>
            <field name="code">payment_pending</field>
            <field name="description">Waiting for payment to be completed</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">payment_request</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="is_payment_stage">True</field>
            <field name="entry_action">request_payment</field>
            <field name="send_email_notification">True</field>
            <field name="send_whatsapp_notification">True</field>
            <field name="color">#fd7e14</field>
            <field name="icon">fa-credit-card</field>
            <field name="sequence">30</field>
        </record>

        <record id="demo_workflow_booking_confirmed" model="workflow.state">
            <field name="name">Confirmed</field>
            <field name="code">confirmed</field>
            <field name="description">Booking has been confirmed and scheduled</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-calendar-check</field>
            <field name="sequence">40</field>
        </record>

        <record id="demo_workflow_booking_completed" model="workflow.state">
            <field name="name">Completed</field>
            <field name="code">completed</field>
            <field name="description">Service has been completed successfully</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">final</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-check-circle</field>
            <field name="sequence">50</field>
        </record>

        <!-- Demo Add-ons for Service Booking Form -->
        <record id="demo_addon_express_service" model="form.builder.addon">
            <field name="name">Express Service</field>
            <field name="description">Get your service completed in half the usual time with our express processing</field>
            <field name="price">500.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_document_review" model="form.builder.addon">
            <field name="name">Document Review Service</field>
            <field name="description">Professional review of all documents before final submission</field>
            <field name="price">300.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_consultation_call" model="form.builder.addon">
            <field name="name">Initial Consultation Call</field>
            <field name="description">Mandatory 30-minute consultation call to understand your requirements</field>
            <field name="price">200.00</field>
            <field name="is_required">True</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_premium_support" model="form.builder.addon">
            <field name="name">Premium Support Package</field>
            <field name="description">24/7 premium support throughout the service delivery process</field>
            <field name="price">750.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">40</field>
            <field name="active">True</field>
        </record>

        <!-- Demo FAQs for Service Booking Form -->
        <record id="demo_faq_service_duration" model="form.builder.faq">
            <field name="question">How long does the service take to complete?</field>
            <field name="answer">Standard services typically take 5-7 business days. Legal consultations are usually completed within 2-3 days, while document preparation may take 7-10 days. Express service reduces these timelines by 50%.</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_payment_methods" model="form.builder.faq">
            <field name="question">What payment methods do you accept?</field>
            <field name="answer">We accept all major credit cards, debit cards, UPI payments, net banking, and digital wallets. Payment is processed securely through our encrypted payment gateway.</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_service_guarantee" model="form.builder.faq">
            <field name="question">Do you provide any service guarantee?</field>
            <field name="answer">Yes, we provide a 100% satisfaction guarantee. If you're not completely satisfied with our service, we'll revise it free of charge or provide a full refund within 15 days.</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_cancellation_policy" model="form.builder.faq">
            <field name="question">Can I cancel my service booking?</field>
            <field name="answer">You can cancel your booking up to 24 hours before the scheduled service time for a full refund. Cancellations within 24 hours are subject to a 25% cancellation fee.</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">40</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_required_documents" model="form.builder.faq">
            <field name="question">What documents do I need to provide?</field>
            <field name="answer">Required documents vary by service type. Generally, you'll need valid ID proof, address proof, and any relevant existing documents. Our team will provide a detailed checklist after booking confirmation.</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="sequence">50</field>
            <field name="active">True</field>
        </record>

        <!-- Demo Form Builder for E-commerce Orders with Enhanced Features -->
        <record id="demo_form_ecommerce_order_main" model="form.builder">
            <field name="name">E-commerce Order Form</field>
            <field name="code">ecommerce_order_main</field>
            <field name="description">Advanced e-commerce order form with instant payment processing and premium features</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="sequence">10</field>

            <!-- Enhanced Form Configuration -->
            <field name="form_type">custom</field>
            <field name="layout_type">single_page</field>
            <field name="enable_step_form">False</field>
            <field name="require_login_before_form">False</field>
            <field name="enable_review_step">False</field>
            <field name="enable_edit_in_review">True</field>

            <!-- Payment Configuration -->
            <field name="enable_payment">True</field>
            <field name="payment_type">auto</field>
            <field name="default_payment_rate">0.00</field>
            <field name="create_sales_order">True</field>

            <!-- Publishing & Access -->
            <field name="website_published">True</field>
            <field name="portal_access">False</field>
            <field name="api_enabled">True</field>
            <field name="active">True</field>
        </record>

        <!-- Demo Workflow States for E-commerce Order Template -->
        <record id="demo_workflow_order_draft" model="workflow.state">
            <field name="name">Draft</field>
            <field name="code">draft</field>
            <field name="description">Initial draft state for new orders</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">initial</field>
            <field name="is_default">True</field>
            <field name="is_readonly">False</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#6c757d</field>
            <field name="icon">fa-shopping-cart</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_workflow_order_submitted" model="workflow.state">
            <field name="name">Submitted</field>
            <field name="code">submitted</field>
            <field name="description">Order has been submitted and is being processed</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#007bff</field>
            <field name="icon">fa-paper-plane</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_workflow_order_payment_received" model="workflow.state">
            <field name="name">Payment Received</field>
            <field name="code">payment_received</field>
            <field name="description">Payment has been received and verified</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-money-bill</field>
            <field name="sequence">30</field>
        </record>

        <record id="demo_workflow_order_processing" model="workflow.state">
            <field name="name">Processing</field>
            <field name="code">processing</field>
            <field name="description">Order is being prepared for shipment</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#ffc107</field>
            <field name="icon">fa-cogs</field>
            <field name="sequence">40</field>
        </record>

        <record id="demo_workflow_order_shipped" model="workflow.state">
            <field name="name">Shipped</field>
            <field name="code">shipped</field>
            <field name="description">Order has been shipped to customer</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-truck</field>
            <field name="sequence">50</field>
        </record>

        <record id="demo_workflow_order_delivered" model="workflow.state">
            <field name="name">Delivered</field>
            <field name="code">delivered</field>
            <field name="description">Order has been delivered to customer</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">final</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-check-circle</field>
            <field name="sequence">60</field>
        </record>

        <!-- Demo Add-ons for E-commerce Order Form -->
        <record id="demo_addon_gift_wrapping" model="form.builder.addon">
            <field name="name">Gift Wrapping</field>
            <field name="description">Beautiful gift wrapping with personalized message card</field>
            <field name="price">50.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_express_delivery" model="form.builder.addon">
            <field name="name">Express Delivery</field>
            <field name="description">Next-day delivery for urgent orders</field>
            <field name="price">150.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_insurance" model="form.builder.addon">
            <field name="name">Shipping Insurance</field>
            <field name="description">Protect your order with comprehensive shipping insurance</field>
            <field name="price">25.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <!-- Demo FAQs for E-commerce Order Form -->
        <record id="demo_faq_shipping_time" model="form.builder.faq">
            <field name="question">How long does shipping take?</field>
            <field name="answer">Standard shipping takes 3-5 business days within the country. International shipping takes 7-14 business days. Express delivery is available for next-day delivery.</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_return_policy" model="form.builder.faq">
            <field name="question">What is your return policy?</field>
            <field name="answer">We offer a 30-day return policy for all items in original condition. Return shipping is free for defective items, and customer pays return shipping for other returns.</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_order_tracking" model="form.builder.faq">
            <field name="question">Can I track my order?</field>
            <field name="answer">Yes, you'll receive a tracking number via email once your order ships. You can track your package in real-time using our order tracking system.</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <!-- Demo Field Definitions for E-commerce Order Form -->
        <record id="demo_field_product_name" model="field.definition">
            <field name="name">product_name</field>
            <field name="field_description">Product Name</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_field_quantity" model="field.definition">
            <field name="name">quantity</field>
            <field name="field_description">Quantity</field>
            <field name="field_type">integer</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_field_customer_info" model="field.definition">
            <field name="name">customer_info</field>
            <field name="field_description">Customer</field>
            <field name="field_type">many2one</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('customer_rank', '>', 0)]</field>
        </record>

        <record id="demo_field_order_notes" model="field.definition">
            <field name="name">order_notes</field>
            <field name="field_description">Special Instructions</field>
            <field name="field_type">text</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="form_builder_id" ref="demo_form_ecommerce_order_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
        </record>

        <!-- Demo Workflow States for Customer Feedback Template -->
        <record id="demo_workflow_feedback_draft" model="workflow.state">
            <field name="name">Draft</field>
            <field name="code">draft</field>
            <field name="description">Initial draft state for new feedback records</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">initial</field>
            <field name="is_default">True</field>
            <field name="is_readonly">False</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#6c757d</field>
            <field name="icon">fa-edit</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_workflow_feedback_submitted" model="workflow.state">
            <field name="name">Submitted</field>
            <field name="code">submitted</field>
            <field name="description">Customer has submitted their feedback for review</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#007bff</field>
            <field name="icon">fa-paper-plane</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_workflow_feedback_under_review" model="workflow.state">
            <field name="name">Under Review</field>
            <field name="code">under_review</field>
            <field name="description">Feedback is being reviewed by the team</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#ffc107</field>
            <field name="icon">fa-search</field>
            <field name="sequence">30</field>
        </record>

        <record id="demo_workflow_feedback_responded" model="workflow.state">
            <field name="name">Responded</field>
            <field name="code">responded</field>
            <field name="description">Response has been sent to the customer</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">final</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-check</field>
            <field name="sequence">40</field>
        </record>

        <!-- Demo Module Template: Complete Legal Services Showcase -->
        <record id="demo_template_legal_services_complete" model="module.template">
            <field name="name">Complete Legal Services Portal</field>
            <field name="code">legal_services_complete</field>
            <field name="description">Comprehensive legal services portal showcasing all enhanced AI Module Generator features including step-by-step forms, payment integration, add-ons, and FAQs</field>
            <field name="template_type">service_based</field>
            <field name="state">active</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">Legal Services</field>
        </record>

        <!-- Complete Demo Form Builder showcasing ALL enhanced features -->
        <record id="demo_form_legal_services_complete" model="form.builder">
            <field name="name">Complete Legal Services Application</field>
            <field name="code">legal_services_complete</field>
            <field name="description">Comprehensive legal services application form demonstrating all enhanced features: step-by-step interface, payment processing, service add-ons, comprehensive FAQs, and professional UI</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="sequence">10</field>

            <!-- Complete Enhanced Form Configuration -->
            <field name="form_type">application</field>
            <field name="layout_type">step_by_step</field>
            <field name="enable_step_form">True</field>
            <field name="require_login_before_form">True</field>
            <field name="enable_review_step">True</field>
            <field name="enable_edit_in_review">True</field>

            <!-- Complete Payment Configuration -->
            <field name="enable_payment">True</field>
            <field name="payment_type">admin_approval</field>
            <field name="default_payment_rate">2500.00</field>
            <field name="create_sales_order">True</field>

            <!-- Complete Publishing & Access Configuration -->
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_enabled">True</field>
            <field name="active">True</field>
        </record>

        <!-- Complete Demo Field Definitions showcasing various field types -->
        <record id="demo_field_complete_client_name" model="field.definition">
            <field name="name">client_name</field>
            <field name="field_description">Full Name</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_field_complete_email" model="field.definition">
            <field name="name">client_email</field>
            <field name="field_description">Email Address</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_field_complete_phone" model="field.definition">
            <field name="name">client_phone</field>
            <field name="field_description">Phone Number</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
        </record>

        <record id="demo_field_complete_service_type" model="field.definition">
            <field name="name">service_type</field>
            <field name="field_description">Legal Service Required</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">True</field>
            <field name="sequence">40</field>
            <field name="selection_options">[["business_registration", "Business Registration"], ["legal_notice", "Legal Notice"], ["contract_drafting", "Contract Drafting"], ["court_representation", "Court Representation"], ["property_documentation", "Property Documentation"], ["trademark_registration", "Trademark Registration"]]</field>
        </record>

        <record id="demo_field_complete_urgency" model="field.definition">
            <field name="name">urgency_level</field>
            <field name="field_description">Urgency Level</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">True</field>
            <field name="sequence">50</field>
            <field name="selection_options">[["standard", "Standard (7-10 days)"], ["priority", "Priority (3-5 days)"], ["urgent", "Urgent (1-2 days)"], ["emergency", "Emergency (Same day)"]]</field>
        </record>

        <record id="demo_field_complete_case_details" model="field.definition">
            <field name="name">case_details</field>
            <field name="field_description">Case Details &amp; Requirements</field>
            <field name="field_type">text</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">True</field>
            <field name="sequence">60</field>
        </record>

        <record id="demo_field_complete_assigned_lawyer" model="field.definition">
            <field name="name">assigned_lawyer</field>
            <field name="field_description">Preferred Lawyer</field>
            <field name="field_type">many2one</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="required">False</field>
            <field name="sequence">70</field>
            <field name="relation_model">res.users</field>
            <field name="domain">[('active', '=', True)]</field>
        </record>

        <!-- Demo Workflow States for Legal Services Complete Template -->
        <record id="demo_workflow_legal_draft" model="workflow.state">
            <field name="name">Draft</field>
            <field name="code">draft</field>
            <field name="description">Initial draft state for new legal service requests</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">initial</field>
            <field name="is_default">True</field>
            <field name="is_readonly">False</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#6c757d</field>
            <field name="icon">fa-edit</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_workflow_legal_submitted" model="workflow.state">
            <field name="name">Submitted</field>
            <field name="code">submitted</field>
            <field name="description">Legal service request has been submitted for review</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#007bff</field>
            <field name="icon">fa-paper-plane</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_workflow_legal_consultation_scheduled" model="workflow.state">
            <field name="name">Consultation Scheduled</field>
            <field name="code">consultation_scheduled</field>
            <field name="description">Initial consultation has been scheduled with the client</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="send_whatsapp_notification">True</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-calendar</field>
            <field name="sequence">30</field>
        </record>

        <record id="demo_workflow_legal_payment_pending" model="workflow.state">
            <field name="name">Payment Pending</field>
            <field name="code">payment_pending</field>
            <field name="description">Waiting for payment to proceed with legal services</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">payment_request</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="is_payment_stage">True</field>
            <field name="entry_action">send_payment_link</field>
            <field name="send_email_notification">True</field>
            <field name="send_whatsapp_notification">True</field>
            <field name="color">#fd7e14</field>
            <field name="icon">fa-credit-card</field>
            <field name="sequence">40</field>
        </record>

        <record id="demo_workflow_legal_in_progress" model="workflow.state">
            <field name="name">In Progress</field>
            <field name="code">in_progress</field>
            <field name="description">Legal work is in progress</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#ffc107</field>
            <field name="icon">fa-briefcase</field>
            <field name="sequence">50</field>
        </record>

        <record id="demo_workflow_legal_document_review" model="workflow.state">
            <field name="name">Document Review</field>
            <field name="code">document_review</field>
            <field name="description">Documents are being reviewed by senior legal experts</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">intermediate</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="color">#6f42c1</field>
            <field name="icon">fa-search</field>
            <field name="sequence">60</field>
        </record>

        <record id="demo_workflow_legal_completed" model="workflow.state">
            <field name="name">Completed</field>
            <field name="code">completed</field>
            <field name="description">Legal service has been completed successfully</field>
            <field name="template_id" ref="demo_template_legal_services_complete"/>
            <field name="state_type">final</field>
            <field name="is_default">False</field>
            <field name="is_readonly">True</field>
            <field name="is_portal_visible">True</field>
            <field name="entry_action">send_email</field>
            <field name="send_email_notification">True</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-check-circle</field>
            <field name="sequence">70</field>
        </record>

        <!-- Complete Demo Add-ons showcasing all addon types and pricing strategies -->
        <record id="demo_addon_complete_consultation" model="form.builder.addon">
            <field name="name">Initial Legal Consultation</field>
            <field name="description">Mandatory 45-minute consultation with our senior legal expert to understand your case requirements and provide initial guidance</field>
            <field name="price">500.00</field>
            <field name="is_required">True</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_complete_document_review" model="form.builder.addon">
            <field name="name">Professional Document Review</field>
            <field name="description">Comprehensive review of all your existing documents by our legal team before proceeding with your case</field>
            <field name="price">750.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_complete_express_processing" model="form.builder.addon">
            <field name="name">Express Processing</field>
            <field name="description">Fast-track your legal service with priority processing and dedicated case manager</field>
            <field name="price">1200.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_complete_premium_support" model="form.builder.addon">
            <field name="name">Premium Support Package</field>
            <field name="description">24/7 premium support with direct access to your assigned lawyer via phone, email, and WhatsApp</field>
            <field name="price">2000.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">40</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_complete_court_representation" model="form.builder.addon">
            <field name="name">Court Representation Service</field>
            <field name="description">Professional court representation by experienced advocates if your case requires court proceedings</field>
            <field name="price">5000.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">50</field>
            <field name="active">True</field>
        </record>

        <record id="demo_addon_complete_compliance_monitoring" model="form.builder.addon">
            <field name="name">Ongoing Compliance Monitoring</field>
            <field name="description">6-month compliance monitoring service to ensure all legal requirements are met post-service completion</field>
            <field name="price">1500.00</field>
            <field name="is_required">False</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">60</field>
            <field name="active">True</field>
        </record>

        <!-- Complete Demo FAQs covering all aspects of the service -->
        <record id="demo_faq_complete_service_timeline" model="form.builder.faq">
            <field name="question">How long does the legal service process take?</field>
            <field name="answer">Service timelines vary by type: Business Registration (7-10 days), Legal Notices (3-5 days), Contract Drafting (5-7 days), Court Representation (varies by case complexity), Property Documentation (10-15 days), Trademark Registration (6-8 months). Express processing reduces these timelines by 50%.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">10</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_pricing_structure" model="form.builder.faq">
            <field name="question">How is the pricing calculated?</field>
            <field name="answer">Our pricing includes a base service fee plus optional add-ons. The initial consultation (₹500) is mandatory. Additional services like document review (₹750), express processing (₹1,200), and premium support (₹2,000) can be added based on your needs. All prices are inclusive of taxes.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">20</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_payment_process" model="form.builder.faq">
            <field name="question">How does the payment process work?</field>
            <field name="answer">We use an admin approval payment system. After you submit your application, our team reviews it and sends you a payment link with the exact amount. Payment is processed securely through our encrypted gateway. Work begins only after payment confirmation.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">30</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_document_requirements" model="form.builder.faq">
            <field name="question">What documents do I need to provide?</field>
            <field name="answer">Required documents vary by service type. Generally needed: Valid ID proof, Address proof, PAN card, and service-specific documents. For business registration: MOA/AOA drafts. For property: Sale deed, NOC. For trademark: Logo/mark samples. We provide a detailed checklist after consultation.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">40</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_lawyer_assignment" model="form.builder.faq">
            <field name="question">How is a lawyer assigned to my case?</field>
            <field name="answer">We assign lawyers based on expertise and case type. You can request a preferred lawyer during application. Our senior partners handle complex cases, while experienced associates manage routine matters. All lawyers are bar-certified with 5+ years experience.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">50</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_communication" model="form.builder.faq">
            <field name="question">How will I be updated on my case progress?</field>
            <field name="answer">Regular updates via email and SMS at key milestones. Premium support customers get direct lawyer access via phone/WhatsApp. All clients can track progress through our client portal. Weekly status reports for complex cases.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">60</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_guarantee" model="form.builder.faq">
            <field name="question">Do you provide any service guarantee?</field>
            <field name="answer">Yes, we provide a 100% satisfaction guarantee. If you're not satisfied with our service quality, we'll revise it free of charge or provide a full refund within 15 days of service completion. Court case outcomes cannot be guaranteed due to their nature.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">70</field>
            <field name="active">True</field>
        </record>

        <record id="demo_faq_complete_confidentiality" model="form.builder.faq">
            <field name="question">How do you ensure confidentiality of my case?</field>
            <field name="answer">We maintain strict attorney-client privilege. All communications are confidential, documents are stored securely with encryption, and access is limited to assigned team members only. We sign NDAs for sensitive business matters.</field>
            <field name="form_builder_id" ref="demo_form_legal_services_complete"/>
            <field name="sequence">80</field>
            <field name="active">True</field>
        </record>

    </data>
</odoo>
