<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Demo Module Template: Customer Feedback System -->
        <record id="demo_template_customer_feedback" model="module.template">
            <field name="name">Customer Feedback System</field>
            <field name="code">customer_feedback</field>
            <field name="description">Complete customer feedback collection and management system with AI analysis and payment integration</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">Customer Relationship</field>
        </record>

        <!-- Demo Module Template: Service Booking System -->
        <record id="demo_template_service_booking" model="module.template">
            <field name="name">Service Booking System</field>
            <field name="code">service_booking</field>
            <field name="description">Professional service booking system with payment processing, calendar integration, and automated workflows</field>
            <field name="template_type">service_based</field>
            <field name="state">active</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">Services</field>
        </record>

        <!-- Demo Module Template: E-commerce Orders -->
        <record id="demo_template_ecommerce_orders" model="module.template">
            <field name="name">E-commerce Order Management</field>
            <field name="code">ecommerce_orders</field>
            <field name="description">Advanced e-commerce order management with payment processing, inventory tracking, and customer portal</field>
            <field name="template_type">custom</field>
            <field name="state">validated</field>
            <field name="module_author">Oneclickvakil</field>
            <field name="module_version">********.0</field>
            <field name="module_category">E-commerce</field>
        </record>

        <!-- Demo Form Builder for Customer Feedback -->
        <record id="demo_form_customer_feedback_main" model="form.builder">
            <field name="name">Customer Feedback Form</field>
            <field name="code">customer_feedback_main</field>
            <field name="description">Main customer feedback collection form with rating and comments</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="sequence">10</field>
        </record>

        <!-- Demo Field Definitions for Customer Feedback -->
        <record id="demo_field_customer_name" model="field.definition">
            <field name="name">customer_name</field>
            <field name="field_description">Customer Name</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_field_customer_email" model="field.definition">
            <field name="name">customer_email</field>
            <field name="field_description">Email Address</field>
            <field name="field_type">char</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_field_overall_rating" model="field.definition">
            <field name="name">overall_rating</field>
            <field name="field_description">Overall Rating</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="selection_options">[["1", "1 Star"], ["2", "2 Stars"], ["3", "3 Stars"], ["4", "4 Stars"], ["5", "5 Stars"]]</field>
        </record>

        <!-- Advanced Field Examples: Many2one Field -->
        <record id="demo_field_assigned_user" model="field.definition">
            <field name="name">assigned_user</field>
            <field name="field_description">Assigned User</field>
            <field name="field_type">many2one</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">35</field>
            <field name="relation_model">res.users</field>
            <field name="domain">[('active', '=', True)]</field>
        </record>

        <!-- Advanced Field Examples: Selection Field with Priority -->
        <record id="demo_field_priority" model="field.definition">
            <field name="name">priority</field>
            <field name="field_description">Priority Level</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">36</field>
            <field name="selection_options">[["low", "Low Priority"], ["medium", "Medium Priority"], ["high", "High Priority"], ["urgent", "Urgent"]]</field>
        </record>

        <!-- Advanced Field Examples: Many2many Field -->
        <record id="demo_field_feedback_tags" model="field.definition">
            <field name="name">feedback_tags</field>
            <field name="field_description">Feedback Tags</field>
            <field name="field_type">many2many</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">37</field>
            <field name="relation_model">project.tags</field>
            <field name="widget">many2many_tags</field>
            <field name="domain">[]</field>
        </record>

        <record id="demo_field_feedback_comments" model="field.definition">
            <field name="name">feedback_comments</field>
            <field name="field_description">Comments &amp; Suggestions</field>
            <field name="field_type">text</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
        </record>

        <!-- Demo Form Builder for Service Booking -->
        <record id="demo_form_service_booking_main" model="form.builder">
            <field name="name">Service Booking Form</field>
            <field name="code">service_booking_main</field>
            <field name="description">Main service booking form with customer details and service selection</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="sequence">10</field>
        </record>

        <!-- Service Booking Advanced Fields -->
        <record id="demo_field_service_type" model="field.definition">
            <field name="name">service_type</field>
            <field name="field_description">Service Type</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="selection_options">[["consultation", "Legal Consultation"], ["documentation", "Document Preparation"], ["representation", "Court Representation"], ["contract", "Contract Review"]]</field>
        </record>

        <record id="demo_field_customer" model="field.definition">
            <field name="name">customer</field>
            <field name="field_description">Customer</field>
            <field name="field_type">many2one</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('is_company', '=', False), ('customer_rank', '>', 0)]</field>
        </record>

        <record id="demo_field_booking_status" model="field.definition">
            <field name="name">booking_status</field>
            <field name="field_description">Booking Status</field>
            <field name="field_type">selection</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="selection_options">[["draft", "Draft"], ["confirmed", "Confirmed"], ["in_progress", "In Progress"], ["completed", "Completed"], ["cancelled", "Cancelled"]]</field>
        </record>

        <record id="demo_field_service_lines" model="field.definition">
            <field name="name">service_lines</field>
            <field name="field_description">Service Lines</field>
            <field name="field_type">one2many</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
            <field name="relation_model">sale.order.line</field>
            <field name="relation_field">order_id</field>
        </record>

        <!-- Demo Workflow States for Customer Feedback -->
        <record id="demo_workflow_feedback_submitted" model="workflow.state">
            <field name="name">Feedback Submitted</field>
            <field name="code">feedback_submitted</field>
            <field name="description">Customer has submitted their feedback</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">initial</field>
            <field name="sequence">10</field>
        </record>

        <record id="demo_workflow_feedback_under_review" model="workflow.state">
            <field name="name">Under Review</field>
            <field name="code">feedback_under_review</field>
            <field name="description">Feedback is being reviewed by the team</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">intermediate</field>
            <field name="sequence">20</field>
        </record>

        <record id="demo_workflow_feedback_responded" model="workflow.state">
            <field name="name">Responded</field>
            <field name="code">feedback_responded</field>
            <field name="description">Response has been sent to the customer</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">final</field>
            <field name="sequence">30</field>
        </record>

    </data>
</odoo>
