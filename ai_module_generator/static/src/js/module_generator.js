/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onWillStart } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * AI Module Generator Main Component
 */
export class ModuleGeneratorComponent extends Component {
    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        
        this.state = useState({
            templates: [],
            selectedTemplate: null,
            generationProgress: {},
            isGenerating: false,
        });
        
        onWillStart(async () => {
            await this.loadTemplates();
        });
    }
    
    async loadTemplates() {
        try {
            this.state.templates = await this.orm.searchRead(
                "module.template",
                [],
                ["name", "code", "description", "state", "template_type", "generation_count"]
            );
        } catch (error) {
            this.notification.add("Failed to load templates", { type: "danger" });
        }
    }
    
    async generateModule(templateId) {
        if (this.state.isGenerating) {
            this.notification.add("Module generation already in progress", { type: "warning" });
            return;
        }
        
        this.state.isGenerating = true;
        this.state.generationProgress = {
            step: 1,
            totalSteps: 6,
            currentStep: "Initializing...",
            progress: 0
        };
        
        try {
            // Step 1: Initialize
            this.updateProgress(1, "Initializing module generation...");
            
            // Step 2: Generate Models
            this.updateProgress(2, "Generating models...");
            await this.orm.call("module.template", "generate_models", [templateId]);
            
            // Step 3: Generate Views
            this.updateProgress(3, "Generating views...");
            await this.orm.call("module.template", "generate_views", [templateId]);
            
            // Step 4: Generate Controllers
            this.updateProgress(4, "Generating controllers...");
            await this.orm.call("module.template", "generate_controllers", [templateId]);
            
            // Step 5: Generate Security
            this.updateProgress(5, "Generating security rules...");
            await this.orm.call("module.template", "generate_security", [templateId]);
            
            // Step 6: Finalize
            this.updateProgress(6, "Finalizing module...");
            const result = await this.orm.call("module.template", "action_generate_module", [templateId]);
            
            this.notification.add("Module generated successfully!", { type: "success" });
            
            if (result && result.url) {
                window.open(result.url, '_blank');
            }
            
        } catch (error) {
            this.notification.add(`Module generation failed: ${error.message}`, { type: "danger" });
        } finally {
            this.state.isGenerating = false;
            this.state.generationProgress = {};
            await this.loadTemplates(); // Refresh templates
        }
    }
    
    updateProgress(step, message) {
        this.state.generationProgress = {
            ...this.state.generationProgress,
            step: step,
            currentStep: message,
            progress: (step / this.state.generationProgress.totalSteps) * 100
        };
    }
    
    async duplicateTemplate(templateId) {
        try {
            const result = await this.orm.call("module.template", "action_duplicate_template", [templateId]);
            this.notification.add("Template duplicated successfully!", { type: "success" });
            await this.loadTemplates();
            
            if (result && result.res_id) {
                // Open the duplicated template
                this.openTemplate(result.res_id);
            }
        } catch (error) {
            this.notification.add(`Failed to duplicate template: ${error.message}`, { type: "danger" });
        }
    }
    
    openTemplate(templateId) {
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'module.template',
            res_id: templateId,
            view_mode: 'form',
            target: 'current',
        });
    }
    
    getTemplateStatusClass(state) {
        const statusClasses = {
            'draft': 'badge-secondary',
            'validated': 'badge-warning',
            'active': 'badge-success',
            'archived': 'badge-dark'
        };
        return statusClasses[state] || 'badge-secondary';
    }
    
    getTemplateTypeIcon(templateType) {
        const typeIcons = {
            'form_based': 'fa-wpforms',
            'service_based': 'fa-cogs',
            'ecommerce': 'fa-shopping-cart',
            'survey': 'fa-poll',
            'custom': 'fa-puzzle-piece'
        };
        return typeIcons[templateType] || 'fa-file';
    }
}

ModuleGeneratorComponent.template = "ai_module_generator.ModuleGeneratorTemplate";

/**
 * Form Builder Component
 */
export class FormBuilderComponent extends Component {
    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        
        this.state = useState({
            forms: [],
            selectedForm: null,
            fields: [],
            isEditing: false,
        });
    }
    
    async loadForms(templateId) {
        try {
            this.state.forms = await this.orm.searchRead(
                "form.builder",
                [["template_id", "=", templateId]],
                ["name", "code", "description", "form_type", "website_published", "portal_access"]
            );
        } catch (error) {
            this.notification.add("Failed to load forms", { type: "danger" });
        }
    }
    
    async loadFields(formId) {
        try {
            this.state.fields = await this.orm.searchRead(
                "field.definition",
                [["form_builder_id", "=", formId]],
                ["name", "field_description", "field_type", "required", "sequence"]
            );
        } catch (error) {
            this.notification.add("Failed to load fields", { type: "danger" });
        }
    }
    
    async addField(formId, fieldData) {
        try {
            await this.orm.create("field.definition", [{
                ...fieldData,
                form_builder_id: formId
            }]);
            
            this.notification.add("Field added successfully!", { type: "success" });
            await this.loadFields(formId);
        } catch (error) {
            this.notification.add(`Failed to add field: ${error.message}`, { type: "danger" });
        }
    }
    
    async updateField(fieldId, fieldData) {
        try {
            await this.orm.write("field.definition", [fieldId], fieldData);
            this.notification.add("Field updated successfully!", { type: "success" });
            await this.loadFields(this.state.selectedForm);
        } catch (error) {
            this.notification.add(`Failed to update field: ${error.message}`, { type: "danger" });
        }
    }
    
    async deleteField(fieldId) {
        try {
            await this.orm.unlink("field.definition", [fieldId]);
            this.notification.add("Field deleted successfully!", { type: "success" });
            await this.loadFields(this.state.selectedForm);
        } catch (error) {
            this.notification.add(`Failed to delete field: ${error.message}`, { type: "danger" });
        }
    }
}

FormBuilderComponent.template = "ai_module_generator.FormBuilderTemplate";

/**
 * AI Integration Component
 */
export class AIIntegrationComponent extends Component {
    setup() {
        this.orm = useService("orm");
        this.notification = useService("notification");
        
        this.state = useState({
            prompts: [],
            selectedPrompt: null,
            testResult: null,
            isTestingPrompt: false,
        });
    }
    
    async loadPrompts(formId) {
        try {
            this.state.prompts = await this.orm.searchRead(
                "ai.prompt.config",
                [["form_builder_id", "=", formId]],
                ["name", "prompt_type", "ai_model", "active", "execution_count"]
            );
        } catch (error) {
            this.notification.add("Failed to load AI prompts", { type: "danger" });
        }
    }
    
    async testPrompt(promptId) {
        if (this.state.isTestingPrompt) {
            this.notification.add("Prompt test already in progress", { type: "warning" });
            return;
        }
        
        this.state.isTestingPrompt = true;
        this.state.testResult = null;
        
        try {
            const result = await this.orm.call("ai.prompt.config", "test_prompt", [promptId]);
            
            if (result && result.type === 'ir.actions.act_window') {
                // Open test result wizard
                this.env.services.action.doAction(result);
            }
            
        } catch (error) {
            this.notification.add(`Prompt test failed: ${error.message}`, { type: "danger" });
        } finally {
            this.state.isTestingPrompt = false;
        }
    }
    
    async executePrompt(promptId, recordData) {
        try {
            const result = await this.orm.call("ai.prompt.config", "execute_prompt", [promptId], {
                record_data: recordData
            });
            
            this.notification.add("AI prompt executed successfully!", { type: "success" });
            return result;
        } catch (error) {
            this.notification.add(`AI prompt execution failed: ${error.message}`, { type: "danger" });
            throw error;
        }
    }
}

AIIntegrationComponent.template = "ai_module_generator.AIIntegrationTemplate";

// Register components
registry.category("actions").add("ai_module_generator.module_generator", ModuleGeneratorComponent);
registry.category("actions").add("ai_module_generator.form_builder", FormBuilderComponent);
registry.category("actions").add("ai_module_generator.ai_integration", AIIntegrationComponent);
