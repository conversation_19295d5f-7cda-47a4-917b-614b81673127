/**
 * Frontend Form Validation for Generated Forms
 */

(function() {
    'use strict';
    
    // Form validation class
    class GeneratedFormValidator {
        constructor(formElement) {
            this.form = formElement;
            this.fields = {};
            this.errors = {};
            this.isSubmitting = false;
            
            this.init();
        }
        
        init() {
            this.bindEvents();
            this.setupFields();
            this.setupFileUploads();
        }
        
        bindEvents() {
            // Form submission
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
            
            // Real-time validation
            this.form.addEventListener('input', (e) => {
                if (e.target.matches('input, textarea, select')) {
                    this.validateField(e.target);
                }
            });
            
            // Blur validation
            this.form.addEventListener('blur', (e) => {
                if (e.target.matches('input, textarea, select')) {
                    this.validateField(e.target);
                }
            }, true);
        }
        
        setupFields() {
            const inputs = this.form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                const fieldName = input.name;
                if (fieldName) {
                    this.fields[fieldName] = {
                        element: input,
                        type: input.type || input.tagName.toLowerCase(),
                        required: input.hasAttribute('required'),
                        pattern: input.getAttribute('pattern'),
                        minLength: input.getAttribute('minlength'),
                        maxLength: input.getAttribute('maxlength'),
                        min: input.getAttribute('min'),
                        max: input.getAttribute('max'),
                    };
                }
            });
        }
        
        setupFileUploads() {
            const fileInputs = this.form.querySelectorAll('input[type="file"]');
            fileInputs.forEach(input => {
                this.setupDragAndDrop(input);
            });
        }
        
        setupDragAndDrop(fileInput) {
            const container = fileInput.closest('.form_file_input') || fileInput.parentElement;
            
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                });
            });
            
            ['dragenter', 'dragover'].forEach(eventName => {
                container.addEventListener(eventName, () => {
                    container.classList.add('dragover');
                });
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                container.addEventListener(eventName, () => {
                    container.classList.remove('dragover');
                });
            });
            
            container.addEventListener('drop', (e) => {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    this.validateField(fileInput);
                    this.updateFileDisplay(fileInput, files[0]);
                }
            });
            
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.updateFileDisplay(e.target, e.target.files[0]);
                }
            });
        }
        
        updateFileDisplay(fileInput, file) {
            const container = fileInput.closest('.form_file_input') || fileInput.parentElement;
            let display = container.querySelector('.file-display');
            
            if (!display) {
                display = document.createElement('div');
                display.className = 'file-display mt-2';
                container.appendChild(display);
            }
            
            display.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fa fa-file mr-2"></i>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size ml-2 text-muted">(${this.formatFileSize(file.size)})</span>
                    <button type="button" class="btn btn-sm btn-outline-danger ml-auto" onclick="this.closest('.file-display').remove(); this.closest('.form_file_input').querySelector('input').value = '';">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            `;
        }
        
        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        validateField(fieldElement) {
            const fieldName = fieldElement.name;
            const fieldConfig = this.fields[fieldName];
            
            if (!fieldConfig) return true;
            
            const value = this.getFieldValue(fieldElement);
            const errors = [];
            
            // Required validation
            if (fieldConfig.required && this.isEmpty(value)) {
                errors.push('This field is required');
            }
            
            // Type-specific validation
            if (!this.isEmpty(value)) {
                switch (fieldConfig.type) {
                    case 'email':
                        if (!this.isValidEmail(value)) {
                            errors.push('Please enter a valid email address');
                        }
                        break;
                    case 'url':
                        if (!this.isValidUrl(value)) {
                            errors.push('Please enter a valid URL');
                        }
                        break;
                    case 'tel':
                        if (!this.isValidPhone(value)) {
                            errors.push('Please enter a valid phone number');
                        }
                        break;
                    case 'number':
                        if (!this.isValidNumber(value)) {
                            errors.push('Please enter a valid number');
                        }
                        break;
                    case 'date':
                        if (!this.isValidDate(value)) {
                            errors.push('Please enter a valid date');
                        }
                        break;
                }
                
                // Length validation
                if (fieldConfig.minLength && value.length < parseInt(fieldConfig.minLength)) {
                    errors.push(`Minimum length is ${fieldConfig.minLength} characters`);
                }
                if (fieldConfig.maxLength && value.length > parseInt(fieldConfig.maxLength)) {
                    errors.push(`Maximum length is ${fieldConfig.maxLength} characters`);
                }
                
                // Numeric range validation
                if (fieldConfig.min && parseFloat(value) < parseFloat(fieldConfig.min)) {
                    errors.push(`Minimum value is ${fieldConfig.min}`);
                }
                if (fieldConfig.max && parseFloat(value) > parseFloat(fieldConfig.max)) {
                    errors.push(`Maximum value is ${fieldConfig.max}`);
                }
                
                // Pattern validation
                if (fieldConfig.pattern && !new RegExp(fieldConfig.pattern).test(value)) {
                    errors.push('Please enter a value in the correct format');
                }
            }
            
            this.setFieldErrors(fieldName, errors);
            return errors.length === 0;
        }
        
        getFieldValue(fieldElement) {
            switch (fieldElement.type) {
                case 'checkbox':
                    return fieldElement.checked;
                case 'radio':
                    const radioGroup = this.form.querySelectorAll(`input[name="${fieldElement.name}"]`);
                    for (let radio of radioGroup) {
                        if (radio.checked) return radio.value;
                    }
                    return '';
                case 'file':
                    return fieldElement.files.length > 0 ? fieldElement.files[0] : null;
                default:
                    return fieldElement.value.trim();
            }
        }
        
        isEmpty(value) {
            if (value === null || value === undefined) return true;
            if (typeof value === 'string') return value.trim() === '';
            if (typeof value === 'boolean') return false;
            return !value;
        }
        
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        isValidUrl(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        }
        
        isValidPhone(phone) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
        }
        
        isValidNumber(value) {
            return !isNaN(value) && isFinite(value);
        }
        
        isValidDate(date) {
            return !isNaN(Date.parse(date));
        }
        
        setFieldErrors(fieldName, errors) {
            const fieldConfig = this.fields[fieldName];
            if (!fieldConfig) return;
            
            const fieldElement = fieldConfig.element;
            const errorContainer = this.getErrorContainer(fieldElement);
            
            // Update field styling
            if (errors.length > 0) {
                fieldElement.classList.add('error');
                this.errors[fieldName] = errors;
            } else {
                fieldElement.classList.remove('error');
                delete this.errors[fieldName];
            }
            
            // Update error display
            if (errors.length > 0) {
                errorContainer.textContent = errors[0];
                errorContainer.classList.add('show');
            } else {
                errorContainer.textContent = '';
                errorContainer.classList.remove('show');
            }
        }
        
        getErrorContainer(fieldElement) {
            let errorContainer = fieldElement.parentElement.querySelector('.form_field_error');
            
            if (!errorContainer) {
                errorContainer = document.createElement('div');
                errorContainer.className = 'form_field_error';
                fieldElement.parentElement.appendChild(errorContainer);
            }
            
            return errorContainer;
        }
        
        validateForm() {
            let isValid = true;
            
            Object.keys(this.fields).forEach(fieldName => {
                const fieldConfig = this.fields[fieldName];
                if (!this.validateField(fieldConfig.element)) {
                    isValid = false;
                }
            });
            
            return isValid;
        }
        
        async handleSubmit() {
            if (this.isSubmitting) return;
            
            // Validate form
            if (!this.validateForm()) {
                this.showMessage('Please correct the errors below', 'error');
                return;
            }
            
            this.isSubmitting = true;
            this.showLoading(true);
            
            try {
                // Prepare form data
                const formData = new FormData(this.form);
                
                // Submit form
                const response = await fetch(this.form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                });
                
                if (response.ok) {
                    const result = await response.text();
                    
                    // Check if it's a redirect response
                    if (response.redirected || result.includes('thank-you')) {
                        window.location.href = response.url || this.form.action + '/thank-you';
                    } else {
                        this.showMessage('Form submitted successfully!', 'success');
                        this.form.reset();
                    }
                } else {
                    throw new Error('Submission failed');
                }
                
            } catch (error) {
                this.showMessage('An error occurred while submitting the form. Please try again.', 'error');
            } finally {
                this.isSubmitting = false;
                this.showLoading(false);
            }
        }
        
        showLoading(show) {
            const loadingElement = this.form.querySelector('.form_loading');
            const submitButton = this.form.querySelector('.form_submit_button');
            
            if (loadingElement) {
                loadingElement.classList.toggle('show', show);
            }
            
            if (submitButton) {
                submitButton.disabled = show;
                submitButton.textContent = show ? 'Submitting...' : 'Submit';
            }
        }
        
        showMessage(message, type) {
            const messageElement = this.form.querySelector(`.form_${type}_message`);
            
            if (messageElement) {
                messageElement.textContent = message;
                messageElement.classList.add('show');
                
                // Auto-hide success messages
                if (type === 'success') {
                    setTimeout(() => {
                        messageElement.classList.remove('show');
                    }, 5000);
                }
            }
        }
    }
    
    // Initialize form validation when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('.generated_form form, .s_website_form');
        forms.forEach(form => {
            new GeneratedFormValidator(form);
        });
    });
    
    // Export for use in other scripts
    window.GeneratedFormValidator = GeneratedFormValidator;
    
})();
