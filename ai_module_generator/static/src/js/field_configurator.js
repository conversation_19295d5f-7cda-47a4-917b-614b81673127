/** @odoo-module **/

import { Component, useState, useRef, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * Advanced Field Configurator Component
 */
export class AdvancedFieldConfigurator extends Component {
    setup() {
        this.notification = useService("notification");
        this.orm = useService("orm");
        
        this.state = useState({
            // Basic field properties
            fieldName: '',
            fieldDescription: '',
            fieldType: 'char',
            required: false,
            readonly: false,
            helpText: '',
            defaultValue: '',
            
            // Advanced properties
            sequence: 10,
            groups: [],
            domain: '',
            context: '',
            
            // Selection field specific
            selectionOptions: [],
            
            // Relational field specific
            comodelName: '',
            relationField: '',
            
            // Display properties
            widget: '',
            options: '',
            
            // Form visibility
            websiteFormVisible: true,
            websiteFormRequired: false,
            websiteFormPlaceholder: '',
            portalVisible: true,
            apiVisible: false,
            
            // Validation
            minLength: null,
            maxLength: null,
            minValue: null,
            maxValue: null,
            pattern: '',
            
            // Current step in wizard
            currentStep: 1,
            totalSteps: 4,
            
            // Available options
            availableWidgets: [],
            availableModels: [],
            availableGroups: [],
        });
        
        onMounted(() => {
            this.loadAvailableOptions();
        });
    }
    
    async loadAvailableOptions() {
        try {
            // Load available models for relational fields
            this.state.availableModels = await this.orm.searchRead(
                "ir.model",
                [["transient", "=", false]],
                ["model", "name"],
                { order: "name" }
            );
            
            // Load available groups
            this.state.availableGroups = await this.orm.searchRead(
                "res.groups",
                [],
                ["name", "full_name"],
                { order: "name" }
            );
            
            // Set available widgets based on field type
            this.updateAvailableWidgets();
            
        } catch (error) {
            console.error("Failed to load available options:", error);
        }
    }
    
    updateAvailableWidgets() {
        const widgetsByType = {
            'char': ['text', 'email', 'phone', 'url', 'color'],
            'text': ['text', 'html', 'ace'],
            'integer': ['integer', 'float_time', 'progressbar', 'handle'],
            'float': ['float', 'monetary', 'percentage', 'progressbar'],
            'boolean': ['boolean', 'boolean_button', 'boolean_toggle'],
            'selection': ['selection', 'radio', 'priority'],
            'date': ['date', 'daterange'],
            'datetime': ['datetime', 'daterange'],
            'binary': ['binary', 'image', 'pdf_viewer'],
            'many2one': ['many2one', 'selection', 'radio'],
            'one2many': ['one2many', 'one2many_list'],
            'many2many': ['many2many', 'many2many_tags', 'many2many_checkboxes'],
        };
        
        this.state.availableWidgets = widgetsByType[this.state.fieldType] || [];
    }
    
    nextStep() {
        if (this.state.currentStep < this.state.totalSteps) {
            this.state.currentStep++;
        }
    }
    
    previousStep() {
        if (this.state.currentStep > 1) {
            this.state.currentStep--;
        }
    }
    
    goToStep(step) {
        if (step >= 1 && step <= this.state.totalSteps) {
            this.state.currentStep = step;
        }
    }
    
    updateFieldType(fieldType) {
        this.state.fieldType = fieldType;
        this.updateAvailableWidgets();
        
        // Reset type-specific properties
        if (fieldType !== 'selection') {
            this.state.selectionOptions = [];
        }
        
        if (!['many2one', 'one2many', 'many2many'].includes(fieldType)) {
            this.state.comodelName = '';
            this.state.relationField = '';
        }
        
        // Auto-generate field name if not set
        if (this.state.fieldDescription && !this.state.fieldName) {
            this.state.fieldName = this.generateFieldName(this.state.fieldDescription);
        }
    }
    
    generateFieldName(description) {
        return description
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 50);
    }
    
    addSelectionOption() {
        const optionNumber = this.state.selectionOptions.length + 1;
        this.state.selectionOptions.push({
            key: `option${optionNumber}`,
            value: `Option ${optionNumber}`,
            description: ''
        });
    }
    
    removeSelectionOption(index) {
        this.state.selectionOptions.splice(index, 1);
    }
    
    updateSelectionOption(index, field, value) {
        this.state.selectionOptions[index][field] = value;
    }
    
    validateCurrentStep() {
        const errors = [];
        
        switch (this.state.currentStep) {
            case 1: // Basic Properties
                if (!this.state.fieldName.trim()) {
                    errors.push('Field name is required');
                }
                if (!this.state.fieldDescription.trim()) {
                    errors.push('Field description is required');
                }
                if (!/^[a-z][a-z0-9_]*$/.test(this.state.fieldName)) {
                    errors.push('Field name must start with a letter and contain only lowercase letters, numbers, and underscores');
                }
                break;
                
            case 2: // Type-specific Properties
                if (this.state.fieldType === 'selection' && this.state.selectionOptions.length < 2) {
                    errors.push('Selection fields must have at least 2 options');
                }
                if (['many2one', 'one2many', 'many2many'].includes(this.state.fieldType) && !this.state.comodelName) {
                    errors.push('Relational fields must specify a related model');
                }
                break;
                
            case 3: // Display Properties
                if (this.state.options) {
                    try {
                        JSON.parse(this.state.options);
                    } catch (e) {
                        errors.push('Widget options must be valid JSON');
                    }
                }
                break;
                
            case 4: // Validation & Visibility
                if (this.state.minLength !== null && this.state.maxLength !== null) {
                    if (this.state.minLength > this.state.maxLength) {
                        errors.push('Minimum length cannot be greater than maximum length');
                    }
                }
                if (this.state.minValue !== null && this.state.maxValue !== null) {
                    if (this.state.minValue > this.state.maxValue) {
                        errors.push('Minimum value cannot be greater than maximum value');
                    }
                }
                break;
        }
        
        return errors;
    }
    
    async saveField() {
        // Validate all steps
        for (let step = 1; step <= this.state.totalSteps; step++) {
            this.state.currentStep = step;
            const errors = this.validateCurrentStep();
            if (errors.length > 0) {
                this.notification.add(`Step ${step}: ${errors.join(', ')}`, { type: "danger" });
                return;
            }
        }
        
        const fieldData = {
            name: this.state.fieldName,
            field_description: this.state.fieldDescription,
            field_type: this.state.fieldType,
            required: this.state.required,
            readonly: this.state.readonly,
            help_text: this.state.helpText,
            default_value: this.state.defaultValue,
            sequence: this.state.sequence,
            
            // Advanced properties
            domain: this.state.domain,
            context: this.state.context,
            widget: this.state.widget,
            options: this.state.options,
            
            // Validation
            min_length: this.state.minLength,
            max_length: this.state.maxLength,
            min_value: this.state.minValue,
            max_value: this.state.maxValue,
            pattern: this.state.pattern,
            
            // Visibility
            website_form_visible: this.state.websiteFormVisible,
            website_form_required: this.state.websiteFormRequired,
            website_form_placeholder: this.state.websiteFormPlaceholder,
            portal_visible: this.state.portalVisible,
            api_visible: this.state.apiVisible,
        };
        
        // Type-specific properties
        if (this.state.fieldType === 'selection') {
            fieldData.selection_options = JSON.stringify(this.state.selectionOptions);
        }
        
        if (['many2one', 'one2many', 'many2many'].includes(this.state.fieldType)) {
            fieldData.comodel_name = this.state.comodelName;
            fieldData.relation_field = this.state.relationField;
        }
        
        try {
            await this.props.onSaveField(fieldData);
            this.notification.add("Field saved successfully!", { type: "success" });
        } catch (error) {
            this.notification.add(`Failed to save field: ${error.message}`, { type: "danger" });
        }
    }
    
    resetForm() {
        Object.assign(this.state, {
            fieldName: '',
            fieldDescription: '',
            fieldType: 'char',
            required: false,
            readonly: false,
            helpText: '',
            defaultValue: '',
            sequence: 10,
            groups: [],
            domain: '',
            context: '',
            selectionOptions: [],
            comodelName: '',
            relationField: '',
            widget: '',
            options: '',
            websiteFormVisible: true,
            websiteFormRequired: false,
            websiteFormPlaceholder: '',
            portalVisible: true,
            apiVisible: false,
            minLength: null,
            maxLength: null,
            minValue: null,
            maxValue: null,
            pattern: '',
            currentStep: 1,
        });
    }
    
    getStepTitle(step) {
        const titles = {
            1: 'Basic Properties',
            2: 'Type Configuration',
            3: 'Display Options',
            4: 'Validation & Visibility'
        };
        return titles[step] || `Step ${step}`;
    }
    
    getStepDescription(step) {
        const descriptions = {
            1: 'Define the basic properties of your field',
            2: 'Configure type-specific options and relationships',
            3: 'Set display widgets and formatting options',
            4: 'Configure validation rules and visibility settings'
        };
        return descriptions[step] || '';
    }
    
    isStepValid(step) {
        const currentStep = this.state.currentStep;
        this.state.currentStep = step;
        const errors = this.validateCurrentStep();
        this.state.currentStep = currentStep;
        return errors.length === 0;
    }
    
    getProgressPercentage() {
        return (this.state.currentStep / this.state.totalSteps) * 100;
    }
}

AdvancedFieldConfigurator.template = "ai_module_generator.AdvancedFieldConfiguratorTemplate";

/**
 * Field Type Selector Component
 */
export class FieldTypeSelector extends Component {
    setup() {
        this.fieldTypes = [
            {
                category: 'Text Fields',
                types: [
                    { value: 'char', label: 'Short Text', icon: 'fa-font', description: 'Single line text input' },
                    { value: 'text', label: 'Long Text', icon: 'fa-align-left', description: 'Multi-line text area' },
                    { value: 'html', label: 'Rich Text', icon: 'fa-code', description: 'HTML editor' },
                ]
            },
            {
                category: 'Number Fields',
                types: [
                    { value: 'integer', label: 'Integer', icon: 'fa-hashtag', description: 'Whole numbers' },
                    { value: 'float', label: 'Decimal', icon: 'fa-calculator', description: 'Decimal numbers' },
                    { value: 'monetary', label: 'Currency', icon: 'fa-dollar-sign', description: 'Currency amounts' },
                ]
            },
            {
                category: 'Choice Fields',
                types: [
                    { value: 'boolean', label: 'Checkbox', icon: 'fa-check-square', description: 'True/False checkbox' },
                    { value: 'selection', label: 'Dropdown', icon: 'fa-list', description: 'Predefined options' },
                ]
            },
            {
                category: 'Date & Time',
                types: [
                    { value: 'date', label: 'Date', icon: 'fa-calendar', description: 'Date picker' },
                    { value: 'datetime', label: 'Date & Time', icon: 'fa-clock', description: 'Date and time picker' },
                ]
            },
            {
                category: 'Contact Fields',
                types: [
                    { value: 'email', label: 'Email', icon: 'fa-envelope', description: 'Email address' },
                    { value: 'phone', label: 'Phone', icon: 'fa-phone', description: 'Phone number' },
                    { value: 'url', label: 'Website', icon: 'fa-link', description: 'Website URL' },
                ]
            },
            {
                category: 'File & Media',
                types: [
                    { value: 'binary', label: 'File Upload', icon: 'fa-file', description: 'File attachment' },
                    { value: 'image', label: 'Image', icon: 'fa-image', description: 'Image upload' },
                ]
            },
            {
                category: 'Relationships',
                types: [
                    { value: 'many2one', label: 'Many to One', icon: 'fa-arrow-right', description: 'Link to another record' },
                    { value: 'one2many', label: 'One to Many', icon: 'fa-arrow-left', description: 'List of related records' },
                    { value: 'many2many', label: 'Many to Many', icon: 'fa-arrows-alt-h', description: 'Multiple related records' },
                ]
            }
        ];
    }
    
    selectFieldType(fieldType) {
        this.props.onSelectFieldType(fieldType);
    }
    
    isSelected(fieldType) {
        return this.props.selectedType === fieldType;
    }
}

FieldTypeSelector.template = "ai_module_generator.FieldTypeSelectorTemplate";
