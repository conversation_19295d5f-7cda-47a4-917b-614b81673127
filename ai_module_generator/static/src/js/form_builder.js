/** @odoo-module **/

import { Component, useState, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * Field Configurator Component
 */
export class FieldConfigurator extends Component {
    setup() {
        this.notification = useService("notification");
        this.modalRef = useRef("modal");
        
        this.state = useState({
            fieldType: 'char',
            fieldName: '',
            fieldDescription: '',
            required: false,
            helpText: '',
            defaultValue: '',
            selectionOptions: [],
            websiteFormVisible: true,
            portalVisible: true,
            apiVisible: false,
            isEditing: false,
            editingFieldId: null,
        });
        
        this.fieldTypes = [
            { value: 'char', label: 'Text (Short)', icon: 'fa-font' },
            { value: 'text', label: 'Text (Long)', icon: 'fa-align-left' },
            { value: 'integer', label: 'Number (Integer)', icon: 'fa-hashtag' },
            { value: 'float', label: 'Number (Decimal)', icon: 'fa-calculator' },
            { value: 'boolean', label: 'Checkbox', icon: 'fa-check-square' },
            { value: 'selection', label: 'Dropdown', icon: 'fa-list' },
            { value: 'date', label: 'Date', icon: 'fa-calendar' },
            { value: 'datetime', label: 'Date & Time', icon: 'fa-clock' },
            { value: 'email', label: 'Email', icon: 'fa-envelope' },
            { value: 'phone', label: 'Phone', icon: 'fa-phone' },
            { value: 'url', label: 'Website URL', icon: 'fa-link' },
            { value: 'binary', label: 'File Upload', icon: 'fa-file' },
            { value: 'monetary', label: 'Currency', icon: 'fa-dollar-sign' },
        ];
    }
    
    openModal(fieldData = null) {
        if (fieldData) {
            // Editing existing field
            this.state.isEditing = true;
            this.state.editingFieldId = fieldData.id;
            this.state.fieldType = fieldData.field_type;
            this.state.fieldName = fieldData.name;
            this.state.fieldDescription = fieldData.field_description;
            this.state.required = fieldData.required;
            this.state.helpText = fieldData.help_text || '';
            this.state.defaultValue = fieldData.default_value || '';
            this.state.websiteFormVisible = fieldData.website_form_visible;
            this.state.portalVisible = fieldData.portal_visible;
            this.state.apiVisible = fieldData.api_visible;
            
            if (fieldData.field_type === 'selection' && fieldData.selection_options) {
                this.state.selectionOptions = JSON.parse(fieldData.selection_options);
            }
        } else {
            // Creating new field
            this.resetForm();
        }
        
        if (this.modalRef.el) {
            $(this.modalRef.el).modal('show');
        }
    }
    
    closeModal() {
        if (this.modalRef.el) {
            $(this.modalRef.el).modal('hide');
        }
        this.resetForm();
    }
    
    resetForm() {
        this.state.isEditing = false;
        this.state.editingFieldId = null;
        this.state.fieldType = 'char';
        this.state.fieldName = '';
        this.state.fieldDescription = '';
        this.state.required = false;
        this.state.helpText = '';
        this.state.defaultValue = '';
        this.state.selectionOptions = [];
        this.state.websiteFormVisible = true;
        this.state.portalVisible = true;
        this.state.apiVisible = false;
    }
    
    selectFieldType(fieldType) {
        this.state.fieldType = fieldType;
        
        // Auto-generate field name from description
        if (this.state.fieldDescription && !this.state.fieldName) {
            this.state.fieldName = this.generateFieldName(this.state.fieldDescription);
        }
        
        // Initialize selection options for selection fields
        if (fieldType === 'selection' && this.state.selectionOptions.length === 0) {
            this.state.selectionOptions = [
                { key: 'option1', value: 'Option 1' },
                { key: 'option2', value: 'Option 2' }
            ];
        }
    }
    
    generateFieldName(description) {
        return description
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 50);
    }
    
    addSelectionOption() {
        const optionNumber = this.state.selectionOptions.length + 1;
        this.state.selectionOptions.push({
            key: `option${optionNumber}`,
            value: `Option ${optionNumber}`
        });
    }
    
    removeSelectionOption(index) {
        this.state.selectionOptions.splice(index, 1);
    }
    
    updateSelectionOption(index, field, value) {
        this.state.selectionOptions[index][field] = value;
    }
    
    validateForm() {
        const errors = [];
        
        if (!this.state.fieldName.trim()) {
            errors.push('Field name is required');
        }
        
        if (!this.state.fieldDescription.trim()) {
            errors.push('Field description is required');
        }
        
        if (!/^[a-z][a-z0-9_]*$/.test(this.state.fieldName)) {
            errors.push('Field name must start with a letter and contain only lowercase letters, numbers, and underscores');
        }
        
        if (this.state.fieldType === 'selection' && this.state.selectionOptions.length < 2) {
            errors.push('Selection fields must have at least 2 options');
        }
        
        if (this.state.fieldType === 'selection') {
            const keys = this.state.selectionOptions.map(opt => opt.key);
            const uniqueKeys = [...new Set(keys)];
            if (keys.length !== uniqueKeys.length) {
                errors.push('Selection option keys must be unique');
            }
        }
        
        return errors;
    }
    
    async saveField() {
        const errors = this.validateForm();
        if (errors.length > 0) {
            this.notification.add(errors.join(', '), { type: "danger" });
            return;
        }
        
        const fieldData = {
            name: this.state.fieldName,
            field_description: this.state.fieldDescription,
            field_type: this.state.fieldType,
            required: this.state.required,
            help_text: this.state.helpText,
            default_value: this.state.defaultValue,
            website_form_visible: this.state.websiteFormVisible,
            portal_visible: this.state.portalVisible,
            api_visible: this.state.apiVisible,
        };
        
        if (this.state.fieldType === 'selection') {
            fieldData.selection_options = JSON.stringify(this.state.selectionOptions);
        }
        
        try {
            if (this.state.isEditing) {
                await this.props.onUpdateField(this.state.editingFieldId, fieldData);
            } else {
                await this.props.onAddField(fieldData);
            }
            
            this.closeModal();
        } catch (error) {
            this.notification.add(`Failed to save field: ${error.message}`, { type: "danger" });
        }
    }
    
    getFieldTypeIcon(fieldType) {
        const fieldTypeData = this.fieldTypes.find(ft => ft.value === fieldType);
        return fieldTypeData ? fieldTypeData.icon : 'fa-question';
    }
    
    getFieldTypeLabel(fieldType) {
        const fieldTypeData = this.fieldTypes.find(ft => ft.value === fieldType);
        return fieldTypeData ? fieldTypeData.label : fieldType;
    }
}

FieldConfigurator.template = "ai_module_generator.FieldConfiguratorTemplate";

/**
 * Drag and Drop Field Sorter
 */
export class FieldSorter extends Component {
    setup() {
        this.notification = useService("notification");
        this.containerRef = useRef("container");
    }
    
    mounted() {
        this.initializeSortable();
    }
    
    initializeSortable() {
        if (this.containerRef.el && window.Sortable) {
            this.sortable = Sortable.create(this.containerRef.el, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: (evt) => {
                    this.handleSort(evt);
                }
            });
        }
    }
    
    async handleSort(evt) {
        const { oldIndex, newIndex } = evt;
        
        if (oldIndex === newIndex) {
            return;
        }
        
        try {
            await this.props.onReorderFields(oldIndex, newIndex);
            this.notification.add("Fields reordered successfully!", { type: "success" });
        } catch (error) {
            this.notification.add(`Failed to reorder fields: ${error.message}`, { type: "danger" });
            // Revert the visual change
            if (oldIndex < newIndex) {
                evt.to.insertBefore(evt.item, evt.to.children[oldIndex]);
            } else {
                evt.to.insertBefore(evt.item, evt.to.children[oldIndex + 1]);
            }
        }
    }
    
    willUnmount() {
        if (this.sortable) {
            this.sortable.destroy();
        }
    }
}

FieldSorter.template = "ai_module_generator.FieldSorterTemplate";

/**
 * Form Preview Component
 */
export class FormPreview extends Component {
    setup() {
        this.state = useState({
            previewData: {},
            isSubmitting: false,
        });
    }
    
    updatePreviewData(fieldName, value) {
        this.state.previewData[fieldName] = value;
    }
    
    async submitPreview() {
        this.state.isSubmitting = true;
        
        try {
            // Simulate form submission
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            this.notification.add("Preview form submitted successfully!", { type: "success" });
            this.state.previewData = {};
        } catch (error) {
            this.notification.add(`Preview submission failed: ${error.message}`, { type: "danger" });
        } finally {
            this.state.isSubmitting = false;
        }
    }
    
    resetPreview() {
        this.state.previewData = {};
    }
    
    getFieldValue(fieldName) {
        return this.state.previewData[fieldName] || '';
    }
    
    renderField(field) {
        const commonProps = {
            id: field.name,
            name: field.name,
            required: field.required,
            placeholder: field.help_text || '',
            value: this.getFieldValue(field.name),
            onChange: (event) => this.updatePreviewData(field.name, event.target.value)
        };
        
        switch (field.field_type) {
            case 'char':
            case 'email':
            case 'phone':
            case 'url':
                return `<input type="${field.field_type === 'char' ? 'text' : field.field_type}" class="form-control" ${this.propsToString(commonProps)} />`;
            
            case 'text':
                return `<textarea class="form-control" rows="3" ${this.propsToString(commonProps)}></textarea>`;
            
            case 'integer':
            case 'float':
            case 'monetary':
                return `<input type="number" class="form-control" ${this.propsToString({...commonProps, step: field.field_type === 'float' ? '0.01' : '1'})} />`;
            
            case 'boolean':
                return `<div class="form-check"><input type="checkbox" class="form-check-input" ${this.propsToString(commonProps)} /><label class="form-check-label" for="${field.name}">${field.help_text || field.field_description}</label></div>`;
            
            case 'selection':
                const options = field.selection_options ? JSON.parse(field.selection_options) : [];
                const optionsHtml = options.map(opt => `<option value="${opt.key}">${opt.value}</option>`).join('');
                return `<select class="form-control" ${this.propsToString(commonProps)}><option value="">-- Select ${field.field_description} --</option>${optionsHtml}</select>`;
            
            case 'date':
                return `<input type="date" class="form-control" ${this.propsToString(commonProps)} />`;
            
            case 'datetime':
                return `<input type="datetime-local" class="form-control" ${this.propsToString(commonProps)} />`;
            
            case 'binary':
                return `<input type="file" class="form-control-file" ${this.propsToString(commonProps)} />`;
            
            default:
                return `<input type="text" class="form-control" ${this.propsToString(commonProps)} />`;
        }
    }
    
    propsToString(props) {
        return Object.entries(props)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ');
    }
}

FormPreview.template = "ai_module_generator.FormPreviewTemplate";
