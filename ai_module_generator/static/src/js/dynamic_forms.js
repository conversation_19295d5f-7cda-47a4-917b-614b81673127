/**
 * Dynamic Forms JavaScript for Generated Forms
 */

(function() {
    'use strict';
    
    class DynamicFormHandler {
        constructor() {
            this.conditionalFields = new Map();
            this.calculatedFields = new Map();
            this.dependentFields = new Map();
            
            this.init();
        }
        
        init() {
            this.setupConditionalFields();
            this.setupCalculatedFields();
            this.setupDependentFields();
            this.setupFormEnhancements();
        }
        
        setupConditionalFields() {
            // Find fields with conditional visibility
            const conditionalElements = document.querySelectorAll('[data-conditional]');
            
            conditionalElements.forEach(element => {
                const condition = JSON.parse(element.dataset.conditional);
                this.conditionalFields.set(element, condition);
                
                // Listen for changes on trigger fields
                condition.triggers.forEach(trigger => {
                    const triggerElement = document.querySelector(`[name="${trigger.field}"]`);
                    if (triggerElement) {
                        triggerElement.addEventListener('change', () => {
                            this.evaluateConditionalField(element, condition);
                        });
                    }
                });
                
                // Initial evaluation
                this.evaluateConditionalField(element, condition);
            });
        }
        
        evaluateConditionalField(element, condition) {
            let showField = true;
            
            condition.triggers.forEach(trigger => {
                const triggerElement = document.querySelector(`[name="${trigger.field}"]`);
                if (!triggerElement) return;
                
                const triggerValue = this.getFieldValue(triggerElement);
                const conditionMet = this.evaluateCondition(triggerValue, trigger.operator, trigger.value);
                
                if (condition.logic === 'AND') {
                    showField = showField && conditionMet;
                } else if (condition.logic === 'OR') {
                    showField = showField || conditionMet;
                }
            });
            
            this.toggleFieldVisibility(element, showField);
        }
        
        evaluateCondition(fieldValue, operator, expectedValue) {
            switch (operator) {
                case 'equals':
                    return fieldValue == expectedValue;
                case 'not_equals':
                    return fieldValue != expectedValue;
                case 'contains':
                    return String(fieldValue).includes(expectedValue);
                case 'greater_than':
                    return parseFloat(fieldValue) > parseFloat(expectedValue);
                case 'less_than':
                    return parseFloat(fieldValue) < parseFloat(expectedValue);
                case 'is_empty':
                    return !fieldValue || fieldValue.trim() === '';
                case 'is_not_empty':
                    return fieldValue && fieldValue.trim() !== '';
                default:
                    return true;
            }
        }
        
        toggleFieldVisibility(element, show) {
            const fieldGroup = element.closest('.form_field_group') || element.parentElement;
            
            if (show) {
                fieldGroup.style.display = '';
                fieldGroup.classList.remove('hidden');
                element.removeAttribute('disabled');
            } else {
                fieldGroup.style.display = 'none';
                fieldGroup.classList.add('hidden');
                element.setAttribute('disabled', 'disabled');
                
                // Clear field value when hidden
                if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = false;
                } else {
                    element.value = '';
                }
            }
        }
        
        setupCalculatedFields() {
            // Find fields with calculations
            const calculatedElements = document.querySelectorAll('[data-calculation]');
            
            calculatedElements.forEach(element => {
                const calculation = JSON.parse(element.dataset.calculation);
                this.calculatedFields.set(element, calculation);
                
                // Listen for changes on source fields
                calculation.sources.forEach(source => {
                    const sourceElement = document.querySelector(`[name="${source}"]`);
                    if (sourceElement) {
                        sourceElement.addEventListener('input', () => {
                            this.calculateFieldValue(element, calculation);
                        });
                    }
                });
                
                // Initial calculation
                this.calculateFieldValue(element, calculation);
            });
        }
        
        calculateFieldValue(element, calculation) {
            try {
                let expression = calculation.formula;
                
                // Replace field references with actual values
                calculation.sources.forEach(source => {
                    const sourceElement = document.querySelector(`[name="${source}"]`);
                    if (sourceElement) {
                        const value = parseFloat(this.getFieldValue(sourceElement)) || 0;
                        expression = expression.replace(new RegExp(`\\b${source}\\b`, 'g'), value);
                    }
                });
                
                // Evaluate the expression safely
                const result = this.safeEval(expression);
                
                if (!isNaN(result)) {
                    element.value = calculation.format ? this.formatNumber(result, calculation.format) : result;
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                }
            } catch (error) {
                console.warn('Calculation error:', error);
            }
        }
        
        safeEval(expression) {
            // Only allow basic mathematical operations
            const allowedChars = /^[0-9+\-*/.() ]+$/;
            if (!allowedChars.test(expression)) {
                throw new Error('Invalid expression');
            }
            
            return Function('"use strict"; return (' + expression + ')')();
        }
        
        formatNumber(number, format) {
            switch (format) {
                case 'currency':
                    return new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: 'USD'
                    }).format(number);
                case 'percentage':
                    return (number * 100).toFixed(2) + '%';
                case 'decimal2':
                    return number.toFixed(2);
                case 'integer':
                    return Math.round(number);
                default:
                    return number;
            }
        }
        
        setupDependentFields() {
            // Find fields with dependencies (like cascading dropdowns)
            const dependentElements = document.querySelectorAll('[data-depends-on]');
            
            dependentElements.forEach(element => {
                const dependency = element.dataset.dependsOn;
                const parentElement = document.querySelector(`[name="${dependency}"]`);
                
                if (parentElement) {
                    this.dependentFields.set(element, dependency);
                    
                    parentElement.addEventListener('change', () => {
                        this.updateDependentField(element, parentElement);
                    });
                    
                    // Initial update
                    this.updateDependentField(element, parentElement);
                }
            });
        }
        
        async updateDependentField(dependentElement, parentElement) {
            const parentValue = this.getFieldValue(parentElement);
            
            if (!parentValue) {
                this.clearSelectOptions(dependentElement);
                return;
            }
            
            try {
                // Make AJAX request to get dependent options
                const response = await fetch('/web/dataset/call_kw', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify({
                        jsonrpc: '2.0',
                        method: 'call',
                        params: {
                            model: dependentElement.dataset.model,
                            method: 'get_dependent_options',
                            args: [parentValue],
                            kwargs: {}
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.result) {
                    this.updateSelectOptions(dependentElement, result.result);
                }
            } catch (error) {
                console.warn('Failed to load dependent options:', error);
            }
        }
        
        clearSelectOptions(selectElement) {
            selectElement.innerHTML = '<option value="">-- Select --</option>';
            selectElement.disabled = true;
        }
        
        updateSelectOptions(selectElement, options) {
            selectElement.innerHTML = '<option value="">-- Select --</option>';
            
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                selectElement.appendChild(optionElement);
            });
            
            selectElement.disabled = false;
        }
        
        setupFormEnhancements() {
            this.setupFieldMasks();
            this.setupAutoComplete();
            this.setupFieldValidation();
            this.setupProgressIndicator();
        }
        
        setupFieldMasks() {
            // Phone number masking
            const phoneFields = document.querySelectorAll('input[type="tel"]');
            phoneFields.forEach(field => {
                field.addEventListener('input', (e) => {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length >= 6) {
                        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                    } else if (value.length >= 3) {
                        value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
                    }
                    e.target.value = value;
                });
            });
            
            // Credit card masking
            const cardFields = document.querySelectorAll('input[data-mask="card"]');
            cardFields.forEach(field => {
                field.addEventListener('input', (e) => {
                    let value = e.target.value.replace(/\D/g, '');
                    value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
                    e.target.value = value;
                });
            });
        }
        
        setupAutoComplete() {
            const autoCompleteFields = document.querySelectorAll('[data-autocomplete]');
            
            autoCompleteFields.forEach(field => {
                let timeout;
                
                field.addEventListener('input', (e) => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        this.fetchAutoCompleteOptions(field, e.target.value);
                    }, 300);
                });
            });
        }
        
        async fetchAutoCompleteOptions(field, query) {
            if (query.length < 2) return;
            
            try {
                const response = await fetch(`/api/autocomplete/${field.dataset.autocomplete}?q=${encodeURIComponent(query)}`);
                const options = await response.json();
                
                this.showAutoCompleteDropdown(field, options);
            } catch (error) {
                console.warn('Autocomplete failed:', error);
            }
        }
        
        showAutoCompleteDropdown(field, options) {
            // Remove existing dropdown
            const existingDropdown = field.parentElement.querySelector('.autocomplete-dropdown');
            if (existingDropdown) {
                existingDropdown.remove();
            }
            
            if (options.length === 0) return;
            
            // Create dropdown
            const dropdown = document.createElement('div');
            dropdown.className = 'autocomplete-dropdown';
            dropdown.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #ccc;
                border-top: none;
                max-height: 200px;
                overflow-y: auto;
                z-index: 1000;
            `;
            
            options.forEach(option => {
                const item = document.createElement('div');
                item.className = 'autocomplete-item';
                item.textContent = option.label;
                item.style.cssText = 'padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #eee;';
                
                item.addEventListener('click', () => {
                    field.value = option.value;
                    dropdown.remove();
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                });
                
                item.addEventListener('mouseenter', () => {
                    item.style.backgroundColor = '#f5f5f5';
                });
                
                item.addEventListener('mouseleave', () => {
                    item.style.backgroundColor = '';
                });
                
                dropdown.appendChild(item);
            });
            
            field.parentElement.style.position = 'relative';
            field.parentElement.appendChild(dropdown);
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!field.parentElement.contains(e.target)) {
                    dropdown.remove();
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }
        
        setupFieldValidation() {
            // Real-time validation feedback
            const validatedFields = document.querySelectorAll('[data-validate]');
            
            validatedFields.forEach(field => {
                field.addEventListener('blur', () => {
                    this.validateFieldRealTime(field);
                });
            });
        }
        
        validateFieldRealTime(field) {
            const validation = field.dataset.validate;
            const value = this.getFieldValue(field);
            let isValid = true;
            let message = '';
            
            switch (validation) {
                case 'email':
                    isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                    message = 'Please enter a valid email address';
                    break;
                case 'phone':
                    isValid = /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''));
                    message = 'Please enter a valid phone number';
                    break;
                case 'url':
                    try {
                        new URL(value);
                        isValid = true;
                    } catch {
                        isValid = false;
                        message = 'Please enter a valid URL';
                    }
                    break;
            }
            
            this.showValidationFeedback(field, isValid, message);
        }
        
        showValidationFeedback(field, isValid, message) {
            field.classList.toggle('is-valid', isValid && field.value);
            field.classList.toggle('is-invalid', !isValid && field.value);
            
            let feedback = field.parentElement.querySelector('.validation-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'validation-feedback';
                field.parentElement.appendChild(feedback);
            }
            
            feedback.textContent = isValid ? '' : message;
            feedback.style.color = isValid ? 'green' : 'red';
        }
        
        setupProgressIndicator() {
            const form = document.querySelector('.generated_form form');
            if (!form) return;
            
            const requiredFields = form.querySelectorAll('[required]');
            if (requiredFields.length === 0) return;
            
            // Create progress indicator
            const progressContainer = document.createElement('div');
            progressContainer.className = 'form-progress';
            progressContainer.innerHTML = `
                <div class="progress mb-3">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <small class="text-muted">Form completion: <span class="progress-text">0%</span></small>
            `;
            
            form.insertBefore(progressContainer, form.firstChild);
            
            const progressBar = progressContainer.querySelector('.progress-bar');
            const progressText = progressContainer.querySelector('.progress-text');
            
            // Update progress on field changes
            requiredFields.forEach(field => {
                field.addEventListener('input', () => {
                    this.updateFormProgress(requiredFields, progressBar, progressText);
                });
            });
            
            // Initial progress calculation
            this.updateFormProgress(requiredFields, progressBar, progressText);
        }
        
        updateFormProgress(requiredFields, progressBar, progressText) {
            let completedFields = 0;
            
            requiredFields.forEach(field => {
                const value = this.getFieldValue(field);
                if (value && value !== '') {
                    completedFields++;
                }
            });
            
            const percentage = Math.round((completedFields / requiredFields.length) * 100);
            
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
            progressText.textContent = percentage + '%';
            
            // Change color based on progress
            progressBar.className = 'progress-bar';
            if (percentage < 30) {
                progressBar.classList.add('bg-danger');
            } else if (percentage < 70) {
                progressBar.classList.add('bg-warning');
            } else {
                progressBar.classList.add('bg-success');
            }
        }
        
        getFieldValue(element) {
            switch (element.type) {
                case 'checkbox':
                    return element.checked;
                case 'radio':
                    const radioGroup = document.querySelectorAll(`input[name="${element.name}"]`);
                    for (let radio of radioGroup) {
                        if (radio.checked) return radio.value;
                    }
                    return '';
                default:
                    return element.value;
            }
        }
    }
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        new DynamicFormHandler();
    });
    
})();
