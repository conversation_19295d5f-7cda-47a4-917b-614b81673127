<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- Module Generator Main Template -->
    <t t-name="ai_module_generator.ModuleGeneratorTemplate" owl="1">
        <div class="ai_module_generator_container">
            <div class="row">
                <div class="col-12">
                    <h2>AI Module Generator</h2>
                    <p class="text-muted">Generate complete Odoo modules with AI integration, payment processing, and advanced features.</p>
                </div>
            </div>
            
            <!-- Generation Progress -->
            <div t-if="state.isGenerating" class="module_generation_progress">
                <h4>Generating Module...</h4>
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         t-att-style="'width: ' + state.generationProgress.progress + '%'"></div>
                </div>
                <p><strong>Step <t t-esc="state.generationProgress.step"/> of <t t-esc="state.generationProgress.totalSteps"/>:</strong> <t t-esc="state.generationProgress.currentStep"/></p>
            </div>
            
            <!-- Templates Grid -->
            <div class="row">
                <div t-foreach="state.templates" t-as="template" t-key="template.id" class="col-md-6 col-lg-4 mb-4">
                    <div class="module_template_card">
                        <div class="module_template_header">
                            <h5 class="module_template_title">
                                <i t-att-class="'fa ' + getTemplateTypeIcon(template.template_type)"></i>
                                <t t-esc="template.name"/>
                            </h5>
                            <span t-att-class="'badge module_template_status ' + getTemplateStatusClass(template.state)">
                                <t t-esc="template.state"/>
                            </span>
                        </div>
                        
                        <p class="text-muted"><t t-esc="template.description"/></p>
                        
                        <div class="template_stats">
                            <div class="stat_item">
                                <div class="stat_number"><t t-esc="template.generation_count || 0"/></div>
                                <div class="stat_label">Generated</div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm mr-2" 
                                    t-on-click="() => this.generateModule(template.id)"
                                    t-att-disabled="state.isGenerating">
                                <i class="fa fa-cog"></i> Generate Module
                            </button>
                            <button class="btn btn-outline-secondary btn-sm mr-2" 
                                    t-on-click="() => this.openTemplate(template.id)">
                                <i class="fa fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-outline-info btn-sm" 
                                    t-on-click="() => this.duplicateTemplate(template.id)">
                                <i class="fa fa-copy"></i> Duplicate
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
    
    <!-- Form Builder Template -->
    <t t-name="ai_module_generator.FormBuilderTemplate" owl="1">
        <div class="form_builder_container">
            <div class="row">
                <div class="col-md-8">
                    <h3>Form Builder</h3>
                    
                    <!-- Forms List -->
                    <div class="forms_list">
                        <div t-foreach="state.forms" t-as="form" t-key="form.id" class="form_builder_section">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><t t-esc="form.name"/></h5>
                                <div>
                                    <span t-if="form.website_published" class="badge badge-success">Published</span>
                                    <span t-if="form.portal_access" class="badge badge-info">Portal</span>
                                </div>
                            </div>
                            
                            <p class="text-muted"><t t-esc="form.description"/></p>
                            
                            <!-- Fields List -->
                            <div class="fields_list">
                                <div t-foreach="state.fields" t-as="field" t-key="field.id" 
                                     t-if="field.form_builder_id[0] === form.id" class="field_definition_row">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><t t-esc="field.field_description"/></strong>
                                            <span class="text-muted">(<t t-esc="field.field_type"/>)</span>
                                            <span t-if="field.required" class="text-danger">*</span>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary" 
                                                    t-on-click="() => this.editField(field)">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    t-on-click="() => this.deleteField(field.id)">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="btn btn-success btn-sm mt-2" 
                                    t-on-click="() => this.addField(form.id)">
                                <i class="fa fa-plus"></i> Add Field
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <h4>Form Preview</h4>
                    <div class="form_preview_container">
                        <!-- Form preview will be rendered here -->
                    </div>
                </div>
            </div>
        </div>
    </t>
    
    <!-- Field Configurator Template -->
    <t t-name="ai_module_generator.FieldConfiguratorTemplate" owl="1">
        <div class="modal fade" t-ref="modal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <t t-if="state.isEditing">Edit Field</t>
                            <t t-else="">Add New Field</t>
                        </h5>
                        <button type="button" class="close" t-on-click="closeModal">
                            <span>×</span>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <!-- Field Type Selection -->
                        <div class="form-group">
                            <label>Field Type</label>
                            <div class="field_type_selector">
                                <div t-foreach="fieldTypes" t-as="fieldType" t-key="fieldType.value"
                                     t-att-class="'field_type_option ' + (state.fieldType === fieldType.value ? 'selected' : '')"
                                     t-on-click="() => this.selectFieldType(fieldType.value)">
                                    <i t-att-class="'field_type_icon fa ' + fieldType.icon"></i>
                                    <div><strong><t t-esc="fieldType.label"/></strong></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Basic Properties -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Field Description *</label>
                                    <input type="text" class="form-control" 
                                           t-model="state.fieldDescription" 
                                           placeholder="Enter field description"/>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Field Name *</label>
                                    <input type="text" class="form-control" 
                                           t-model="state.fieldName" 
                                           placeholder="field_name"/>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Field Options -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" 
                                           t-model="state.required"/>
                                    <label class="form-check-label">Required</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" 
                                           t-model="state.websiteFormVisible"/>
                                    <label class="form-check-label">Website Visible</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" 
                                           t-model="state.portalVisible"/>
                                    <label class="form-check-label">Portal Visible</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Help Text -->
                        <div class="form-group">
                            <label>Help Text</label>
                            <textarea class="form-control" rows="2" 
                                      t-model="state.helpText" 
                                      placeholder="Optional help text for users"></textarea>
                        </div>
                        
                        <!-- Selection Options (for selection fields) -->
                        <div t-if="state.fieldType === 'selection'" class="form-group">
                            <label>Selection Options</label>
                            <div t-foreach="state.selectionOptions" t-as="option" t-key="option_index" class="row mb-2">
                                <div class="col-md-5">
                                    <input type="text" class="form-control" 
                                           t-att-value="option.key"
                                           t-on-input="(e) => this.updateSelectionOption(option_index, 'key', e.target.value)"
                                           placeholder="Key"/>
                                </div>
                                <div class="col-md-5">
                                    <input type="text" class="form-control" 
                                           t-att-value="option.value"
                                           t-on-input="(e) => this.updateSelectionOption(option_index, 'value', e.target.value)"
                                           placeholder="Display Value"/>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            t-on-click="() => this.removeSelectionOption(option_index)">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                    t-on-click="addSelectionOption">
                                <i class="fa fa-plus"></i> Add Option
                            </button>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" t-on-click="closeModal">Cancel</button>
                        <button type="button" class="btn btn-primary" t-on-click="saveField">
                            <t t-if="state.isEditing">Update Field</t>
                            <t t-else="">Add Field</t>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </t>
    
    <!-- AI Integration Template -->
    <t t-name="ai_module_generator.AIIntegrationTemplate" owl="1">
        <div class="ai_integration_container">
            <h3>AI Integration</h3>
            
            <div class="row">
                <div t-foreach="state.prompts" t-as="prompt" t-key="prompt.id" class="col-md-6 mb-4">
                    <div class="ai_prompt_config_section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5><t t-esc="prompt.name"/></h5>
                            <span t-att-class="'ai_integration_status ' + (prompt.active ? 'active' : 'inactive')">
                                <t t-if="prompt.active">Active</t>
                                <t t-else="">Inactive</t>
                            </span>
                        </div>
                        
                        <p class="text-muted">
                            <strong>Type:</strong> <t t-esc="prompt.prompt_type"/><br/>
                            <strong>Model:</strong> <t t-esc="prompt.ai_model"/><br/>
                            <strong>Executions:</strong> <t t-esc="prompt.execution_count"/>
                        </p>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary btn-sm mr-2" 
                                    t-on-click="() => this.testPrompt(prompt.id)"
                                    t-att-disabled="state.isTestingPrompt">
                                <i class="fa fa-play"></i> Test Prompt
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" 
                                    t-on-click="() => this.editPrompt(prompt.id)">
                                <i class="fa fa-edit"></i> Edit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div t-if="state.isTestingPrompt" class="alert alert-info">
                <i class="fa fa-spinner fa-spin"></i> Testing AI prompt...
            </div>
        </div>
    </t>
    
    <!-- Field Sorter Template -->
    <t t-name="ai_module_generator.FieldSorterTemplate" owl="1">
        <div class="field_sorter_container" t-ref="container">
            <div t-foreach="props.fields" t-as="field" t-key="field.id" 
                 class="field_sort_item" t-att-data-field-id="field.id">
                <div class="d-flex align-items-center">
                    <i class="fa fa-grip-vertical text-muted mr-2"></i>
                    <div class="flex-grow-1">
                        <strong><t t-esc="field.field_description"/></strong>
                        <span class="text-muted">(<t t-esc="field.field_type"/>)</span>
                    </div>
                    <span class="badge badge-secondary"><t t-esc="field.sequence"/></span>
                </div>
            </div>
        </div>
    </t>
    
    <!-- Form Preview Template -->
    <t t-name="ai_module_generator.FormPreviewTemplate" owl="1">
        <div class="form_preview">
            <form class="generated_form" t-on-submit.prevent="submitPreview">
                <div t-foreach="props.fields" t-as="field" t-key="field.id" class="form_field_group">
                    <label t-att-for="field.name" 
                           t-att-class="'form_field_label ' + (field.required ? 'required' : '')">
                        <t t-esc="field.field_description"/>
                    </label>
                    
                    <div t-raw="renderField(field)"></div>
                    
                    <div t-if="field.help_text" class="form_field_help">
                        <t t-esc="field.help_text"/>
                    </div>
                </div>
                
                <div class="form_submit_section">
                    <button type="submit" class="form_submit_button" 
                            t-att-disabled="state.isSubmitting">
                        <t t-if="state.isSubmitting">Submitting...</t>
                        <t t-else="">Submit Preview</t>
                    </button>
                    <button type="button" class="btn btn-outline-secondary ml-2" 
                            t-on-click="resetPreview">
                        Reset
                    </button>
                </div>
            </form>
        </div>
    </t>
</templates>
