/* Generated Forms Frontend Styles */

.generated_form_container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.generated_form_header {
    text-align: center;
    margin-bottom: 30px;
}

.generated_form_title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.generated_form_description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
}

.generated_form {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.form_field_group {
    margin-bottom: 25px;
}

.form_field_label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form_field_label.required::after {
    content: " *";
    color: #dc3545;
}

.form_field_input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form_field_input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form_field_input.error {
    border-color: #dc3545;
}

.form_field_input.error:focus {
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form_field_help {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.form_field_error {
    font-size: 12px;
    color: #dc3545;
    margin-top: 4px;
    display: none;
}

.form_field_error.show {
    display: block;
}

.form_checkbox_group {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.form_checkbox {
    margin-right: 10px;
    transform: scale(1.2);
}

.form_checkbox_label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.form_radio_group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.form_radio_option {
    display: flex;
    align-items: center;
}

.form_radio {
    margin-right: 10px;
    transform: scale(1.2);
}

.form_radio_label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.form_select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
}

.form_select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form_textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

.form_textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form_file_input {
    width: 100%;
    padding: 12px 16px;
    border: 2px dashed #e1e5e9;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.form_file_input:hover {
    border-color: #007bff;
}

.form_file_input.dragover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.form_submit_section {
    margin-top: 30px;
    text-align: center;
}

.form_submit_button {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 14px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
}

.form_submit_button:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.form_submit_button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.form_loading {
    display: none;
    text-align: center;
    margin-top: 20px;
}

.form_loading.show {
    display: block;
}

.form_loading_spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

.form_success_message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 16px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: center;
    display: none;
}

.form_success_message.show {
    display: block;
}

.form_error_message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 16px;
    border-radius: 8px;
    margin-top: 20px;
    text-align: center;
    display: none;
}

.form_error_message.show {
    display: block;
}

/* Thank You Page Styles */
.thank_you_container {
    text-align: center;
    padding: 60px 20px;
}

.thank_you_icon {
    font-size: 64px;
    color: #28a745;
    margin-bottom: 20px;
}

.thank_you_title {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.thank_you_message {
    font-size: 18px;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.reference_number {
    background: #e7f3ff;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 20px;
    margin: 30px auto;
    max-width: 400px;
}

.reference_label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 8px;
}

.reference_value {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    font-family: monospace;
}

.thank_you_actions {
    margin-top: 40px;
}

.thank_you_button {
    display: inline-block;
    background: #007bff;
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    margin: 0 10px;
    transition: background-color 0.3s ease;
}

.thank_you_button:hover {
    background: #0056b3;
    text-decoration: none;
    color: white;
}

.thank_you_button.secondary {
    background: #6c757d;
}

.thank_you_button.secondary:hover {
    background: #545b62;
}

/* Responsive Design */
@media (max-width: 768px) {
    .generated_form_container {
        padding: 15px;
    }
    
    .generated_form {
        padding: 20px;
    }
    
    .generated_form_title {
        font-size: 24px;
    }
    
    .form_submit_button {
        width: 100%;
        min-width: auto;
    }
    
    .thank_you_actions {
        flex-direction: column;
    }
    
    .thank_you_button {
        display: block;
        margin: 5px 0;
    }
}

/* Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.form_field_group {
    animation: fadeIn 0.5s ease-out;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .generated_form {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .form_field_label {
        color: #e2e8f0;
    }
    
    .form_field_input,
    .form_select,
    .form_textarea {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form_field_input:focus,
    .form_select:focus,
    .form_textarea:focus {
        border-color: #63b3ed;
    }
}
