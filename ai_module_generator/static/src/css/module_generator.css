/* AI Module Generator Backend Styles */

.ai_module_generator_container {
    padding: 20px;
}

.form_builder_section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.field_definition_row {
    border-bottom: 1px solid #e9ecef;
    padding: 10px 0;
}

.field_definition_row:last-child {
    border-bottom: none;
}

.workflow_state_badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.workflow_state_badge.initial {
    background-color: #d4edda;
    color: #155724;
}

.workflow_state_badge.intermediate {
    background-color: #fff3cd;
    color: #856404;
}

.workflow_state_badge.final {
    background-color: #d1ecf1;
    color: #0c5460;
}

.ai_prompt_config_section {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.payment_config_section {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.module_generation_progress {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.generation_step {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
}

.generation_step:last-child {
    border-bottom: none;
}

.generation_step_icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 12px;
    font-weight: bold;
}

.generation_step_icon.pending {
    background-color: #6c757d;
    color: white;
}

.generation_step_icon.running {
    background-color: #007bff;
    color: white;
}

.generation_step_icon.completed {
    background-color: #28a745;
    color: white;
}

.generation_step_icon.error {
    background-color: #dc3545;
    color: white;
}

.field_configurator_modal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.field_type_selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.field_type_option {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.field_type_option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.field_type_option.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.field_type_icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: #6c757d;
}

.field_type_option.selected .field_type_icon {
    color: #007bff;
}

.ai_integration_status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.ai_integration_status.active {
    background-color: #d4edda;
    color: #155724;
}

.ai_integration_status.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.payment_status_indicator {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.payment_status_indicator.enabled {
    background-color: #d1ecf1;
    color: #0c5460;
}

.payment_status_indicator.disabled {
    background-color: #e2e3e5;
    color: #6c757d;
}

.module_template_card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.module_template_header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.module_template_title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.module_template_status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.template_stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat_item {
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat_number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
}

.stat_label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .field_type_selector {
        grid-template-columns: 1fr;
    }
    
    .template_stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .module_template_header {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* Animation for loading states */
.loading_spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert_success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 10px 0;
}

.alert_error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 10px 0;
}

.alert_warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 10px 0;
}

.alert_info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 10px 0;
}
