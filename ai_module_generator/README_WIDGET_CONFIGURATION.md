# Widget Configuration Guide for AI Module Generator

## Overview
The AI Module Generator allows users to customize how fields are displayed using different widgets. This is especially important for many2one, many2many, and one2many fields.

## How to Change Widget for Many2one Fields

### Method 1: During Field Creation
1. **Navigate to AI Module Generator**
   - Go to `Apps > AI Module Generator`
   - Create or edit a Module Template
   - Add or edit Field Definitions

2. **Configure Field Widget**
   - In the Field Definition form, go to the **"Widget & Display"** tab
   - Select your preferred widget from the **Widget** dropdown
   - Save the field definition

### Method 2: Quick Edit in Tree View
1. **Open Field Definitions**
   - Navigate to `AI Module Generator > Field Definitions`
   - Use the tree view for quick editing
   - The **Widget** column is visible and editable

2. **Edit Widget Directly**
   - Click on the Widget field in the tree view
   - Select from the dropdown options
   - Changes are saved automatically

## Available Many2one Widget Options

### 1. **many2one_dropdown** (Recommended Default)
- **Description**: Simple dropdown list
- **Best for**: Small to medium datasets (< 100 records)
- **Features**: 
  - Clean, simple interface
  - Fast loading
  - Works well with API integration
  - Mobile-friendly

### 2. **many2one_searchable**
- **Description**: Searchable dropdown with text input
- **Best for**: Large datasets (> 100 records)
- **Features**:
  - Search functionality
  - Autocomplete suggestions
  - Better for large datasets
  - More complex UI

### 3. **many2one_radio**
- **Description**: Radio button list
- **Best for**: Very small datasets (< 10 records)
- **Features**:
  - All options visible at once
  - Single selection
  - Good for forms with few options

### 4. **many2one_autocomplete**
- **Description**: Text input with autocomplete
- **Best for**: Large datasets with known values
- **Features**:
  - Type-ahead functionality
  - Minimal UI footprint
  - Good for power users

## Widget Selection Guidelines

### For Website Forms (Customer-facing)
- **Recommended**: `many2one_dropdown`
- **Reason**: Simple, intuitive, works well with JavaScript
- **Fallback**: Real API data with static fallback options

### For Backend Forms (Admin/Staff)
- **Recommended**: `many2one_searchable` or `many2one_dropdown`
- **Reason**: More functionality for power users
- **Consider**: Dataset size and user experience

### For Mobile-First Applications
- **Recommended**: `many2one_dropdown`
- **Reason**: Better touch interface, simpler interaction
- **Avoid**: Complex searchable widgets on small screens

## Technical Implementation

### Default Widget Assignment
The system automatically assigns widgets based on field type:

```python
# In field_definition.py
@api.onchange('field_type')
def _onchange_field_type(self):
    if self.field_type == 'many2one':
        self.widget = 'many2one_dropdown'  # Default for better UX
```

### Widget Options in Selection Field
```python
# Available widget options
widget = fields.Selection([
    ('many2one_dropdown', 'Many2one: Simple Dropdown'),
    ('many2one_searchable', 'Many2one: Searchable Dropdown'),
    ('many2one_radio', 'Many2one: Radio Button List'),
    ('many2one_autocomplete', 'Many2one: Autocomplete Input'),
    # ... other widgets
])
```

### Generated HTML Templates
Each widget generates different HTML:

#### many2one_dropdown
```html
<select class="form-control" name="field_name">
    <option value="">Select Option...</option>
    <!-- Options populated by JavaScript -->
</select>
```

#### many2one_searchable
```html
<div class="many2one-searchable">
    <input type="text" class="form-control" placeholder="Search..."/>
    <div class="many2one-dropdown-results"></div>
    <input type="hidden" name="field_name"/>
</div>
```

## Best Practices

### 1. **Consider Your Audience**
- **End Users**: Simple dropdown widgets
- **Power Users**: Searchable widgets with more features
- **Mobile Users**: Touch-friendly simple widgets

### 2. **Dataset Size Matters**
- **< 10 records**: Radio buttons or simple dropdown
- **10-100 records**: Simple dropdown
- **> 100 records**: Searchable dropdown or autocomplete

### 3. **Performance Considerations**
- Simple dropdowns load faster
- Searchable widgets require more JavaScript
- Consider API response times

### 4. **Consistency**
- Use the same widget type for similar fields
- Maintain consistency across your application
- Document your widget choices for the team

## Troubleshooting

### Widget Not Showing Correctly
1. **Check Field Type**: Ensure field type is `many2one`
2. **Verify Widget Selection**: Confirm widget is properly selected
3. **Regenerate Module**: Some changes require module regeneration
4. **Clear Cache**: Browser cache might need clearing

### JavaScript Not Working
1. **Check API Endpoints**: Ensure `/api/users/list` is accessible
2. **Verify JavaScript Loading**: Check browser console for errors
3. **Test Fallback**: Ensure fallback data is properly configured

### Styling Issues
1. **CSS Loading**: Verify enhanced_forms.css is loading
2. **Bootstrap Classes**: Ensure Bootstrap grid classes are working
3. **Mobile Responsive**: Test on different screen sizes

## Examples

### Creating a User Assignment Field
```python
# Field Configuration
field_type = 'many2one'
relation_model = 'res.users'
widget = 'many2one_dropdown'  # Simple dropdown for user selection
```

### Creating a Product Selection Field
```python
# Field Configuration
field_type = 'many2one'
relation_model = 'product.product'
widget = 'many2one_searchable'  # Searchable for large product catalog
```

## Support

For additional help with widget configuration:
1. Check the **Widget & Display** tab help text
2. Review the generated module templates
3. Test different widgets in development
4. Consult the Odoo documentation for advanced widget options

---

**Note**: Changes to widget configuration may require regenerating the module to take effect in the website templates.
