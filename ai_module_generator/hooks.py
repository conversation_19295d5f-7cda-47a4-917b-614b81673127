# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def uninstall_hook(cr, registry):
    """
    Uninstall hook to clean up data and prevent database issues
    """
    _logger.info("Running AI Module Generator uninstall hook...")
    
    try:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Clean up transient records
        if 'form.builder.preview' in env:
            preview_model = env['form.builder.preview']
            preview_records = preview_model.search([])
            if preview_records:
                preview_records.unlink()
                _logger.info("Cleaned up %d form builder preview records", len(preview_records))
        
        # Clean up any generated modules that might have references
        if 'generated.module' in env:
            generated_modules = env['generated.module'].search([])
            for module in generated_modules:
                try:
                    # Set state to uninstalled to avoid conflicts
                    module.write({'state': 'uninstalled'})
                except Exception as e:
                    _logger.warning("Could not update generated module %s: %s", module.name, e)
        
        # Force commit to ensure cleanup is saved
        cr.commit()
        
        _logger.info("AI Module Generator uninstall hook completed successfully")
        
    except Exception as e:
        _logger.error("Error during AI Module Generator uninstall: %s", e)
        # Don't raise the exception to avoid blocking uninstallation
        pass


def post_init_hook(cr, registry):
    """
    Post-installation hook to set up initial data
    """
    _logger.info("Running AI Module Generator post-install hook...")
    
    try:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Ensure demo data is properly loaded
        if not env['module.template'].search_count([]):
            _logger.info("Loading demo data...")
            # Demo data will be loaded by the data files
        
        _logger.info("AI Module Generator post-install hook completed successfully")
        
    except Exception as e:
        _logger.error("Error during AI Module Generator post-install: %s", e)
        # Don't raise the exception to avoid blocking installation
        pass
