<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- ✨ AI PROMPT CONFIGURATIONS DEMO DATA -->

        <!-- Simple Summary Configuration -->
        <record id="demo_ai_simple_summary" model="ai.prompt.config">
            <field name="name">Simple Record Summary</field>
            <field name="prompt_type">system_prompt</field>
            <field name="ai_model">gpt-3.5-turbo</field>
            <field name="button_label">Summarize</field>
            <field name="button_icon">fa-file-text-o</field>
            <field name="enable_pdf_generation" eval="False"/>
            <field name="portal_access">none</field>
            <field name="system_prompt">You are a helpful assistant that creates clear, concise summaries.</field>
            <field name="prompt_template">Create a simple summary of this record:

Record Name: {name}
Status: {state}
Created: {create_date}

Please provide a brief summary.</field>
            <field name="trigger_event">manual</field>
            <field name="output_format">html</field>
            <field name="active" eval="True"/>
            <field name="sequence">10</field>
        </record>

        <!-- Comprehensive Analysis Configuration -->
        <record id="demo_ai_comprehensive_analysis" model="ai.prompt.config">
            <field name="name">Comprehensive Analysis</field>
            <field name="prompt_type">system_prompt</field>
            <field name="ai_model">gpt-4</field>
            <field name="button_label">Analyze</field>
            <field name="button_icon">fa-chart-line</field>
            <field name="enable_pdf_generation" eval="True"/>
            <field name="pdf_template_name">Analysis Report</field>
            <field name="portal_access">paid_only</field>
            <field name="system_prompt">You are an expert analyst with years of experience in data analysis and providing actionable insights.</field>
            <field name="prompt_template">Please analyze this record:

Name: {name}
Status: {state}
Created: {create_date}

Provide a comprehensive analysis with insights and recommendations.</field>
            <field name="trigger_event">manual</field>
            <field name="output_format">html</field>
            <field name="active" eval="True"/>
            <field name="sequence">20</field>
        </record>

        <!-- Customer Feedback Analysis Configuration -->
        <record id="demo_ai_feedback_analysis" model="ai.prompt.config">
            <field name="name">Customer Feedback Analysis</field>
            <field name="prompt_type">system_prompt</field>
            <field name="ai_model">gpt-4</field>
            <field name="button_label">Analyze Feedback</field>
            <field name="button_icon">fa-chart-line</field>
            <field name="enable_pdf_generation" eval="True"/>
            <field name="pdf_template_name">Feedback Analysis Report</field>
            <field name="portal_access">paid_only</field>
            <field name="system_prompt">You are an expert customer service analyst with years of experience in analyzing customer feedback and providing actionable insights.</field>
            <field name="prompt_template">Please analyze this customer feedback:

Customer: {customer_name}
Email: {customer_email}
Rating: {overall_rating}
Status: {state}
Comments: {feedback_comments}

Please provide:
1. Sentiment analysis
2. Key themes
3. Recommendations
4. Priority assessment</field>
            <field name="trigger_event">manual</field>
            <field name="output_format">html</field>
            <field name="active" eval="True"/>
            <field name="sequence">30</field>
        </record>

        <!-- Field Variables Tutorial Configuration -->
        <record id="demo_ai_field_variables" model="ai.prompt.config">
            <field name="name">Field Variables Tutorial</field>
            <field name="prompt_type">system_prompt</field>
            <field name="ai_model">gpt-3.5-turbo</field>
            <field name="button_label">Show Variables</field>
            <field name="button_icon">fa-code</field>
            <field name="enable_pdf_generation" eval="True"/>
            <field name="pdf_template_name">Field Variables Reference</field>
            <field name="portal_access">all_customers</field>
            <field name="system_prompt">You are a helpful documentation assistant that explains how field variables work in AI prompts.</field>
            <field name="prompt_template">This example shows how to use field variables:

Available variables:
- {name} = Record name
- {state} = Current status
- {create_date} = Creation date
- {customer_name} = Customer name
- {customer_email} = Customer email
- {overall_rating} = Rating value
- {feedback_comments} = Comments

Example usage: "Customer {customer_name} gave us {overall_rating} rating."</field>
            <field name="trigger_event">manual</field>
            <field name="output_format">html</field>
            <field name="active" eval="True"/>
            <field name="sequence">40</field>
        </record>

        <!-- Groq Custom API Configuration -->
        <record id="demo_ai_groq_custom" model="ai.prompt.config">
            <field name="name">Groq Fast Analysis (Custom API)</field>
            <field name="prompt_type">system_prompt</field>
            <field name="ai_model">custom-openai-compatible</field>
            <field name="use_custom_api" eval="True"/>
            <field name="custom_api_url">https://api.groq.com/openai/v1/chat/completions</field>
            <field name="custom_api_key">your-groq-api-key-here</field>
            <field name="custom_model_name">llama3-70b-8192</field>
            <field name="button_label">Fast Analysis</field>
            <field name="button_icon">fa-bolt</field>
            <field name="enable_pdf_generation" eval="True"/>
            <field name="pdf_template_name">Groq Analysis Report</field>
            <field name="portal_access">paid_only</field>
            <field name="system_prompt">You are a fast and efficient AI assistant powered by Groq's lightning-fast inference.</field>
            <field name="prompt_template">Provide a fast analysis of this record:

Name: {name}
Status: {state}
Created: {create_date}

Please provide quick insights and recommendations.</field>
            <field name="trigger_event">manual</field>
            <field name="output_format">html</field>
            <field name="active" eval="True"/>
            <field name="sequence">100</field>
        </record>

        <!-- Groq Built-in Configuration -->
        <record id="demo_ai_groq_builtin" model="ai.prompt.config">
            <field name="name">Groq Built-in Summary</field>
            <field name="prompt_type">system_prompt</field>
            <field name="ai_model">groq-llama3-70b</field>
            <field name="button_label">Groq Summary</field>
            <field name="button_icon">fa-flash</field>
            <field name="enable_pdf_generation" eval="False"/>
            <field name="portal_access">none</field>
            <field name="system_prompt">You are a helpful assistant that creates concise summaries using Groq's fast inference.</field>
            <field name="prompt_template">Create a concise summary of this record:

Record: {name}
Status: {state}
Date: {create_date}

Please provide a brief summary with key points.</field>
            <field name="trigger_event">manual</field>
            <field name="output_format">html</field>
            <field name="active" eval="True"/>
            <field name="sequence">101</field>
        </record>

    </data>
</odoo>
