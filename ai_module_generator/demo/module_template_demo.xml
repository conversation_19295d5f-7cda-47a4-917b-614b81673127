<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Demo Module Templates -->
        <record id="demo_template_customer_feedback" model="module.template">
            <field name="name">Customer Feedback System</field>
            <field name="code">ovakil_customer_feedback</field>
            <field name="description">Complete customer feedback collection and management system with AI analysis and payment integration</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">Customer Relationship</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">True</field>
            <field name="generation_count">5</field>
        </record>

        <record id="demo_template_service_booking" model="module.template">
            <field name="name">Service Booking System</field>
            <field name="code">ovakil_service_booking</field>
            <field name="description">Professional service booking system with payment processing, calendar integration, and automated workflows</field>
            <field name="template_type">service_based</field>
            <field name="state">active</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">Services</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">True</field>
            <field name="generation_count">3</field>
        </record>

        <record id="demo_template_product_survey" model="module.template">
            <field name="name">Product Survey &amp; Research</field>
            <field name="code">ovakil_product_survey</field>
            <field name="description">Comprehensive product research and survey system with AI-powered insights and analytics</field>
            <field name="template_type">survey</field>
            <field name="state">active</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">Research</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">False</field>
            <field name="whatsapp_notifications">False</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">False</field>
            <field name="generation_count">2</field>
        </record>

        <record id="demo_template_ecommerce_orders" model="module.template">
            <field name="name">E-commerce Order Management</field>
            <field name="code">ovakil_ecommerce_orders</field>
            <field name="description">Advanced e-commerce order management with payment processing, inventory tracking, and customer portal</field>
            <field name="template_type">ecommerce</field>
            <field name="state">validated</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">E-commerce</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">True</field>
            <field name="generation_count">1</field>
        </record>

        <record id="demo_template_custom_crm" model="module.template">
            <field name="name">Custom CRM Extension</field>
            <field name="code">ovakil_custom_crm</field>
            <field name="description">Custom CRM extension with lead scoring, automated follow-ups, and advanced reporting</field>
            <field name="template_type">custom</field>
            <field name="state">draft</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">CRM</field>
            <field name="website_integration">False</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">False</field>
            <field name="generation_count">0</field>
        </record>

        <!-- ✨ ENHANCED FORMS DEMO TEMPLATES (Phase 1 + Phase 2) -->

        <record id="demo_template_enhanced_forms" model="module.template">
            <field name="name">Enhanced Forms Showcase</field>
            <field name="code">enhanced_forms_showcase</field>
            <field name="description">Comprehensive showcase of enhanced form design and relational field widgets (Phase 1 + Phase 2)</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">Demo/Testing</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">False</field>
            <field name="generation_count">0</field>
        </record>

        <record id="demo_template_project_management" model="module.template">
            <field name="name">Project Management System</field>
            <field name="code">project_management_enhanced</field>
            <field name="description">Advanced project management with team assignments, task tracking, and enhanced relational field widgets</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">Project Management</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">False</field>
            <field name="generation_count">0</field>
        </record>

        <record id="demo_template_hr_recruitment" model="module.template">
            <field name="name">HR Recruitment System</field>
            <field name="code">hr_recruitment_enhanced</field>
            <field name="description">Modern HR recruitment system showcasing all relational field widget types</field>
            <field name="template_type">form_based</field>
            <field name="state">active</field>
            <field name="author">Oneclickvakil</field>
            <field name="version">1.0.0</field>
            <field name="category">Human Resources</field>
            <field name="website_integration">True</field>
            <field name="portal_integration">True</field>
            <field name="api_integration">True</field>
            <field name="whatsapp_notifications">True</field>
            <field name="email_notifications">True</field>
            <field name="auto_sales_order">False</field>
            <field name="generation_count">0</field>
        </record>

    </data>
</odoo>
