<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Test Suites for Customer Feedback Template -->
        <record id="demo_test_suite_customer_feedback" model="module.test.suite">
            <field name="name">Customer Feedback System Tests</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="description">Comprehensive test suite for customer feedback system functionality</field>
            <field name="state">active</field>
            <field name="auto_run">True</field>
            <field name="run_on_generation">True</field>
            <field name="success_rate">0.0</field>
            <field name="total_executions">0</field>
        </record>

        <!-- Test Cases for Customer Feedback -->
        <record id="demo_test_case_feedback_form_validation" model="module.test.case">
            <field name="name">Feedback Form Validation</field>
            <field name="test_suite_id" ref="demo_test_suite_customer_feedback"/>
            <field name="test_type">validation</field>
            <field name="description">Test form validation rules for customer feedback form</field>
            <field name="test_data">{
                "valid_data": {
                    "customer_name": "<PERSON>",
                    "customer_email": "<EMAIL>",
                    "overall_rating": "excellent",
                    "feedback_comments": "Great service!",
                    "recommend_service": true
                },
                "invalid_data": [
                    {"customer_name": "", "expected_error": "Customer name is required"},
                    {"customer_email": "invalid-email", "expected_error": "Invalid email format"},
                    {"overall_rating": "", "expected_error": "Rating is required"}
                ]
            }</field>
            <field name="expected_result">All validation rules should work correctly</field>
            <field name="sequence">10</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <record id="demo_test_case_feedback_workflow" model="module.test.case">
            <field name="name">Feedback Workflow Transitions</field>
            <field name="test_suite_id" ref="demo_test_suite_customer_feedback"/>
            <field name="test_type">workflow</field>
            <field name="description">Test workflow state transitions for feedback processing</field>
            <field name="test_data">{
                "workflow_steps": [
                    {"from": "feedback_submitted", "to": "feedback_under_review", "trigger": "auto_transition"},
                    {"from": "feedback_under_review", "to": "feedback_responded", "trigger": "manual_action"}
                ],
                "test_scenarios": [
                    {"scenario": "normal_flow", "expected_states": ["feedback_submitted", "feedback_under_review", "feedback_responded"]},
                    {"scenario": "auto_transition", "wait_time": 24, "expected_state": "feedback_under_review"}
                ]
            }</field>
            <field name="expected_result">Workflow should transition correctly through all states</field>
            <field name="sequence">20</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <record id="demo_test_case_feedback_ai_integration" model="module.test.case">
            <field name="name">AI Integration Testing</field>
            <field name="test_suite_id" ref="demo_test_suite_customer_feedback"/>
            <field name="test_type">integration</field>
            <field name="description">Test AI prompt execution and response handling</field>
            <field name="test_data">{
                "ai_prompts": [
                    {"prompt_id": "feedback_sentiment_analysis", "test_input": "Great service, very satisfied!", "expected_sentiment": "positive"},
                    {"prompt_id": "feedback_response_generator", "test_input": "Poor service quality", "expected_tone": "empathetic"}
                ],
                "mock_responses": {
                    "sentiment_analysis": {"sentiment": "positive", "themes": ["service quality"], "urgency": "low"},
                    "response_generator": "Thank you for your feedback. We apologize for not meeting your expectations..."
                }
            }</field>
            <field name="expected_result">AI prompts should execute and return appropriate responses</field>
            <field name="sequence">30</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <!-- Test Suite for Service Booking Template -->
        <record id="demo_test_suite_service_booking" model="module.test.suite">
            <field name="name">Service Booking System Tests</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="description">Test suite for service booking functionality including payment processing</field>
            <field name="state">active</field>
            <field name="auto_run">True</field>
            <field name="run_on_generation">True</field>
            <field name="success_rate">0.0</field>
            <field name="total_executions">0</field>
        </record>

        <!-- Test Cases for Service Booking -->
        <record id="demo_test_case_booking_payment" model="module.test.case">
            <field name="name">Booking Payment Processing</field>
            <field name="test_suite_id" ref="demo_test_suite_service_booking"/>
            <field name="test_type">payment</field>
            <field name="description">Test payment processing for service bookings</field>
            <field name="test_data">{
                "payment_scenarios": [
                    {"service_type": "consultation", "expected_amount": 150.00, "currency": "USD"},
                    {"service_type": "documentation", "expected_amount": 300.00, "currency": "USD"},
                    {"service_type": "representation", "expected_amount": 500.00, "currency": "USD"}
                ],
                "payment_methods": ["credit_card", "paypal", "bank_transfer"],
                "test_cards": [
                    {"number": "****************", "expected": "success"},
                    {"number": "****************", "expected": "decline"}
                ]
            }</field>
            <field name="expected_result">Payment processing should work correctly for all service types</field>
            <field name="sequence">10</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <record id="demo_test_case_booking_calendar" model="module.test.case">
            <field name="name">Calendar Integration</field>
            <field name="test_suite_id" ref="demo_test_suite_service_booking"/>
            <field name="test_type">integration</field>
            <field name="description">Test calendar integration for appointment scheduling</field>
            <field name="test_data">{
                "calendar_tests": [
                    {"date": "2024-12-01", "time": "morning", "expected": "available"},
                    {"date": "2024-12-25", "time": "morning", "expected": "holiday_blocked"},
                    {"date": "2024-12-02", "time": "evening", "expected": "available"}
                ],
                "booking_conflicts": [
                    {"existing_booking": "2024-12-01 10:00", "new_booking": "2024-12-01 10:30", "expected": "conflict"},
                    {"existing_booking": "2024-12-01 10:00", "new_booking": "2024-12-01 14:00", "expected": "available"}
                ]
            }</field>
            <field name="expected_result">Calendar should correctly handle availability and conflicts</field>
            <field name="sequence">20</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <!-- Test Suite for E-commerce Orders Template -->
        <record id="demo_test_suite_ecommerce_orders" model="module.test.suite">
            <field name="name">E-commerce Order Tests</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="description">Comprehensive testing for e-commerce order management system</field>
            <field name="state">active</field>
            <field name="auto_run">True</field>
            <field name="run_on_generation">True</field>
            <field name="success_rate">0.0</field>
            <field name="total_executions">0</field>
        </record>

        <!-- Test Cases for E-commerce Orders -->
        <record id="demo_test_case_order_crud" model="module.test.case">
            <field name="name">Order CRUD Operations</field>
            <field name="test_suite_id" ref="demo_test_suite_ecommerce_orders"/>
            <field name="test_type">crud</field>
            <field name="description">Test Create, Read, Update, Delete operations for orders</field>
            <field name="test_data">{
                "crud_operations": [
                    {"operation": "create", "data": {"customer": "Test Customer", "product": "Test Product", "quantity": 2}},
                    {"operation": "read", "order_id": "test_order_1"},
                    {"operation": "update", "order_id": "test_order_1", "data": {"quantity": 3}},
                    {"operation": "delete", "order_id": "test_order_1"}
                ],
                "expected_results": ["created", "found", "updated", "deleted"]
            }</field>
            <field name="expected_result">All CRUD operations should work correctly</field>
            <field name="sequence">10</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <record id="demo_test_case_order_api" model="module.test.case">
            <field name="name">Order API Endpoints</field>
            <field name="test_suite_id" ref="demo_test_suite_ecommerce_orders"/>
            <field name="test_type">api</field>
            <field name="description">Test REST API endpoints for order management</field>
            <field name="test_data">{
                "api_endpoints": [
                    {"method": "GET", "endpoint": "/api/orders", "expected_status": 200},
                    {"method": "POST", "endpoint": "/api/orders", "data": {"customer": "API Test"}, "expected_status": 201},
                    {"method": "GET", "endpoint": "/api/orders/1", "expected_status": 200},
                    {"method": "PUT", "endpoint": "/api/orders/1", "data": {"status": "shipped"}, "expected_status": 200},
                    {"method": "DELETE", "endpoint": "/api/orders/1", "expected_status": 204}
                ],
                "authentication": {"type": "api_key", "required": true}
            }</field>
            <field name="expected_result">All API endpoints should respond correctly</field>
            <field name="sequence">20</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

        <record id="demo_test_case_order_performance" model="module.test.case">
            <field name="name">Order Performance Testing</field>
            <field name="test_suite_id" ref="demo_test_suite_ecommerce_orders"/>
            <field name="test_type">performance</field>
            <field name="description">Test system performance under load</field>
            <field name="test_data">{
                "performance_tests": [
                    {"test": "concurrent_orders", "users": 100, "duration": "5min", "expected_response_time": "&lt; 2s"},
                    {"test": "bulk_order_creation", "orders": 1000, "expected_completion_time": "&lt; 30s"},
                    {"test": "order_search", "database_size": 10000, "search_queries": 100, "expected_avg_time": "&lt; 500ms"}
                ],
                "thresholds": {
                    "response_time": 2000,
                    "error_rate": 0.01,
                    "throughput": 50
                }
            }</field>
            <field name="expected_result">System should meet performance thresholds</field>
            <field name="sequence">30</field>
            <field name="active">True</field>
            <field name="result">pending</field>
        </record>

    </data>
</odoo>
