<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Field Definitions for Customer Feedback Form -->
        <record id="demo_field_customer_name" model="field.definition">
            <field name="name">customer_name</field>
            <field name="field_description">Customer Name</field>
            <field name="field_type">char</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Please enter your full name</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="max_length">100</field>
        </record>

        <record id="demo_field_customer_email" model="field.definition">
            <field name="name">customer_email</field>
            <field name="field_description">Email Address</field>
            <field name="field_type">email</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="help_text">We'll use this to send you updates</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_overall_rating" model="field.definition">
            <field name="name">overall_rating</field>
            <field name="field_description">Overall Rating</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="help_text">Rate your overall experience</field>
            <field name="selection_options">[["1", "1 Star"], ["2", "2 Stars"], ["3", "3 Stars"], ["4", "4 Stars"], ["5", "5 Stars"]]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">3</field>
        </record>

        <record id="demo_field_feedback_comments" model="field.definition">
            <field name="name">feedback_comments</field>
            <field name="field_description">Comments &amp; Suggestions</field>
            <field name="field_type">text</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
            <field name="help_text">Please share your detailed feedback</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="max_length">1000</field>
        </record>

        <record id="demo_field_recommend_service" model="field.definition">
            <field name="name">recommend_service</field>
            <field name="field_description">Would you recommend our service?</field>
            <field name="field_type">boolean</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">True</field>
            <field name="sequence">50</field>
            <field name="help_text">Help us understand if you'd recommend us to others</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">True</field>
        </record>

        <!-- Advanced Field Types Demo - Many2one Field -->
        <record id="demo_field_assigned_user" model="field.definition">
            <field name="name">assigned_user</field>
            <field name="field_description">Assigned User</field>
            <field name="field_type">many2one</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">60</field>
            <field name="help_text">User responsible for handling this feedback</field>
            <field name="relation_model">res.users</field>
            <field name="domain">[('active', '=', True)]</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Priority Selection Field -->
        <record id="demo_field_priority" model="field.definition">
            <field name="name">priority</field>
            <field name="field_description">Priority Level</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">70</field>
            <field name="help_text">Priority level for handling this feedback</field>
            <field name="selection_options">[["low", "Low Priority"], ["medium", "Medium Priority"], ["high", "High Priority"], ["urgent", "Urgent"]]</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">medium</field>
        </record>

        <!-- Many2many Field for Tags -->
        <record id="demo_field_feedback_tags" model="field.definition">
            <field name="name">feedback_tags</field>
            <field name="field_description">Feedback Tags</field>
            <field name="field_type">many2many</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="required">False</field>
            <field name="sequence">80</field>
            <field name="help_text">Tags to categorize this feedback</field>
            <field name="relation_model">project.tags</field>
            <field name="domain">[('active', '=', True)]</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="widget">many2many_tags</field>
        </record>

        <!-- Field Definitions for Service Booking Form -->
        <record id="demo_field_service_type" model="field.definition">
            <field name="name">service_type</field>
            <field name="field_description">Service Type</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Select the type of service you need</field>
            <field name="selection_options">[["consultation", "Legal Consultation"], ["documentation", "Document Preparation"], ["representation", "Court Representation"], ["advisory", "Legal Advisory"], ["other", "Other Services"]]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">consultation</field>
        </record>

        <!-- Customer Many2one Field -->
        <record id="demo_field_customer" model="field.definition">
            <field name="name">customer</field>
            <field name="field_description">Customer</field>
            <field name="field_type">many2one</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">15</field>
            <field name="help_text">Select existing customer or create new</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('is_company', '=', False), ('customer_rank', '>', 0)]</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_preferred_date" model="field.definition">
            <field name="name">preferred_date</field>
            <field name="field_description">Preferred Date</field>
            <field name="field_type">date</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="help_text">Select your preferred appointment date</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_preferred_time" model="field.definition">
            <field name="name">preferred_time</field>
            <field name="field_description">Preferred Time</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="help_text">Select your preferred time slot</field>
            <field name="selection_options">[["morning", "Morning (9:00 AM - 12:00 PM)"], ["afternoon", "Afternoon (12:00 PM - 5:00 PM)"], ["evening", "Evening (5:00 PM - 8:00 PM)"]]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">morning</field>
        </record>

        <!-- Status Selection Field -->
        <record id="demo_field_booking_status" model="field.definition">
            <field name="name">booking_status</field>
            <field name="field_description">Booking Status</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">35</field>
            <field name="help_text">Current status of the booking</field>
            <field name="selection_options">[["draft", "Draft"], ["confirmed", "Confirmed"], ["in_progress", "In Progress"], ["completed", "Completed"], ["cancelled", "Cancelled"]]</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">draft</field>
        </record>

        <!-- One2many Field for Service Lines -->
        <record id="demo_field_service_lines" model="field.definition">
            <field name="name">service_lines</field>
            <field name="field_description">Service Lines</field>
            <field name="field_type">one2many</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">False</field>
            <field name="sequence">37</field>
            <field name="help_text">Detailed service line items</field>
            <field name="relation_model">sale.order.line</field>
            <field name="relation_field">order_id</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_service_description" model="field.definition">
            <field name="name">service_description</field>
            <field name="field_description">Service Description</field>
            <field name="field_type">text</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">40</field>
            <field name="help_text">Please describe your requirements in detail</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="max_length">500</field>
        </record>

        <record id="demo_field_contact_phone" model="field.definition">
            <field name="name">contact_phone</field>
            <field name="field_description">Contact Phone</field>
            <field name="field_type">phone</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">True</field>
            <field name="sequence">50</field>
            <field name="help_text">We'll call you to confirm the appointment</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_estimated_budget" model="field.definition">
            <field name="name">estimated_budget</field>
            <field name="field_description">Estimated Budget</field>
            <field name="field_type">monetary</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="required">False</field>
            <field name="sequence">60</field>
            <field name="help_text">Optional: Your estimated budget for this service</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="min_value">0</field>
        </record>

        <!-- Field Definitions for Product Survey Form -->
        <record id="demo_field_product_category" model="field.definition">
            <field name="name">product_category</field>
            <field name="field_description">Product Category Interest</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_product_survey_main"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Which product category interests you most?</field>
            <field name="selection_options">[["electronics", "Electronics"], ["clothing", "Clothing &amp; Fashion"], ["home", "Home &amp; Garden"], ["sports", "Sports &amp; Outdoors"], ["books", "Books &amp; Media"]]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">False</field>
            <field name="api_visible">False</field>
            <field name="default_value">electronics</field>
        </record>

        <record id="demo_field_purchase_frequency" model="field.definition">
            <field name="name">purchase_frequency</field>
            <field name="field_description">Purchase Frequency</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_product_survey_main"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="help_text">How often do you make online purchases?</field>
            <field name="selection_options">[["daily", "Daily"], ["weekly", "Weekly"], ["monthly", "Monthly"], ["quarterly", "Quarterly"], ["rarely", "Rarely"]]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">False</field>
            <field name="api_visible">False</field>
            <field name="default_value">monthly</field>
        </record>

        <record id="demo_field_age_group" model="field.definition">
            <field name="name">age_group</field>
            <field name="field_description">Age Group</field>
            <field name="field_type">selection</field>
            <field name="form_builder_id" ref="demo_form_product_survey_main"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="help_text">Please select your age group</field>
            <field name="selection_options">[["18-25", "18-25 years"], ["26-35", "26-35 years"], ["36-45", "36-45 years"], ["46-55", "46-55 years"], ["55+", "55+ years"]]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">False</field>
            <field name="api_visible">False</field>
            <field name="default_value">26-35</field>
        </record>

        <!-- Additional Advanced Field Examples for Product Survey -->
        <!-- Company Many2one Field -->
        <record id="demo_field_company" model="field.definition">
            <field name="name">company</field>
            <field name="field_description">Company</field>
            <field name="field_type">many2one</field>
            <field name="form_builder_id" ref="demo_form_product_survey_main"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
            <field name="help_text">Your company (optional)</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('is_company', '=', True)]</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Product Categories Many2many Field -->
        <record id="demo_field_interested_categories" model="field.definition">
            <field name="name">interested_categories</field>
            <field name="field_description">Interested Product Categories</field>
            <field name="field_type">many2many</field>
            <field name="form_builder_id" ref="demo_form_product_survey_main"/>
            <field name="required">False</field>
            <field name="sequence">50</field>
            <field name="help_text">Select all categories you're interested in</field>
            <field name="relation_model">product.category</field>
            <field name="domain">[('parent_id', '!=', False)]</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="widget">many2many_checkboxes</field>
        </record>

        <!-- Survey Response Lines One2many Field -->
        <record id="demo_field_response_lines" model="field.definition">
            <field name="name">response_lines</field>
            <field name="field_description">Survey Response Lines</field>
            <field name="field_type">one2many</field>
            <field name="form_builder_id" ref="demo_form_product_survey_main"/>
            <field name="required">False</field>
            <field name="sequence">60</field>
            <field name="help_text">Detailed survey responses</field>
            <field name="relation_model">survey.user_input.line</field>
            <field name="relation_field">user_input_id</field>
            <field name="website_form_visible">False</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- ✨ ENHANCED FORMS SHOWCASE FIELD DEFINITIONS -->

        <!-- Basic Information Fields (Phase 1 Features) -->
        <record id="demo_field_enhanced_name" model="field.definition">
            <field name="name">full_name</field>
            <field name="field_description">Full Name</field>
            <field name="field_type">char</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Enter your complete full name</field>
            <field name="website_form_visible">True</field>
            <field name="website_form_required">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="max_length">100</field>
        </record>

        <record id="demo_field_enhanced_email" model="field.definition">
            <field name="name">email_address</field>
            <field name="field_description">Email Address</field>
            <field name="field_type">email</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="help_text">Your primary email address</field>
            <field name="website_form_visible">True</field>
            <field name="website_form_required">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_enhanced_phone" model="field.definition">
            <field name="name">phone_number</field>
            <field name="field_description">Phone Number</field>
            <field name="field_type">phone</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">True</field>
            <field name="sequence">30</field>
            <field name="help_text">Your contact phone number</field>
            <field name="website_form_visible">True</field>
            <field name="website_form_required">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Many2one Fields (Phase 2 Features) -->
        <record id="demo_field_customer_searchable" model="field.definition">
            <field name="name">customer_id</field>
            <field name="field_description">Customer (Searchable Dropdown)</field>
            <field name="field_type">many2one</field>
            <field name="widget">many2one_searchable</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('is_company', '=', False)]</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">True</field>
            <field name="sequence">40</field>
            <field name="help_text">Search and select a customer</field>
            <field name="website_form_visible">True</field>
            <field name="website_form_required">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_priority_radio" model="field.definition">
            <field name="name">priority_id</field>
            <field name="field_description">Priority Level (Radio Buttons)</field>
            <field name="field_type">many2one</field>
            <field name="widget">many2one_radio</field>
            <field name="relation_model">project.task.type</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">50</field>
            <field name="help_text">Select priority level using radio buttons</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_company_dropdown" model="field.definition">
            <field name="name">company_id</field>
            <field name="field_description">Company (Simple Dropdown)</field>
            <field name="field_type">many2one</field>
            <field name="widget">many2one_dropdown</field>
            <field name="relation_model">res.partner</field>
            <field name="domain">[('is_company', '=', True)]</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">60</field>
            <field name="help_text">Select company from dropdown</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Many2many Fields (Phase 2 Features) -->
        <record id="demo_field_skills_tags" model="field.definition">
            <field name="name">skill_ids</field>
            <field name="field_description">Skills (Tags Widget)</field>
            <field name="field_type">many2many</field>
            <field name="widget">many2many_tags</field>
            <field name="relation_model">hr.skill</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">70</field>
            <field name="help_text">Add your skills using tag-based selection</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_interests_checkboxes" model="field.definition">
            <field name="name">interest_ids</field>
            <field name="field_description">Interests (Checkbox List)</field>
            <field name="field_type">many2many</field>
            <field name="widget">many2many_checkboxes</field>
            <field name="relation_model">product.category</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">80</field>
            <field name="help_text">Select your interests using checkboxes</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_categories_multiselect" model="field.definition">
            <field name="name">category_ids</field>
            <field name="field_description">Categories (Multi-select Dropdown)</field>
            <field name="field_type">many2many</field>
            <field name="widget">many2many_multiselect</field>
            <field name="relation_model">product.category</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">90</field>
            <field name="help_text">Select multiple categories from dropdown</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- One2many Fields (Phase 2 Features) -->
        <record id="demo_field_experience_list" model="field.definition">
            <field name="name">experience_ids</field>
            <field name="field_description">Work Experience (List View)</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_list</field>
            <field name="relation_model">hr.employee</field>
            <field name="relation_field">partner_id</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">100</field>
            <field name="help_text">Add your work experience entries</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_documents_inline" model="field.definition">
            <field name="name">document_ids</field>
            <field name="field_description">Documents (Inline Form)</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_inline</field>
            <field name="relation_model">ir.attachment</field>
            <field name="relation_field">res_id</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">False</field>
            <field name="sequence">110</field>
            <field name="help_text">Upload and manage documents inline</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- Selection Fields with Enhanced Styling -->
        <record id="demo_field_status_selection" model="field.definition">
            <field name="name">status</field>
            <field name="field_description">Application Status</field>
            <field name="field_type">selection</field>
            <field name="selection_options">[["draft", "Draft"], ["submitted", "Submitted"], ["under_review", "Under Review"], ["approved", "Approved"], ["rejected", "Rejected"]]</field>
            <field name="form_builder_id" ref="demo_form_enhanced_showcase"/>
            <field name="required">True</field>
            <field name="sequence">120</field>
            <field name="help_text">Current application status</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
            <field name="default_value">draft</field>
        </record>

        <!-- ✨ PROJECT MANAGEMENT DEMO FIELDS -->

        <record id="demo_field_project_name" model="field.definition">
            <field name="name">project_name</field>
            <field name="field_description">Project Name</field>
            <field name="field_type">char</field>
            <field name="form_builder_id" ref="demo_form_project_management"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Enter the project name</field>
            <field name="website_form_visible">True</field>
            <field name="website_form_required">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_project_manager" model="field.definition">
            <field name="name">project_manager_id</field>
            <field name="field_description">Project Manager (Searchable)</field>
            <field name="field_type">many2one</field>
            <field name="widget">many2one_searchable</field>
            <field name="relation_model">res.users</field>
            <field name="form_builder_id" ref="demo_form_project_management"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="help_text">Search and assign project manager</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_team_members" model="field.definition">
            <field name="name">team_member_ids</field>
            <field name="field_description">Team Members (Tags)</field>
            <field name="field_type">many2many</field>
            <field name="widget">many2many_tags</field>
            <field name="relation_model">res.users</field>
            <field name="form_builder_id" ref="demo_form_project_management"/>
            <field name="required">False</field>
            <field name="sequence">30</field>
            <field name="help_text">Add team members using tags</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_project_type" model="field.definition">
            <field name="name">project_type</field>
            <field name="field_description">Project Type (Radio)</field>
            <field name="field_type">selection</field>
            <field name="widget">radio</field>
            <field name="selection_options">[["development", "Software Development"], ["design", "Design Project"], ["research", "Research Project"], ["marketing", "Marketing Campaign"]]</field>
            <field name="form_builder_id" ref="demo_form_project_management"/>
            <field name="required">True</field>
            <field name="sequence">40</field>
            <field name="help_text">Select project type</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <!-- ✨ HR RECRUITMENT DEMO FIELDS -->

        <record id="demo_field_applicant_name" model="field.definition">
            <field name="name">applicant_name</field>
            <field name="field_description">Applicant Name</field>
            <field name="field_type">char</field>
            <field name="form_builder_id" ref="demo_form_hr_recruitment"/>
            <field name="required">True</field>
            <field name="sequence">10</field>
            <field name="help_text">Enter your full name</field>
            <field name="website_form_visible">True</field>
            <field name="website_form_required">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_position_applied" model="field.definition">
            <field name="name">position_id</field>
            <field name="field_description">Position Applied (Dropdown)</field>
            <field name="field_type">many2one</field>
            <field name="widget">many2one_dropdown</field>
            <field name="relation_model">hr.job</field>
            <field name="form_builder_id" ref="demo_form_hr_recruitment"/>
            <field name="required">True</field>
            <field name="sequence">20</field>
            <field name="help_text">Select the position you're applying for</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_technical_skills" model="field.definition">
            <field name="name">technical_skill_ids</field>
            <field name="field_description">Technical Skills (Checkboxes)</field>
            <field name="field_type">many2many</field>
            <field name="widget">many2many_checkboxes</field>
            <field name="relation_model">hr.skill</field>
            <field name="form_builder_id" ref="demo_form_hr_recruitment"/>
            <field name="required">False</field>
            <field name="sequence">30</field>
            <field name="help_text">Select your technical skills</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_languages" model="field.definition">
            <field name="name">language_ids</field>
            <field name="field_description">Languages (Multi-select)</field>
            <field name="field_type">many2many</field>
            <field name="widget">many2many_multiselect</field>
            <field name="relation_model">res.lang</field>
            <field name="form_builder_id" ref="demo_form_hr_recruitment"/>
            <field name="required">False</field>
            <field name="sequence">40</field>
            <field name="help_text">Select languages you speak</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_work_experience" model="field.definition">
            <field name="name">work_experience_ids</field>
            <field name="field_description">Work Experience (List)</field>
            <field name="field_type">one2many</field>
            <field name="widget">one2many_list</field>
            <field name="relation_model">hr.employee</field>
            <field name="relation_field">user_id</field>
            <field name="form_builder_id" ref="demo_form_hr_recruitment"/>
            <field name="required">False</field>
            <field name="sequence">50</field>
            <field name="help_text">Add your work experience</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

        <record id="demo_field_availability" model="field.definition">
            <field name="name">availability</field>
            <field name="field_description">Availability</field>
            <field name="field_type">selection</field>
            <field name="selection_options">[["immediate", "Immediate"], ["two_weeks", "2 Weeks Notice"], ["one_month", "1 Month Notice"], ["negotiable", "Negotiable"]]</field>
            <field name="form_builder_id" ref="demo_form_hr_recruitment"/>
            <field name="required">True</field>
            <field name="sequence">60</field>
            <field name="help_text">When can you start?</field>
            <field name="website_form_visible">True</field>
            <field name="portal_visible">True</field>
            <field name="api_visible">True</field>
        </record>

    </data>
</odoo>
