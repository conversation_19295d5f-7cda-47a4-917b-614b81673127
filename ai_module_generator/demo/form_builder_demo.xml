<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Demo Form Builders for Customer Feedback Template -->
        <record id="demo_form_customer_feedback_main" model="form.builder">
            <field name="name">Customer Feedback Form</field>
            <field name="code">customer_feedback_main</field>
            <field name="description">Main customer feedback collection form with rating and comments</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_type">standard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">False</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_1"/>
            <field name="sequence">10</field>
        </record>

        <record id="demo_form_customer_contact" model="form.builder">
            <field name="name">Customer Contact Information</field>
            <field name="code">customer_contact_info</field>
            <field name="description">Collect customer contact details for follow-up</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="form_type">wizard</field>
            <field name="layout_type">two_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">False</field>
            <field name="requires_login">False</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">20</field>
        </record>

        <!-- Demo Form Builders for Service Booking Template -->
        <record id="demo_form_service_booking_main" model="form.builder">
            <field name="name">Service Booking Form</field>
            <field name="code">service_booking_main</field>
            <field name="description">Main service booking form with date/time selection and payment</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_type">standard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">True</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_2"/>
            <field name="sequence">10</field>
        </record>

        <record id="demo_form_service_preferences" model="form.builder">
            <field name="name">Service Preferences</field>
            <field name="code">service_preferences</field>
            <field name="description">Collect customer preferences for service customization</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="form_type">popup</field>
            <field name="layout_type">tabs</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">False</field>
            <field name="requires_login">True</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">20</field>
        </record>

        <!-- Demo Form Builders for Product Survey Template -->
        <record id="demo_form_product_survey_main" model="form.builder">
            <field name="name">Product Survey Form</field>
            <field name="code">product_survey_main</field>
            <field name="description">Comprehensive product survey with multiple question types</field>
            <field name="template_id" ref="demo_template_product_survey"/>
            <field name="form_type">standard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">False</field>
            <field name="api_access">False</field>
            <field name="requires_login">False</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">10</field>
        </record>

        <!-- Demo Form Builders for E-commerce Orders Template -->
        <record id="demo_form_ecommerce_order" model="form.builder">
            <field name="name">Order Placement Form</field>
            <field name="code">ecommerce_order_main</field>
            <field name="description">E-commerce order placement form with product selection and payment</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="form_type">standard</field>
            <field name="layout_type">two_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">True</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_3"/>
            <field name="sequence">10</field>
        </record>

        <record id="demo_form_ecommerce_shipping" model="form.builder">
            <field name="name">Shipping Information</field>
            <field name="code">ecommerce_shipping_info</field>
            <field name="description">Collect shipping and delivery preferences</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="form_type">wizard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">True</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">20</field>
        </record>

        <!-- ✨ ENHANCED FORMS DEMO FORM BUILDERS -->

        <!-- Enhanced Forms Showcase Form -->
        <record id="demo_form_enhanced_showcase" model="form.builder">
            <field name="name">Enhanced Forms Demo</field>
            <field name="code">enhanced_forms_demo</field>
            <field name="description">Comprehensive demo showcasing Phase 1 enhanced design and Phase 2 relational field widgets</field>
            <field name="template_id" ref="demo_template_enhanced_forms"/>
            <field name="form_type">standard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">False</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">10</field>
        </record>

        <!-- Project Management Form -->
        <record id="demo_form_project_management" model="form.builder">
            <field name="name">Project Registration</field>
            <field name="code">project_registration</field>
            <field name="description">Project registration form with team assignments and relational field widgets</field>
            <field name="template_id" ref="demo_template_project_management"/>
            <field name="form_type">standard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">True</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">10</field>
        </record>

        <!-- HR Recruitment Form -->
        <record id="demo_form_hr_recruitment" model="form.builder">
            <field name="name">Job Application</field>
            <field name="code">job_application</field>
            <field name="description">Job application form showcasing all relational field widget types</field>
            <field name="template_id" ref="demo_template_hr_recruitment"/>
            <field name="form_type">standard</field>
            <field name="layout_type">single_column</field>
            <field name="website_published">True</field>
            <field name="portal_access">True</field>
            <field name="api_access">True</field>
            <field name="requires_login">False</field>
            <field name="auto_create_sales_order">False</field>
            <field name="sequence">10</field>
        </record>

    </data>
</odoo>
