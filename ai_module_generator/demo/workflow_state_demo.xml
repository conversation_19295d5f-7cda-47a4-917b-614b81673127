<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Workflow States for Customer Feedback Template -->
        <record id="demo_workflow_feedback_submitted" model="workflow.state">
            <field name="name">Feedback Submitted</field>
            <field name="code">feedback_submitted</field>
            <field name="description">Customer has submitted their feedback</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">initial</field>
            <field name="sequence">10</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-paper-plane</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Thank you for your feedback! We have received your submission and will review it shortly.</field>
            <field name="auto_transition">False</field>
        </record>

        <record id="demo_workflow_feedback_under_review" model="workflow.state">
            <field name="name">Under Review</field>
            <field name="code">feedback_under_review</field>
            <field name="description">Feedback is being reviewed by the team</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">intermediate</field>
            <field name="sequence">20</field>
            <field name="color">#ffc107</field>
            <field name="icon">fa-search</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your feedback is now under review. We'll get back to you within 24 hours.</field>
            <field name="auto_transition">True</field>
            <field name="auto_transition_delay">24</field>
        </record>

        <record id="demo_workflow_feedback_responded" model="workflow.state">
            <field name="name">Responded</field>
            <field name="code">feedback_responded</field>
            <field name="description">Response has been sent to the customer</field>
            <field name="template_id" ref="demo_template_customer_feedback"/>
            <field name="state_type">final</field>
            <field name="sequence">30</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-check-circle</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Thank you for your patience. We have responded to your feedback. Check your email for details.</field>
            <field name="auto_transition">False</field>
        </record>

        <!-- Workflow States for Service Booking Template -->
        <record id="demo_workflow_booking_requested" model="workflow.state">
            <field name="name">Booking Requested</field>
            <field name="code">booking_requested</field>
            <field name="description">Service booking has been requested</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">initial</field>
            <field name="sequence">10</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-calendar-plus</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your service booking request has been received. We'll confirm your appointment shortly.</field>
            <field name="auto_transition">False</field>
        </record>

        <record id="demo_workflow_booking_confirmed" model="workflow.state">
            <field name="name">Booking Confirmed</field>
            <field name="code">booking_confirmed</field>
            <field name="description">Service booking has been confirmed</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">intermediate</field>
            <field name="sequence">20</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-calendar-check</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Great! Your appointment is confirmed for {preferred_date} at {preferred_time}. We'll send you a reminder 24 hours before.</field>
            <field name="auto_transition">False</field>
        </record>

        <record id="demo_workflow_booking_in_progress" model="workflow.state">
            <field name="name">Service In Progress</field>
            <field name="code">booking_in_progress</field>
            <field name="description">Service is currently being provided</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">intermediate</field>
            <field name="sequence">30</field>
            <field name="color">#fd7e14</field>
            <field name="icon">fa-cogs</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your service session is now in progress. Thank you for choosing our services.</field>
            <field name="auto_transition">False</field>
        </record>

        <record id="demo_workflow_booking_completed" model="workflow.state">
            <field name="name">Service Completed</field>
            <field name="code">booking_completed</field>
            <field name="description">Service has been completed successfully</field>
            <field name="template_id" ref="demo_template_service_booking"/>
            <field name="state_type">final</field>
            <field name="sequence">40</field>
            <field name="color">#6f42c1</field>
            <field name="icon">fa-trophy</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your service has been completed successfully! We'd love to hear your feedback.</field>
            <field name="auto_transition">False</field>
        </record>

        <!-- Workflow States for Product Survey Template -->
        <record id="demo_workflow_survey_submitted" model="workflow.state">
            <field name="name">Survey Submitted</field>
            <field name="code">survey_submitted</field>
            <field name="description">Product survey has been submitted</field>
            <field name="template_id" ref="demo_template_product_survey"/>
            <field name="state_type">initial</field>
            <field name="sequence">10</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-clipboard-check</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Thank you for participating in our product survey!</field>
            <field name="auto_transition">True</field>
            <field name="auto_transition_delay">1</field>
        </record>

        <record id="demo_workflow_survey_analyzed" model="workflow.state">
            <field name="name">Survey Analyzed</field>
            <field name="code">survey_analyzed</field>
            <field name="description">Survey data has been analyzed</field>
            <field name="template_id" ref="demo_template_product_survey"/>
            <field name="state_type">final</field>
            <field name="sequence">20</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-chart-bar</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your survey responses have been analyzed and added to our research database.</field>
            <field name="auto_transition">False</field>
        </record>

        <!-- Workflow States for E-commerce Orders Template -->
        <record id="demo_workflow_order_placed" model="workflow.state">
            <field name="name">Order Placed</field>
            <field name="code">order_placed</field>
            <field name="description">Order has been placed by customer</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">initial</field>
            <field name="sequence">10</field>
            <field name="color">#17a2b8</field>
            <field name="icon">fa-shopping-cart</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your order has been placed successfully! Order ID: {order_number}</field>
            <field name="auto_transition">False</field>
        </record>

        <record id="demo_workflow_order_confirmed" model="workflow.state">
            <field name="name">Order Confirmed</field>
            <field name="code">order_confirmed</field>
            <field name="description">Order has been confirmed and payment received</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">intermediate</field>
            <field name="sequence">20</field>
            <field name="color">#28a745</field>
            <field name="icon">fa-check-circle</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Payment confirmed! Your order is now being processed.</field>
            <field name="auto_transition">True</field>
            <field name="auto_transition_delay">2</field>
        </record>

        <record id="demo_workflow_order_shipped" model="workflow.state">
            <field name="name">Order Shipped</field>
            <field name="code">order_shipped</field>
            <field name="description">Order has been shipped to customer</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">intermediate</field>
            <field name="sequence">30</field>
            <field name="color">#fd7e14</field>
            <field name="icon">fa-truck</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your order has been shipped! Tracking number: {tracking_number}</field>
            <field name="auto_transition">False</field>
        </record>

        <record id="demo_workflow_order_delivered" model="workflow.state">
            <field name="name">Order Delivered</field>
            <field name="code">order_delivered</field>
            <field name="description">Order has been delivered to customer</field>
            <field name="template_id" ref="demo_template_ecommerce_orders"/>
            <field name="state_type">final</field>
            <field name="sequence">40</field>
            <field name="color">#6f42c1</field>
            <field name="icon">fa-home</field>
            <field name="email_template_id" ref="mail.mail_template_data_notification_email_default"/>
            <field name="whatsapp_message">Your order has been delivered! Thank you for shopping with us.</field>
            <field name="auto_transition">False</field>
        </record>

    </data>
</odoo>
