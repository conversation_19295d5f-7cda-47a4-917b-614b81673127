<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo Module Templates -->
    
    <!-- Legal Document Request Template -->
    <record id="demo_legal_document_template" model="module.template">
        <field name="name">Legal Document Request System</field>
        <field name="code">legal_document_request</field>
        <field name="description">Complete system for managing legal document requests with AI generation, payment processing, and customer portal access.</field>
        <field name="template_type">service_based</field>
        <field name="module_prefix">ovakil</field>
        <field name="module_author">Oneclickvakil</field>
        <field name="module_website">https://oneclickvakil.com</field>
        <field name="module_category">Legal/Legal</field>
        <field name="module_version">1.0.0</field>
        <field name="state">active</field>
    </record>

    <!-- Legal Document Request Form -->
    <record id="demo_legal_document_form" model="form.builder">
        <field name="name">Legal Document Request</field>
        <field name="code">legal_document_request</field>
        <field name="description">Request legal documents with AI-powered generation</field>
        <field name="template_id" ref="demo_legal_document_template"/>
        <field name="form_type">application</field>
        <field name="website_published">True</field>
        <field name="portal_access">True</field>
        <field name="api_enabled">True</field>
        <field name="create_sales_order">True</field>
        <field name="enable_payment">True</field>
        <field name="payment_required">True</field>
        <field name="enable_document_generation">True</field>
        <field name="enable_email_notifications">True</field>
        <field name="enable_whatsapp_notifications">True</field>
    </record>

    <!-- Field Definitions for Legal Document Request -->
    <record id="demo_field_client_name" model="field.definition">
        <field name="name">client_name</field>
        <field name="field_description">Client Name</field>
        <field name="field_type">char</field>
        <field name="required">True</field>
        <field name="sequence">10</field>
        <field name="form_builder_id" ref="demo_legal_document_form"/>
        <field name="template_id" ref="demo_legal_document_template"/>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="portal_visible">True</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_document_type" model="field.definition">
        <field name="name">document_type</field>
        <field name="field_description">Document Type</field>
        <field name="field_type">selection</field>
        <field name="required">True</field>
        <field name="sequence">20</field>
        <field name="form_builder_id" ref="demo_legal_document_form"/>
        <field name="template_id" ref="demo_legal_document_template"/>
        <field name="selection_options">[('contract', 'Contract'), ('agreement', 'Agreement'), ('notice', 'Legal Notice'), ('petition', 'Petition'), ('affidavit', 'Affidavit')]</field>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="portal_visible">True</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_urgency" model="field.definition">
        <field name="name">urgency</field>
        <field name="field_description">Urgency Level</field>
        <field name="field_type">selection</field>
        <field name="required">True</field>
        <field name="sequence">30</field>
        <field name="form_builder_id" ref="demo_legal_document_form"/>
        <field name="template_id" ref="demo_legal_document_template"/>
        <field name="selection_options">[('low', 'Low - 7 days'), ('medium', 'Medium - 3 days'), ('high', 'High - 1 day'), ('urgent', 'Urgent - Same day')]</field>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="portal_visible">True</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_description" model="field.definition">
        <field name="name">description</field>
        <field name="field_description">Document Description</field>
        <field name="field_type">text</field>
        <field name="required">True</field>
        <field name="sequence">40</field>
        <field name="form_builder_id" ref="demo_legal_document_form"/>
        <field name="template_id" ref="demo_legal_document_template"/>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="website_form_placeholder">Please describe the legal document you need...</field>
        <field name="portal_visible">True</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_supporting_docs" model="field.definition">
        <field name="name">supporting_documents</field>
        <field name="field_description">Supporting Documents</field>
        <field name="field_type">binary</field>
        <field name="required">False</field>
        <field name="sequence">50</field>
        <field name="form_builder_id" ref="demo_legal_document_form"/>
        <field name="template_id" ref="demo_legal_document_template"/>
        <field name="website_form_visible">True</field>
        <field name="portal_visible">True</field>
    </record>

    <!-- Workflow States -->
    <record id="demo_state_draft" model="workflow.state">
        <field name="name">Draft</field>
        <field name="code">draft</field>
        <field name="state_type">initial</field>
        <field name="is_default">True</field>
        <field name="sequence">10</field>
        <field name="template_id" ref="demo_legal_document_template"/>
    </record>

    <record id="demo_state_submitted" model="workflow.state">
        <field name="name">Submitted</field>
        <field name="code">submitted</field>
        <field name="state_type">intermediate</field>
        <field name="sequence">20</field>
        <field name="template_id" ref="demo_legal_document_template"/>
    </record>

    <record id="demo_state_payment_pending" model="workflow.state">
        <field name="name">Payment Pending</field>
        <field name="code">payment_pending</field>
        <field name="state_type">intermediate</field>
        <field name="sequence">30</field>
        <field name="template_id" ref="demo_legal_document_template"/>
    </record>

    <record id="demo_state_in_progress" model="workflow.state">
        <field name="name">In Progress</field>
        <field name="code">in_progress</field>
        <field name="state_type">intermediate</field>
        <field name="sequence">40</field>
        <field name="template_id" ref="demo_legal_document_template"/>
    </record>

    <record id="demo_state_completed" model="workflow.state">
        <field name="name">Completed</field>
        <field name="code">completed</field>
        <field name="state_type">final</field>
        <field name="sequence">50</field>
        <field name="template_id" ref="demo_legal_document_template"/>
    </record>

    <!-- Business Registration Template -->
    <record id="demo_business_registration_template" model="module.template">
        <field name="name">Business Registration System</field>
        <field name="code">business_registration</field>
        <field name="description">Complete business registration management system with document generation and compliance tracking.</field>
        <field name="template_type">form_based</field>
        <field name="module_prefix">ovakil</field>
        <field name="module_author">Oneclickvakil</field>
        <field name="module_website">https://oneclickvakil.com</field>
        <field name="module_category">Business/Business</field>
        <field name="module_version">1.0.0</field>
        <field name="state">validated</field>
    </record>

    <!-- Business Registration Form -->
    <record id="demo_business_registration_form" model="form.builder">
        <field name="name">Business Registration</field>
        <field name="code">business_registration</field>
        <field name="description">Register your business with automated document generation</field>
        <field name="template_id" ref="demo_business_registration_template"/>
        <field name="form_type">registration</field>
        <field name="website_published">True</field>
        <field name="portal_access">True</field>
        <field name="api_enabled">False</field>
        <field name="create_sales_order">True</field>
        <field name="enable_payment">True</field>
        <field name="payment_required">True</field>
        <field name="enable_document_generation">True</field>
        <field name="enable_email_notifications">True</field>
    </record>

    <!-- Field Definitions for Business Registration -->
    <record id="demo_field_business_name" model="field.definition">
        <field name="name">business_name</field>
        <field name="field_description">Business Name</field>
        <field name="field_type">char</field>
        <field name="required">True</field>
        <field name="sequence">10</field>
        <field name="form_builder_id" ref="demo_business_registration_form"/>
        <field name="template_id" ref="demo_business_registration_template"/>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="portal_visible">True</field>
    </record>

    <record id="demo_field_business_type" model="field.definition">
        <field name="name">business_type</field>
        <field name="field_description">Business Type</field>
        <field name="field_type">selection</field>
        <field name="required">True</field>
        <field name="sequence">20</field>
        <field name="form_builder_id" ref="demo_business_registration_form"/>
        <field name="template_id" ref="demo_business_registration_template"/>
        <field name="selection_options">[('sole_proprietorship', 'Sole Proprietorship'), ('partnership', 'Partnership'), ('llp', 'Limited Liability Partnership'), ('private_limited', 'Private Limited Company'), ('public_limited', 'Public Limited Company')]</field>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="portal_visible">True</field>
    </record>

    <record id="demo_field_registration_state" model="field.definition">
        <field name="name">registration_state</field>
        <field name="field_description">State of Registration</field>
        <field name="field_type">selection</field>
        <field name="required">True</field>
        <field name="sequence">30</field>
        <field name="form_builder_id" ref="demo_business_registration_form"/>
        <field name="template_id" ref="demo_business_registration_template"/>
        <field name="selection_options">[('maharashtra', 'Maharashtra'), ('delhi', 'Delhi'), ('karnataka', 'Karnataka'), ('gujarat', 'Gujarat'), ('tamil_nadu', 'Tamil Nadu')]</field>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="portal_visible">True</field>
    </record>

    <!-- Survey Template -->
    <record id="demo_customer_feedback_template" model="module.template">
        <field name="name">Customer Feedback System</field>
        <field name="code">customer_feedback</field>
        <field name="description">Customer feedback collection and analysis system with AI-powered insights.</field>
        <field name="template_type">survey</field>
        <field name="module_prefix">ovakil</field>
        <field name="module_author">Oneclickvakil</field>
        <field name="module_website">https://oneclickvakil.com</field>
        <field name="module_category">Marketing/Surveys</field>
        <field name="module_version">1.0.0</field>
        <field name="state">draft</field>
    </record>

    <!-- Customer Feedback Form -->
    <record id="demo_customer_feedback_form" model="form.builder">
        <field name="name">Customer Feedback</field>
        <field name="code">customer_feedback</field>
        <field name="description">Share your feedback to help us improve our services</field>
        <field name="template_id" ref="demo_customer_feedback_template"/>
        <field name="form_type">survey</field>
        <field name="website_published">True</field>
        <field name="portal_access">False</field>
        <field name="api_enabled">True</field>
        <field name="create_sales_order">False</field>
        <field name="enable_payment">False</field>
        <field name="enable_document_generation">False</field>
        <field name="enable_email_notifications">True</field>
        <field name="auto_confirm_submission">True</field>
    </record>

    <!-- Field Definitions for Customer Feedback -->
    <record id="demo_field_customer_email" model="field.definition">
        <field name="name">customer_email</field>
        <field name="field_description">Email Address</field>
        <field name="field_type">email</field>
        <field name="required">True</field>
        <field name="sequence">10</field>
        <field name="form_builder_id" ref="demo_customer_feedback_form"/>
        <field name="template_id" ref="demo_customer_feedback_template"/>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_satisfaction_rating" model="field.definition">
        <field name="name">satisfaction_rating</field>
        <field name="field_description">Overall Satisfaction</field>
        <field name="field_type">selection</field>
        <field name="required">True</field>
        <field name="sequence">20</field>
        <field name="form_builder_id" ref="demo_customer_feedback_form"/>
        <field name="template_id" ref="demo_customer_feedback_template"/>
        <field name="selection_options">[('1', 'Very Dissatisfied'), ('2', 'Dissatisfied'), ('3', 'Neutral'), ('4', 'Satisfied'), ('5', 'Very Satisfied')]</field>
        <field name="website_form_visible">True</field>
        <field name="website_form_required">True</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_feedback_comments" model="field.definition">
        <field name="name">comments</field>
        <field name="field_description">Additional Comments</field>
        <field name="field_type">text</field>
        <field name="required">False</field>
        <field name="sequence">30</field>
        <field name="form_builder_id" ref="demo_customer_feedback_form"/>
        <field name="template_id" ref="demo_customer_feedback_template"/>
        <field name="website_form_visible">True</field>
        <field name="website_form_placeholder">Please share any additional feedback...</field>
        <field name="api_visible">True</field>
    </record>

    <record id="demo_field_recommend" model="field.definition">
        <field name="name">would_recommend</field>
        <field name="field_description">Would you recommend us?</field>
        <field name="field_type">boolean</field>
        <field name="required">False</field>
        <field name="sequence">40</field>
        <field name="form_builder_id" ref="demo_customer_feedback_form"/>
        <field name="template_id" ref="demo_customer_feedback_template"/>
        <field name="website_form_visible">True</field>
        <field name="api_visible">True</field>
    </record>
</odoo>
