<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- Payment Integration for Customer Feedback (Premium Feedback Service) -->
        <record id="demo_payment_feedback_premium" model="payment.integration">
            <field name="name">Premium Feedback Analysis</field>
            <field name="form_builder_id" ref="demo_form_customer_feedback_main"/>
            <field name="pricing_model">fixed</field>
            <field name="fixed_amount">25.00</field>
            <field name="currency_id" ref="base.USD"/>
            <field name="payment_required">True</field>
            <field name="payment_timing">after_submit</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_1"/>
            <field name="payment_description">Premium feedback analysis with detailed AI insights and personalized response</field>
            <field name="success_message">Thank you for choosing our premium feedback service! You'll receive detailed analysis within 24 hours.</field>
            <field name="failure_message">Payment failed. Please try again or contact support.</field>
            <field name="active">True</field>
            <field name="sequence">10</field>
        </record>

        <!-- Payment Integration for Service Booking -->
        <record id="demo_payment_service_booking" model="payment.integration">
            <field name="name">Service Booking Payment</field>
            <field name="form_builder_id" ref="demo_form_service_booking_main"/>
            <field name="pricing_model">variable</field>
            <field name="pricing_rules">[
                {"service_type": "consultation", "amount": 150.00},
                {"service_type": "documentation", "amount": 300.00},
                {"service_type": "representation", "amount": 500.00},
                {"service_type": "advisory", "amount": 200.00},
                {"service_type": "other", "amount": 100.00}
            ]</field>
            <field name="currency_id" ref="base.USD"/>
            <field name="payment_required">True</field>
            <field name="payment_timing">before_confirmation</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_2"/>
            <field name="payment_description">Service booking fee - {service_type}</field>
            <field name="success_message">Payment successful! Your appointment is confirmed. You'll receive a confirmation email shortly.</field>
            <field name="failure_message">Payment failed. Your appointment is on hold. Please complete payment to confirm.</field>
            <field name="active">True</field>
            <field name="sequence">10</field>
        </record>

        <!-- Payment Integration for E-commerce Orders -->
        <record id="demo_payment_ecommerce_order" model="payment.integration">
            <field name="name">E-commerce Order Payment</field>
            <field name="form_builder_id" ref="demo_form_ecommerce_order"/>
            <field name="pricing_model">field_based</field>
            <field name="field_based_calculation">{
                "base_amount": 0,
                "fields": [
                    {"field": "product_quantity", "multiplier": "product_price"},
                    {"field": "shipping_cost", "multiplier": 1},
                    {"field": "tax_amount", "multiplier": 1}
                ],
                "discounts": [
                    {"condition": "order_total > 100", "type": "percentage", "value": 10},
                    {"condition": "customer_type == 'premium'", "type": "fixed", "value": 20}
                ]
            }</field>
            <field name="currency_id" ref="base.USD"/>
            <field name="payment_required">True</field>
            <field name="payment_timing">immediate</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_3"/>
            <field name="payment_description">E-commerce order payment - Order #{order_number}</field>
            <field name="success_message">Order placed successfully! You'll receive tracking information once your order ships.</field>
            <field name="failure_message">Payment failed. Please check your payment details and try again.</field>
            <field name="active">True</field>
            <field name="sequence">10</field>
        </record>

        <!-- Payment Integration for Service Preferences (Add-on Services) -->
        <record id="demo_payment_service_addons" model="payment.integration">
            <field name="name">Service Add-ons</field>
            <field name="form_builder_id" ref="demo_form_service_preferences"/>
            <field name="pricing_model">tier_based</field>
            <field name="tier_based_pricing">[
                {"tier": "basic", "amount": 50.00, "description": "Basic service package"},
                {"tier": "standard", "amount": 100.00, "description": "Standard service package with extras"},
                {"tier": "premium", "amount": 200.00, "description": "Premium service package with full support"}
            ]</field>
            <field name="currency_id" ref="base.USD"/>
            <field name="payment_required">False</field>
            <field name="payment_timing">optional</field>
            <field name="auto_create_sales_order">True</field>
            <field name="sales_order_product_id" ref="product.product_product_4"/>
            <field name="payment_description">Service add-ons and preferences - {selected_tier}</field>
            <field name="success_message">Add-on services activated! These will be included in your appointment.</field>
            <field name="failure_message">Payment for add-ons failed. Basic service will proceed as scheduled.</field>
            <field name="active">True</field>
            <field name="sequence">20</field>
        </record>

    </data>
</odoo>
