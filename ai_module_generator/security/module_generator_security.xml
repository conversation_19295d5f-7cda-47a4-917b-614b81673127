<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Groups for AI Module Generator -->

    <!-- Module Generator User Group -->
    <record id="group_module_generator_user" model="res.groups">
        <field name="name">Module Generator User</field>
        <field name="category_id" ref="base.module_category_tools"/>
        <field name="comment">Users can create and manage module templates and generate modules</field>
    </record>

    <!-- Module Generator Manager Group -->
    <record id="group_module_generator_manager" model="res.groups">
        <field name="name">Module Generator Manager</field>
        <field name="category_id" ref="base.module_category_tools"/>
        <field name="implied_ids" eval="[(4, ref('group_module_generator_user'))]"/>
        <field name="comment">Managers can install/uninstall generated modules and access all templates</field>
    </record>

    <!-- Module Generator Administrator Group -->
    <record id="group_module_generator_admin" model="res.groups">
        <field name="name">Module Generator Administrator</field>
        <field name="category_id" ref="base.module_category_tools"/>
        <field name="implied_ids" eval="[(4, ref('group_module_generator_manager'))]"/>
        <field name="comment">Administrators have full access to all module generator features</field>
    </record>

    <!-- Record Rules -->

    <!-- Module Template Rules -->
    <record id="module_template_rule_user" model="ir.rule">
        <field name="name">Module Template: User Access</field>
        <field name="model_id" ref="model_module_template"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_module_generator_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="module_template_rule_manager" model="ir.rule">
        <field name="name">Module Template: Manager Access</field>
        <field name="model_id" ref="model_module_template"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_module_generator_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Generated Module Rules -->
    <record id="generated_module_rule_user" model="ir.rule">
        <field name="name">Generated Module: User Access</field>
        <field name="model_id" ref="model_generated_module"/>
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_module_generator_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

    <record id="generated_module_rule_manager" model="ir.rule">
        <field name="name">Generated Module: Manager Access</field>
        <field name="model_id" ref="model_generated_module"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_module_generator_manager'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Form Builder Rules -->
    <record id="form_builder_rule_user" model="ir.rule">
        <field name="name">Form Builder: User Access</field>
        <field name="model_id" ref="model_form_builder"/>
        <field name="domain_force">[('template_id.company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_module_generator_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>

    <!-- Field Definition Rules -->
    <record id="field_definition_rule_user" model="ir.rule">
        <field name="name">Field Definition: User Access</field>
        <field name="model_id" ref="model_field_definition"/>
        <field name="domain_force">[('template_id.company_id', 'in', company_ids)]</field>
        <field name="groups" eval="[(4, ref('group_module_generator_user'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
    </record>


</odoo>
