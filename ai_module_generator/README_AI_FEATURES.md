# 🤖 AI Integration Features Guide

## 📋 Overview

The AI Module Generator now includes powerful AI integration features that allow you to create intelligent, AI-powered modules with dynamic content generation, analysis, and PDF creation capabilities.

## ✨ Key Features

### 🎯 AI System Prompts
- **Dynamic field variables** → Use any form field in AI prompts
- **Customizable buttons** → Configure button labels and icons
- **Multiple AI models** → Support for GPT, Claude, Gemini, and more
- **Chatbot integration** → Connect with existing ai_chatbot_integration

### 📄 PDF Generation
- **HTML to PDF** → Convert AI responses to professional PDFs
- **Admin controls** → Only authorized users can generate PDFs
- **Portal access** → Customers can download PDFs based on payment status

### 🔧 Portal Integration
- **Paid customers only** → Restrict PDF downloads to paid customers
- **All customers** → Allow all customers to access content
- **No portal access** → Backend only functionality

## 🚀 Quick Start Guide

### Step 1: Configure AI Prompt
1. Go to **AI Module Generator** → **AI Integration** → **AI Prompt Configurations**
2. Click **Create** to add new AI prompt
3. Fill in the configuration:
   - **Name**: Descriptive name (e.g., "Customer Feedback Analyzer")
   - **Form Builder**: Select the form this applies to
   - **Prompt Type**: Choose "System Prompt for Records"
   - **Chatbot Configuration**: Select from ai_chatbot_integration
   - **Button Label**: Text for the AI button (e.g., "Analyze Feedback")
   - **Button Icon**: Font Awesome icon (e.g., "fa-chart-line")

### Step 2: Write Your Prompt Template
Use field variables in curly braces to include record data:

```
Analyze this customer feedback:

Customer: {customer_name}
Email: {customer_email}
Rating: {overall_rating}
Comments: {feedback_comments}
Status: {state}

Please provide insights and recommendations.
```

### Step 3: Configure PDF & Portal Access
- **Enable PDF Generation**: Check to allow PDF creation
- **PDF Template Name**: Name for the PDF report
- **Portal Access**: Choose who can download PDFs
  - `paid_only`: Only customers who have paid
  - `all_customers`: All customers can access
  - `none`: Backend only

### Step 4: Test in Generated Modules
1. Open any record in your generated module
2. Look for the AI button (e.g., "Analyze Feedback")
3. Click the button to generate AI response
4. View the response in the "AI Response" tab
5. Generate PDF if enabled

## 📝 Field Variables Reference

### Available Variables
All form fields are automatically available as variables:

#### Text Fields
- `{name}` → Record name/title
- `{customer_name}` → Customer name
- `{customer_email}` → Email address
- `{feedback_comments}` → Text content

#### Number Fields
- `{payment_amount}` → Payment amount
- `{payment_rate}` → Hourly rate

#### Date Fields
- `{create_date}` → Creation date
- `{cash_collection_date}` → Collection date

#### Selection Fields
- `{overall_rating}` → Rating value
- `{priority}` → Priority level
- `{state}` → Current status
- `{payment_status}` → Payment status

#### Relationship Fields
- `{assigned_user}` → Assigned user name
- `{cash_collected_by}` → Collector name

### Usage Examples

#### Simple Summary
```
Customer {customer_name} submitted feedback with {overall_rating} rating.
Current status: {state}
```

#### Detailed Analysis
```
Analyze feedback from {customer_name} ({customer_email}):
- Rating: {overall_rating}/5 stars
- Priority: {priority}
- Payment: ${payment_amount} ({payment_status})
- Comments: {feedback_comments}

Provide recommendations for improvement.
```

#### Conditional Logic
```
Customer: {customer_name}
Status: {state}

Payment Information:
- Amount: ${payment_amount}
- Status: {payment_status}

Please provide appropriate response based on payment status.
```

## 🎯 Demo Examples

The system includes comprehensive demo data with real examples:

### 📊 Comprehensive Feedback Analysis
- **Purpose**: Detailed customer feedback analysis
- **Features**: Sentiment analysis, recommendations, priority assessment
- **PDF**: Enabled for paid customers
- **Button**: "Analyze Feedback" with chart icon

### 📝 Customer Response Generator
- **Purpose**: Generate professional customer responses
- **Features**: Personalized emails, internal notes, action items
- **PDF**: Enabled for all customers
- **Button**: "Generate Response" with reply icon

### 🎯 Service Consultation Assistant
- **Purpose**: Provide service consultation and recommendations
- **Features**: Budget analysis, timeline recommendations, checklists
- **PDF**: Enabled for paid customers
- **Button**: "Get Consultation" with lightbulb icon

### 📄 Legal Document Analyzer
- **Purpose**: Analyze legal requirements and compliance
- **Features**: Documentation checklists, regulatory compliance, risk assessment
- **PDF**: Enabled for paid customers
- **Button**: "Analyze Requirements" with gavel icon

### 📝 Simple Record Summary
- **Purpose**: Basic record summarization (learning example)
- **Features**: Simple format, easy to understand
- **PDF**: Disabled
- **Button**: "Summarize" with text icon

### 📚 Field Variables Tutorial
- **Purpose**: Documentation and examples of field variables
- **Features**: Complete reference, usage examples
- **PDF**: Enabled for all customers
- **Button**: "Show Variables" with code icon

## 🔧 Technical Implementation

### Generated Module Features
Every generated module automatically includes:

#### AI Response Fields
- `ai_response` → HTML field for AI content
- `ai_response_generated` → Boolean tracking
- `ai_response_pdf` → Binary PDF storage
- `ai_response_pdf_filename` → PDF filename
- `ai_last_prompt` → Last prompt used

#### AI Methods
- `action_get_ai_response()` → Generate AI response
- `_prepare_ai_data()` → Extract field values
- `action_generate_ai_pdf()` → Create PDF
- `_generate_pdf_from_html()` → Convert to PDF

#### Form View Enhancements
- AI response buttons in header
- Dynamic button visibility
- AI Response notebook tab
- PDF download functionality

## 🎨 Customization Options

### Button Configuration
- **Label**: Custom text for the button
- **Icon**: Font Awesome icon class
- **Visibility**: Based on AI response status

### PDF Options
- **Template Name**: Custom PDF title
- **Portal Access**: Control customer access
- **Admin Only**: Backend PDF generation

### AI Model Selection
- **GPT Models**: OpenAI GPT-3.5, GPT-4
- **Claude Models**: Anthropic Claude-3
- **Gemini Models**: Google Gemini Pro
- **Custom Models**: Via chatbot integration

## 🚀 Best Practices

### Writing Effective Prompts
1. **Be specific** → Clear instructions get better results
2. **Use context** → Include relevant field variables
3. **Structure output** → Request specific formats
4. **Test thoroughly** → Use demo data to validate

### Field Variable Usage
1. **Check field names** → Use exact field names from form
2. **Handle empty values** → AI handles missing data gracefully
3. **Use meaningful context** → Explain what each field represents

### PDF Generation
1. **Enable selectively** → Not all prompts need PDFs
2. **Consider portal access** → Match business requirements
3. **Test PDF output** → Verify formatting and content

## 📞 Support & Troubleshooting

### Common Issues
1. **No AI button** → Check prompt configuration and form builder reference
2. **Empty response** → Verify chatbot configuration and API keys
3. **PDF generation fails** → Check user permissions and content format

### Getting Help
- Check demo configurations for examples
- Review field variables tutorial
- Test with simple prompts first
- Verify chatbot integration setup

## 🎉 Conclusion

The AI integration features provide powerful capabilities for creating intelligent, responsive modules that can analyze data, generate insights, and create professional reports automatically. Use the demo examples as starting points and customize them for your specific needs.
