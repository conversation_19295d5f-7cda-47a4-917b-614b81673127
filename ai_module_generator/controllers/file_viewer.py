from odoo import http, _
from odoo.http import request
import os
import mimetypes
import base64
import logging

_logger = logging.getLogger(__name__)


class ModuleFileViewer(http.Controller):
    """Controller for viewing generated module files"""

    @http.route('/module_generator/view_files/<int:module_id>', type='http', auth='user', website=True)
    def view_module_files(self, module_id, **kwargs):
        """Display the file browser for a generated module"""
        try:
            # Get the generated module record
            generated_module = request.env['generated.module'].sudo().browse(module_id)
            if not generated_module.exists():
                return request.render('website.404')

            # Get the module path
            module_path = generated_module.module_path
            if not module_path or not os.path.exists(module_path):
                return request.render('ai_module_generator.file_viewer_error', {
                    'error_message': _('Module files not found. The module may have been moved or deleted.'),
                    'module_name': generated_module.name
                })

            # Get file structure
            file_structure = self._get_file_structure(module_path)

            values = {
                'module': generated_module,
                'file_structure': file_structure,
                'module_path': module_path,
            }

            return request.render('ai_module_generator.file_viewer', values)

        except Exception as e:
            _logger.error(f"Error viewing module files: {str(e)}")
            return request.render('ai_module_generator.file_viewer_error', {
                'error_message': _('An error occurred while loading the module files.'),
                'module_name': f'Module {module_id}'
            })

    @http.route('/module_generator/view_file/<int:module_id>', type='http', auth='user', website=True)
    def view_file_content(self, module_id, file_path=None, **kwargs):
        """Display the content of a specific file"""
        try:
            # Get the generated module record
            generated_module = request.env['generated.module'].sudo().browse(module_id)
            if not generated_module.exists():
                return request.render('website.404')

            if not file_path:
                return request.redirect(f'/module_generator/view_files/{module_id}')

            # Security check: ensure file is within module directory
            module_path = generated_module.module_path
            full_file_path = os.path.join(module_path, file_path)

            # Normalize paths to prevent directory traversal
            module_path = os.path.abspath(module_path)
            full_file_path = os.path.abspath(full_file_path)

            if not full_file_path.startswith(module_path):
                return request.render('ai_module_generator.file_viewer_error', {
                    'error_message': _('Access denied: File is outside module directory.'),
                    'module_name': generated_module.name
                })

            if not os.path.exists(full_file_path) or not os.path.isfile(full_file_path):
                return request.render('ai_module_generator.file_viewer_error', {
                    'error_message': _('File not found: %s') % file_path,
                    'module_name': generated_module.name
                })

            # Read file content
            try:
                with open(full_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # Handle binary files
                with open(full_file_path, 'rb') as f:
                    content = base64.b64encode(f.read()).decode('utf-8')
                    is_binary = True
            else:
                is_binary = False

            # Get file info
            file_info = {
                'name': os.path.basename(full_file_path),
                'path': file_path,
                'size': os.path.getsize(full_file_path),
                'is_binary': is_binary,
                'mime_type': mimetypes.guess_type(full_file_path)[0] or 'text/plain',
            }

            values = {
                'module': generated_module,
                'file_info': file_info,
                'file_content': content,
                'file_path': file_path,
            }

            return request.render('ai_module_generator.file_content_viewer', values)

        except Exception as e:
            _logger.error(f"Error viewing file content: {str(e)}")
            return request.render('ai_module_generator.file_viewer_error', {
                'error_message': _('An error occurred while loading the file.'),
                'module_name': f'Module {module_id}'
            })

    @http.route('/module_generator/download_file/<int:module_id>', type='http', auth='user')
    def download_file(self, module_id, file_path=None, **kwargs):
        """Download a specific file from the generated module"""
        try:
            # Get the generated module record
            generated_module = request.env['generated.module'].sudo().browse(module_id)
            if not generated_module.exists():
                return request.not_found()

            if not file_path:
                return request.not_found()

            # Security check: ensure file is within module directory
            module_path = generated_module.module_path
            full_file_path = os.path.join(module_path, file_path)

            # Normalize paths to prevent directory traversal
            module_path = os.path.abspath(module_path)
            full_file_path = os.path.abspath(full_file_path)

            if not full_file_path.startswith(module_path) or not os.path.exists(full_file_path):
                return request.not_found()

            # Read file and return as download
            with open(full_file_path, 'rb') as f:
                file_data = f.read()

            filename = os.path.basename(full_file_path)
            mime_type = mimetypes.guess_type(full_file_path)[0] or 'application/octet-stream'

            return request.make_response(
                file_data,
                headers=[
                    ('Content-Type', mime_type),
                    ('Content-Disposition', f'attachment; filename="{filename}"'),
                    ('Content-Length', len(file_data)),
                ]
            )

        except Exception as e:
            _logger.error(f"Error downloading file: {str(e)}")
            return request.not_found()

    @http.route('/module_generator/download_module/<int:module_id>', type='http', auth='user')
    def download_module(self, module_id, **kwargs):
        """Download the entire generated module as a ZIP file"""
        try:
            import zipfile
            import tempfile

            # Get the generated module record
            generated_module = request.env['generated.module'].sudo().browse(module_id)
            if not generated_module.exists():
                return request.not_found()

            module_path = generated_module.module_path
            if not module_path or not os.path.exists(module_path):
                return request.not_found()

            # Create a temporary ZIP file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_zip:
                with zipfile.ZipFile(temp_zip.name, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # Add all files from the module directory
                    for root, dirs, files in os.walk(module_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            # Calculate relative path for the ZIP
                            arcname = os.path.relpath(file_path, os.path.dirname(module_path))
                            zipf.write(file_path, arcname)

                # Read the ZIP file content
                with open(temp_zip.name, 'rb') as f:
                    zip_data = f.read()

                # Clean up temporary file
                os.unlink(temp_zip.name)

                filename = f"{generated_module.code}.zip"

                return request.make_response(
                    zip_data,
                    headers=[
                        ('Content-Type', 'application/zip'),
                        ('Content-Disposition', f'attachment; filename="{filename}"'),
                        ('Content-Length', len(zip_data)),
                    ]
                )

        except Exception as e:
            _logger.error(f"Error downloading module: {str(e)}")
            return request.not_found()

    def _get_file_structure(self, module_path, max_depth=3, current_depth=0):
        """Get the file structure of the module directory"""
        if current_depth >= max_depth:
            return []

        structure = []
        try:
            for item in sorted(os.listdir(module_path)):
                if item.startswith('.'):
                    continue

                item_path = os.path.join(module_path, item)
                relative_path = os.path.relpath(item_path, module_path)

                if os.path.isdir(item_path):
                    # Directory
                    children = self._get_file_structure(item_path, max_depth, current_depth + 1)
                    structure.append({
                        'name': item,
                        'type': 'directory',
                        'path': relative_path,
                        'children': children,
                        'size': len(children)
                    })
                else:
                    # File
                    try:
                        size = os.path.getsize(item_path)
                        mime_type = mimetypes.guess_type(item_path)[0] or 'text/plain'
                        structure.append({
                            'name': item,
                            'type': 'file',
                            'path': relative_path,
                            'size': size,
                            'mime_type': mime_type,
                            'is_text': mime_type.startswith('text/') or item.endswith(('.py', '.xml', '.js', '.css', '.md', '.txt', '.json', '.yml', '.yaml'))
                        })
                    except OSError:
                        # Skip files that can't be accessed
                        continue

        except OSError:
            # Skip directories that can't be accessed
            pass

        return structure
