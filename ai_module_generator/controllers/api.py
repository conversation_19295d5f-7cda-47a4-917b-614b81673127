from odoo import http
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)


class ModuleGeneratorAPI(http.Controller):
    """API Controller for Module Generator helper endpoints"""

    @http.route('/api/users/search', type='http', auth='public', methods=['GET'], csrf=False)
    def search_users(self, **kwargs):
        """Search users for many2one fields"""
        try:
            query = kwargs.get('query', '')
            limit = int(kwargs.get('limit', 10))
            
            domain = [('active', '=', True)]
            if query:
                domain.append('|')
                domain.append(('name', 'ilike', query))
                domain.append(('email', 'ilike', query))
            
            users = request.env['res.users'].sudo().search(domain, limit=limit)
            
            result = {
                'success': True,
                'data': [{
                    'id': user.id,
                    'name': user.name,
                    'email': user.email or '',
                    'display_name': f"{user.name} ({user.email})" if user.email else user.name
                } for user in users]
            }
            
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/users/list', type='http', auth='public', methods=['GET'], csrf=False)
    def list_users(self, **kwargs):
        """List all active users for dropdown"""
        try:
            limit = int(kwargs.get('limit', 50))
            
            users = request.env['res.users'].sudo().search([
                ('active', '=', True)
            ], limit=limit, order='name')
            
            result = {
                'success': True,
                'data': [{
                    'id': user.id,
                    'name': user.name,
                    'email': user.email or '',
                    'display_name': f"{user.name} ({user.email})" if user.email else user.name
                } for user in users]
            }
            
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/partners/search', type='http', auth='public', methods=['GET'], csrf=False)
    def search_partners(self, **kwargs):
        """Search partners for many2one fields"""
        try:
            query = kwargs.get('query', '')
            limit = int(kwargs.get('limit', 10))
            
            domain = [('active', '=', True)]
            if query:
                domain.append('|')
                domain.append(('name', 'ilike', query))
                domain.append(('email', 'ilike', query))
            
            partners = request.env['res.partner'].sudo().search(domain, limit=limit)
            
            result = {
                'success': True,
                'data': [{
                    'id': partner.id,
                    'name': partner.name,
                    'email': partner.email or '',
                    'display_name': f"{partner.name} ({partner.email})" if partner.email else partner.name
                } for partner in partners]
            }
            
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/partners/list', type='http', auth='public', methods=['GET'], csrf=False)
    def list_partners(self, **kwargs):
        """List all active partners for dropdown"""
        try:
            limit = int(kwargs.get('limit', 50))
            
            partners = request.env['res.partner'].sudo().search([
                ('active', '=', True)
            ], limit=limit, order='name')
            
            result = {
                'success': True,
                'data': [{
                    'id': partner.id,
                    'name': partner.name,
                    'email': partner.email or '',
                    'display_name': f"{partner.name} ({partner.email})" if partner.email else partner.name
                } for partner in partners]
            }
            
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/model/search', type='http', auth='public', methods=['GET'], csrf=False)
    def search_model_records(self, **kwargs):
        """Generic search for any model records"""
        try:
            model_name = kwargs.get('model', '')
            query = kwargs.get('query', '')
            limit = int(kwargs.get('limit', 10))
            search_field = kwargs.get('search_field', 'name')
            
            if not model_name:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Model name is required'}),
                    headers=[('Content-Type', 'application/json')]
                )
            
            # Check if model exists
            try:
                model = request.env[model_name].sudo()
            except KeyError:
                return request.make_response(
                    json.dumps({'success': False, 'error': f'Model {model_name} not found'}),
                    headers=[('Content-Type', 'application/json')]
                )
            
            # Build domain
            domain = []
            if hasattr(model, 'active'):
                domain.append(('active', '=', True))
            
            if query and search_field:
                domain.append((search_field, 'ilike', query))
            
            records = model.search(domain, limit=limit)
            
            result = {
                'success': True,
                'data': [{
                    'id': record.id,
                    'name': getattr(record, search_field, str(record)),
                    'display_name': record.display_name if hasattr(record, 'display_name') else str(record)
                } for record in records]
            }
            
            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )
