from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
import os
import shutil
import logging

_logger = logging.getLogger(__name__)


class ModuleGeneratorWizard(models.TransientModel):
    """
    Wizard for generating complete Odoo modules from templates
    """
    _name = 'module.generator.wizard'
    _description = 'Module Generator Wizard'

    # Template Selection
    template_id = fields.Many2one('module.template', string='Module Template',
                                 required=True, help="Select the template to generate module from")

    # Module Configuration
    module_name = fields.Char(string='Module Name', required=True,
                             help="Technical name of the module (will be prefixed with 'ovakil_')")
    module_title = fields.Char(string='Module Title', required=True,
                              help="Human-readable title for the module")
    module_description = fields.Text(string='Module Description',
                                   help="Detailed description of the module")
    module_version = fields.Char(string='Version', default='********.0',
                               help="Module version")
    module_author = fields.Char(string='Author', default='Oneclickvakil',
                               help="Module author")
    module_category = fields.Char(string='Category', default='Custom',
                                 help="Module category")

    # Generation Options
    include_demo_data = fields.Boolean(string='Include Demo Data', default=True,
                                     help="Include demo data in the generated module")
    include_tests = fields.Boolean(string='Include Tests', default=True,
                                 help="Include test files in the generated module")
    include_website = fields.Boolean(string='Include Website Integration', default=True,
                                   help="Include website forms and controllers")
    include_portal = fields.Boolean(string='Include Portal Integration', default=True,
                                  help="Include customer portal integration")
    include_api = fields.Boolean(string='Include API Endpoints', default=True,
                               help="Include REST API endpoints")

    # Advanced Options
    custom_prefix = fields.Char(string='Custom Prefix', default='ovakil',
                               help="Custom prefix for the module name")
    target_directory = fields.Char(string='Target Directory',
                                  default='/mnt/extra-addons',
                                  help="Directory where the module will be generated")

    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('error', 'Error'),
    ], string='State', default='draft')

    generation_log = fields.Text(string='Generation Log', readonly=True,
                                help="Log of the generation process")
    generated_module_path = fields.Char(string='Generated Module Path', readonly=True,
                                       help="Path to the generated module")
    module_installed = fields.Boolean(string='Module Installed', default=False, readonly=True,
                                     help="Whether the generated module has been installed")

    @api.onchange('template_id')
    def _onchange_template_id(self):
        """Auto-fill fields based on template"""
        if self.template_id:
            self.module_title = self.template_id.name
            self.module_description = self.template_id.description
            if self.template_id.code:
                # Remove any existing prefix and use the template code
                code = self.template_id.code
                if code.startswith('ovakil_'):
                    code = code[7:]  # Remove 'ovakil_' prefix
                self.module_name = code
            # Set author and other defaults from template
            if self.template_id.module_author:
                self.module_author = self.template_id.module_author
            if self.template_id.module_category:
                self.module_category = self.template_id.module_category

    @api.onchange('module_title')
    def _onchange_module_title(self):
        """Auto-generate module name from title"""
        if self.module_title and not self.module_name:
            # Convert title to module name format
            name = self.module_title.lower().replace(' ', '_')
            # Remove special characters
            name = ''.join(c for c in name if c.isalnum() or c == '_')
            self.module_name = name

    @api.constrains('module_name')
    def _check_module_name(self):
        """Validate module name"""
        for record in self:
            if record.module_name:
                if not record.module_name.replace('_', '').isalnum():
                    raise ValidationError(_("Module name must contain only letters, numbers, and underscores"))
                if not record.module_name.islower():
                    raise ValidationError(_("Module name must be lowercase"))

    def action_generate_module(self):
        """Generate the module using the core generation engine"""
        self.ensure_one()

        try:
            self.state = 'generating'
            self.generation_log = "Starting advanced module generation...\n"

            # Validate inputs
            self._validate_inputs()

            # Prepare module configuration
            module_config = self._prepare_module_config()
            self.generation_log += "Prepared module configuration\n"

            # Use direct generation approach (bypassing complex Jinja2 templates)
            self.generation_log += "Using direct generation approach...\n"

            # Generate full module name with prefix
            full_module_name = module_config['module_name']
            module_path = os.path.join(self.target_directory, full_module_name)

            self.generation_log += f"Generating module: {full_module_name}\n"
            self.generation_log += f"Target path: {module_path}\n"

            # Check if module already exists
            if os.path.exists(module_path):
                self.generation_log += f"Removing existing module directory: {module_path}\n"
                shutil.rmtree(module_path)

            # Create module directory
            os.makedirs(module_path, exist_ok=True)
            self.generation_log += "Created module directory\n"

            # Generate module files using direct approach
            self._generate_manifest(module_path, full_module_name)
            self._generate_init_file(module_path)
            self._generate_models(module_path)
            self._generate_views(module_path)
            self._generate_reports(module_path)
            self._generate_security(module_path)
            self._generate_data(module_path)

            if self.include_website:
                self._generate_controllers(module_path)
                self._generate_templates(module_path)
                self._generate_enhanced_static_files(module_path)

            # Always generate portal controllers for customer access
            self._generate_portal_controllers(module_path)

            # Store the generated module path for later use
            self.generated_module_path = module_path

            if self.include_tests:
                self._generate_tests(module_path)

            if self.include_demo_data:
                self._generate_demo_data(module_path)

            self.generation_log += f"Direct generation completed successfully\n"

            # Create a mock generation result for compatibility
            generation_result = {
                'success': True,
                'module_path': module_path,
                'message': 'Module generated successfully using direct approach'
            }

            # Create generated module record
            self._create_generated_module_record(module_config, generation_result)

            # Update template generation count
            self.template_id.generation_count += 1

            self.state = 'completed'
            self.generation_log += "\n🎉 Advanced module generation completed successfully!\n"
            self.generation_log += f"Module generated at: {self.generated_module_path}\n"
            self.generation_log += "\n📋 Generated Features:\n"
            self.generation_log += "✅ Complete models with template fields\n"
            self.generation_log += "✅ Workflow states and transitions\n"
            self.generation_log += "✅ Website forms and controllers\n"
            self.generation_log += "✅ Customer portal integration\n"
            self.generation_log += "✅ REST API endpoints\n"

            if self._has_ai_integration():
                self.generation_log += "✅ AI integration and automation\n"
            if self._has_payment_integration():
                self.generation_log += "✅ Payment processing workflows\n"
            if self.include_tests:
                self.generation_log += "✅ Automated testing framework\n"

            self.generation_log += "\n🚀 Next steps:\n"
            self.generation_log += "1. Restart Odoo server\n"
            self.generation_log += "2. Update module list\n"
            self.generation_log += "3. Install the new module\n"
            self.generation_log += "4. Configure AI and payment settings (if applicable)\n"

            return {
                'type': 'ir.actions.act_window',
                'name': 'Advanced Module Generation Complete',
                'res_model': 'module.generator.wizard',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'new',
                'context': {'default_state': 'completed'}
            }

        except Exception as e:
            self.state = 'error'
            self.generation_log += f"\n❌ ERROR: {str(e)}\n"
            _logger.error("Advanced module generation failed: %s", str(e))
            raise UserError(_("Module generation failed: %s") % str(e))

    def action_update_apps_list(self):
        """Update the apps list to include newly generated module"""
        self.ensure_one()

        try:
            # Update the module list
            self.env['ir.module.module'].update_list()

            # Find the generated module
            module_code = f"{self.custom_prefix}_{self.module_name}"
            module = self.env['ir.module.module'].search([('name', '=', module_code)], limit=1)

            if module:
                self.generation_log += f"✅ Module '{module_code}' found in apps list\n"
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Success',
                        'message': f"Module '{module_code}' is now available in the apps list!",
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                self.generation_log += f"❌ Module '{module_code}' not found in apps list\n"
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Warning',
                        'message': f"Module '{module_code}' was not found in the apps list. Please check the module path and try again.",
                        'type': 'warning',
                        'sticky': True,
                    }
                }

        except Exception as e:
            self.generation_log += f"❌ Error updating apps list: {str(e)}\n"
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': f"Failed to update apps list: {str(e)}",
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_install_module(self):
        """Install the generated module"""
        self.ensure_one()

        if self.state != 'completed':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Warning',
                    'message': "Please generate the module first before installing.",
                    'type': 'warning',
                    'sticky': False,
                }
            }

        try:
            # First update the apps list
            self.env['ir.module.module'].update_list()

            # Find the generated module
            module_code = f"{self.custom_prefix}_{self.module_name}"
            module = self.env['ir.module.module'].search([('name', '=', module_code)], limit=1)

            if not module:
                self.generation_log += f"❌ Module '{module_code}' not found in apps list\n"
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Error',
                        'message': f"Module '{module_code}' not found. Please update apps list first.",
                        'type': 'danger',
                        'sticky': True,
                    }
                }

            # Check if module is already installed
            if module.state == 'installed':
                self.generation_log += f"ℹ️ Module '{module_code}' is already installed\n"
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Info',
                        'message': f"Module '{module_code}' is already installed!",
                        'type': 'info',
                        'sticky': False,
                    }
                }

            # Install the module
            self.generation_log += f"🔄 Installing module '{module_code}'...\n"

            # Use sudo() to avoid permission issues and commit the transaction
            module.sudo().button_immediate_install()

            # Commit the transaction to ensure installation is complete
            self.env.cr.commit()

            self.generation_log += f"✅ Module '{module_code}' installed successfully!\n"

            # Update wizard state
            self.module_installed = True

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Success',
                    'message': f"Module '{module_code}' has been installed successfully!",
                    'type': 'success',
                    'sticky': False,
                }
            }

        except Exception as e:
            self.generation_log += f"❌ Error installing module: {str(e)}\n"
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': f"Failed to install module: {str(e)}",
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_open_installed_module(self):
        """Open the installed module in Apps"""
        self.ensure_one()

        module_code = f"{self.custom_prefix}_{self.module_name}"
        module = self.env['ir.module.module'].search([('name', '=', module_code)], limit=1)

        if not module:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': f"Module '{module_code}' not found.",
                    'type': 'danger',
                    'sticky': False,
                }
            }

        return {
            'type': 'ir.actions.act_window',
            'name': f'Module: {module.shortdesc}',
            'res_model': 'ir.module.module',
            'res_id': module.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def _validate_inputs(self):
        """Validate wizard inputs"""
        if not self.template_id:
            raise UserError(_("Please select a template"))
        if not self.module_name:
            raise UserError(_("Please enter a module name"))
        if not self.module_title:
            raise UserError(_("Please enter a module title"))

        # Validate template has required components
        if not self.template_id.form_builder_ids:
            raise UserError(_("Selected template has no form builders configured"))
        if not self.template_id.workflow_state_ids:
            raise UserError(_("Selected template has no workflow states configured"))

    def _prepare_module_config(self):
        """Prepare module configuration for the core generator"""
        return {
            'module_name': f"{self.custom_prefix}_{self.module_name}",
            'module_title': self.module_title,
            'module_description': self.module_description or self.module_title,
            'module_version': self.module_version,
            'module_author': self.module_author,
            'module_category': self.module_category,
            'target_directory': self.target_directory,

            # Generation options
            'include_demo_data': self.include_demo_data,
            'include_tests': self.include_tests,
            'include_website': self.include_website,
            'include_portal': self.include_portal,
            'include_api': self.include_api,

            # Advanced features
            'enable_ai_integration': self._has_ai_integration(),
            'enable_payment_integration': self._has_payment_integration(),
            'enable_whatsapp_notifications': True,  # Always enable for generated modules
            'enable_email_notifications': True,     # Always enable for generated modules

            # Template data
            'template_type': self.template_id.template_type,
            'forms': self._prepare_forms_config(),
            'workflow_states': self._prepare_workflow_config(),
            'ai_prompts': self._prepare_ai_config(),
            'payment_config': self._prepare_payment_config(),
        }

    def _prepare_forms_config(self):
        """Prepare forms configuration"""
        forms_config = []
        for form in self.template_id.form_builder_ids:
            form_config = {
                'name': form.name,
                'code': form.code,
                'description': form.description,
                'form_type': form.form_type,
                'model_name': form.model_name,
                'fields': self._prepare_fields_config(form),
                'portal_visible': form.portal_access,  # Use correct field name
                'website_visible': form.website_published,  # Use correct field name
                'api_enabled': form.api_enabled,
            }
            forms_config.append(form_config)
        return forms_config

    def _prepare_fields_config(self, form):
        """Prepare fields configuration for a form"""
        fields_config = []
        for field in form.field_definition_ids.sorted('sequence'):
            field_config = {
                'name': field.name,
                'field_description': field.field_description,
                'field_type': field.field_type,
                'required': field.required,
                'readonly': field.readonly,
                'help_text': field.help_text,
                'default_value': field.default_value,
                'selection_options': field.selection_options,
                'relation_model': field.relation_model,
                'sequence': field.sequence,
                'portal_visible': field.portal_visible,
                'api_visible': field.api_visible,
            }
            fields_config.append(field_config)
        return fields_config

    def _prepare_workflow_config(self):
        """Prepare workflow states configuration"""
        workflow_config = []
        for state in self.template_id.workflow_state_ids.sorted('sequence'):
            state_config = {
                'name': state.name,
                'code': state.code,
                'description': state.description,
                'state_type': state.state_type,
                'is_default': state.is_default,
                'is_readonly': state.is_readonly,
                'is_portal_visible': state.is_portal_visible,
                'sequence': state.sequence,
                'email_template_id': state.email_template_id.id if state.email_template_id else False,
                'whatsapp_template': state.whatsapp_template,
                'entry_action': state.entry_action,
                'exit_action': state.exit_action,
                'color': state.color,
                'icon': state.icon,
            }
            workflow_config.append(state_config)
        return workflow_config

    def _has_ai_integration(self):
        """Check if template has AI integration"""
        for form in self.template_id.form_builder_ids:
            if form.ai_prompt_config_ids:
                return True
        return False

    def _has_payment_integration(self):
        """Check if template has payment integration"""
        # For now, return False as payment integration is not yet implemented
        # This will be updated when payment.config model is created
        return False

    def _prepare_ai_config(self):
        """Prepare AI integration configuration"""
        ai_config = []
        for form in self.template_id.form_builder_ids:
            for ai_prompt in form.ai_prompt_config_ids:
                prompt_config = {
                    'name': ai_prompt.name,
                    'form_code': form.code,
                    'prompt_type': ai_prompt.prompt_type,
                    'prompt_template': ai_prompt.prompt_template,
                    'system_prompt': ai_prompt.system_prompt,
                    'ai_model': ai_prompt.ai_model,
                    'max_tokens': ai_prompt.max_tokens,
                    'temperature': ai_prompt.temperature,
                    'trigger_event': ai_prompt.trigger_event,
                    'output_format': ai_prompt.output_format,
                    'auto_execute': ai_prompt.auto_execute,
                }
                ai_config.append(prompt_config)
        return ai_config

    def _prepare_payment_config(self):
        """Prepare payment integration configuration"""
        payment_config = []
        # Payment configuration will be implemented when payment.config model is created
        # For now, return empty list
        return payment_config

    def _create_generated_module_record(self, module_config, generation_result):
        """Create a record of the generated module"""
        generated_module = self.env['generated.module'].create({
            'name': module_config['module_title'],
            'code': module_config['module_name'],
            'description': module_config['module_description'],
            'template_id': self.template_id.id,
            'generation_config': str(module_config),
            'module_path': self.generated_module_path,
            'state': 'generated',
            'version': module_config['module_version'],
            # Note: author is stored in generation_config, not as a separate field
        })

        self.generation_log += f"Created generated module record: {generated_module.id}\n"
        return generated_module

    def _generate_manifest(self, module_path, module_name):
        """Generate __manifest__.py file"""
        self.generation_log += "Generating manifest file...\n"

        # Prepare demo data list based on include_demo_data flag
        demo_data = []
        if self.include_demo_data:
            demo_data = ['demo/demo.xml']

        # Prepare data list based on options (correct loading order)
        data_files = [
            'security/ir.model.access.csv',  # Load security first
            'data/data.xml',
            'data/cron_jobs.xml',  # Add cron jobs file
            'views/views.xml',  # Load views after data
        ]

        # Add report files dynamically based on forms
        if self.template_id and self.template_id.form_builder_ids:
            for form in self.template_id.form_builder_ids:
                data_files.insert(-1, f'reports/{form.code}_report.xml')  # Insert before views

        if self.include_website:
            data_files.append('views/website_templates.xml')

        # Add portal templates for each form
        if self.template_id and self.template_id.form_builder_ids:
            for form in self.template_id.form_builder_ids:
                if form.portal_access:
                    data_files.append(f'views/portal/{form.code}_portal.xml')
                    data_files.append(f'views/portal/{form.code}_portal_menu.xml')

        # Generate assets section for website modules
        assets_section = ""
        if self.include_website:
            module_name = module_name.replace('_', '_')  # Ensure proper module name format
            assets_section = f"""
    'assets': {{
        'web.assets_frontend': [
            '{module_name}/static/src/css/enhanced_forms.css',
            '{module_name}/static/src/js/enhanced_forms.js',
        ],
    }},"""

        # Add post_init_hook to ensure proper database ownership
        post_init_hook = ""
        if self.template_id and any(field.field_type == 'many2many' for form in self.template_id.form_builder_ids for field in form.field_definition_ids):
            post_init_hook = """
    'post_init_hook': 'post_init_hook',"""

        manifest_content = f'''{{
    'name': '{self.module_title}',
    'version': '{self.module_version}',
    'category': '{self.module_category}',
    'summary': '{self.module_title}',
    'description': """{self.module_description or self.module_title}""",
    'author': '{self.module_author}',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': ['base', 'web', 'mail', 'portal', 'sale', 'website', 'project', 'account', 'payment', 'ai_module_generator', 'ai_chatbot_integration', 'ai_cash_management'],
    'data': {data_files},
    'demo': {demo_data},{assets_section}{post_init_hook}
    'installable': True,
    'auto_install': False,
    'application': True,
}}'''

        with open(os.path.join(module_path, '__manifest__.py'), 'w') as f:
            f.write(manifest_content)

    def _generate_init_file(self, module_path):
        """Generate __init__.py file"""
        self.generation_log += "Generating init file...\n"

        init_content = "from . import models"
        if self.include_website:
            init_content += "\nfrom . import controllers"

        # Add post_init_hook if module has many2many fields
        if self.template_id and any(field.field_type == 'many2many' for form in self.template_id.form_builder_ids for field in form.field_definition_ids):
            module_name = self.module_name
            init_content += f'''

# Post-installation hook to ensure proper database setup
def post_init_hook(cr, registry):
    """Post-installation hook to ensure proper database ownership and setup"""
    import logging
    _logger = logging.getLogger(__name__)

    try:
        # Ensure all tables have proper ownership
        cr.execute("""
            SELECT tablename
            FROM pg_tables
            WHERE schemaname = 'public'
            AND tablename LIKE %s
        """, ('{module_name.replace(".", "_")}%',))

        tables = cr.fetchall()
        for table in tables:
            table_name = table[0]
            try:
                # Ensure table is owned by the correct user (odoo)
                cr.execute(f"ALTER TABLE {{table_name}} OWNER TO odoo")
                _logger.info(f"Updated ownership for table: {{table_name}}")
            except Exception as e:
                _logger.warning(f"Could not update ownership for table {{table_name}}: {{e}}")

    except Exception as e:
        _logger.error(f"Error in post_init_hook: {{e}}")
'''

        with open(os.path.join(module_path, '__init__.py'), 'w') as f:
            f.write(init_content)

    def _generate_models(self, module_path):
        """Generate models directory and files based on template configuration"""
        self.generation_log += "Generating models from template configuration...\n"

        models_dir = os.path.join(module_path, 'models')
        os.makedirs(models_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            raise UserError(_("No template selected"))

        # Generate models/__init__.py with all form models
        init_imports = []
        for form in template.form_builder_ids:
            init_imports.append(f"from . import {form.code}")

        init_content = "\n".join(init_imports) if init_imports else "# No models to import"
        with open(os.path.join(models_dir, '__init__.py'), 'w') as f:
            f.write(init_content)

        # Generate a model for each form in the template
        for form in template.form_builder_ids:
            self._generate_form_model(models_dir, form, template)

    def _generate_form_model(self, models_dir, form, template):
        """Generate a model file for a specific form"""
        self.generation_log += f"Generating model for form: {form.name}...\n"

        # Prepare model data
        model_name = form.model_name
        class_name = ''.join(word.capitalize() for word in form.code.split('_'))

        # Get workflow states from template
        workflow_states = template.workflow_state_ids.sorted('sequence')
        default_state = workflow_states[0].code if workflow_states else 'draft'

        # Generate state selection
        state_selection = []
        for state in workflow_states:
            state_selection.append(f"        ('{state.code}', '{state.name}')")
        state_selection_str = ",\n".join(state_selection) if state_selection else "        ('draft', 'Draft')"

        # Generate fields from field definitions
        field_definitions = form.field_definition_ids.sorted('sequence')
        fields_code = []

        # Always include basic fields
        fields_code.append("    name = fields.Char(string='Name', required=True, tracking=True)")

        for field_def in field_definitions:
            field_code = self._generate_field_code(field_def)
            if field_code:
                fields_code.append(f"    {field_code}")

        # Add state field
        fields_code.append(f"""    state = fields.Selection([
{state_selection_str}
    ], string='State', default='{default_state}', tracking=True)""")

        fields_code.append("    active = fields.Boolean(string='Active', default=True)")
        fields_code.append("    color = fields.Integer(string='Color', default=0, help='Color for kanban view')")

        # Customer portal fields (add if not already present)
        has_customer_email = any(field.name == 'customer_email' for field in field_definitions)
        has_partner_id = any(field.name == 'partner_id' for field in field_definitions)

        if not has_customer_email:
            fields_code.append("    customer_email = fields.Char(string='Customer Email', help='Customer email for portal access')")
        if not has_partner_id:
            fields_code.append("    partner_id = fields.Many2one('res.partner', string='Customer', help='Related customer partner')")

        # Payment-related fields with rate tracking
        default_rate = template.default_rate if template.default_product_id else 0.0
        fields_code.append("    # Payment Fields")
        fields_code.append(f"    payment_rate = fields.Float(string='Payment Rate', default={default_rate}, tracking=True, help='Rate for this record (can be overridden)')")
        fields_code.append("    payment_amount = fields.Float(string='Payment Amount', compute='_compute_payment_amount', store=True, help='Computed payment amount based on rate')")
        fields_code.append("    rate_changed_by = fields.Many2one('res.users', string='Rate Changed By', readonly=True, help='User who last changed the rate')")
        fields_code.append("    rate_changed_date = fields.Datetime(string='Rate Changed Date', readonly=True, help='When the rate was last changed')")
        fields_code.append("    sales_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True, help='Related sales order for payment')")
        fields_code.append("    payment_status = fields.Selection([")
        fields_code.append("        ('none', 'No Payment Required'),")
        fields_code.append("        ('pending', 'Payment Pending'),")
        fields_code.append("        ('paid', 'Payment Completed'),")
        fields_code.append("        ('failed', 'Payment Failed'),")
        fields_code.append("    ], string='Payment Status', default='none', tracking=True, compute='_compute_payment_status', store=True)")
        fields_code.append("    payment_url = fields.Char(string='Payment URL', readonly=True, help='Customer portal payment link')")
        fields_code.append("    payment_method = fields.Selection([")
        fields_code.append("        ('online', 'Online Payment'),")
        fields_code.append("        ('cash', 'Cash Payment'),")
        fields_code.append("        ('bank_transfer', 'Bank Transfer'),")
        fields_code.append("        ('check', 'Check'),")
        fields_code.append("    ], string='Payment Method', readonly=True, help='Method used for payment')")
        fields_code.append("    cash_collected_by = fields.Many2one('res.users', string='Cash Collected By', readonly=True, help='User who collected cash payment')")
        fields_code.append("    cash_collection_date = fields.Datetime(string='Cash Collection Date', readonly=True, help='When cash was collected')")

        # ✨ AI Response Fields (Always included)
        fields_code.append("    # ✨ AI Response Fields")
        fields_code.append("    ai_response = fields.Html(string='AI Response', help='AI-generated response based on record data')")
        fields_code.append("    ai_response_generated = fields.Boolean(string='AI Response Generated', default=False)")
        fields_code.append("    ai_response_pdf = fields.Binary(string='AI Response PDF')")
        fields_code.append("    ai_response_pdf_filename = fields.Char(string='AI Response PDF Filename')")
        fields_code.append("    ai_last_prompt = fields.Text(string='Last AI Prompt', help='Last prompt sent to AI')")
        fields_code.append("    ai_response_last_updated = fields.Datetime(string='AI Response Last Updated', help='When AI response was last modified')")

        # Generate workflow methods
        workflow_methods = []

        # Add rate tracking methods first
        workflow_methods.append(f"""
    @api.depends('payment_rate')
    def _compute_payment_amount(self):
        \"\"\"Compute payment amount based on rate\"\"\"
        for record in self:
            record.payment_amount = record.payment_rate

    def _compute_payment_status(self):
        \"\"\"Compute payment status based on sales order state\"\"\"
        for record in self:
            # Store old payment status to detect changes
            old_payment_status = record.payment_status

            if not record.sales_order_id:
                record.payment_status = 'none'
                continue

            # Get all posted invoices from the sales order
            invoices = record.sales_order_id.invoice_ids.filtered(lambda inv: inv.state == 'posted' and inv.move_type == 'out_invoice')

            if not invoices:
                # No invoices yet
                if record.sales_order_id.state in ['sale', 'done']:
                    record.payment_status = 'pending'
                else:
                    record.payment_status = 'none'
            else:
                # Check payment status of invoices
                total_amount = sum(invoices.mapped('amount_total'))
                paid_amount = sum(invoices.mapped('amount_residual_signed'))

                if paid_amount <= 0:  # Fully paid (residual is 0 or negative)
                    record.payment_status = 'paid'
                elif paid_amount < total_amount:  # Partially paid
                    record.payment_status = 'pending'
                else:  # Not paid
                    record.payment_status = 'pending'

            # Auto-generate AI response and PDF when payment becomes "paid"
            if old_payment_status != 'paid' and record.payment_status == 'paid':
                record._auto_generate_ai_response_and_pdf()

    def _auto_generate_ai_response_and_pdf(self):
        \"\"\"Automatically generate AI response and PDF when payment is completed\"\"\"
        try:
            # Generate AI response if not already generated
            if not self.ai_response or not self.ai_response_generated:
                self.action_get_ai_response()

            # Generate PDF if not already generated or if AI response was updated
            if not self.ai_response_pdf or self.ai_response:
                self.action_generate_ai_pdf()

        except Exception as e:
            # Log error but don't fail the payment process
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Failed to auto-generate AI response and PDF for {{self.name}}: {{str(e)}}")

    @api.model
    def create(self, vals):
        \"\"\"Override create to automatically link customer\"\"\"
        # Auto-link customer based on email if not already set
        if not vals.get('partner_id') and vals.get('customer_email'):
            partner = self._find_or_create_partner(vals.get('customer_email'), vals.get('customer_name'))
            if partner:
                vals['partner_id'] = partner.id

        return super().create(vals)

    def _find_or_create_partner(self, email, name=None):
        \"\"\"Find existing partner by email or create new one\"\"\"
        if not email:
            return False

        # Search for existing partner
        partner = self.env['res.partner'].search([('email', '=', email)], limit=1)

        if not partner and name:
            # Create new partner if not found
            partner = self.env['res.partner'].create({{
                'name': name or email,
                'email': email,
                'is_company': False,
                'customer_rank': 1,  # Mark as customer
            }})

        return partner

    def _create_sales_order(self):
        \"\"\"Create sales order for payment request\"\"\"
        if self.sales_order_id:
            return self.sales_order_id

        # Ensure we have a partner
        if not self.partner_id:
            partner = self._find_or_create_partner(self.customer_email, self.customer_name)
            if partner:
                self.partner_id = partner.id

        if not self.partner_id:
            raise ValueError("Cannot create sales order without customer information")

        # Create sales order
        order_vals = {{
            'partner_id': self.partner_id.id,
            'origin': self.name,
            'order_line': [(0, 0, {{
                'name': f'Service for {{self.name}}',
                'product_uom_qty': 1,
                'price_unit': self.payment_amount,
            }})],
        }}

        # Try to find a service product, or create a generic one
        service_product = self.env['product.product'].search([
            ('type', '=', 'service'),
            ('sale_ok', '=', True)
        ], limit=1)

        if not service_product:
            # Create a generic service product
            service_product = self.env['product.product'].create({{
                'name': 'Generic Service',
                'type': 'service',
                'sale_ok': True,
                'purchase_ok': False,
                'list_price': 0.0,
            }})

        order_vals['order_line'][0][2]['product_id'] = service_product.id

        sales_order = self.env['sale.order'].create(order_vals)
        self.sales_order_id = sales_order.id

        return sales_order

    @api.onchange('payment_rate')
    def _onchange_payment_rate(self):
        \"\"\"Track rate changes\"\"\"
        if self.payment_rate != self._origin.payment_rate:
            self.rate_changed_by = self.env.user
            self.rate_changed_date = fields.Datetime.now()

    def write(self, vals):
        \"\"\"Override write to track rate changes\"\"\"
        if 'payment_rate' in vals:
            vals.update({{
                'rate_changed_by': self.env.user.id,
                'rate_changed_date': fields.Datetime.now(),
            }})
        return super().write(vals)""")

        for i, state in enumerate(workflow_states):
            if i < len(workflow_states) - 1:  # Not the last state
                next_state = workflow_states[i + 1]
                method_name = f"action_{state.code}_to_{next_state.code}"
                workflow_methods.append(f"""    def {method_name}(self):
        \"\"\"Move from {state.name} to {next_state.name}\"\"\"
        self.state = '{next_state.code}'""")

        # Add payment workflow methods
        workflow_methods.append(f"""
    def action_request_payment(self):
        \"\"\"Request payment from customer\"\"\"
        self.ensure_one()

        # Validate payment rate is set (can be 0)
        if not hasattr(self, 'payment_rate'):
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'Rate Not Set',
                    'message': 'Payment rate must be set before requesting payment.',
                    'type': 'warning',
                }}
            }}

        # Find payment stage in workflow (if workflow module is available)
        try:
            if 'workflow.state' in self.env:
                payment_states = self.env['workflow.state'].search([
                    ('template_id.name', '=', '{template.name}'),
                    ('is_payment_stage', '=', True)
                ])

                if payment_states:
                    payment_state = payment_states[0]
                    # Execute payment request with current rate
                    payment_state._request_payment(self)
                else:
                    # Fallback: Create sales order directly
                    self._create_sales_order()
            else:
                # Fallback: Create sales order directly
                self._create_sales_order()
        except Exception:
            # Fallback: Create sales order directly
            self._create_sales_order()

        # Update payment status
        self.payment_status = 'pending'

        # Generate payment URL
        if self.sales_order_id:
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            self.payment_url = base_url + "/my/orders/" + str(self.sales_order_id.id)

        return {{
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {{
                'title': 'Payment Requested',
                'message': 'Payment request sent for amount: ' + str(self.payment_amount),
                'type': 'success',
            }}
        }}

    def action_view_sales_order(self):
        \"\"\"View related sales order\"\"\"
        self.ensure_one()

        if not self.sales_order_id:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'No Sales Order',
                    'message': 'No sales order found for this record.',
                    'type': 'warning',
                }}
            }}

        return {{
            'name': 'Sales Order',
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order',
            'res_id': self.sales_order_id.id,
            'view_mode': 'form',
            'target': 'current',
        }}

    def action_open_payment_portal(self):
        \"\"\"Open customer payment portal\"\"\"
        self.ensure_one()

        if not self.payment_url:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'No Payment URL',
                    'message': 'Payment URL not available. Please request payment first.',
                    'type': 'warning',
                }}
            }}

        return {{
            'type': 'ir.actions.act_url',
            'url': self.payment_url,
            'target': 'new',
        }}

    def action_record_cash_payment(self):
        \"\"\"Record cash payment for this record\"\"\"
        self.ensure_one()

        if not self.sales_order_id:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'No Sales Order',
                    'message': 'Please request payment first to create a sales order.',
                    'type': 'warning',
                }}
            }}

        if self.payment_status == 'paid':
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'Already Paid',
                    'message': 'This order has already been paid.',
                    'type': 'warning',
                }}
            }}

        # Confirm sales order if not already confirmed
        if self.sales_order_id.state == 'draft':
            self.sales_order_id.action_confirm()

        # For service products, mark as delivered to enable invoicing
        for line in self.sales_order_id.order_line:
            if line.product_id.type == 'service':
                line.qty_delivered = line.product_uom_qty

        # Create invoice if not already created
        if not self.sales_order_id.invoice_ids:
            # Check if we can create invoice
            if self.sales_order_id._get_invoiceable_lines():
                invoice = self.sales_order_id._create_invoices()
                if invoice:
                    invoice.action_post()
                else:
                    # If invoice creation fails, create manual invoice
                    invoice = self._create_manual_invoice()
            else:
                # Create manual invoice if no invoiceable lines
                invoice = self._create_manual_invoice()
        else:
            invoice = self.sales_order_id.invoice_ids[0]
            if invoice.state == 'draft':
                invoice.action_post()

        # Register cash payment
        payment_method_line = self._get_cash_payment_method()

        payment_vals = {{
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.sales_order_id.partner_id.id,
            'amount': self.sales_order_id.amount_total,
            'currency_id': self.sales_order_id.currency_id.id,
            'payment_method_line_id': payment_method_line.id,
            'journal_id': payment_method_line.journal_id.id,  # ✅ CRITICAL FIX
            'ref': f'Cash payment for {{self.name}}',
            'date': fields.Date.context_today(self),
        }}

        payment = self.env['account.payment'].create(payment_vals)
        payment.action_post()

        # Reconcile payment with invoice
        invoice = self.sales_order_id.invoice_ids[0]
        payment_lines = payment.line_ids.filtered(lambda line: line.account_id == payment.destination_account_id)
        invoice_lines = invoice.line_ids.filtered(lambda line: line.account_id == payment.destination_account_id)
        (payment_lines + invoice_lines).reconcile()

        # Update payment tracking fields
        self.write({{
            'payment_method': 'cash',
            'cash_collected_by': self.env.user.id,
            'cash_collection_date': fields.Datetime.now(),
        }})

        # Integrate with AI Cash Management System
        cash_collection_record = self._integrate_with_cash_management(payment)

        message = f'Cash payment of {{self.sales_order_id.amount_total}} {{self.sales_order_id.currency_id.name}} has been recorded successfully.'
        if cash_collection_record:
            message += f' Cash collection record {{cash_collection_record.name}} created in Cash Management System.'

        return {{
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {{
                'title': 'Cash Payment Recorded',
                'message': message,
                'type': 'success',
            }}
        }}

    def _get_cash_payment_method(self):
        \"\"\"Get cash payment method\"\"\"
        # First try to find existing cash journal
        cash_journal = self.env['account.journal'].search([
            ('type', '=', 'cash'),
            ('company_id', '=', self.env.company.id)
        ], limit=1)

        if not cash_journal:
            # Create a cash journal if none exists
            cash_journal = self.env['account.journal'].create({{
                'name': 'Cash',
                'type': 'cash',
                'code': 'CSH1',
                'company_id': self.env.company.id,
            }})

        # Ensure the journal is properly configured
        if not cash_journal.default_account_id:
            # Create cash account if it doesn't exist
            cash_account = self.env['account.account'].search([
                ('account_type', '=', 'asset_cash'),
                ('company_id', '=', self.env.company.id)
            ], limit=1)

            if not cash_account:
                cash_account = self.env['account.account'].create({{
                    'name': 'Cash',
                    'code': '101001',
                    'account_type': 'asset_cash',
                    'company_id': self.env.company.id,
                }})

            cash_journal.default_account_id = cash_account.id

        # Get or create the payment method line for cash
        payment_method_line = self.env['account.payment.method.line'].search([
            ('journal_id', '=', cash_journal.id),
            ('payment_method_id.payment_type', '=', 'inbound')
        ], limit=1)

        if not payment_method_line:
            # Get the manual payment method
            manual_in = self.env.ref('account.account_payment_method_manual_in', raise_if_not_found=False)
            if not manual_in:
                # Create manual payment method if it doesn't exist
                manual_in = self.env['account.payment.method'].create({{
                    'name': 'Manual',
                    'code': 'manual',
                    'payment_type': 'inbound',
                }})

            payment_method_line = self.env['account.payment.method.line'].create({{
                'journal_id': cash_journal.id,
                'payment_method_id': manual_in.id,
                'name': 'Manual',
            }})

        return payment_method_line

    def _integrate_with_cash_management(self, payment):
        \"\"\"Integrate cash payment with AI Cash Management System\"\"\"
        try:
            # Check if AI Cash Management System is installed
            if 'ai.cash.management.cash.collection.simple' not in self.env:
                return False

            cash_collection_model = self.env['ai.cash.management.cash.collection.simple']
            cash_collector_model = self.env['ai.cash.management.cash.collector.simple']

            # Get or create a cash collector for the current user
            collector = cash_collector_model.search([('user_id', '=', self.env.user.id)], limit=1)
            if not collector:
                # Create a collector for the current user
                collector = cash_collector_model.create({{
                    'name': self.env.user.name,
                    'user_id': self.env.user.id,
                    'phone': self.env.user.phone or '',
                    'state': 'active',
                }})

            # Create cash collection record
            collection_vals = {{
                'collector_id': collector.id,
                'customer_name': self.sales_order_id.partner_id.name,
                'customer_phone': self.sales_order_id.partner_id.phone or '',
                'customer_email': self.sales_order_id.partner_id.email or '',
                'amount': payment.amount,
                'payment_method': 'cash',
                'source_reference': f"{{self.name}} - {{self.sales_order_id.name}}",
                'description': f"Cash payment for {{self.name}}",
                'notes': f"Collected via Record Cash Payment feature. Payment ID: {{payment.id}}",
                'collection_date': fields.Datetime.now(),
                'state': 'confirmed',  # Auto-confirm since payment is already recorded
            }}

            cash_collection = cash_collection_model.create(collection_vals)

            # Auto-submit to manager if collector is not a manager
            try:
                if not self.env.user.has_group('ai_cash_management.group_branch_manager'):
                    cash_collection.action_submit()
            except:
                # If group doesn't exist, just continue
                pass

            return cash_collection

        except Exception as e:
            # Log the error but don't fail the payment process
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Failed to integrate with Cash Management System: {{str(e)}}")
            return False

    def _create_manual_invoice(self):
        \"\"\"Create manual invoice when automatic creation fails\"\"\"
        invoice_vals = {{
            'move_type': 'out_invoice',
            'partner_id': self.sales_order_id.partner_id.id,
            'invoice_origin': self.sales_order_id.name,
            'invoice_line_ids': [(0, 0, {{
                'name': f'Payment for {{self.name}}',
                'quantity': 1,
                'price_unit': self.payment_amount,
                'account_id': self._get_income_account().id,
            }})],
        }}

        invoice = self.env['account.move'].create(invoice_vals)
        invoice.action_post()

        # Link invoice to sales order
        self.sales_order_id.invoice_ids = [(4, invoice.id)]

        return invoice

    def _get_income_account(self):
        \"\"\"Get income account for manual invoice\"\"\"
        # Try to get income account from company
        company = self.env.company
        income_account = company.account_sale_tax_id.invoice_repartition_line_ids.filtered(
            lambda line: line.repartition_type == 'base'
        ).account_id

        if not income_account:
            # Fallback to finding any income account
            income_account = self.env['account.account'].search([
                ('account_type', '=', 'income'),
                ('company_id', '=', company.id)
            ], limit=1)

        if not income_account:
            # Create a basic income account if none exists
            income_account = self.env['account.account'].create({{
                'name': 'Service Income',
                'code': '400001',
                'account_type': 'income',
                'company_id': company.id,
            }})

        return income_account

    def action_refresh_payment_status(self):
        \"\"\"Manually trigger payment status computation\"\"\"
        # Force refresh by invalidating cache and recomputing
        self.invalidate_recordset(['payment_status'])
        self._compute_payment_status()

        # Get human-readable status
        status_dict = dict(self._fields["payment_status"].selection)
        current_status = status_dict.get(self.payment_status, self.payment_status)

        return {{
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {{
                'title': 'Payment Status Refreshed',
                'message': f'Payment status updated to: {{current_status}}',
                'type': 'success',
            }}
        }}

    @api.model
    def _cron_update_payment_status(self):
        \"\"\"Cron job to update payment status for pending payments\"\"\"
        pending_records = self.search([('payment_status', 'in', ['pending', 'none'])])
        for record in pending_records:
            old_status = record.payment_status
            # Force refresh by invalidating cache
            record.invalidate_recordset(['payment_status'])
            record._compute_payment_status()
            if record.payment_status != old_status:
                status_dict = dict(record._fields["payment_status"].selection)
                old_status_label = status_dict.get(old_status, old_status)
                new_status_label = status_dict.get(record.payment_status, record.payment_status)
                record.message_post(
                    body=f\"Payment status automatically updated from {{old_status_label}} to {{new_status_label}}\",
                    subject=\"Payment Status Update\"
                )""")

        # ✨ Add Generic Display Method for All Fields
        workflow_methods.append(f"""
    # ✨ Generic Display Methods for Selection and Many2one Fields
    def get_field_display(self, field_name):
        \"\"\"Get human-readable value for any selection or many2one field\"\"\"
        if not hasattr(self, field_name):
            return ''

        field_value = getattr(self, field_name)
        if not field_value:
            return ''

        field_obj = self._fields.get(field_name)
        if not field_obj:
            return str(field_value)

        # Handle Selection fields
        if hasattr(field_obj, 'selection') and field_obj.selection:
            if callable(field_obj.selection):
                # Dynamic selection
                selection_list = field_obj.selection(self)
            else:
                # Static selection
                selection_list = field_obj.selection

            selection_dict = dict(selection_list)
            return selection_dict.get(field_value, str(field_value))

        # Handle Many2one fields
        elif hasattr(field_obj, 'comodel_name'):
            if hasattr(field_value, 'name'):
                return field_value.name
            elif hasattr(field_value, 'display_name'):
                return field_value.display_name
            else:
                return str(field_value)

        # Default: return string representation
        return str(field_value)

    def get_state_display(self):
        \"\"\"Get human-readable state value\"\"\"
        return self.get_field_display('state')

    def get_payment_status_display(self):
        \"\"\"Get human-readable payment status value\"\"\"
        return self.get_field_display('payment_status')""")

        # ✨ Add AI Response Auto-Regeneration Methods
        workflow_methods.append(f"""
    # ✨ AI Response Auto-Regeneration Methods
    @api.onchange('ai_response')
    def _onchange_ai_response(self):
        \"\"\"Clear PDF when AI response is manually updated\"\"\"
        if self.ai_response and self.ai_response_pdf:
            # Mark that PDF needs regeneration
            self.ai_response_pdf = False
            self.ai_response_pdf_filename = False

    def write(self, vals):
        \"\"\"Override write to regenerate PDF when AI response is updated\"\"\"
        result = super().write(vals)

        # If AI response was updated, regenerate PDF automatically
        if 'ai_response' in vals and vals.get('ai_response'):
            for record in self:
                if record.ai_response:
                    try:
                        # Update the last updated timestamp
                        record.ai_response_last_updated = fields.Datetime.now()
                        # Regenerate PDF with current AI response content
                        record.action_generate_ai_pdf()
                    except Exception as e:
                        # Log error but don't fail the write operation
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.warning(f"Failed to auto-regenerate PDF for {{record.name}}: {{str(e)}}")

        return result""")

        # ✨ Add AI Response Methods
        workflow_methods.append(f"""
    # ✨ AI Response Methods
    def action_get_ai_response(self):
        \"\"\"Get AI response for this record\"\"\"
        self.ensure_one()

        # Get AI prompt configuration
        ai_prompt_configs = self.env['ai.prompt.config'].search([
            ('form_builder_id.code', '=', '{form.code}'),
            ('prompt_type', '=', 'system_prompt'),
            ('active', '=', True)
        ], limit=1)

        if not ai_prompt_configs:
            # Create a fallback AI response if no configuration is found
            fallback_response = self._generate_fallback_ai_response()
            self.write({{
                'ai_response': fallback_response,
                'ai_response_generated': True,
                'ai_last_prompt': 'Fallback response - no AI configuration found',
            }})

            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'AI Response Generated',
                    'message': 'AI response has been generated using fallback method.',
                    'type': 'success',
                }}
            }}

        ai_config = ai_prompt_configs[0]

        try:
            # Prepare record data for AI prompt
            record_data = self._prepare_ai_data()

            # Execute AI prompt
            ai_response = ai_config.execute_prompt(record_data)

            # Store AI response
            self.write({{
                'ai_response': ai_response,
                'ai_response_generated': True,
                'ai_last_prompt': ai_config._format_prompt(record_data),
            }})

            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'AI Response Generated',
                    'message': 'AI response has been generated successfully.',
                    'type': 'success',
                }}
            }}

        except Exception as e:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'AI Error',
                    'message': f'Failed to generate AI response: {{str(e)}}',
                    'type': 'danger',
                }}
            }}

    def _prepare_ai_data(self):
        \"\"\"Prepare record data for AI prompt\"\"\"
        self.ensure_one()

        data = {{
            'name': self.name or '',
            'state': dict(self._fields['state'].selection).get(self.state, self.state),
            'create_date': self.create_date.strftime('%Y-%m-%d %H:%M:%S') if self.create_date else '',
        }}

        # Add all form fields dynamically
        for field_name in {[field.name for field in form.field_definition_ids]}:
            if hasattr(self, field_name):
                field_value = getattr(self, field_name, '')
                if field_value:
                    # Handle different field types
                    if hasattr(field_value, 'name'):  # Many2one
                        data[field_name] = field_value.name
                    elif hasattr(field_value, 'mapped'):  # Many2many/One2many
                        data[field_name] = ', '.join(field_value.mapped('name'))
                    else:
                        data[field_name] = str(field_value)
                else:
                    data[field_name] = ''

        return data

    def _generate_fallback_ai_response(self):
        \"\"\"Generate a fallback AI response when no AI configuration is available\"\"\"
        self.ensure_one()

        # Create a professional response based on the form data
        response_parts = [
            f"<h2>Thank you for your submission!</h2>",
            "<p>We have received your form submission and appreciate you taking the time to provide this information.</p>",
            "<h3>Submission Summary:</h3>",
            "<ul>"
        ]

        # Add form field data dynamically
        for field_name in {[field.name for field in form.field_definition_ids]}:
            if hasattr(self, field_name):
                field_value = getattr(self, field_name, '')
                if field_value:
                    field_label = field_name.replace('_', ' ').title()
                    # Handle different field types
                    if hasattr(field_value, 'name'):  # Many2one
                        response_parts.append(f"<li><strong>{{field_label}}:</strong> {{field_value.name}}</li>")
                    elif hasattr(field_value, 'mapped'):  # Many2many/One2many
                        values_str = ', '.join(field_value.mapped('name'))
                        response_parts.append(f"<li><strong>{{field_label}}:</strong> {{values_str}}</li>")
                    else:
                        response_parts.append(f"<li><strong>{{field_label}}:</strong> {{str(field_value)}}</li>")

        response_parts.extend([
            "</ul>",
            "<h3>Our Response:</h3>",
            "<p>Your submission is valuable to us and helps us improve our services. We are committed to addressing your needs and will take appropriate action based on your input.</p>",
            "<p>If you have any urgent matters or need immediate assistance, please don't hesitate to contact our support team.</p>",
            "<p><strong>Thank you for choosing our services!</strong></p>",
            "<hr>",
            f"<p><small>Reference: {{self.name}} | Date: {{self.create_date.strftime('%Y-%m-%d') if self.create_date else 'N/A'}}</small></p>"
        ])

        return '\\n'.join(response_parts)

    def action_generate_ai_pdf(self):
        \"\"\"Generate PDF from AI response\"\"\"
        self.ensure_one()

        if not self.ai_response:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'No AI Response',
                    'message': 'Please generate AI response first.',
                    'type': 'warning',
                }}
            }}

        try:
            # Generate PDF from AI response
            pdf_content = self._generate_pdf_from_html(self.ai_response)
            filename = f"ai_response_{{self.name or 'record'}}.pdf"

            self.write({{
                'ai_response_pdf': pdf_content,
                'ai_response_pdf_filename': filename,
            }})

            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'PDF Generated',
                    'message': f'PDF has been generated: {{filename}}',
                    'type': 'success',
                }}
            }}

        except Exception as e:
            return {{
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {{
                    'title': 'PDF Error',
                    'message': f'Failed to generate PDF: {{str(e)}}',
                    'type': 'danger',
                }}
            }}

    def _generate_pdf_from_html(self, html_content):
        \"\"\"Generate PDF from HTML content using Odoo's built-in report system\"\"\"
        import base64
        import logging
        import tempfile
        import os
        _logger = logging.getLogger(__name__)

        try:
            # Create a simple HTML template for PDF generation
            html_template = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI Response Document</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }}
        .header {{
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #007bff;
            margin: 0;
        }}
        .content {{
            margin: 20px 0;
        }}
        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
            text-align: center;
        }}
        h2, h3 {{
            color: #007bff;
        }}
        ul {{
            margin: 10px 0;
            padding-left: 20px;
        }}
        li {{
            margin: 5px 0;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AI Response Document</h1>
    </div>
    <div class="content">
        {{html_content}}
    </div>
    <div class="footer">
        <p>Generated on: {{self.create_date.strftime('%Y-%m-%d %H:%M:%S') if self.create_date else 'N/A'}}</p>
        <p>Reference: {{self.name}}</p>
        <p>© AI Generated Document</p>
    </div>
</body>
</html>
            '''

            # Use Odoo's report system to generate PDF
            IrActionsReport = self.env['ir.actions.report']

            try:
                # Generate PDF using wkhtmltopdf
                pdf_content = IrActionsReport._run_wkhtmltopdf(
                    [html_template],
                    landscape=False,
                    specific_paperformat_args={{
                        'data-report-margin-top': 15,
                        'data-report-margin-bottom': 15,
                        'data-report-margin-left': 10,
                        'data-report-margin-right': 10,
                        'data-report-page-size': 'A4',
                        'data-report-orientation': 'Portrait'
                    }}
                )

                if pdf_content:
                    return base64.b64encode(pdf_content)
                else:
                    raise Exception("PDF content is empty")

            except Exception as e:
                raise e

        except Exception as e1:
            _logger.error(f"Odoo PDF generation failed: {{str(e1)}}")

            # Fallback: Create a very simple PDF using basic method
            try:
                import subprocess
                import tempfile
                import os

                # Create simple HTML content
                simple_html = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI Response Document</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 20px;">
    <h1 style="color: #007bff; text-align: center;">AI Response Document</h1>
    <hr>
    <div>
        {{html_content.replace('<', '&lt;').replace('>', '&gt;') if '<script' in html_content.lower() else html_content}}
    </div>
    <hr>
    <p style="text-align: center; font-size: 12px; color: #666;">
        Generated: {{self.create_date.strftime('%Y-%m-%d') if self.create_date else 'N/A'}} |
        Reference: {{self.name}}
    </p>
</body>
</html>
                '''

                # Write to temporary file
                with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as html_file:
                    html_file.write(simple_html)
                    html_file_path = html_file.name

                # Generate PDF using wkhtmltopdf command directly
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as pdf_file:
                    pdf_file_path = pdf_file.name

                try:
                    # Run wkhtmltopdf
                    cmd = [
                        'wkhtmltopdf',
                        '--page-size', 'A4',
                        '--margin-top', '15mm',
                        '--margin-bottom', '15mm',
                        '--margin-left', '10mm',
                        '--margin-right', '10mm',
                        '--encoding', 'UTF-8',
                        '--quiet',
                        html_file_path,
                        pdf_file_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                    if result.returncode == 0 and os.path.exists(pdf_file_path):
                        # Read the generated PDF
                        with open(pdf_file_path, 'rb') as pdf_file:
                            pdf_content = pdf_file.read()

                        # Clean up
                        os.unlink(html_file_path)
                        os.unlink(pdf_file_path)

                        if pdf_content:
                            return base64.b64encode(pdf_content)
                        else:
                            raise Exception("Generated PDF is empty")
                    else:
                        raise Exception(f"wkhtmltopdf failed: {{result.stderr}}")

                except Exception as e:
                    # Clean up on error
                    if os.path.exists(html_file_path):
                        os.unlink(html_file_path)
                    if os.path.exists(pdf_file_path):
                        os.unlink(pdf_file_path)
                    raise e

            except Exception as e2:
                _logger.error(f"Fallback PDF generation failed: {{str(e2)}}")

                # Last resort: Create a minimal text-based response
                error_html = f'''
                <h1>PDF Generation Error</h1>
                <p>We apologize, but there was an issue generating your PDF document.</p>
                <p>Please contact support with reference: {{self.name}}</p>
                <p>Error details: PDF generation system temporarily unavailable</p>
                '''

                # Return a simple base64 encoded error message instead of failing completely
                return base64.b64encode(error_html.encode('utf-8'))""")

        # Generate model content
        model_content = f'''from odoo import api, fields, models, _


class {class_name}(models.Model):
    """
    {form.description or form.name} Model
    Generated from template: {template.name}
    """
    _name = '{model_name}'
    _description = '{form.description or form.name}'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

{chr(10).join(fields_code)}

{chr(10).join(workflow_methods) if workflow_methods else "    # No workflow methods generated"}
'''

        # Write model file
        model_file_path = os.path.join(models_dir, f'{form.code}.py')
        with open(model_file_path, 'w') as f:
            f.write(model_content)

    def _generate_field_code(self, field_def):
        """Generate Odoo field code from field definition"""
        field_type_mapping = {
            'char': 'Char',
            'text': 'Text',
            'integer': 'Integer',
            'float': 'Float',
            'boolean': 'Boolean',
            'date': 'Date',
            'datetime': 'Datetime',
            'selection': 'Selection',
            'many2one': 'Many2one',
            'many2many': 'Many2many',
            'one2many': 'One2many',
            'binary': 'Binary',
            'html': 'Html',
        }

        odoo_type = field_type_mapping.get(field_def.field_type, 'Char')

        # Build field parameters
        params = []
        params.append(f"string='{field_def.field_description}'")

        if field_def.required:
            params.append("required=True")
        if field_def.readonly:
            params.append("readonly=True")
        if field_def.help_text:
            params.append(f"help='{field_def.help_text}'")
        if field_def.default_value:
            if field_def.field_type in ['char', 'text', 'html']:
                params.append(f"default='{field_def.default_value}'")
            else:
                params.append(f"default={field_def.default_value}")

        # Handle special field types
        if field_def.field_type == 'selection':
            if field_def.selection_options:
                try:
                    # Try to parse as JSON first
                    import json
                    options_data = json.loads(field_def.selection_options)
                    if isinstance(options_data, list):
                        selection_list = []
                        for option in options_data:
                            if isinstance(option, list) and len(option) == 2:
                                key, value = option
                                selection_list.append(f"('{key}', '{value}')")
                        if selection_list:
                            params.insert(0, f"[{', '.join(selection_list)}]")
                        else:
                            # If no valid selection options, provide default ones
                            default_selections = self._get_default_selection_options(field_def.name)
                            params.insert(0, f"[{', '.join(default_selections)}]")
                    else:
                        # Invalid JSON format, use defaults
                        default_selections = self._get_default_selection_options(field_def.name)
                        params.insert(0, f"[{', '.join(default_selections)}]")
                except (json.JSONDecodeError, ValueError):
                    # Fallback to old comma-separated format
                    selection_list = []
                    for option in field_def.selection_options.split('\n'):
                        if ',' in option:
                            key, value = option.split(',', 1)
                            selection_list.append(f"('{key.strip()}', '{value.strip()}')")
                    if selection_list:
                        params.insert(0, f"[{', '.join(selection_list)}]")
                    else:
                        # If no valid selection options, provide default ones
                        default_selections = self._get_default_selection_options(field_def.name)
                        params.insert(0, f"[{', '.join(default_selections)}]")
            else:
                # If no selection_options at all, provide appropriate defaults based on field name
                default_selections = self._get_default_selection_options(field_def.name)
                params.insert(0, f"[{', '.join(default_selections)}]")

        if field_def.field_type in ['many2one', 'many2many', 'one2many'] and field_def.relation_model:
            params.insert(0, f"'{field_def.relation_model}'")

            # Handle many2many table name length issue
            if field_def.field_type == 'many2many':
                # Generate a shorter relation table name to avoid PostgreSQL 63-char limit
                model_name = field_def.form_builder_id.model_name if field_def.form_builder_id else 'unknown'
                relation_model = field_def.relation_model
                field_name = field_def.name

                # Create a shortened table name
                model_short = model_name.replace('.', '_')[:15]  # Limit model name part
                relation_short = relation_model.replace('.', '_')[:15]  # Limit relation model part
                field_short = field_name[:10]  # Limit field name part

                # Generate relation table name (max 63 chars)
                relation_table = f"{model_short}_{field_short}_{relation_short}_rel"

                # If still too long, use hash-based approach
                if len(relation_table) > 63:
                    import hashlib
                    # Create a hash of the full name for uniqueness
                    full_name = f"{model_name}_{field_name}_{relation_model}"
                    name_hash = hashlib.md5(full_name.encode()).hexdigest()[:8]
                    relation_table = f"m2m_{name_hash}_rel"

                params.append(f"relation='{relation_table}'")

        # Skip fields that would cause errors
        if field_def.field_type == 'selection' and not any('(' in param for param in params):
            # If somehow we still don't have selection options, convert to Char field
            return f"{field_def.name} = fields.Char({', '.join(params)})"

        return f"{field_def.name} = fields.{odoo_type}({', '.join(params)})"

    def _escape_xml(self, text):
        """Escape special characters for XML content"""
        if not text:
            return ''

        # Convert to string if not already
        text = str(text)

        # Escape XML special characters
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('"', '&quot;')
        text = text.replace("'", '&apos;')

        return text

    def _get_default_selection_options(self, field_name):
        """Get appropriate default selection options based on field name"""
        field_name_lower = field_name.lower()

        # Common field patterns and their appropriate selections
        if 'rating' in field_name_lower:
            return ["('1', '1 Star')", "('2', '2 Stars')", "('3', '3 Stars')", "('4', '4 Stars')", "('5', '5 Stars')"]
        elif 'priority' in field_name_lower:
            return ["('low', 'Low')", "('medium', 'Medium')", "('high', 'High')", "('urgent', 'Urgent')"]
        elif 'status' in field_name_lower:
            return ["('active', 'Active')", "('inactive', 'Inactive')", "('pending', 'Pending')"]
        elif 'type' in field_name_lower:
            return ["('type1', 'Type 1')", "('type2', 'Type 2')", "('type3', 'Type 3')"]
        elif 'category' in field_name_lower:
            return ["('category1', 'Category 1')", "('category2', 'Category 2')", "('category3', 'Category 3')"]
        elif 'gender' in field_name_lower:
            return ["('male', 'Male')", "('female', 'Female')", "('other', 'Other')"]
        elif 'size' in field_name_lower:
            return ["('small', 'Small')", "('medium', 'Medium')", "('large', 'Large')", "('xl', 'Extra Large')"]
        elif 'level' in field_name_lower:
            return ["('beginner', 'Beginner')", "('intermediate', 'Intermediate')", "('advanced', 'Advanced')"]
        elif 'grade' in field_name_lower:
            return ["('a', 'Grade A')", "('b', 'Grade B')", "('c', 'Grade C')", "('d', 'Grade D')"]
        else:
            # Generic default options
            return ["('option1', 'Option 1')", "('option2', 'Option 2')", "('option3', 'Option 3')"]

    def _generate_views(self, module_path):
        """Generate views directory and files based on template configuration"""
        self.generation_log += "Generating views from template configuration...\n"

        views_dir = os.path.join(module_path, 'views')
        os.makedirs(views_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            raise UserError(_("No template selected"))

        # Generate views for each form in the template
        all_views = []
        all_actions = []
        all_menus = []

        # Add root menu if multiple forms
        if len(template.form_builder_ids) > 1:
            module_root_menu = f"{self.custom_prefix}_{self.module_name}_main_menu"
            root_menu = f'''    <!-- {self.module_title} Root Menu -->
    <menuitem id="{module_root_menu}" name="{self.module_title}" sequence="10"/>'''
            all_menus.append(root_menu)

        for form in template.form_builder_ids:
            form_views = self._generate_form_views(form, template)
            all_views.extend(form_views['views'])
            all_actions.extend(form_views['actions'])
            all_menus.extend(form_views['menus'])

        # Combine all views into one file
        views_content = f'''<?xml version="1.0" encoding="utf-8"?>
<odoo>
{chr(10).join(all_views)}

{chr(10).join(all_actions)}

{chr(10).join(all_menus)}
</odoo>'''

        with open(os.path.join(views_dir, 'views.xml'), 'w') as f:
            f.write(views_content)

    def _generate_reports(self, module_path):
        """Generate PDF reports directory and files"""
        self.generation_log += "Generating PDF reports...\n"

        reports_dir = os.path.join(module_path, 'reports')
        os.makedirs(reports_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            raise UserError(_("No template selected"))

        # Generate reports for each form in the template
        for form in template.form_builder_ids:
            self._generate_form_report(form, reports_dir)

    def _generate_form_report(self, form, reports_dir):
        """Generate PDF report for a specific form"""
        model_name = form.model_name
        form_code = form.code
        form_name = form.name

        # Find HTML fields for AI response content
        html_fields = [field for field in form.field_definition_ids if field.field_type == 'html']

        # Always include the ai_response field (automatically added to all generated models)
        has_ai_response = True

        # Generate report content
        report_content = f'''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- PDF Report Action -->
        <record id="action_report_{form_code}" model="ir.actions.report">
            <field name="name">{form_name} AI Response</field>
            <field name="model">{model_name}</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">{self.custom_prefix}_{self.module_name}.report_{form_code}</field>
            <field name="report_file">{self.custom_prefix}_{self.module_name}.report_{form_code}</field>
            <field name="binding_model_id" ref="model_{model_name.replace('.', '_')}"/>
            <field name="binding_type">report</field>
            <field name="print_report_name">'{form_name} AI Response - %s' % (object.name)</field>
        </record>

        <!-- PDF Report Template -->
        <template id="report_{form_code}">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">'''

        # Always include ai_response field (automatically added to all generated models)
        report_content += '''
                            <!-- ✨ AI Response / Document Content Section (Only Content) -->
                            <div t-if="doc.ai_response">
                                <!-- Raw HTML content from the ai_response field -->
                                <t t-raw="doc.ai_response"/>
                            </div>

                            <!-- Placeholder when no AI response -->
                            <div t-if="not doc.ai_response" class="text-center text-muted" style="padding: 50px;">
                                <h3><em>No AI response or document content has been generated yet.</em></h3>
                                <p><small>Use the "Get AI Response" button or manually add content in the backend to populate this document.</small></p>
                            </div>'''

        # Add additional HTML field content sections if any exist
        if html_fields:
            for field in html_fields:
                field_name = field.name
                field_label = field.label or field_name.replace('_', ' ').title()
                report_content += f'''

                            <!-- {field_label} Content -->
                            <div t-if="doc.{field_name}" class="mt-4">
                                <h4>{field_label}</h4>
                                <div class="border-top pt-3">
                                    <!-- Raw HTML content from the {field_name} field -->
                                    <t t-raw="doc.{field_name}"/>
                                </div>
                            </div>'''

        report_content += '''
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>'''

        # Write report file
        report_file_path = os.path.join(reports_dir, f'{form_code}_report.xml')
        with open(report_file_path, 'w') as f:
            f.write(report_content)



    def _generate_form_views(self, form, template):
        """Generate views for a specific form"""
        model_name = form.model_name
        form_code = form.code
        form_name = form.name

        # Get workflow states
        workflow_states = template.workflow_state_ids.sorted('sequence')

        # Generate tree view fields with proper optional settings
        tree_fields = ['<field name="name"/>']

        # Add first 3 non-name fields to tree view
        field_count = 0
        for field_def in form.field_definition_ids.sorted('sequence'):
            if field_def.name != 'name' and field_count < 3:
                field_attrs = []

                # Make relational fields optional but visible by default for testing
                if field_def.field_type in ['many2one', 'many2many', 'one2many']:
                    # Show many2one fields by default, hide one2many/many2many as they take more space
                    if field_def.field_type == 'many2one':
                        field_attrs.append('optional="show"')
                    else:
                        field_attrs.append('optional="hide"')
                else:
                    field_attrs.append('optional="show"')

                # Add widget for specific field types in tree view
                if field_def.field_type == 'many2many':
                    field_attrs.append('widget="many2many_tags"')
                elif field_def.field_type == 'selection':
                    field_attrs.append('widget="badge"')

                attrs_str = ' ' + ' '.join(field_attrs) if field_attrs else ''
                tree_fields.append(f'<field name="{field_def.name}"{attrs_str}/>')
                field_count += 1

        # Add remaining relational fields as optional
        for field_def in form.field_definition_ids.sorted('sequence'):
            if field_def.name != 'name' and field_def.field_type in ['many2one', 'many2many', 'one2many']:
                if field_count >= 3:  # Only add if not already added above
                    field_attrs = ['optional="hide"']
                    if field_def.field_type == 'many2many':
                        field_attrs.append('widget="many2many_tags"')
                    attrs_str = ' ' + ' '.join(field_attrs)
                    tree_fields.append(f'<field name="{field_def.name}"{attrs_str}/>')

        tree_fields.append('<field name="state"/>')
        tree_fields.append('<field name="create_date"/>')

        # Generate form view fields with proper widgets and organization
        form_fields = []
        for field_def in form.field_definition_ids.sorted('sequence'):
            if field_def.name != 'name':  # name is handled separately
                field_attrs = []

                # Add widget if specified or determine based on field type
                widget = field_def.widget
                if not widget:
                    if field_def.field_type == 'many2many':
                        widget = 'many2many_tags'
                    elif field_def.field_type == 'one2many':
                        widget = 'one2many_list'
                    elif field_def.field_type == 'text':
                        widget = 'text'
                    elif field_def.field_type == 'html':
                        widget = 'html'
                    elif field_def.field_type == 'selection':
                        widget = 'selection'

                if widget:
                    field_attrs.append(f'widget="{widget}"')

                # Add domain if specified for relational fields
                if field_def.field_type in ['many2one', 'many2many', 'one2many'] and field_def.domain:
                    # Check if the domain is valid for the relation model
                    domain = field_def.domain
                    # Remove invalid active domain for models that don't have active field
                    if field_def.relation_model == 'project.tags' and "[('active', '=', True)]" in domain:
                        domain = "[]"  # Use empty domain for project.tags
                    if domain and domain != "[]":
                        # Properly escape domain for XML
                        escaped_domain = domain.replace("'", "&apos;").replace('"', "&quot;")
                        field_attrs.append(f'domain="{escaped_domain}"')

                # Add options for relational fields
                if field_def.field_type == 'many2many':
                    field_attrs.append("options=\"{'no_create': False, 'no_open': False}\"")
                elif field_def.field_type == 'many2one':
                    field_attrs.append("options=\"{'no_create': False, 'no_open': False}\"")

                # Add context for relational fields if needed
                if field_def.field_type in ['many2one', 'many2many', 'one2many']:
                    if field_def.relation_model:
                        field_attrs.append(f"context=\"{{'default_model': '{field_def.relation_model}'}}\"")

                attrs_str = ' ' + ' '.join(field_attrs) if field_attrs else ''
                form_fields.append(f'                        <field name="{field_def.name}"{attrs_str}/>')

        # Generate workflow buttons
        workflow_buttons = []
        for i, state in enumerate(workflow_states):
            if i < len(workflow_states) - 1:  # Not the last state
                next_state = workflow_states[i + 1]
                method_name = f"action_{state.code}_to_{next_state.code}"
                button_label = f"{state.name} → {next_state.name}"
                workflow_buttons.append(f'''                    <button name="{method_name}" type="object" string="{button_label}"
                            class="btn-primary" invisible="state != '{state.code}'"/>''')

        # Add payment action buttons
        workflow_buttons.append('''                    <button name="action_request_payment" type="object" string="Request Payment"
                            class="btn-warning" invisible="payment_status != 'none'"/>''')
        workflow_buttons.append('''                    <button name="action_record_cash_payment" type="object" string="Record Cash Payment"
                            class="btn-success" invisible="payment_status != 'pending'"/>''')
        workflow_buttons.append('''                    <button name="action_refresh_payment_status" type="object" string="Refresh Payment Status"
                            class="btn-info" invisible="payment_status not in ['pending', 'none']"/>''')
        workflow_buttons.append('''                    <button name="action_view_sales_order" type="object" string="View Sales Order"
                            class="btn-info" invisible="not sales_order_id"/>''')
        workflow_buttons.append('''                    <button name="action_open_payment_portal" type="object" string="Open Payment Portal"
                            class="btn-secondary" invisible="not payment_url"/>''')

        # ✨ Add AI Response Buttons
        workflow_buttons.append('''                    <!-- ✨ AI Response Buttons -->''')
        workflow_buttons.append('''                    <button name="action_get_ai_response" type="object" string="Get AI Response"
                            class="btn-secondary" icon="fa-magic"
                            invisible="ai_response_generated == True"/>''')
        workflow_buttons.append('''                    <button name="action_generate_ai_pdf" type="object" string="Regenerate PDF"
                            class="btn-info" icon="fa-file-pdf-o"
                            invisible="ai_response == False"
                            help="Generate fresh PDF from current AI response content"
                            groups="base.group_user"/>''')

        # Generate statusbar states
        statusbar_states = []
        for state in workflow_states:
            statusbar_states.append(state.code)
        statusbar_visible = ','.join(statusbar_states) if statusbar_states else 'draft'

        # Tree View
        tree_view = f'''    <!-- {form_name} Tree View -->
    <record id="{form_code}_tree_view" model="ir.ui.view">
        <field name="name">{form_name} Tree</field>
        <field name="model">{model_name}</field>
        <field name="arch" type="xml">
            <tree>
                {chr(10).join(tree_fields)}
            </tree>
        </field>
    </record>'''

        # Organize form fields by type for better layout
        basic_fields = []
        relational_fields = []
        text_fields = []

        for field_def in form.field_definition_ids.sorted('sequence'):
            if field_def.name != 'name':
                field_attrs = []

                # Add widget if specified or determine based on field type
                widget = field_def.widget
                if not widget:
                    if field_def.field_type == 'many2many':
                        widget = 'many2many_tags'
                    elif field_def.field_type == 'one2many':
                        widget = 'one2many_list'
                    elif field_def.field_type == 'text':
                        widget = 'text'
                    elif field_def.field_type == 'html':
                        widget = 'html'

                if widget:
                    field_attrs.append(f'widget="{widget}"')

                # Add domain if specified for relational fields
                if field_def.field_type in ['many2one', 'many2many', 'one2many'] and field_def.domain:
                    domain = field_def.domain
                    if field_def.relation_model == 'project.tags' and "[('active', '=', True)]" in domain:
                        domain = "[]"
                    if domain and domain != "[]":
                        escaped_domain = domain.replace("'", "&apos;").replace('"', "&quot;")
                        field_attrs.append(f'domain="{escaped_domain}"')

                # Add options for relational fields
                if field_def.field_type == 'many2many':
                    field_attrs.append("options=\"{'no_create': False, 'no_open': False}\"")
                elif field_def.field_type == 'many2one':
                    field_attrs.append("options=\"{'no_create': False, 'no_open': False}\"")

                attrs_str = ' ' + ' '.join(field_attrs) if field_attrs else ''
                field_html = f'                        <field name="{field_def.name}"{attrs_str}/>'

                # Categorize fields for better organization
                if field_def.field_type in ['many2one', 'many2many', 'one2many']:
                    relational_fields.append(field_html)
                elif field_def.field_type in ['text', 'html']:
                    text_fields.append(field_html)
                else:
                    basic_fields.append(field_html)

        # Form View with organized layout
        form_view = f'''    <!-- {form_name} Form View -->
    <record id="{form_code}_form_view" model="ir.ui.view">
        <field name="name">{form_name} Form</field>
        <field name="model">{model_name}</field>
        <field name="arch" type="xml">
            <form>
                <header>
{chr(10).join(workflow_buttons) if workflow_buttons else "                    <!-- No workflow buttons -->"}
                    <field name="state" widget="statusbar" statusbar_visible="{statusbar_visible}"/>
                </header>
                <sheet>
                    <group>
                        <group name="basic_info">
                            <field name="name"/>
{chr(10).join(basic_fields) if basic_fields else "                            <!-- No basic fields -->"}
                        </group>
                        <group name="relational_info">
{chr(10).join(relational_fields) if relational_fields else "                            <!-- No relational fields -->"}
                            <field name="active"/>
                        </group>
                    </group>
                    <group name="payment_info" string="Payment Information">
                        <group>
                            <field name="payment_rate"/>
                            <field name="payment_amount" readonly="1"/>
                            <field name="payment_status"/>
                        </group>
                        <group>
                            <field name="rate_changed_by" readonly="1"/>
                            <field name="rate_changed_date" readonly="1"/>
                            <field name="sales_order_id" readonly="1"/>
                            <field name="payment_url" readonly="1"/>
                        </group>
                    </group>
{chr(10).join(text_fields) if text_fields else "                    <!-- No text fields -->"}

                    <!-- ✨ AI Response Section -->
                    <!-- Hidden fields for AI functionality -->
                    <field name="ai_response_generated" invisible="1"/>
                    <field name="ai_response_pdf" invisible="1"/>
                    <field name="ai_response_pdf_filename" invisible="1"/>

                    <notebook>
                        <page string="AI Response &amp; Document Draft">
                            <group>
                                <div class="alert alert-info" invisible="ai_response_generated == True">
                                    <p><strong>AI Response &amp; Document Draft</strong></p>
                                    <p>Use this section to:</p>
                                    <ul>
                                        <li>Generate AI response using the "Get AI Response" button above</li>
                                        <li>Manually create or edit document content</li>
                                        <li>Format content using the rich text editor</li>
                                        <li>Generate PDF for customer download</li>
                                    </ul>
                                </div>
                                <field name="ai_response" widget="html" nolabel="1"
                                       placeholder="Click 'Get AI Response' button to generate AI content, or manually type your document content here..."/>
                                <separator string="AI Response Details" invisible="ai_response_generated == False"/>
                                <group invisible="ai_response_generated == False">
                                    <field name="ai_last_prompt" readonly="1" string="Last AI Prompt Used"/>
                                    <field name="ai_response_pdf" filename="ai_response_pdf_filename"
                                           invisible="ai_response_pdf == False"/>
                                    <field name="ai_response_pdf_filename" invisible="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>'''

        # Kanban View - Generate kanban view for workflow visualization
        kanban_view = self._generate_kanban_view(form, template, workflow_states)

        # Action with specific view binding and default grouping
        state_field = 'state'  # Default state field for grouping
        action = f'''    <!-- {form_name} Action -->
    <record id="{form_code}_action" model="ir.actions.act_window">
        <field name="name">{form_name}</field>
        <field name="res_model">{model_name}</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
                                      (0, 0, {{'view_mode': 'kanban', 'view_id': ref('{form_code}_kanban_view')}}),
                                      (0, 0, {{'view_mode': 'tree', 'view_id': ref('{form_code}_tree_view')}}),
                                      (0, 0, {{'view_mode': 'form', 'view_id': ref('{form_code}_form_view')}})]"/>
        <field name="context">{{'group_by': '{state_field}'}}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first {form_name.lower()}!
            </p>
            <p>
                Click the "New" button to create a new {form_name.lower()}.
            </p>
        </field>
    </record>'''

        # Menu - Create a single menu structure
        if len(template.form_builder_ids) == 1:
            # Single form - create direct menu
            menu = f'''    <!-- {form_name} Menu -->
    <menuitem id="{form_code}_menu" name="{form_name}" action="{form_code}_action" sequence="10"/>'''
        else:
            # Multiple forms - create under module root menu
            module_root_menu = f"{self.custom_prefix}_{self.module_name}_main_menu"
            menu = f'''    <!-- {form_name} Menu -->
    <menuitem id="{form_code}_menu" name="{form_name}"
              parent="{module_root_menu}" action="{form_code}_action" sequence="10"/>'''

        return {
            'views': [tree_view, form_view, kanban_view],
            'actions': [action],
            'menus': [menu]
        }

    def _generate_kanban_view(self, form, template, workflow_states):
        """Generate kanban view with workflow state grouping"""
        form_name = form.name
        form_code = form.code
        model_name = form.model_name

        # Determine key fields for kanban cards
        key_fields = []
        priority_field = None
        date_field = None
        user_field = None

        for field_def in form.field_definition_ids:
            if field_def.field_type == 'many2one' and 'user' in field_def.name.lower():
                user_field = field_def.name
            elif field_def.field_type == 'date' or field_def.field_type == 'datetime':
                if not date_field:  # Take first date field
                    date_field = field_def.name
            elif field_def.field_type == 'selection' and 'priority' in field_def.name.lower():
                priority_field = field_def.name
            elif field_def.field_type in ['char', 'text'] and field_def.name != 'name':
                key_fields.append(field_def.name)

        # Build kanban card content
        card_content = []

        # Add priority indicator if available
        if priority_field:
            card_content.append(f'''                        <div class="oe_kanban_bottom_left">
                            <field name="{priority_field}" widget="priority"/>
                        </div>''')

        # Add user avatar if available
        if user_field:
            card_content.append(f'''                        <div class="oe_kanban_bottom_right">
                            <field name="{user_field}" widget="many2one_avatar_user"/>
                        </div>''')

        # Add date information if available
        if date_field:
            card_content.append(f'''                        <div class="oe_kanban_footer">
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="{date_field}"/>
                                </div>
                            </div>
                        </div>''')

        # Add key field content
        key_field_content = []
        for field_name in key_fields[:2]:  # Limit to 2 key fields for clean display
            key_field_content.append(f'''                        <div class="o_kanban_record_subtitle">
                            <field name="{field_name}"/>
                        </div>''')

        # Generate state-based color coding
        state_colors = []
        for i, state in enumerate(workflow_states):
            color_class = ['danger', 'warning', 'info', 'success', 'primary'][i % 5]
            state_colors.append(f'''                    <field name="color"/>
                    <t t-if="record.state.raw_value == '{state[0]}'">
                        <t t-set="color" t-value="'{color_class}'"/>
                    </t>''')

        kanban_view = f'''    <!-- {form_name} Kanban View -->
    <record id="{form_code}_kanban_view" model="ir.ui.view">
        <field name="name">{form_name} Kanban</field>
        <field name="model">{model_name}</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column" quick_create="false">
                <field name="id"/>
                <field name="name"/>
                <field name="state"/>
                <field name="color"/>
{chr(10).join([f'                <field name="{field}"/>' for field in [user_field, date_field, priority_field] if field])}
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="{{{{kanban_color(record.color.raw_value)}}}} oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="o_dropdown_kanban dropdown">
                                    <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                        <span class="fa fa-ellipsis-v"/>
                                    </a>
                                    <div class="dropdown-menu" role="menu">
                                        <a t-if="widget.editable" role="menuitem" type="edit" class="dropdown-item">Edit</a>
                                        <a t-if="widget.deletable" role="menuitem" type="delete" class="dropdown-item">Delete</a>
                                        <div role="separator" class="dropdown-divider"/>
                                        <div class="dropdown-item-text text-muted">
                                            <small>ID: <field name="id"/></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
{chr(10).join(key_field_content)}
                            </div>
{chr(10).join(card_content)}
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>'''

        return kanban_view

    def _generate_portal_controllers(self, module_path):
        """Generate customer portal controllers for generated modules"""
        self.generation_log += "Generating customer portal controllers...\n"

        controllers_dir = os.path.join(module_path, 'controllers')
        os.makedirs(controllers_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            raise UserError(_("No template selected"))

        # Generate controllers/__init__.py
        controller_imports = []

        # Import main website controller
        controller_imports.append("from . import main")
        controller_imports.append("from . import api")

        # Import portal controllers for each form
        for form in template.form_builder_ids:
            controller_imports.append(f"from . import {form.code}_portal")

        init_content = "\n".join(controller_imports)
        with open(os.path.join(controllers_dir, '__init__.py'), 'w') as f:
            f.write(init_content)

        # Generate portal controller for each form
        for form in template.form_builder_ids:
            self._generate_simple_portal_controller(controllers_dir, form, template)

    def _generate_simple_portal_controller(self, controllers_dir, form, template):
        """Generate a simple portal controller without complex f-strings"""
        self.generation_log += f"Generating simple portal controller for form: {form.name}...\n"

        form_name = form.name
        form_code = form.code
        model_name = form.model_name
        class_name = ''.join(word.capitalize() for word in form.code.split('_'))
        route_path = form_code.replace('_', '-')

        # Generate simple controller content
        controller_content = f'''from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal
from odoo.exceptions import AccessError, MissingError
import logging

_logger = logging.getLogger(__name__)


class {class_name}Portal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        """Add {form_name} count to portal home"""
        values = super()._prepare_home_portal_values(counters)

        if '{form_code}' in counters:
            partner = request.env.user.partner_id
            {form_code}_count = request.env['{model_name}'].search_count([
                '|', '|',
                ('customer_email', '=', partner.email),
                ('partner_id', '=', partner.id),
                ('create_uid', '=', request.env.user.id)
            ]) if partner.email else 0
            values['{form_code}_count'] = {form_code}_count

        return values

    @http.route(['/my/{route_path}'], type='http', auth="user", website=True)
    def portal_{form_code}_list(self, **kw):
        """Display {form_name} list in customer portal"""
        partner = request.env.user.partner_id

        if not partner.email:
            return request.render('portal.portal_my_home')

        # Base domain for user's records
        domain = [
            '|', '|',
            ('customer_email', '=', partner.email),
            ('partner_id', '=', partner.id),
            ('create_uid', '=', request.env.user.id)
        ]

        # Handle search functionality
        search_query = kw.get('search', '').strip()
        if search_query:
            search_domain = [
                '|', '|', '|',
                ('name', 'ilike', search_query),
                ('customer_name', 'ilike', search_query),
                ('customer_email', 'ilike', search_query),
                ('state', 'ilike', search_query)
            ]
            domain = ['&'] + domain + search_domain

        # Handle state filter
        state_filter = kw.get('state', '').strip()
        if state_filter:
            domain.append(('state', '=', state_filter))

        {form_code}_records = request.env['{model_name}'].search(domain, order='create_date desc')

        # Get available states for filter dropdown
        all_states = request.env['{model_name}']._fields['state'].selection

        values = {{
            '{form_code}_records': {form_code}_records,
            'page_name': '{form_name}',
            'search_query': search_query,
            'state_filter': state_filter,
            'available_states': all_states,
        }}

        return request.render('{self.custom_prefix}_{self.module_name}.portal_{form_code}_list', values)

    @http.route(['/my/{route_path}/<int:record_id>'], type='http', auth="user", website=True)
    def portal_{form_code}_detail(self, record_id, **kw):
        """Display {form_name} detail in customer portal"""
        try:
            {form_code}_sudo = self._document_check_access('{model_name}', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {{
            '{form_code}': {form_code}_sudo,
            'page_name': '{form_name}',
        }}

        return request.render('{self.custom_prefix}_{self.module_name}.portal_{form_code}_detail', values)

    @http.route(['/my/{route_path}/<int:record_id>/edit'], type='http', auth="user", website=True)
    def portal_{form_code}_edit(self, record_id, **kw):
        """Edit {form_name} in customer portal (only for draft state)"""
        try:
            {form_code}_sudo = self._document_check_access('{model_name}', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if record is in initial state (editable) - feedback_submitted is the initial state
        editable_state = 'feedback_submitted'

        if {form_code}_sudo.state != editable_state:
            return request.redirect('/my/{route_path}/' + str(record_id))

        values = {{
            '{form_code}': {form_code}_sudo,
            'page_name': '{form_name}',
            'is_edit_mode': True,
        }}

        return request.render('{self.custom_prefix}_{self.module_name}.portal_{form_code}_edit', values)

    @http.route(['/my/{route_path}/<int:record_id>/update'], type='http', auth="user", website=True, methods=['POST'], csrf=False)
    def portal_{form_code}_update(self, record_id, **kw):
        """Update {form_name} from customer portal"""
        try:
            {form_code}_sudo = self._document_check_access('{model_name}', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if record is in initial state (editable) - feedback_submitted is the initial state
        editable_state = 'feedback_submitted'

        if {form_code}_sudo.state != editable_state:
            return request.redirect('/my/{route_path}/' + str(record_id))

        # Extract form data and update record
        update_values = {{}}

        # Process form fields
        for field_name, field_value in kw.items():
            if field_name.startswith('csrf_token') or field_name in ['submit']:
                continue
            if field_value:
                update_values[field_name] = field_value

        # Update the record
        if update_values:
            {form_code}_sudo.sudo().write(update_values)

        return request.redirect('/my/{route_path}/' + str(record_id) + '?updated=1')

    @http.route(['/my/{route_path}/<int:record_id>/download-pdf'], type='http', auth="user", website=True)
    def portal_{form_code}_download_pdf(self, record_id, **kw):
        """Download AI Response PDF for paid customers"""
        try:
            {form_code}_sudo = self._document_check_access('{model_name}', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if customer has paid
        if {form_code}_sudo.payment_status != 'paid':
            return request.render('portal.portal_error', {{
                'error_title': _('Access Denied'),
                'error_message': _('PDF download is only available for paid customers. Please complete your payment first.'),
            }})

        # ✨ ALWAYS regenerate PDF based on current AI response content
        try:
            # If no AI response exists, generate it first
            if not {form_code}_sudo.ai_response:
                _logger.info(f"Generating AI response for portal download - record {{record_id}}")
                {form_code}_sudo.action_get_ai_response()
                # Refresh the record to get updated data
                {form_code}_sudo = {form_code}_sudo.browse(record_id)

            # ALWAYS generate fresh PDF based on current AI response content
            _logger.info(f"Generating fresh PDF from current AI response for portal download - record {{record_id}}")
            {form_code}_sudo.action_generate_ai_pdf()
            # Refresh the record to get updated data
            {form_code}_sudo = {form_code}_sudo.browse(record_id)

        except Exception as e:
            _logger.error(f"Error generating AI response/PDF for record {{record_id}}: {{str(e)}}")
            return request.render('portal.portal_error', {{
                'error_title': _('Generation Error'),
                'error_message': _('Failed to generate AI response or PDF. Please try again later or contact support.'),
            }})

        # Final check if PDF is available after generation attempts
        if not {form_code}_sudo.ai_response_pdf:
            return request.render('portal.portal_error', {{
                'error_title': _('PDF Not Available'),
                'error_message': _('AI Response PDF could not be generated. Please contact support.'),
            }})

        # Return the PDF file
        pdf_data_base64 = {form_code}_sudo.ai_response_pdf
        filename = {form_code}_sudo.ai_response_pdf_filename or f'{form_code}_ai_response_{{record_id}}.pdf'

        # Decode base64 PDF data to binary
        import base64
        try:
            pdf_data = base64.b64decode(pdf_data_base64)
        except Exception as e:
            _logger.error(f"Error decoding PDF data for record {{record_id}}: {{str(e)}}")
            return request.render('portal.portal_error', {{
                'error_title': _('PDF Decode Error'),
                'error_message': _('Failed to decode PDF data. Please try regenerating the PDF.'),
            }})

        return request.make_response(
            pdf_data,
            headers=[
                ('Content-Type', 'application/pdf'),
                ('Content-Disposition', f'attachment; filename="{{filename}}"'),
                ('Content-Length', len(pdf_data)),
            ]
        )
'''

        # Write controller file with error handling
        controller_file_path = os.path.join(controllers_dir, f'{form_code}_portal.py')
        try:
            # Check if file exists and remove it first to avoid permission issues
            if os.path.exists(controller_file_path):
                os.remove(controller_file_path)

            with open(controller_file_path, 'w', encoding='utf-8') as f:
                f.write(controller_content)

            self.generation_log += f"Successfully generated portal controller: {controller_file_path}\n"
        except PermissionError as e:
            error_msg = f"Permission denied writing controller file {controller_file_path}: {str(e)}"
            self.generation_log += f"ERROR: {error_msg}\n"
            raise UserError(_(error_msg))
        except Exception as e:
            error_msg = f"Error writing controller file {controller_file_path}: {str(e)}"
            self.generation_log += f"ERROR: {error_msg}\n"
            raise UserError(_(error_msg))

        # Generate simple portal templates
        self._generate_simple_portal_templates(form, template, controllers_dir)

    def _generate_simple_portal_templates(self, form, template, controllers_dir):
        """Generate simple portal templates without complex syntax"""
        form_name = form.name
        form_code = form.code

        # Get module path from controllers directory
        module_path = os.path.dirname(controllers_dir)

        # Create portal templates directory
        templates_dir = os.path.join(module_path, 'views', 'portal')
        os.makedirs(templates_dir, exist_ok=True)

        # Generate simple list template
        list_template = f'''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="portal_{form_code}_list" name="{form_name} List">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>{form_name} List</h3>
                            <div class="d-flex">
                                <form method="get" class="form-inline mr-3">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control" placeholder="Search records..."
                                               t-att-value="search_query" style="min-width: 200px;"/>
                                        <div class="input-group-append">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search"/> Search
                                            </button>
                                        </div>
                                    </div>
                                    <input type="hidden" name="state" t-att-value="state_filter"/>
                                </form>
                                <form method="get" class="form-inline">
                                    <select name="state" class="form-control mr-2" onchange="this.form.submit()">
                                        <option value="">All States</option>
                                        <t t-foreach="available_states" t-as="state_option">
                                            <option t-att-value="state_option[0]"
                                                    t-att-selected="'selected' if state_filter == state_option[0] else None">
                                                <t t-esc="state_option[1]"/>
                                            </option>
                                        </t>
                                    </select>
                                    <input type="hidden" name="search" t-att-value="search_query"/>
                                </form>
                            </div>
                        </div>

                        <t t-if="search_query or state_filter">
                            <div class="alert alert-info">
                                <strong>Filters Applied:</strong>
                                <t t-if="search_query">
                                    Search: "<t t-esc="search_query"/>"
                                </t>
                                <t t-if="state_filter">
                                    State: <t t-esc="dict(available_states).get(state_filter, state_filter)"/>
                                </t>
                                <a href="/my/{form_code.replace('_', '-')}" class="btn btn-sm btn-outline-secondary ml-2">Clear Filters</a>
                            </div>
                        </t>

                        <t t-if="{form_code}_records">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-foreach="{form_code}_records" t-as="record">
                                        <tr>
                                            <td><t t-esc="record.name"/></td>
                                            <td><span t-field="record.state" class="badge badge-info"/></td>
                                            <td><t t-esc="record.create_date"/></td>
                                            <td>
                                                <a t-attf-href="/my/{form_code.replace('_', '-')}/{{{{record.id}}}}" class="btn btn-sm btn-primary">View</a>
                                                <t t-if="record.state == 'feedback_submitted'">
                                                    <a t-attf-href="/my/{form_code.replace('_', '-')}/{{{{record.id}}}}/edit" class="btn btn-sm btn-warning ml-1">Edit</a>
                                                </t>
                                                <t t-if="record.sales_order_id and record.payment_status == 'pending'">
                                                    <a t-attf-href="/my/orders/{{{{record.sales_order_id.id}}}}" class="btn btn-sm btn-success ml-1">
                                                        <i class="fa fa-credit-card"/> Pay Now
                                                    </a>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                </tbody>
                            </table>
                        </t>
                        <t t-else="">
                            <div class="alert alert-warning">
                                <t t-if="search_query or state_filter">
                                    <h5>No results found</h5>
                                    <p>No {form_name.lower()} match your search criteria.</p>
                                    <a href="/my/{form_code.replace('_', '-')}" class="btn btn-primary">View All Records</a>
                                </t>
                                <t t-else="">
                                    <h5>No records found</h5>
                                    <p>You haven't submitted any {form_name.lower()} yet.</p>
                                </t>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_{form_code}_detail" name="{form_name} Detail">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="mb-0"><t t-esc="{form_code}.name"/></h3>
                                <div>
                                    <span t-field="{form_code}.state" class="badge badge-info"/>
                                    <t t-if="{form_code}.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/{form_code.replace('_', '-')}/{{{{ {form_code}.id }}}}/edit" class="btn btn-sm btn-warning ml-2">
                                            <i class="fa fa-edit"/> Edit
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <t t-if="request.params.get('updated')">
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        <strong>Success!</strong> Your information has been updated successfully.
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span>×</span>
                                        </button>
                                    </div>
                                </t>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Details</h5>
                                        <p><strong>Name:</strong> <t t-esc="{form_code}.name"/></p>
                                        <p><strong>Status:</strong> <span t-field="{form_code}.state" class="badge badge-info"/></p>
                                        <p><strong>Created:</strong> <t t-esc="{form_code}.create_date"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Actions</h5>
                                        <t t-if="{form_code}.state == 'feedback_submitted'">
                                            <p class="text-muted">You can edit this record while it's in initial state.</p>
                                            <a t-attf-href="/my/{form_code.replace('_', '-')}/{{{{ {form_code}.id }}}}/edit" class="btn btn-warning">
                                                <i class="fa fa-edit"/> Edit Information
                                            </a>
                                        </t>
                                        <t t-else="">
                                            <p class="text-muted">This record cannot be edited in its current state.</p>
                                        </t>

                                        <!-- Payment Section -->
                                        <t t-if="{form_code}.sales_order_id">
                                            <h5 class="mt-4">Payment Information</h5>
                                            <div class="card">
                                                <div class="card-body">
                                                    <p><strong>Order Number:</strong> <t t-esc="{form_code}.sales_order_id.name"/></p>
                                                    <p><strong>Amount:</strong> <t t-esc="{form_code}.sales_order_id.amount_total"/> <t t-esc="{form_code}.sales_order_id.currency_id.name"/></p>
                                                    <p><strong>Payment Status:</strong>
                                                        <t t-if="{form_code}.payment_status == 'pending'">
                                                            <span class="badge badge-warning">Payment Pending</span>
                                                        </t>
                                                        <t t-elif="{form_code}.payment_status == 'paid'">
                                                            <span class="badge badge-success">Payment Completed</span>
                                                        </t>
                                                        <t t-elif="{form_code}.payment_status == 'failed'">
                                                            <span class="badge badge-danger">Payment Failed</span>
                                                        </t>
                                                        <t t-else="">
                                                            <span class="badge badge-secondary">No Payment Required</span>
                                                        </t>
                                                    </p>

                                                    <t t-if="{form_code}.payment_status == 'pending'">
                                                        <div class="mt-3">
                                                            <a t-attf-href="/my/orders/{{{{ {form_code}.sales_order_id.id }}}}" class="btn btn-success">
                                                                <i class="fa fa-credit-card"/> Make Payment
                                                            </a>
                                                        </div>
                                                    </t>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_{form_code}_edit" name="{form_name} Edit">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="mb-0">Edit <t t-esc="{form_code}.name"/></h3>
                            </div>
                            <div class="card-body">
                                <form method="post" t-attf-action="/my/{form_code.replace('_', '-')}/{{{{ {form_code}.id }}}}/update" class="needs-validation" novalidate="true">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="customer_name">Customer Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" name="customer_name"
                                                       t-att-value="{form_code}.customer_name" required="true"/>
                                                <div class="invalid-feedback">Please provide a valid customer name.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="customer_email">Email Address <span class="text-danger">*</span></label>
                                                <input type="email" class="form-control" name="customer_email"
                                                       t-att-value="{form_code}.customer_email" required="true"/>
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="overall_rating">Overall Rating <span class="text-danger">*</span></label>
                                                <select class="form-control" name="overall_rating" required="true">
                                                    <option value="">Select Rating</option>
                                                    <option value="1" t-att-selected="'selected' if {form_code}.overall_rating == '1' else None">1 Star</option>
                                                    <option value="2" t-att-selected="'selected' if {form_code}.overall_rating == '2' else None">2 Stars</option>
                                                    <option value="3" t-att-selected="'selected' if {form_code}.overall_rating == '3' else None">3 Stars</option>
                                                    <option value="4" t-att-selected="'selected' if {form_code}.overall_rating == '4' else None">4 Stars</option>
                                                    <option value="5" t-att-selected="'selected' if {form_code}.overall_rating == '5' else None">5 Stars</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a rating.</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="priority">Priority Level</label>
                                                <select class="form-control" name="priority">
                                                    <option value="">Select Priority</option>
                                                    <option value="low" t-att-selected="'selected' if {form_code}.priority == 'low' else None">Low Priority</option>
                                                    <option value="medium" t-att-selected="'selected' if {form_code}.priority == 'medium' else None">Medium Priority</option>
                                                    <option value="high" t-att-selected="'selected' if {form_code}.priority == 'high' else None">High Priority</option>
                                                    <option value="urgent" t-att-selected="'selected' if {form_code}.priority == 'urgent' else None">Urgent</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="feedback_comments">Comments &amp; Suggestions</label>
                                        <textarea class="form-control" name="feedback_comments" rows="4"
                                                  placeholder="Please share your detailed feedback..."><t t-esc="{form_code}.feedback_comments"/></textarea>
                                    </div>

                                    <div class="form-group text-right">
                                        <a t-attf-href="/my/{form_code.replace('_', '-')}/{{{{ {form_code}.id }}}}" class="btn btn-secondary mr-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary">Update Information</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>'''

        # Write portal templates file with error handling
        portal_templates_file = os.path.join(templates_dir, f'{form_code}_portal.xml')
        try:
            # Check if file exists and remove it first to avoid permission issues
            if os.path.exists(portal_templates_file):
                os.remove(portal_templates_file)

            with open(portal_templates_file, 'w', encoding='utf-8') as f:
                f.write(list_template)

            self.generation_log += f"Successfully generated portal templates: {portal_templates_file}\n"
        except PermissionError as e:
            error_msg = f"Permission denied writing template file {portal_templates_file}: {str(e)}"
            self.generation_log += f"ERROR: {error_msg}\n"
            raise UserError(_(error_msg))
        except Exception as e:
            error_msg = f"Error writing template file {portal_templates_file}: {str(e)}"
            self.generation_log += f"ERROR: {error_msg}\n"
            raise UserError(_(error_msg))

        # Generate portal menu integration
        self._generate_simple_portal_menu(form, template, module_path)

        # Update manifest to include portal templates
        self._update_manifest_for_portal(form_code, module_path)

    def _generate_simple_portal_menu(self, form, template, module_path):
        """Generate simple portal menu integration"""
        form_name = form.name
        form_code = form.code
        route_path = form_code.replace('_', '-')

        # Create simple portal menu template using working pattern
        menu_template = f'''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Portal Menu Integration -->
    <template id="portal_my_home_{form_code}" name="Portal My Home {form_name}" inherit_id="portal.portal_my_home" priority="40">
        <xpath expr="//div[@class='o_portal_docs row g-2']" position="inside">
            <div class="o_portal_category row g-2 mt-3" id="portal_{form_code}_category">
                <div class="o_portal_index_card col-md-6">
                    <a href="/my/{route_path}" title="{form_name}"
                       class="d-flex justify-content-start gap-2 gap-md-3 align-items-center py-3 pe-2 px-md-3 h-100 rounded text-decoration-none text-reset text-bg-light">
                        <div class="o_portal_icon align-self-start">
                            <i class="fa fa-file-text fa-3x text-primary"></i>
                        </div>
                        <div>
                            <h5 class="mt-0 mb-1">{form_name}</h5>
                            <p class="mb-0 text-muted">View and manage your {form_name.lower()} submissions</p>
                        </div>
                    </a>
                </div>
            </div>
        </xpath>
    </template>
</odoo>'''

        # Write portal menu file with error handling
        portal_menu_file = os.path.join(module_path, 'views', 'portal', f'{form_code}_portal_menu.xml')
        try:
            # Check if file exists and remove it first to avoid permission issues
            if os.path.exists(portal_menu_file):
                os.remove(portal_menu_file)

            with open(portal_menu_file, 'w', encoding='utf-8') as f:
                f.write(menu_template)

            self.generation_log += f"Successfully generated portal menu: {portal_menu_file}\n"
        except PermissionError as e:
            error_msg = f"Permission denied writing menu file {portal_menu_file}: {str(e)}"
            self.generation_log += f"ERROR: {error_msg}\n"
            raise UserError(_(error_msg))
        except Exception as e:
            error_msg = f"Error writing menu file {portal_menu_file}: {str(e)}"
            self.generation_log += f"ERROR: {error_msg}\n"
            raise UserError(_(error_msg))

    def _generate_form_portal_controller(self, controllers_dir, form, template):
        """Generate portal controller for a specific form - DEPRECATED, use simple version"""
        # This method is deprecated in favor of _generate_simple_portal_controller
        # to avoid f-string syntax issues
        self._generate_simple_portal_controller(controllers_dir, form, template)

    def _generate_portal_templates(self, form, template):
        """Generate portal templates for a form"""
        form_name = form.name
        form_code = form.code

        # Create portal templates directory
        templates_dir = os.path.join(self.generated_module_path, 'views', 'portal')
        os.makedirs(templates_dir, exist_ok=True)

        # Generate list template using string formatting to avoid f-string issues
        route_path = form_code.replace('_', '-')

        list_template = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="portal_{form_code}_list" name="{form_name} List">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">{form_name}</t>
            </t>

            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>{form_name} List</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="{form_code}_records">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                    <th>Payment Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="{form_code}_records" t-as="record">
                                                    <tr>
                                                        <td>
                                                            <a t-attf-href="/my/{route_path}/{{{{record.id}}}}">
                                                                <t t-esc="record.name"/>
                                                            </a>
                                                        </td>
                                                        <td>
                                                            <span t-field="record.state" class="badge badge-info"/>
                                                        </td>
                                                        <td>
                                                            <span t-field="record.create_date" t-options="{{'widget': 'date'}}"/>
                                                        </td>
                                                        <td>
                                                            <span t-field="record.payment_status" class="badge" t-attf-class="badge badge-{{{{record.payment_status == 'paid' and 'success' or record.payment_status == 'pending' and 'warning' or 'secondary'}}}}"/>
                                                        </td>
                                                        <td>
                                                            <a t-attf-href="/my/{route_path}/{{{{record.id}}}}" class="btn btn-sm btn-primary">View</a>
                                                            <t t-if="record.payment_status == 'pending' and record.sales_order_id">
                                                                <a t-attf-href="/my/{route_path}/{{{{record.id}}}}/payment" class="btn btn-sm btn-success">Pay Now</a>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div t-call="portal.pager"/>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        <p>No {form_name_lower} found.</p>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_{form_code}_detail" name="{form_name} Detail">
        <t t-call="portal.portal_layout">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4><t t-esc="{form_code}.name"/></h4>
                                <span t-field="{form_code}.state" class="badge badge-info float-right"/>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Details</h5>
                                        <p><strong>Name:</strong> <span t-field="{form_code}.name"/></p>
                                        <p><strong>Status:</strong> <span t-field="{form_code}.state"/></p>
                                        <p><strong>Created:</strong> <span t-field="{form_code}.create_date"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Status:</strong>
                                            <span t-field="{form_code}.payment_status" class="badge" t-attf-class="badge badge-{{{form_code}.payment_status == 'paid' and 'success' or {form_code}.payment_status == 'pending' and 'warning' or 'secondary'}}"/>
                                        </p>
                                        <t t-if="{form_code}.payment_amount > 0">
                                            <p><strong>Amount:</strong> <span t-field="{form_code}.payment_amount" t-options="{{'widget': 'monetary'}}"/></p>
                                        </t>
                                        <t t-if="{form_code}.payment_status == 'pending' and {form_code}.sales_order_id">
                                            <a t-attf-href="/my/{route_path}/{{{form_code}.id}}/payment" class="btn btn-success">
                                                <i class="fa fa-credit-card"/> Pay Now
                                            </a>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>'''.format(
            form_code=form_code,
            form_name=form_name,
            form_name_lower=form_name.lower(),
            route_path=route_path
        )

        # Write portal templates file
        portal_templates_file = os.path.join(templates_dir, f'{form_code}_portal.xml')
        with open(portal_templates_file, 'w') as f:
            f.write(list_template)

        # Generate portal menu integration
        self._generate_portal_menu_integration(form, template, self.generated_module_path)

        # Update manifest to include portal templates
        self._update_manifest_for_portal(form_code, self.generated_module_path)

    def _generate_portal_menu_integration(self, form, template, module_path):
        """Generate portal menu integration for customer portal"""
        form_name = form.name
        form_code = form.code
        route_path = form_code.replace('_', '-')

        # Create portal menu template using working pattern
        menu_template = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Portal Menu Integration -->
    <template id="portal_my_home_{form_code}" name="Portal My Home {form_name}" inherit_id="portal.portal_my_home" priority="40">
        <xpath expr="//div[@class='o_portal_docs row g-2']" position="inside">
            <div class="o_portal_category row g-2 mt-3" id="portal_{form_code}_category">
                <div class="o_portal_index_card col-md-6">
                    <a href="/my/{route_path}" title="{form_name}"
                       class="d-flex justify-content-start gap-2 gap-md-3 align-items-center py-3 pe-2 px-md-3 h-100 rounded text-decoration-none text-reset text-bg-light">
                        <div class="o_portal_icon align-self-start">
                            <i class="fa fa-file-text fa-3x text-primary"></i>
                        </div>
                        <div>
                            <h5 class="mt-0 mb-1">{form_name}</h5>
                            <p class="mb-0 text-muted">View and manage your {form_name.lower()} submissions</p>
                        </div>
                    </a>
                </div>
            </div>
        </xpath>
    </template>

    <!-- Portal Breadcrumbs -->
    <template id="portal_breadcrumbs_{form_code}" name="{form_name} Portal Breadcrumbs" inherit_id="portal.portal_breadcrumbs" priority="40">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == '{form_name}'" t-attf-class="breadcrumb-item #{{'{form_code}' if not {form_code} else 'active'}}">
                <a t-if="{form_code}" t-attf-href="/my/{route_path}?{{{{keep_query()}}}}">{form_name}</a>
                <t t-else="">{form_name}</t>
            </li>
            <li t-if="{form_code}" class="breadcrumb-item active">
                <t t-esc="{form_code}.name"/>
            </li>
        </xpath>
    </template>
</odoo>'''.format(
            form_code=form_code,
            form_name=form_name,
            route_path=route_path
        )

        # Write portal menu file
        portal_menu_file = os.path.join(module_path, 'views', 'portal', f'{form_code}_portal_menu.xml')
        with open(portal_menu_file, 'w') as f:
            f.write(menu_template)

    def _update_manifest_for_portal(self, form_code, module_path):
        """Update manifest to include portal dependencies and templates"""
        manifest_path = os.path.join(module_path, '__manifest__.py')

        # Read current manifest
        with open(manifest_path, 'r') as f:
            manifest_content = f.read()

        # Add portal dependency if not present
        if "'portal'" not in manifest_content:
            manifest_content = manifest_content.replace(
                "'depends': ['base', 'web', 'mail']",
                "'depends': ['base', 'web', 'mail', 'portal']"
            )

        # Add portal templates to data files
        portal_template_path = f"'views/portal/{form_code}_portal.xml'"
        portal_menu_path = f"'views/portal/{form_code}_portal_menu.xml'"

        # Check if portal template files need to be added
        if portal_template_path not in manifest_content:
            # Find the data files section and add portal templates
            import re
            data_pattern = r"'data': \[(.*?)\]"
            match = re.search(data_pattern, manifest_content, re.DOTALL)
            if match:
                current_data = match.group(1)
                # Add portal template files
                new_data = current_data.rstrip()
                if not new_data.endswith(','):
                    new_data += ','
                new_data += f"\n        {portal_template_path},"
                new_data += f"\n        {portal_menu_path},"
                manifest_content = manifest_content.replace(
                    f"'data': [{current_data}]",
                    f"'data': [{new_data.rstrip(',')}]"
                )

        # Write updated manifest
        with open(manifest_path, 'w') as f:
            f.write(manifest_content)

    def _generate_security(self, module_path):
        """Generate security directory and files"""
        self.generation_log += "Generating security files...\n"

        security_dir = os.path.join(module_path, 'security')
        os.makedirs(security_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            raise UserError(_("No template selected"))

        # Generate access rights for each form model in the template
        access_lines = ['id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink']

        for form in template.form_builder_ids:
            # Convert model name to external ID format (dots to underscores)
            model_external_id = form.model_name.replace('.', '_')
            access_id = f"access_{form.code}_user"
            access_name = f"{form.name} User Access"

            # Add access rule for each model
            access_lines.append(f"{access_id},{access_name},model_{model_external_id},base.group_user,1,1,1,1")

            # Add manager access
            manager_access_id = f"access_{form.code}_manager"
            manager_access_name = f"{form.name} Manager Access"
            access_lines.append(f"{manager_access_id},{manager_access_name},model_{model_external_id},base.group_system,1,1,1,1")

        access_content = '\n'.join(access_lines) + '\n'

        with open(os.path.join(security_dir, 'ir.model.access.csv'), 'w') as f:
            f.write(access_content)

    def _generate_data(self, module_path):
        """Generate data directory and files"""
        self.generation_log += "Generating data files...\n"

        data_dir = os.path.join(module_path, 'data')
        os.makedirs(data_dir, exist_ok=True)

        # Generate data.xml
        data_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Add your data records here -->
    </data>
</odoo>'''

        with open(os.path.join(data_dir, 'data.xml'), 'w') as f:
            f.write(data_content)

        # Generate cron_jobs.xml
        self._generate_cron_jobs_file(data_dir)

    def _generate_cron_jobs_file(self, data_dir):
        """Generate cron_jobs.xml file for automated tasks"""
        self.generation_log += "Generating cron jobs file...\n"

        # Get template data
        template = self.template_id
        if not template:
            return

        # Generate cron jobs for each form
        cron_jobs = []
        for form in template.form_builder_ids:
            model_name = form.model_name
            model_ref = model_name.replace('.', '_')
            form_title = form.name

            # Add payment status update cron job
            cron_jobs.append(f'''        <!-- Cron job to update payment status for {form_title} -->
        <record id="ir_cron_update_payment_status_{form.code}" model="ir.cron">
            <field name="name">{form_title}: Update Payment Status</field>
            <field name="model_id" ref="model_{model_ref}"/>
            <field name="state">code</field>
            <field name="code">model._cron_update_payment_status()</field>
            <field name="interval_number">30</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
        </record>''')

        cron_content = f'''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
{chr(10).join(cron_jobs)}
    </data>
</odoo>'''

        with open(os.path.join(data_dir, 'cron_jobs.xml'), 'w') as f:
            f.write(cron_content)

    def _generate_controllers(self, module_path):
        """Generate controllers directory and files with API endpoints"""
        self.generation_log += "Generating controllers with API endpoints...\n"

        controllers_dir = os.path.join(module_path, 'controllers')
        os.makedirs(controllers_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            raise UserError(_("No template selected"))

        # Generate controllers/__init__.py
        controller_imports = ["from . import main"]
        if self.include_api:
            controller_imports.append("from . import api")

        with open(os.path.join(controllers_dir, '__init__.py'), 'w') as f:
            f.write("\n".join(controller_imports))

        # Generate main website controller
        self._generate_main_controller(controllers_dir, template)

        # Generate API controller if enabled
        if self.include_api:
            self._generate_api_controller(controllers_dir, template)

    def _generate_main_controller(self, controllers_dir, template):
        """Generate main website controller"""
        controller_class = f"{self.module_title.replace(' ', '')}Controller"

        # Generate routes for each form
        form_routes = []
        for form in template.form_builder_ids:
            if form.website_published:
                route_name = form.code.replace('_', '-')

                # Prepare field lists for this form
                all_field_names = [field.name for field in form.field_definition_ids]
                selection_field_names = [field.name for field in form.field_definition_ids if field.field_type == 'selection']
                many2one_field_names = [field.name for field in form.field_definition_ids if field.field_type == 'many2one']
                name_field_names = [field.name for field in form.field_definition_ids if 'name' in field.name.lower()]

                # Fields that need display method (selection and many2one)
                display_field_names = selection_field_names + many2one_field_names

                # Create field processing logic
                field_processing_code = []
                for field_def in form.field_definition_ids:
                    field_name = field_def.name
                    if field_def.field_type == 'many2one':
                        field_processing_code.append(f'''            if '{field_name}' in kwargs and kwargs['{field_name}']:
                try:
                    values['{field_name}'] = int(kwargs['{field_name}'])
                except (ValueError, TypeError):
                    pass  # Skip invalid values''')
                    elif field_def.field_type == 'many2many':
                        field_processing_code.append(f'''            if '{field_name}' in kwargs and kwargs['{field_name}']:
                try:
                    if isinstance(kwargs['{field_name}'], list):
                        values['{field_name}'] = [(6, 0, [int(x) for x in kwargs['{field_name}']])]
                    else:
                        ids = [int(x.strip()) for x in kwargs['{field_name}'].split(',') if x.strip()]
                        values['{field_name}'] = [(6, 0, ids)]
                except (ValueError, TypeError):
                    pass  # Skip invalid values''')
                    else:
                        field_processing_code.append(f'''            if '{field_name}' in kwargs and kwargs['{field_name}']:
                values['{field_name}'] = kwargs['{field_name}']''')

                # Create selection field defaults
                selection_defaults_code = []
                for field_name in selection_field_names:
                    selection_defaults_code.append(f'''            if '{field_name}' not in values or not values.get('{field_name}'):
                if 'rating' in '{field_name}'.lower():
                    values['{field_name}'] = '3'  # Default to 3 stars
                elif 'priority' in '{field_name}'.lower():
                    values['{field_name}'] = 'medium'  # Default to medium priority
                elif 'status' in '{field_name}'.lower() or 'state' in '{field_name}'.lower():
                    values['{field_name}'] = 'draft'  # Default to draft status
                else:
                    values['{field_name}'] = 'option1'  # Default to first option''')

                # Create name field logic
                name_field_logic = ''
                if name_field_names:
                    name_field_logic = f'''            if 'name' in kwargs:
                values['name'] = kwargs['name']
            elif '{name_field_names[0]}' in values:
                values['name'] = values['{name_field_names[0]}']
            else:
                values['name'] = 'Anonymous' '''
                else:
                    name_field_logic = '''            if 'name' in kwargs:
                values['name'] = kwargs['name']
            else:
                values['name'] = 'Anonymous' '''

                form_routes.append(f'''    @http.route('/{route_name}', type='http', auth='public', website=True)
    def {form.code}_form(self, **kwargs):
        """Form page for {form.name}"""
        return request.render('{self.custom_prefix}_{self.module_name}.{form.code}_website_form', {{}})

    @http.route('/{route_name}/submit', type='http', auth='public', website=True, methods=['POST'], csrf=False)
    def {form.code}_submit(self, **kwargs):
        """Submit {form.name} form"""
        try:
            # Create record from form data
            {form.code}_model = request.env['{form.model_name}'].sudo()
            values = {{}}

            # Extract and process form fields
{chr(10).join(field_processing_code)}

            # Handle missing selection fields with defaults
{chr(10).join(selection_defaults_code)}

            # Add required name field if not present
{name_field_logic}

            # Create the record
            record = {form.code}_model.create(values)

            return request.render('{self.custom_prefix}_{self.module_name}.{form.code}_success', {{
                'record': record
            }})
        except Exception as e:
            # Return simple error response
            return request.make_response(
                f'<html><body><h1>Error</h1><p>Form submission failed: {{str(e)}}</p><a href="/{route_name}">Back to Form</a></body></html>',
                status=500
            )''')

        controller_content = f'''from odoo import http
from odoo.http import request
import json


class {controller_class}(http.Controller):

    @http.route('/{self.module_name}', type='http', auth='public', website=True)
    def index(self, **kwargs):
        """Main page for {self.module_title}"""
        return request.render('{self.custom_prefix}_{self.module_name}.index', {{}})

{chr(10).join(form_routes) if form_routes else "    # No form routes generated"}
'''

        with open(os.path.join(controllers_dir, 'main.py'), 'w') as f:
            f.write(controller_content)

    def _generate_api_controller(self, controllers_dir, template):
        """Generate API controller with REST endpoints"""
        api_routes = []

        for form in template.form_builder_ids:
            model_name = form.model_name
            form_code = form.code

            # Generate CRUD API endpoints for each form
            api_routes.append(f'''    # {form.name} API Endpoints

    @http.route('/api/{form_code}', type='json', auth='user', methods=['GET'])
    def {form_code}_list(self, **kwargs):
        """List {form.name} records"""
        try:
            domain = kwargs.get('domain', [])
            limit = kwargs.get('limit', 100)
            offset = kwargs.get('offset', 0)

            records = request.env['{model_name}'].search(domain, limit=limit, offset=offset)

            return {{
                'success': True,
                'data': [{{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                }} for record in records],
                'total': request.env['{model_name}'].search_count(domain)
            }}
        except Exception as e:
            return {{'success': False, 'error': str(e)}}

    @http.route('/api/{form_code}/<int:record_id>', type='json', auth='user', methods=['GET'])
    def {form_code}_get(self, record_id, **kwargs):
        """Get specific {form.name} record"""
        try:
            record = request.env['{model_name}'].browse(record_id)
            if not record.exists():
                return {{'success': False, 'error': 'Record not found'}}

            return {{
                'success': True,
                'data': {{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                }}
            }}
        except Exception as e:
            return {{'success': False, 'error': str(e)}}

    @http.route('/api/{form_code}', type='json', auth='user', methods=['POST'])
    def {form_code}_create(self, **kwargs):
        """Create new {form.name} record"""
        try:
            values = kwargs.get('values', {{}})
            record = request.env['{model_name}'].create(values)

            return {{
                'success': True,
                'data': {{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }}
            }}
        except Exception as e:
            return {{'success': False, 'error': str(e)}}

    @http.route('/api/{form_code}/<int:record_id>', type='json', auth='user', methods=['PUT'])
    def {form_code}_update(self, record_id, **kwargs):
        """Update {form.name} record"""
        try:
            record = request.env['{model_name}'].browse(record_id)
            if not record.exists():
                return {{'success': False, 'error': 'Record not found'}}

            values = kwargs.get('values', {{}})
            record.write(values)

            return {{
                'success': True,
                'data': {{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }}
            }}
        except Exception as e:
            return {{'success': False, 'error': str(e)}}

    @http.route('/api/{form_code}/<int:record_id>', type='json', auth='user', methods=['DELETE'])
    def {form_code}_delete(self, record_id, **kwargs):
        """Delete {form.name} record"""
        try:
            record = request.env['{model_name}'].browse(record_id)
            if not record.exists():
                return {{'success': False, 'error': 'Record not found'}}

            record.unlink()

            return {{'success': True, 'message': 'Record deleted successfully'}}
        except Exception as e:
            return {{'success': False, 'error': str(e)}}''')

        api_controller_content = f'''from odoo import http
from odoo.http import request
import json


class {self.module_title.replace(' ', '')}ApiController(http.Controller):
    """
    REST API Controller for {self.module_title}
    Provides CRUD operations for all forms in the module
    """

{chr(10).join(api_routes) if api_routes else "    # No API routes generated"}
'''

        with open(os.path.join(controllers_dir, 'api.py'), 'w') as f:
            f.write(api_controller_content)

    def _generate_templates(self, module_path):
        """Generate website templates"""
        self.generation_log += "Generating website templates...\n"

        views_dir = os.path.join(module_path, 'views')
        os.makedirs(views_dir, exist_ok=True)

        # Get template data
        template = self.template_id
        if not template:
            return

        # Generate website templates for each form
        template_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Index Template -->
        <template id="index" name="{} Index">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <h1 class="mt-4">{}</h1>
                                <p class="lead">{}</p>

                                <div class="row mt-4">'''.format(
            self._escape_xml(self.module_title),
            self._escape_xml(self.module_title),
            self._escape_xml(self.module_description or f'Welcome to {self.module_title}')
        )

        # Add form cards to the main index
        form_cards = []
        for form in template.form_builder_ids:
            if form.website_published:
                route_name = form.code.replace('_', '-')
                form_cards.append(f'''
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">{self._escape_xml(form.name)}</h5>
                                                <p class="card-text">{self._escape_xml(form.description or 'Submit your information')}</p>
                                                <a href="/{route_name}" class="btn btn-primary">Access Form</a>
                                            </div>
                                        </div>
                                    </div>''')

        # Close the main index template
        template_content += '\n'.join(form_cards) if form_cards else '''
                                    <div class="col-12">
                                        <p class="text-muted">No forms are currently available.</p>
                                    </div>'''

        template_content += '''
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>'''

        # Generate individual form templates
        for form in template.form_builder_ids:
            if form.website_published:
                route_name = form.code.replace('_', '-')
                form_template = f'''

        <!-- {self._escape_xml(form.name)} Website Template -->
        <template id="{form.code}_website_form" name="{self._escape_xml(form.name)} Form">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Enhanced Form Styling -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                    <link rel="stylesheet" href="/{self.custom_prefix}_{self.module_name}/static/src/css/enhanced_forms.css"/>
                </t>
                <div id="wrap" class="enhanced-form-wrapper">
                    <!-- Progress Steps (only show if step-by-step is enabled) -->
                    <div class="container" t-if="{str(form.enable_step_form).lower()}">
                        <div class="form-progress-steps">
                            <div class="step-item active">
                                <div class="step-number">1</div>
                                <div class="step-label">Form Details</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-label">Review</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-label">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-lg-10 offset-lg-1">
                                <!-- Form Header Card -->
                                <div class="form-header-card">
                                    <div class="form-header-content">
                                        <h1 class="form-title">{self._escape_xml(form.name)}</h1>
                                        <p class="form-description">{self._escape_xml(form.description or '')}</p>
                                        <div class="form-meta">
                                            <span class="form-meta-item">
                                                <i class="fas fa-clock"></i>
                                                Estimated time: 5-10 minutes
                                            </span>
                                            <span class="form-meta-item">
                                                <i class="fas fa-shield-alt"></i>
                                                Secure &amp; Confidential
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Form Card -->
                                <div class="main-form-card">
                                    <form id="enhanced-form" action="/{route_name}/submit" method="post" class="enhanced-form" novalidate="novalidate">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                        <!-- Basic Information Section -->
                                        <div class="form-section">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-user"></i>
                                                Basic Information
                                            </h3>

                                            <div class="nice-form-group">
                                                <label for="name" class="required">Name</label>
                                                <input type="text" class="form-control" name="name" required="required"
                                                       data-field-type="char" data-required="true" placeholder="Enter your full name"/>
                                                <div class="invalid-feedback">Please provide a valid name.</div>
                                            </div>'''

                # Add fields from template - organize into sections
                basic_fields = []
                additional_fields = []

                for field_def in form.field_definition_ids.sorted('sequence'):
                    if field_def.website_form_visible:
                        field_html = self._generate_enhanced_field_html(field_def)
                        # Categorize fields
                        if any(keyword in field_def.name.lower() for keyword in ['name', 'email', 'phone', 'contact']):
                            basic_fields.append(field_html)
                        else:
                            additional_fields.append(field_html)

                # Add basic fields to current section with 2 fields per row
                form_template += self._add_fields_with_row_layout(basic_fields)

                # Close basic section and start additional section
                form_template += '''
                                        </div>

                                        <!-- Additional Details Section -->
                                        <div class="form-section">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-info-circle"></i>
                                                Additional Details
                                            </h3>'''

                # Add additional fields with 2 fields per row
                form_template += self._add_fields_with_row_layout(additional_fields)

                form_template += f'''

                                        </div>

                                        <!-- Submit Section -->
                                        <div class="form-submit-section">
                                            <button type="submit" class="btn btn-submit-enhanced" data-original-text="Submit {self._escape_xml(form.name)}">
                                                <i class="fas fa-paper-plane"></i>
                                                Submit {self._escape_xml(form.name)}
                                            </button>
                                            <p class="form-text mt-3">
                                                <i class="fas fa-lock"></i>
                                                Your information is secure and will be processed confidentially.
                                            </p>
                                        </div>
                                </form>

                                </div>

                                <!-- Success Section (Hidden initially) -->
                                <div class="main-form-card" id="success-section" style="display: none;">
                                    <div class="text-center py-5">
                                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        <h2 class="mt-3 mb-3">Thank You!</h2>
                                        <p class="lead">Your {self._escape_xml(form.name.lower())} has been submitted successfully.</p>
                                        <p class="text-muted">You will receive a confirmation email shortly.</p>
                                        <a href="/{self.module_name}" class="btn btn-primary mt-3">
                                            <i class="fas fa-home"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Form JavaScript -->
                    <script src="/{self.custom_prefix}_{self.module_name}/static/src/js/enhanced_forms.js"></script>
                    <script>
                        // Initialize enhanced form handler with step-by-step configuration
                        document.addEventListener('DOMContentLoaded', function() {{
                            if (typeof EnhancedFormHandler !== 'undefined') {{
                                const formHandler = new EnhancedFormHandler();
                                // Configure step-by-step mode
                                formHandler.enableStepByStep = {str(form.enable_step_form).lower()};
                                if (!formHandler.enableStepByStep) {{
                                    // Show all form sections if not step-by-step
                                    const sections = document.querySelectorAll('.form-section');
                                    sections.forEach(section => section.style.display = 'block');
                                    // Hide progress steps if not step-by-step
                                    const progressSteps = document.querySelector('.form-progress-steps');
                                    if (progressSteps) progressSteps.style.display = 'none';
                                }}
                            }}
                        }});
                    </script>
                </div>
            </t>
        </template>

        <!-- {self._escape_xml(form.name)} Success Template -->
        <template id="{form.code}_success" name="{self._escape_xml(form.name)} Success">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8 offset-lg-2">
                                <h1 class="mt-4 text-success">Thank You!</h1>
                                <p class="lead">Your {self._escape_xml(form.name.lower())} has been submitted successfully.</p>
                                <a href="/{self.module_name}" class="btn btn-primary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>'''

                template_content += form_template

        # Generate portal templates for each form
        for form in template.form_builder_ids:
            route_path = form.code.replace('_', '-')
            portal_templates = f'''

        <!-- Portal Templates for {self._escape_xml(form.name)} -->
        <!-- Customer Portal List Template -->
        <template id="portal_{form.code}_list" name="{self._escape_xml(form.name)} Portal List">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">{self._escape_xml(form.name)}</t>
                </t>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Your {self._escape_xml(form.name)} Records</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="{form.code}_records">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Payment Status</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="{form.code}_records" t-as="record">
                                                    <tr>
                                                        <td><a t-attf-href="/my/{route_path}/#{{record.id}}"><t t-esc="record.name"/></a></td>
                                                        <td><span class="badge bg-primary text-white" t-field="record.state"/></td>
                                                        <td>
                                                            <span t-attf-class="badge #{{record.payment_status == 'paid' and 'bg-success text-white' or record.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}}">
                                                                <span t-field="record.payment_status"/>
                                                            </span>
                                                        </td>
                                                        <td><t t-esc="record.create_date" t-options="{{'widget': 'date'}}"/></td>
                                                        <td>
                                                            <a t-attf-href="/my/{route_path}/#{{record.id}}" class="btn btn-sm btn-primary">View</a>
                                                            <t t-if="record.state == 'feedback_submitted'">
                                                                <a t-attf-href="/my/{route_path}/#{{record.id}}/edit" class="btn btn-sm btn-secondary">Edit</a>
                                                            </t>
                                                            <t t-if="record.payment_status == 'pending' and record.payment_url">
                                                                <a t-att-href="record.payment_url" class="btn btn-sm btn-warning">Pay Now</a>
                                                            </t>
                                                            <t t-elif="record.payment_status == 'pending'">
                                                                <span class="btn btn-sm btn-outline-warning disabled">Payment Pending</span>
                                                            </t>
                                                            <t t-if="record.payment_status == 'paid'">
                                                                <a t-attf-href="/my/{route_path}/#{{record.id}}/download-pdf" class="btn btn-sm btn-success">
                                                                    <i class="fa fa-download"/> Download PDF
                                                                </a>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        <p>You don't have any {self._escape_xml(form.name.lower())} records yet.</p>
                                        <a href="/{route_path}" class="btn btn-primary">Create New Record</a>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Portal Detail Template -->
        <template id="portal_{form.code}_detail" name="{self._escape_xml(form.name)} Portal Detail">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4><t t-esc="{form.code}.name"/></h4>
                                <div>
                                    <t t-if="{form.code}.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/{route_path}/#{{{form.code}.id}}/edit" class="btn btn-secondary">Edit</a>
                                    </t>
                                    <t t-if="{form.code}.payment_status == 'paid'">
                                        <a t-attf-href="/my/{route_path}/#{{{form.code}.id}}/download-pdf" class="btn btn-success">
                                            <i class="fa fa-download"/> Download PDF
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Basic Information</h5>
                                        <p><strong>Name:</strong> <t t-esc="{form.code}.name"/></p>
                                        <p><strong>Status:</strong> <span class="badge bg-primary text-white" t-field="{form.code}.state"/></p>
                                        <p><strong>Created:</strong> <t t-esc="{form.code}.create_date" t-options="{{'widget': 'datetime'}}"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Rate:</strong> $<t t-esc="{form.code}.payment_rate"/></p>
                                        <p><strong>Payment Amount:</strong> $<t t-esc="{form.code}.payment_amount"/></p>
                                        <p><strong>Payment Status:</strong>
                                            <span t-attf-class="badge #{{{form.code}.payment_status == 'paid' and 'bg-success text-white' or {form.code}.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}}">
                                                <span t-field="{form.code}.payment_status"/>
                                            </span>
                                        </p>
                                        <t t-if="{form.code}.payment_method">
                                            <p><strong>Payment Method:</strong>
                                                <span class="badge bg-info text-white" t-field="{form.code}.payment_method"/>
                                            </p>
                                        </t>
                                        <t t-if="{form.code}.payment_url and {form.code}.payment_status == 'pending'">
                                            <p><a t-att-href="{form.code}.payment_url" class="btn btn-warning">Pay Now</a></p>
                                        </t>
                                    </div>
                                </div>

                                <!-- AI Response Section (only for paid customers) -->
                                <t t-if="{form.code}.payment_status == 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">AI Response</h5>
                                                    <a t-attf-href="/my/{route_path}/#{{{form.code}.id}}/download-pdf" class="btn btn-light btn-sm">
                                                        <i class="fa fa-download"/> Download PDF
                                                    </a>
                                                </div>
                                                <div class="card-body">
                                                    <t t-raw="{form.code}.ai_response"/>

                                                    <!-- PDF Download Section -->
                                                    <div class="text-center mt-4 pt-3 border-top">
                                                        <a t-attf-href="/my/{route_path}/#{{{form.code}.id}}/download-pdf" class="btn btn-success btn-lg">
                                                            <i class="fa fa-download"/> Download PDF
                                                        </a>
                                                        <p class="text-muted mt-2">
                                                            <small>Click to download the AI response as PDF document</small>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                <t t-elif="{form.code}.payment_status != 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <h5>AI Response Available After Payment</h5>
                                                <p>Complete your payment to access the AI-generated response and download the PDF report.</p>
                                                <t t-if="{form.code}.payment_url">
                                                    <a t-att-href="{form.code}.payment_url" class="btn btn-warning">Complete Payment</a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>'''
            template_content += portal_templates

        template_content += '''
    </data>
</odoo>'''

        with open(os.path.join(views_dir, 'website_templates.xml'), 'w') as f:
            f.write(template_content)

    def _add_fields_with_row_layout(self, fields_list):
        """Generate fields HTML with 2 fields per row layout"""
        if not fields_list:
            return ''

        fields_html = ''

        # Process fields in pairs for 2-column layout
        for i in range(0, len(fields_list), 2):
            # Start a new row
            fields_html += '\n                                            <div class="row">'

            # Add first field in the pair
            field1_html = fields_list[i]
            # Wrap the field content in col-md-6
            field1_wrapped = field1_html.replace('<div class="nice-form-group">',
                                                '<div class="col-md-6">\n                                                    <div class="nice-form-group">')
            field1_wrapped = field1_wrapped.replace('</div>', '</div>\n                                                </div>', 1)
            fields_html += f'\n                                                {field1_wrapped}'

            # Add second field if it exists
            if i + 1 < len(fields_list):
                field2_html = fields_list[i + 1]
                # Wrap the field content in col-md-6
                field2_wrapped = field2_html.replace('<div class="nice-form-group">',
                                                    '<div class="col-md-6">\n                                                    <div class="nice-form-group">')
                field2_wrapped = field2_wrapped.replace('</div>', '</div>\n                                                </div>', 1)
                fields_html += f'\n                                                {field2_wrapped}'
            else:
                # If odd number of fields, add empty column for spacing
                fields_html += '\n                                                <div class="col-md-6"></div>'

            # Close the row
            fields_html += '\n                                            </div>'

        return fields_html

    def _generate_enhanced_field_html(self, field_def):
        """Generate enhanced HTML for a website form field with modern styling"""
        field_name = field_def.name
        field_label = self._escape_xml(field_def.field_description)
        field_type = field_def.field_type

        # Check if field is required (use both required and website_form_required)
        is_required = field_def.required or getattr(field_def, 'website_form_required', False)
        required_attr = 'required="required"' if is_required else ''
        required_class = 'required' if is_required else ''

        placeholder = f'placeholder="{self._escape_xml(field_def.website_form_placeholder)}"' if getattr(field_def, 'website_form_placeholder', None) else f'placeholder="Enter {field_label.lower()}"'

        # Enhanced field generation with better styling
        if field_type == 'char':
            return f'''<div class="nice-form-group">
                                                <label for="{field_name}" class="{required_class}">{field_label}</label>
                                                <input type="text" class="form-control" name="{field_name}" {required_attr} {placeholder}
                                                       data-field-type="char" data-required="{str(is_required).lower()}" />
                                                <div class="invalid-feedback">Please provide a valid {field_label.lower()}.</div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>'''
        elif field_type == 'text':
            return f'''<div class="nice-form-group">
                                                <label for="{field_name}" class="{required_class}">{field_label}</label>
                                                <textarea class="form-control" name="{field_name}" {required_attr} {placeholder} rows="4"
                                                          data-field-type="text" data-required="{str(is_required).lower()}"></textarea>
                                                <div class="invalid-feedback">Please provide a valid {field_label.lower()}.</div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>'''
        elif field_type == 'email':
            return f'''<div class="nice-form-group">
                                                <label for="{field_name}" class="{required_class}">{field_label}</label>
                                                <input type="email" class="form-control" name="{field_name}" {required_attr} {placeholder}
                                                       data-field-type="email" data-required="{str(is_required).lower()}" />
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                                <div class="valid-feedback">Email format is correct!</div>
                                            </div>'''
        elif field_type == 'phone':
            return f'''<div class="nice-form-group">
                                                <label for="{field_name}" class="{required_class}">{field_label}</label>
                                                <input type="tel" class="form-control" name="{field_name}" {required_attr} {placeholder}
                                                       data-field-type="phone" data-required="{str(is_required).lower()}" />
                                                <div class="invalid-feedback">Please provide a valid phone number.</div>
                                                <div class="valid-feedback">Phone number format is correct!</div>
                                            </div>'''
        elif field_type == 'many2one':
            return self._generate_many2one_field_html(field_def, field_name, field_label, required_class, is_required)
        elif field_type == 'many2many':
            return self._generate_many2many_field_html(field_def, field_name, field_label, required_class, is_required)
        elif field_type == 'one2many':
            return self._generate_one2many_field_html(field_def, field_name, field_label, required_class, is_required)
        elif field_type == 'selection':
            return self._generate_selection_field_html(field_def, field_name, field_label, required_class, is_required)
        else:
            # Fallback to the original method for other field types
            return self._generate_website_field_html(field_def)

    def _generate_website_field_html(self, field_def):
        """Generate HTML for a website form field with proper validation"""
        field_name = field_def.name
        field_label = self._escape_xml(field_def.field_description)
        field_type = field_def.field_type

        # Check if field is required (use both required and website_form_required)
        is_required = field_def.required or getattr(field_def, 'website_form_required', False)
        required_attr = 'required="required"' if is_required else ''
        required_indicator = ' *' if is_required else ''

        placeholder = f'placeholder="{self._escape_xml(field_def.website_form_placeholder)}"' if getattr(field_def, 'website_form_placeholder', None) else ''

        # Add validation classes
        validation_class = 'form-control'
        if is_required:
            validation_class += ' required-field'

        if field_type == 'char':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="text" class="{validation_class}" name="{field_name}" {required_attr} {placeholder}
                                               data-field-type="char" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid {field_label.lower()}.</div>
                                    </div>'''
        elif field_type == 'text':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <textarea class="{validation_class}" name="{field_name}" {required_attr} {placeholder} rows="4"
                                                  data-field-type="text" data-required="{str(is_required).lower()}"></textarea>
                                        <div class="invalid-feedback">Please provide a valid {field_label.lower()}.</div>
                                    </div>'''
        elif field_type == 'selection':
            options_html = ''
            if getattr(field_def, 'selection_options', None):
                try:
                    # Try to parse as JSON first
                    import json
                    options_data = json.loads(field_def.selection_options)
                    if isinstance(options_data, list):
                        for option in options_data:
                            if isinstance(option, list) and len(option) == 2:
                                key, value = option
                                options_html += f'\n                                            <option value="{self._escape_xml(key)}">{self._escape_xml(value)}</option>'
                except (json.JSONDecodeError, ValueError):
                    # Fallback to old comma-separated format
                    for option in field_def.selection_options.split('\n'):
                        if ',' in option:
                            key, value = option.split(',', 1)
                            options_html += f'\n                                            <option value="{self._escape_xml(key.strip())}">{self._escape_xml(value.strip())}</option>'

            # If no options were parsed or field_def.selection_options is empty, use intelligent defaults
            if not options_html:
                if 'rating' in field_name.lower():
                    for i in range(1, 6):
                        star_text = "Star" if i == 1 else "Stars"
                        options_html += f'\n                                            <option value="{i}">{i} {star_text}</option>'
                elif 'priority' in field_name.lower():
                    for priority in [('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')]:
                        options_html += f'\n                                            <option value="{priority[0]}">{priority[1]}</option>'
                elif 'status' in field_name.lower() or 'state' in field_name.lower():
                    for status in [('draft', 'Draft'), ('confirmed', 'Confirmed'), ('done', 'Done')]:
                        options_html += f'\n                                            <option value="{status[0]}">{status[1]}</option>'
                else:
                    # Generic options
                    for option in [('option1', 'Option 1'), ('option2', 'Option 2'), ('option3', 'Option 3')]:
                        options_html += f'\n                                            <option value="{option[0]}">{option[1]}</option>'

            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <select class="{validation_class}" name="{field_name}" {required_attr}
                                                data-field-type="selection" data-required="{str(is_required).lower()}">
                                            <option value="">Select...</option>{options_html}
                                        </select>
                                        <div class="invalid-feedback">Please select a valid option.</div>
                                    </div>'''
        elif field_type == 'boolean':
            return f'''<div class="form-group form-check">
                                        <input type="checkbox" class="form-check-input" name="{field_name}" value="1"
                                               data-field-type="boolean" data-required="{str(is_required).lower()}" />
                                        <label class="form-check-label" for="{field_name}">{field_label}{required_indicator}</label>
                                        <div class="invalid-feedback">This field is required.</div>
                                    </div>'''
        elif field_type == 'email':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="email" class="{validation_class}" name="{field_name}" {required_attr} {placeholder}
                                               data-field-type="email" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid email address.</div>
                                    </div>'''
        elif field_type == 'phone':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="tel" class="{validation_class}" name="{field_name}" {required_attr} {placeholder}
                                               data-field-type="phone" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid phone number.</div>
                                    </div>'''
        elif field_type == 'integer':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="number" class="{validation_class}" name="{field_name}" {required_attr} {placeholder}
                                               data-field-type="integer" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid number.</div>
                                    </div>'''
        elif field_type == 'float':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="number" step="0.01" class="{validation_class}" name="{field_name}" {required_attr} {placeholder}
                                               data-field-type="float" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid decimal number.</div>
                                    </div>'''
        elif field_type == 'date':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="date" class="{validation_class}" name="{field_name}" {required_attr}
                                               data-field-type="date" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid date.</div>
                                    </div>'''
        elif field_type == 'datetime':
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="datetime-local" class="{validation_class}" name="{field_name}" {required_attr}
                                               data-field-type="datetime" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid date and time.</div>
                                    </div>'''
        elif field_type == 'many2one':
            # Generate searchable dropdown for many2one fields
            relation_model = getattr(field_def, 'relation_model', None) or 'res.partner'
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <select class="{validation_class} o_website_form_input" name="{field_name}" {required_attr}
                                                data-model="{relation_model}" data-field-type="many2one" data-required="{str(is_required).lower()}">
                                            <option value="">Select {field_label}...</option>
                                            <!-- Options will be populated dynamically -->
                                        </select>
                                        <small class="form-text text-muted">Start typing to search</small>
                                        <div class="invalid-feedback">Please select a valid {field_label.lower()}.</div>
                                    </div>'''
        elif field_type == 'many2many':
            # Generate multi-select for many2many fields
            relation_model = getattr(field_def, 'relation_model', None) or 'res.partner'
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <select class="{validation_class} o_website_form_input" name="{field_name}" multiple="multiple" {required_attr}
                                                data-model="{relation_model}" data-field-type="many2many" data-required="{str(is_required).lower()}">
                                            <!-- Options will be populated dynamically -->
                                        </select>
                                        <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple items</small>
                                        <div class="invalid-feedback">Please select at least one {field_label.lower()}.</div>
                                    </div>'''
        elif field_type == 'one2many':
            # One2many fields are typically not editable in website forms
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <div class="alert alert-info">
                                            <i class="fa fa-info-circle"></i> {field_label} will be managed after form submission.
                                        </div>
                                    </div>'''
        else:  # Default to text input
            return f'''<div class="form-group">
                                        <label for="{field_name}">{field_label}{required_indicator}</label>
                                        <input type="text" class="{validation_class}" name="{field_name}" {required_attr} {placeholder}
                                               data-field-type="text" data-required="{str(is_required).lower()}" />
                                        <div class="invalid-feedback">Please provide a valid {field_label.lower()}.</div>
                                    </div>'''

    def _generate_tests(self, module_path):
        """Generate test files"""
        self.generation_log += "Generating test files...\n"

        tests_dir = os.path.join(module_path, 'tests')
        os.makedirs(tests_dir, exist_ok=True)

        # Generate tests/__init__.py
        with open(os.path.join(tests_dir, '__init__.py'), 'w') as f:
            f.write("from . import test_models")

        # Generate basic model tests
        test_content = f'''from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError


class Test{self.module_title.replace(' ', '')}(TransactionCase):
    """Test cases for {self.module_title}"""

    def setUp(self):
        super().setUp()
        # Set up test data here

    def test_basic_creation(self):
        """Test basic record creation"""
        # Add your test logic here
        pass

    def test_workflow_transitions(self):
        """Test workflow state transitions"""
        # Add your test logic here
        pass
'''

        with open(os.path.join(tests_dir, 'test_models.py'), 'w') as f:
            f.write(test_content)

    def _generate_demo_data(self, module_path):
        """Generate demo data files"""
        self.generation_log += "Generating demo data...\n"

        demo_dir = os.path.join(module_path, 'demo')
        os.makedirs(demo_dir, exist_ok=True)

        demo_content = '''<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Add your demo data records here -->
    </data>
</odoo>'''

        with open(os.path.join(demo_dir, 'demo.xml'), 'w') as f:
            f.write(demo_content)

    def _generate_enhanced_static_files(self, module_path):
        """Generate enhanced static files (CSS, JS) for better form design"""
        self.generation_log += "Generating enhanced static files...\n"

        static_dir = os.path.join(module_path, 'static', 'src')
        css_dir = os.path.join(static_dir, 'css')
        js_dir = os.path.join(static_dir, 'js')
        img_dir = os.path.join(static_dir, 'img')

        os.makedirs(css_dir, exist_ok=True)
        os.makedirs(js_dir, exist_ok=True)
        os.makedirs(img_dir, exist_ok=True)

        # Copy enhanced forms CSS from AI Module Generator
        enhanced_css_source = '/mnt/extra-addons/ai_module_generator/static/src/css/enhanced_forms.css'
        enhanced_css_dest = os.path.join(css_dir, 'enhanced_forms.css')

        if os.path.exists(enhanced_css_source):
            import shutil
            shutil.copy2(enhanced_css_source, enhanced_css_dest)
            self.generation_log += "Enhanced forms CSS copied successfully.\n"
        else:
            self.generation_log += "Warning: Enhanced forms CSS not found, creating basic CSS.\n"
            # Create basic CSS if source not found
            basic_css = '''/* Basic Enhanced Forms CSS */
.enhanced-form-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.main-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.enhanced-form {
    padding: 40px;
}

.nice-form-group {
    margin-bottom: 25px;
}

.form-control {
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.btn-submit-enhanced {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    padding: 15px 40px;
    border-radius: 50px;
    font-weight: 600;
}'''
            with open(enhanced_css_dest, 'w') as f:
                f.write(basic_css)

        # Copy enhanced forms JS from AI Module Generator
        enhanced_js_source = '/mnt/extra-addons/ai_module_generator/static/src/js/enhanced_forms.js'
        enhanced_js_dest = os.path.join(js_dir, 'enhanced_forms.js')

        if os.path.exists(enhanced_js_source):
            import shutil
            shutil.copy2(enhanced_js_source, enhanced_js_dest)
            self.generation_log += "Enhanced forms JavaScript copied successfully.\n"
        else:
            self.generation_log += "Warning: Enhanced forms JS not found, creating basic JS.\n"
            # Create basic JS if source not found
            basic_js = '''/* Basic Enhanced Forms JavaScript */
(function() {
    'use strict';

    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('enhanced-form');
        if (!form) return;

        // Basic form validation
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            }
        });

        // Real-time validation
        const fields = form.querySelectorAll('input, select, textarea');
        fields.forEach(function(field) {
            field.addEventListener('blur', function() {
                if (field.hasAttribute('required') && !field.value.trim()) {
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            });
        });
    });
})();'''
            with open(enhanced_js_dest, 'w') as f:
                f.write(basic_js)

        self.generation_log += "Enhanced static files generation completed.\n"

    def _generate_many2one_field_html(self, field_def, field_name, field_label, required_class, is_required):
        """Generate HTML for many2one fields with different widget options"""
        widget = getattr(field_def, 'widget', 'many2one_dropdown')  # Default to dropdown for better UX
        relation_model = getattr(field_def, 'relation_model', 'res.partner')
        required_attr = 'required="required"' if is_required else ''

        if widget == 'many2one_radio':
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <select name="{field_name}" {required_attr} style="display: none;"
                                                        data-widget="many2one_radio" data-model="{relation_model}"
                                                        data-field-type="many2one" data-required="{str(is_required).lower()}">
                                                    <option value="">Select {field_label}...</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>'''
        elif widget == 'many2one_dropdown':
            return f'''<div class="nice-form-group">
                                                <label for="{field_name}" class="{required_class}">{field_label}</label>
                                                <select class="form-control" name="{field_name}" {required_attr}
                                                        data-widget="many2one_dropdown" data-model="{relation_model}"
                                                        data-field-type="many2one" data-required="{str(is_required).lower()}"
                                                        data-label="{field_label}">
                                                    <option value="">Select {field_label}...</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="invalid-feedback">Please select a valid {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>'''
        elif widget == 'many2one_autocomplete':
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <input type="text" class="form-control" name="{field_name}" {required_attr}
                                                       placeholder="Type to search {field_label.lower()}..."
                                                       data-widget="many2one_autocomplete" data-model="{relation_model}"
                                                       data-field-type="many2one" data-required="{str(is_required).lower()}"
                                                       data-label="{field_label}"/>
                                                <div class="many2one-dropdown-results"></div>
                                                <div class="invalid-feedback">Please select a valid {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>'''
        else:  # Default to searchable dropdown
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <div class="many2one-searchable">
                                                    <input type="text" class="form-control"
                                                           placeholder="Search for {field_label.lower()}..."
                                                           data-widget="many2one_searchable" data-model="{relation_model}"
                                                           data-field-type="many2one" data-required="{str(is_required).lower()}"
                                                           data-label="{field_label}"/>
                                                    <i class="fas fa-search search-icon"></i>
                                                    <div class="many2one-dropdown-results"></div>
                                                    <input type="hidden" name="{field_name}" {required_attr}/>
                                                </div>
                                                <div class="invalid-feedback">Please select a valid {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>'''

    def _generate_many2many_field_html(self, field_def, field_name, field_label, required_class, is_required):
        """Generate HTML for many2many fields with different widget options"""
        widget = getattr(field_def, 'widget', 'many2many_tags')
        relation_model = getattr(field_def, 'relation_model', 'res.partner')
        required_attr = 'required="required"' if is_required else ''

        if widget == 'many2many_checkboxes':
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <select name="{field_name}" {required_attr} style="display: none;" multiple="multiple"
                                                        data-widget="many2many_checkboxes" data-model="{relation_model}"
                                                        data-field-type="many2many" data-required="{str(is_required).lower()}">
                                                </select>
                                                <div class="invalid-feedback">Please select at least one {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selections confirmed!</div>
                                            </div>'''
        elif widget == 'many2many_multiselect':
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <select name="{field_name}" {required_attr} style="display: none;" multiple="multiple"
                                                        data-widget="many2many_multiselect" data-model="{relation_model}"
                                                        data-field-type="many2many" data-required="{str(is_required).lower()}">
                                                </select>
                                                <div class="invalid-feedback">Please select at least one {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selections confirmed!</div>
                                            </div>'''
        else:  # Default to tags
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <select name="{field_name}" {required_attr} style="display: none;" multiple="multiple"
                                                        data-widget="many2many_tags" data-model="{relation_model}"
                                                        data-field-type="many2many" data-required="{str(is_required).lower()}">
                                                </select>
                                                <div class="invalid-feedback">Please select at least one {field_label.lower()}.</div>
                                                <div class="valid-feedback">Selections confirmed!</div>
                                            </div>'''

    def _generate_one2many_field_html(self, field_def, field_name, field_label, required_class, is_required):
        """Generate HTML for one2many fields with different widget options"""
        widget = getattr(field_def, 'widget', 'one2many_list')
        required_attr = 'required="required"' if is_required else ''

        if widget == 'one2many_inline':
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <input type="hidden" name="{field_name}" {required_attr}
                                                       data-widget="one2many_inline"
                                                       data-field-type="one2many" data-required="{str(is_required).lower()}" />
                                                <div class="invalid-feedback">Please add at least one {field_label.lower()}.</div>
                                                <div class="valid-feedback">Items added successfully!</div>
                                            </div>'''
        else:  # Default to list
            return f'''<div class="nice-form-group">
                                                <label class="{required_class}">{field_label}</label>
                                                <input type="hidden" name="{field_name}" {required_attr}
                                                       data-widget="one2many_list"
                                                       data-field-type="one2many" data-required="{str(is_required).lower()}" />
                                                <div class="invalid-feedback">Please add at least one {field_label.lower()}.</div>
                                                <div class="valid-feedback">Items added successfully!</div>
                                            </div>'''

    def _generate_selection_field_html(self, field_def, field_name, field_label, required_class, is_required):
        """Generate HTML for selection fields with enhanced styling"""
        required_attr = 'required="required"' if is_required else ''

        # Get selection options
        options_html = ''
        if getattr(field_def, 'selection_options', None):
            try:
                import json
                options_data = json.loads(field_def.selection_options)
                if isinstance(options_data, list):
                    for option in options_data:
                        if isinstance(option, list) and len(option) == 2:
                            key, value = option
                            options_html += f'\n                                                    <option value="{self._escape_xml(key)}">{self._escape_xml(value)}</option>'
            except (json.JSONDecodeError, ValueError):
                # Fallback to old comma-separated format
                for option in field_def.selection_options.split('\n'):
                    if ',' in option:
                        key, value = option.split(',', 1)
                        options_html += f'\n                                                    <option value="{self._escape_xml(key.strip())}">{self._escape_xml(value.strip())}</option>'

        # If no options, use intelligent defaults
        if not options_html:
            if 'rating' in field_name.lower():
                for i in range(1, 6):
                    star_text = "Star" if i == 1 else "Stars"
                    options_html += f'\n                                                    <option value="{i}">{i} {star_text}</option>'
            elif 'priority' in field_name.lower():
                for priority in [('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')]:
                    options_html += f'\n                                                    <option value="{priority[0]}">{priority[1]}</option>'
            else:
                for option in [('option1', 'Option 1'), ('option2', 'Option 2'), ('option3', 'Option 3')]:
                    options_html += f'\n                                                    <option value="{option[0]}">{option[1]}</option>'

        return f'''<div class="nice-form-group">
                                                <label for="{field_name}" class="{required_class}">{field_label}</label>
                                                <select class="form-control" name="{field_name}" {required_attr}
                                                        data-field-type="selection" data-required="{str(is_required).lower()}">
                                                    <option value="">Select...</option>{options_html}
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>'''

    def action_close(self):
        """Close the wizard"""
        return {'type': 'ir.actions.act_window_close'}

    def action_view_generated_module(self):
        """View the generated module files"""
        self.ensure_one()
        if not self.generated_module_path:
            raise UserError(_("No module has been generated yet"))

        # Create a generated module record if it doesn't exist
        generated_module = self.env['generated.module'].search([
            ('code', '=', f"{self.custom_prefix}_{self.module_name}")
        ], limit=1)

        if not generated_module:
            # Create the record
            generated_module = self.env['generated.module'].create({
                'name': self.module_title,
                'code': f"{self.custom_prefix}_{self.module_name}",
                'template_id': self.template_id.id,
                'module_path': self.generated_module_path,
                'state': 'generated',
                'description': f"Generated module: {self.module_title}",
            })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/module_generator/view_files/{generated_module.id}',
            'target': 'new',
        }
