<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Module Generator Wizard Form View -->
    <record id="module_generator_wizard_form_view" model="ir.ui.view">
        <field name="name">Module Generator Wizard Form</field>
        <field name="model">module.generator.wizard</field>
        <field name="arch" type="xml">
            <form string="Generate Module">
                <header>
                    <button name="action_generate_module" type="object" string="Generate Module"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_update_apps_list" type="object" string="Update Apps List"
                            class="btn-info" invisible="state != 'completed'"
                            help="Update the apps list to include the generated module"/>
                    <button name="action_install_module" type="object" string="Install Module"
                            class="btn-success" invisible="state != 'completed' or module_installed == True"
                            help="Install the generated module"/>
                    <button name="action_open_installed_module" type="object" string="Open Module"
                            class="btn-warning" invisible="module_installed != True"
                            help="Open the installed module in Apps"/>
                    <button name="action_view_generated_module" type="object" string="View Generated Files"
                            class="btn-secondary" invisible="state != 'completed'"/>
                    <button name="action_close" type="object" string="Close"
                            class="btn-secondary" invisible="state not in ('completed', 'error')"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,generating,completed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="module_title" placeholder="Enter module title..."
                                   readonly="state != 'draft'"/>
                        </h1>
                    </div>

                    <group>
                        <group string="Template Selection">
                            <field name="template_id"
                                   readonly="state != 'draft'"
                                   options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <group string="Module Information">
                            <field name="module_name"
                                   readonly="state != 'draft'"/>
                            <field name="module_version"
                                   readonly="state != 'draft'"/>
                            <field name="module_author"
                                   readonly="state != 'draft'"/>
                            <field name="module_category"
                                   readonly="state != 'draft'"/>
                        </group>
                    </group>

                    <group string="Module Description">
                        <field name="module_description" nolabel="1"
                               readonly="state != 'draft'"/>
                    </group>

                    <group string="Generation Options">
                        <group>
                            <field name="include_demo_data"
                                   readonly="state != 'draft'"/>
                            <field name="include_tests"
                                   readonly="state != 'draft'"/>
                            <field name="include_website"
                                   readonly="state != 'draft'"/>
                        </group>
                        <group>
                            <field name="include_portal"
                                   readonly="state != 'draft'"/>
                            <field name="include_api"
                                   readonly="state != 'draft'"/>
                        </group>
                    </group>

                    <group string="Module Status" invisible="state != 'completed'">
                        <group>
                            <field name="generated_module_path" readonly="1"/>
                        </group>
                        <group>
                            <field name="module_installed" readonly="1"/>
                        </group>
                    </group>

                    <group string="Advanced Options" groups="base.group_no_one">
                        <group>
                            <field name="custom_prefix"
                                   readonly="state != 'draft'"/>
                            <field name="target_directory"
                                   readonly="state != 'draft'"/>
                        </group>
                    </group>

                    <group string="Generation Log"
                           invisible="not generation_log">
                        <field name="generation_log" nolabel="1" readonly="1"
                               widget="text" height="200"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Module Generator Wizard Action -->
    <record id="module_generator_wizard_action" model="ir.actions.act_window">
        <field name="name">Generate Module</field>
        <field name="res_model">module.generator.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'default_state': 'draft'}</field>
    </record>

</odoo>
