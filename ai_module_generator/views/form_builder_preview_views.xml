<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form Builder Preview Views -->

    <!-- Form View -->
    <record id="form_builder_preview_view_form" model="ir.ui.view">
        <field name="name">Form Builder Preview</field>
        <field name="model">form.builder.preview</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <!-- Step Navigation for Step-by-Step Forms -->
                    <div class="o_form_statusbar" invisible="not enable_step_form">
                        <div class="alert alert-info">
                            <strong>Step-by-Step Form Progress:</strong>
                            Step <field name="current_step"/> of <field name="total_steps"/>
                        </div>

                        <button name="action_previous_step" type="object"
                                string="Previous" class="btn-secondary"
                                invisible="current_step &lt;= 1"/>
                        <button name="action_next_step" type="object"
                                string="Next" class="btn-primary"
                                invisible="current_step &gt;= total_steps"/>
                    </div>
                    
                    <!-- Action Buttons -->
                    <button name="action_simulate_submission" type="object" 
                            string="Simulate Submission" class="btn-success"/>
                    <button name="action_reset_preview" type="object" 
                            string="Reset" class="btn-warning"/>
                </header>
                
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <p class="text-muted">
                            <field name="description" readonly="1"/>
                        </p>
                    </div>
                    
                    <!-- Form Configuration Info -->
                    <group string="Form Configuration" invisible="1">
                        <group>
                            <field name="form_builder_id" readonly="1"/>
                            <field name="form_type" readonly="1"/>
                            <field name="layout_type" readonly="1"/>
                        </group>
                        <group>
                            <field name="enable_step_form" readonly="1"/>
                            <field name="enable_payment" readonly="1"/>
                            <field name="payment_type" readonly="1" invisible="not enable_payment"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <!-- Form Fields Tab -->
                        <page string="Form Fields" name="form_fields">
                            <div class="alert alert-info" role="alert" invisible="not enable_step_form">
                                <strong>Step-by-Step Form Preview</strong><br/>
                                This form uses step-by-step navigation. Use the Previous/Next buttons to navigate between steps.
                                Current step: <field name="current_step" readonly="1"/> of <field name="total_steps" readonly="1"/>
                            </div>
                            
                            <!-- Dynamic Field Preview Area -->
                            <group string="Form Fields">
                                <p class="text-muted">
                                    This is a preview of how your form will appear to users. 
                                    The actual form will be generated with proper field types and validation.
                                </p>
                                
                                <!-- Sample Fields Display -->
                                <group name="sample_fields" col="2">
                                    <field name="preview_data" widget="ace" options="{'mode': 'json'}" 
                                           string="Preview Data (JSON)" 
                                           help="This shows the structure of data that will be collected"/>
                                </group>
                            </group>
                        </page>
                        
                        <!-- Add-ons Tab -->
                        <page string="Add-ons" name="addons" invisible="not enable_payment">
                            <group string="Available Add-ons">
                                <field name="addon_ids" nolabel="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="price" widget="monetary"/>
                                        <field name="is_required" widget="boolean_toggle"/>
                                    </tree>
                                </field>
                            </group>
                            
                            <group string="Pricing Summary" invisible="not addon_ids">
                                <group>
                                    <field name="default_payment_rate" widget="monetary" readonly="1" string="Base Rate"/>
                                    <field name="product_price" widget="monetary" readonly="1" string="Product Price"/>
                                    <field name="total_addon_price" widget="monetary" readonly="1"/>
                                </group>
                                <group>
                                    <field name="total_price" widget="monetary" readonly="1"
                                           class="oe_subtotal_footer_separator"/>
                                </group>
                            </group>
                        </page>
                        
                        <!-- FAQs Tab -->
                        <page string="FAQs" name="faqs" invisible="not faq_ids">
                            <field name="faq_ids" readonly="1">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="question"/>
                                    <field name="answer"/>
                                    <field name="active" widget="boolean_toggle"/>
                                </tree>
                            </field>
                        </page>
                        
                        <!-- Step Data Tab (for debugging) -->
                        <page string="Step Data" name="step_data" invisible="not enable_step_form" groups="base.group_no_one">
                            <group>
                                <field name="step_data" widget="ace" options="{'mode': 'json'}"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Simplified Form View for Quick Preview -->
    <record id="form_builder_preview_view_form_simple" model="ir.ui.view">
        <field name="name">Form Builder Simple Preview</field>
        <field name="model">form.builder.preview</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <!-- Hidden fields for modifiers -->
                    <field name="enable_step_form" invisible="1"/>
                    <field name="enable_payment" invisible="1"/>
                    <field name="current_step" invisible="1"/>
                    <field name="total_steps" invisible="1"/>

                    <div class="oe_title">
                        <h2>
                            <field name="name" readonly="1"/>
                        </h2>
                    </div>
                    
                    <!-- Step Progress for Step-by-Step Forms -->
                    <div class="row" invisible="not enable_step_form">
                        <div class="col-12">
                            <div class="alert alert-info mb-4">
                                <strong>Progress:</strong> Step <field name="current_step"/> of <field name="total_steps"/>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Content -->
                    <group>
                        <p class="text-muted">
                            <field name="description" readonly="1"/>
                        </p>
                        
                        <div class="alert alert-success" role="alert">
                            <h5>Form Preview</h5>
                            <p>This is how your form will appear to users. The actual generated form will include:</p>
                            <ul>
                                <li>All configured fields with proper types and validation</li>
                                <li invisible="not enable_step_form">Step-by-step navigation</li>
                                <li invisible="not enable_payment">Payment integration with add-ons</li>
                                <li>FAQ section for user guidance</li>
                                <li>Responsive design for mobile and desktop</li>
                            </ul>
                        </div>
                    </group>
                    
                    <!-- Quick Actions -->
                    <footer>
                        <button name="action_simulate_submission" type="object" 
                                string="Test Submission" class="btn-primary"/>
                        <button special="cancel" string="Close" class="btn-secondary"/>
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

</odoo>
