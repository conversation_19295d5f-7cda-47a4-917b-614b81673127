<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Field Definition Form View -->
    <record id="field_definition_view_form" model="ir.ui.view">
        <field name="name">Field Definition Form</field>
        <field name="model">field.definition</field>
        <field name="arch" type="xml">
            <form string="Field Definition">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="field_description" placeholder="Field Label"/>
                        </h1>
                        <h2>
                            <field name="name" placeholder="field_name"/>
                        </h2>
                    </div>

                    <group>
                        <group name="basic_info">
                            <field name="field_type"/>
                            <field name="sequence"/>
                            <field name="help_text"/>
                        </group>
                        <group name="properties">
                            <field name="required"/>
                            <field name="readonly"/>
                            <field name="store"/>
                            <field name="index"/>
                            <field name="copy"/>
                            <field name="translate"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Selection Options" name="selection_options"
                              invisible="field_type != 'selection'">
                            <group>
                                <field name="selection_options" widget="text"
                                       placeholder='[["key1", "Label 1"], ["key2", "Label 2"]]'
                                       help="JSON format: [[&quot;key&quot;, &quot;Label&quot;], ...]"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Selection Options Format:</strong><br/>
                                Use JSON format with key-value pairs:<br/>
                                <code>[["1", "1 Star"], ["2", "2 Stars"], ["3", "3 Stars"]]</code><br/>
                                <code>[["low", "Low Priority"], ["medium", "Medium"], ["high", "High"]]</code>
                            </div>
                        </page>

                        <page string="Relational Fields" name="relational_fields"
                              invisible="field_type not in ['many2one', 'many2many', 'one2many']">
                            <group>
                                <group name="relation_config">
                                    <field name="relation_model"
                                           placeholder="res.partner"
                                           help="Target model name (e.g., res.partner, product.product)"/>
                                    <field name="relation_field"
                                           placeholder="partner_id"
                                           help="Field name in related model (for one2many)"/>
                                </group>
                                <group name="domain_config">
                                    <field name="domain" widget="text"
                                           placeholder="[('active', '=', True)]"
                                           help="Domain filter for records"/>
                                </group>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Common Relation Models:</strong><br/>
                                • <code>res.partner</code> - Customers/Vendors<br/>
                                • <code>product.product</code> - Products<br/>
                                • <code>res.users</code> - Users<br/>
                                • <code>res.company</code> - Companies<br/>
                                • <code>project.project</code> - Projects<br/>
                                • <code>sale.order</code> - Sales Orders<br/>
                                <strong>Domain Examples:</strong><br/>
                                • <code>[('active', '=', True)]</code><br/>
                                • <code>[('is_company', '=', True)]</code><br/>
                                • <code>[('user_id', '=', uid)]</code>
                            </div>
                        </page>

                        <page string="Default Value" name="default_value">
                            <group>
                                <field name="default_value" widget="text"
                                       help="Default value for the field"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Default Value Examples:</strong><br/>
                                • Text fields: <code>"Default text"</code><br/>
                                • Boolean fields: <code>True</code> or <code>False</code><br/>
                                • Selection fields: <code>"option_key"</code><br/>
                                • Date fields: <code>fields.Date.today()</code><br/>
                                • Integer fields: <code>0</code>
                            </div>
                        </page>

                        <page string="Widget &amp; Display" name="widget_display">
                            <group>
                                <group name="widget_config">
                                    <field name="widget"
                                           help="Choose how this field should be displayed in forms"/>
                                    <field name="form_view_position"/>
                                    <field name="form_view_colspan"/>
                                </group>
                                <group name="visibility">
                                    <field name="invisible"/>
                                    <field name="tree_view_visible"/>
                                    <field name="tree_view_optional"/>
                                </group>
                            </group>

                            <!-- Widget Help Information -->
                            <div class="alert alert-info" role="alert"
                                 invisible="field_type not in ['many2one', 'many2many', 'one2many']">
                                <strong>Widget Options for Relational Fields:</strong><br/>
                                <div invisible="field_type != 'many2one'">
                                    <strong>Many2one Widgets:</strong><br/>
                                    • <strong>Simple Dropdown:</strong> Basic dropdown list (recommended for small datasets)<br/>
                                    • <strong>Searchable Dropdown:</strong> Dropdown with search functionality<br/>
                                    • <strong>Radio Button List:</strong> Radio buttons for selection<br/>
                                    • <strong>Autocomplete Input:</strong> Text input with autocomplete suggestions
                                </div>
                                <div invisible="field_type != 'many2many'">
                                    <strong>Many2many Widgets:</strong><br/>
                                    • <strong>Tags:</strong> Tag-based selection (recommended)<br/>
                                    • <strong>Checkbox List:</strong> Multiple checkboxes<br/>
                                    • <strong>Multi-select Dropdown:</strong> Dropdown allowing multiple selections<br/>
                                    • <strong>List View:</strong> Table-like interface
                                </div>
                                <div invisible="field_type != 'one2many'">
                                    <strong>One2many Widgets:</strong><br/>
                                    • <strong>List View:</strong> Table interface (recommended)<br/>
                                    • <strong>Checkbox List:</strong> Simple checkbox selection<br/>
                                    • <strong>Tag-based Entries:</strong> Tag-like display<br/>
                                    • <strong>Inline Form:</strong> Embedded form interface
                                </div>
                            </div>
                        </page>

                        <page string="Website &amp; Portal" name="website_portal">
                            <group>
                                <group name="website_config">
                                    <field name="website_form_visible"/>
                                    <field name="website_form_required"/>
                                    <field name="website_form_placeholder"/>
                                </group>
                                <group name="portal_config">
                                    <field name="portal_visible"/>
                                    <field name="portal_readonly"/>
                                </group>
                            </group>
                        </page>

                        <page string="Search &amp; API" name="search_api">
                            <group>
                                <group name="search_config">
                                    <field name="search_view_filter"/>
                                    <field name="search_view_group_by"/>
                                </group>
                                <group name="api_config">
                                    <field name="api_visible"/>
                                    <field name="api_readonly"/>
                                </group>
                            </group>
                        </page>

                        <page string="Validation" name="validation">
                            <group>
                                <field name="validation_rules" widget="text"
                                       placeholder='{"min_length": 5, "max_length": 100}'
                                       help="JSON format validation rules"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>Validation Rules Examples:</strong><br/>
                                • <code>{"min_length": 5, "max_length": 100}</code><br/>
                                • <code>{"min_value": 0, "max_value": 999}</code><br/>
                                • <code>{"regex": "^[a-zA-Z0-9]+$"}</code><br/>
                                • <code>{"required_if": "other_field_name"}</code>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Field Definition Tree View -->
    <record id="field_definition_view_tree" model="ir.ui.view">
        <field name="name">Field Definition Tree</field>
        <field name="model">field.definition</field>
        <field name="arch" type="xml">
            <tree string="Field Definitions" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="field_description"/>
                <field name="field_type"/>
                <field name="widget" optional="show"/>
                <field name="required"/>
                <field name="selection_options" optional="hide"/>
                <field name="relation_model" optional="show"/>
                <field name="website_form_visible"/>
                <field name="form_builder_id"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Field Definition Action -->
    <record id="field_definition_action" model="ir.actions.act_window">
        <field name="name">Field Definitions</field>
        <field name="res_model">field.definition</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first field definition!
            </p>
            <p>
                Field definitions specify the structure and behavior of fields
                in your generated modules.
            </p>
        </field>
    </record>
</odoo>
