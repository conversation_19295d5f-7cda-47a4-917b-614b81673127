<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Module Template Views -->

    <!-- Tree View -->
    <record id="module_template_view_tree" model="ir.ui.view">
        <field name="name">Module Template Tree</field>
        <field name="model">module.template</field>
        <field name="arch" type="xml">
            <tree decoration-info="state=='draft'" decoration-success="state=='active'" decoration-muted="state=='archived'">
                <field name="name"/>
                <field name="code"/>
                <field name="template_type"/>
                <field name="module_author"/>
                <field name="generation_count"/>
                <field name="state" decoration-info="state=='draft'" decoration-success="state=='active'"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="module_template_view_form" model="ir.ui.view">
        <field name="name">Module Template Form</field>
        <field name="model">module.template</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_validate_template" type="object" string="Validate Template"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_activate_template" type="object" string="Activate Template"
                            class="btn-primary" invisible="state != 'validated'"/>
                    <button name="action_generate_module" type="object" string="Generate Module"
                            class="btn-success" invisible="state != 'active'"/>
                    <button name="action_duplicate_template" type="object" string="Duplicate"
                            class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,validated,active"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_generated_modules" type="object"
                                class="oe_stat_button" icon="fa-cubes">
                            <field name="generation_count" widget="statinfo" string="Generated"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Template Name"/>
                        </h1>
                        <label for="code" class="oe_edit_only"/>
                        <h2>
                            <field name="code" placeholder="template_code"/>
                        </h2>
                    </div>
                    <group>
                        <group name="basic_info">
                            <field name="description"/>
                            <field name="template_type"/>
                            <field name="active"/>
                        </group>
                        <group name="module_config">
                            <field name="module_prefix"/>
                            <field name="module_author"/>
                            <field name="module_website"/>
                            <field name="module_category"/>
                            <field name="module_version"/>
                        </group>
                    </group>
                    <group string="Payment Configuration">
                        <group>
                            <field name="default_product_id"/>
                            <field name="default_rate" readonly="1"/>
                        </group>
                        <group>
                            <field name="allow_rate_override"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Form Builders" name="form_builders">
                            <field name="form_builder_ids">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="form_type"/>
                                    <field name="layout_type"/>
                                    <field name="enable_step_form" widget="boolean_toggle"/>
                                    <field name="enable_payment" widget="boolean_toggle"/>
                                    <field name="payment_type"/>
                                    <field name="default_payment_rate"/>
                                    <field name="website_published" widget="boolean_toggle"/>
                                    <field name="active" widget="boolean_toggle"/>
                                </tree>
                                <form>
                                    <sheet>
                                        <div class="oe_title">
                                            <h1>
                                                <field name="name" placeholder="Form Builder Name"/>
                                            </h1>
                                            <h2>
                                                <field name="code" placeholder="form_code"/>
                                            </h2>
                                        </div>
                                        <group>
                                            <group name="basic_info">
                                                <field name="form_type"/>
                                                <field name="layout_type"/>
                                                <field name="sequence"/>
                                            </group>
                                            <group name="settings">
                                                <field name="enable_step_form"/>
                                                <field name="enable_payment"/>
                                                <field name="website_published"/>
                                                <field name="active"/>
                                            </group>
                                        </group>
                                        <group string="Description">
                                            <field name="description" nolabel="1"/>
                                        </group>
                                        <div class="alert alert-info" role="alert">
                                            <strong>Note:</strong> Click "Edit" button in the list to access full configuration options including step-by-step settings, payment configuration, add-ons, and FAQs.
                                        </div>
                                    </sheet>
                                </form>
                            </field>
                        </page>

                        <page string="Field Definitions" name="field_definitions">
                            <field name="field_definition_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="field_description"/>
                                    <field name="field_type"/>
                                    <field name="required"/>
                                    <field name="selection_options"
                                           placeholder='[["key", "Label"]]'/>
                                    <field name="relation_model"
                                           placeholder="res.partner"/>
                                    <field name="website_form_visible"/>
                                    <field name="form_builder_id"/>
                                    <field name="active"/>
                                </tree>
                                <form>
                                    <sheet>
                                        <div class="oe_title">
                                            <h1>
                                                <field name="field_description" placeholder="Field Label"/>
                                            </h1>
                                            <h2>
                                                <field name="name" placeholder="field_name"/>
                                            </h2>
                                        </div>

                                        <group>
                                            <group name="basic_info">
                                                <field name="field_type"/>
                                                <field name="sequence"/>
                                                <field name="help_text"/>
                                                <field name="form_builder_id"/>
                                            </group>
                                            <group name="properties">
                                                <field name="required"/>
                                                <field name="readonly"/>
                                                <field name="website_form_visible"/>
                                                <field name="website_form_required"/>
                                                <field name="active"/>
                                            </group>
                                        </group>

                                        <notebook>
                                            <page string="Selection Options" name="selection_options"
                                                  invisible="field_type != 'selection'">
                                                <group>
                                                    <field name="selection_options" widget="text"
                                                           placeholder='[["key1", "Label 1"], ["key2", "Label 2"]]'
                                                           help="JSON format: [[&quot;key&quot;, &quot;Label&quot;], ...]"/>
                                                </group>
                                                <div class="alert alert-info" role="alert">
                                                    <strong>Examples:</strong><br/>
                                                    Rating: <code>[["1", "1 Star"], ["2", "2 Stars"], ["3", "3 Stars"], ["4", "4 Stars"], ["5", "5 Stars"]]</code><br/>
                                                    Priority: <code>[["low", "Low"], ["medium", "Medium"], ["high", "High"], ["urgent", "Urgent"]]</code><br/>
                                                    Status: <code>[["draft", "Draft"], ["confirmed", "Confirmed"], ["done", "Done"]]</code>
                                                </div>
                                            </page>

                                            <page string="Relational Fields" name="relational_fields"
                                                  invisible="field_type not in ['many2one', 'many2many', 'one2many']">
                                                <group>
                                                    <group name="relation_config">
                                                        <field name="relation_model"
                                                               placeholder="res.partner"
                                                               help="Target model (e.g., res.partner, product.product)"/>
                                                        <field name="relation_field"
                                                               placeholder="partner_id"
                                                               help="Field name in related model (for one2many)"/>
                                                    </group>
                                                    <group name="domain_config">
                                                        <field name="domain" widget="text"
                                                               placeholder="[('active', '=', True)]"
                                                               help="Domain filter for records"/>
                                                    </group>
                                                </group>
                                                <div class="alert alert-info" role="alert">
                                                    <strong>Common Models:</strong> res.partner, product.product, res.users, project.project<br/>
                                                    <strong>Domain Examples:</strong> [('active', '=', True)], [('is_company', '=', True)]
                                                </div>
                                            </page>

                                            <page string="Advanced" name="advanced">
                                                <group>
                                                    <group name="default_widget">
                                                        <field name="default_value" widget="text"/>
                                                        <field name="widget"/>
                                                        <field name="website_form_placeholder"/>
                                                    </group>
                                                    <group name="validation">
                                                        <field name="validation_rules" widget="text"
                                                               placeholder='{"min_length": 5}'/>
                                                        <field name="portal_visible"/>
                                                        <field name="api_visible"/>
                                                    </group>
                                                </group>
                                            </page>
                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>

                        <page string="Workflow States" name="workflow_states">
                            <field name="workflow_state_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="state_type"/>
                                    <field name="is_default"/>
                                    <field name="color" widget="color"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>

                        <!-- AI Configuration and Payment Configuration will be added later -->
                        <!--
                        <page string="AI Configuration" name="ai_config">
                            <field name="ai_prompt_config_ids">
                                <tree editable="bottom">
                                    <field name="name"/>
                                    <field name="form_builder_id"/>
                                    <field name="prompt_type"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>

                        <page string="Payment Configuration" name="payment_config">
                            <field name="payment_config_ids">
                                <tree editable="bottom">
                                    <field name="name"/>
                                    <field name="form_builder_id"/>
                                    <field name="pricing_type"/>
                                    <field name="amount"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                        -->

                        <page string="JSON Configuration" name="json_config">
                            <group>
                                <field name="json_config" widget="text" nolabel="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="module_template_view_search" model="ir.ui.view">
        <field name="name">Module Template Search</field>
        <field name="model">module.template</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="description"/>
                <field name="module_author"/>
                <filter string="Draft" name="draft" domain="[('state','=','draft')]"/>
                <filter string="Validated" name="validated" domain="[('state','=','validated')]"/>
                <filter string="Active" name="active" domain="[('state','=','active')]"/>
                <separator/>
                <filter string="Form Based" name="form_based" domain="[('template_type','=','form_based')]"/>
                <filter string="Service Based" name="service_based" domain="[('template_type','=','service_based')]"/>
                <filter string="Document Based" name="document_based" domain="[('template_type','=','document_based')]"/>
                <separator/>
                <filter string="My Templates" name="my_templates" domain="[('create_uid','=',uid)]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by':'state'}"/>
                    <filter string="Template Type" name="group_type" context="{'group_by':'template_type'}"/>
                    <filter string="Author" name="group_author" context="{'group_by':'module_author'}"/>
                    <filter string="Creation Date" name="group_create_date" context="{'group_by':'create_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="module_template_view_kanban" model="ir.ui.view">
        <field name="name">Module Template Kanban</field>
        <field name="model">module.template</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="code"/>
                <field name="template_type"/>
                <field name="state"/>
                <field name="generation_count"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="o_secondary">
                                        <field name="code"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_card_content">
                                <div class="o_kanban_card_manage_pane">
                                    <div class="o_kanban_card_manage_section">
                                        <div>Type: <field name="template_type"/></div>
                                        <div>Generated: <field name="generation_count"/> modules</div>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_card_footer">
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="state" widget="label_selection"
                                               options="{'classes': {'draft': 'secondary', 'validated': 'warning', 'active': 'success'}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action -->
    <record id="module_template_action" model="ir.actions.act_window">
        <field name="name">Module Templates</field>
        <field name="res_model">module.template</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="module_template_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first module template!
            </p>
            <p>
                Module templates define the structure and configuration for generating
                complete Odoo modules with forms, workflows, and integrations.
            </p>
        </field>
    </record>
</odoo>
