<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Prompt Configuration Views -->

    <!-- Tree View -->
    <record id="ai_prompt_config_view_tree" model="ir.ui.view">
        <field name="name">AI Prompt Configuration Tree</field>
        <field name="model">ai.prompt.config</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="form_builder_id"/>
                <field name="prompt_type"/>
                <field name="ai_model"/>
                <field name="trigger_event"/>
                <field name="execution_count"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="ai_prompt_config_view_form" model="ir.ui.view">
        <field name="name">AI Prompt Configuration Form</field>
        <field name="model">ai.prompt.config</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="test_prompt" type="object" string="🧪 Test Prompt"
                            class="btn-primary" icon="fa-flask"/>
                    <div class="oe_button_box">
                        <button class="oe_stat_button" type="object" name="test_prompt" icon="fa-play">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">Test</span>
                                <span class="o_stat_text">Prompt</span>
                            </div>
                        </button>
                    </div>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Prompt Configuration Name"/>
                        </h1>
                    </div>
                    <group>
                        <group name="basic_info">
                            <field name="sequence"/>
                            <field name="form_builder_id"/>
                            <field name="prompt_type"/>
                            <field name="active"/>
                        </group>
                        <group name="ai_config">
                            <field name="ai_model"/>
                            <field name="chatbot_config_id"/>
                            <field name="use_custom_api"/>
                            <field name="custom_api_url" invisible="use_custom_api == False"
                                   placeholder="e.g., https://api.groq.com/openai/v1/chat/completions"/>
                            <field name="custom_api_key" invisible="use_custom_api == False"
                                   placeholder="Your API key"/>
                            <field name="custom_model_name" invisible="use_custom_api == False"
                                   placeholder="e.g., llama3-70b-8192"/>
                            <field name="max_tokens"/>
                            <field name="temperature"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Prompt Configuration" name="prompt_config">
                            <group>
                                <group name="system_prompt_group">
                                    <label for="system_prompt" string="System Prompt"/>
                                    <field name="system_prompt" widget="text" nolabel="1"
                                           placeholder="Enter system instructions for the AI model (optional)"/>
                                </group>
                            </group>
                            <separator string="Main Prompt Template"/>
                            <group>
                                <group name="prompt_template_group">
                                    <label for="prompt_template" string="Prompt Template"/>
                                    <field name="prompt_template" widget="text" nolabel="1"
                                           placeholder="Enter your prompt template here. Use {field_name} for dynamic values."/>
                                </group>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <strong>📝 Available Field Variables:</strong><br/>
                                Use <code>{field_name}</code> to insert field values. Available fields: <code>{name}</code>, <code>{state}</code>, <code>{create_date}</code>, and all custom fields from the selected form.<br/>
                                <strong>Example:</strong> <code>Customer {customer_name} gave us {overall_rating} rating. Comments: {feedback_comments}</code>
                            </div>
                        </page>

                        <page string="Trigger &amp; Output" name="trigger_output">
                            <group>
                                <group name="trigger_config">
                                    <field name="trigger_event"/>
                                    <field name="auto_execute"/>
                                </group>
                                <group name="output_config">
                                    <field name="output_field" domain="[('form_builder_id', '=', form_builder_id)]"/>
                                    <field name="output_format"/>
                                </group>
                            </group>
                        </page>

                        <page string="Button &amp; PDF Configuration" name="button_pdf_config"
                              invisible="prompt_type != 'system_prompt'">
                            <group>
                                <group name="button_config">
                                    <field name="button_label"/>
                                    <field name="button_icon"/>
                                </group>
                                <group name="pdf_config">
                                    <field name="enable_pdf_generation"/>
                                    <field name="pdf_template_name" invisible="enable_pdf_generation == False"/>
                                    <field name="portal_access" invisible="enable_pdf_generation == False"/>
                                </group>
                            </group>
                        </page>

                        <page string="Statistics" name="statistics">
                            <group>
                                <group name="stats">
                                    <field name="execution_count" readonly="1"/>
                                    <field name="last_execution" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="ai_prompt_config_view_search" model="ir.ui.view">
        <field name="name">AI Prompt Configuration Search</field>
        <field name="model">ai.prompt.config</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="form_builder_id"/>
                <field name="prompt_type"/>
                <field name="ai_model"/>
                <filter string="Active" name="active" domain="[('active','=',True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active','=',False)]"/>
                <separator/>
                <filter string="Document Generation" name="document_generation" domain="[('prompt_type','=','document_generation')]"/>
                <filter string="Data Validation" name="data_validation" domain="[('prompt_type','=','data_validation')]"/>
                <filter string="Auto Response" name="auto_response" domain="[('prompt_type','=','auto_response')]"/>
                <group expand="0" string="Group By">
                    <filter string="Form Builder" name="group_form_builder" context="{'group_by':'form_builder_id'}"/>
                    <filter string="Prompt Type" name="group_prompt_type" context="{'group_by':'prompt_type'}"/>
                    <filter string="AI Model" name="group_ai_model" context="{'group_by':'ai_model'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="ai_prompt_config_action" model="ir.actions.act_window">
        <field name="name">AI Prompt Configurations</field>
        <field name="res_model">ai.prompt.config</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="ai_prompt_config_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first AI prompt configuration!
            </p>
            <p>
                AI prompt configurations define how AI models will process and respond to form submissions.
            </p>
        </field>
    </record>

    <!-- AI Prompt Test Wizard -->
    <record id="ai_prompt_test_wizard_view_form" model="ir.ui.view">
        <field name="name">AI Prompt Test Wizard Form</field>
        <field name="model">ai.prompt.test.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>AI Prompt Test Result</h1>
                    </div>
                    <group>
                        <field name="prompt_config_id" readonly="1"/>
                    </group>
                    <notebook>
                        <page string="Sample Data" name="sample_data">
                            <field name="sample_data" widget="text" nolabel="1"/>
                        </page>
                        <page string="AI Response" name="ai_response">
                            <field name="test_result" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button name="action_close" type="object" string="Close" class="btn-primary"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- AI Execution Log Views -->

    <!-- Tree View -->
    <record id="ai_execution_log_view_tree" model="ir.ui.view">
        <field name="name">AI Execution Log Tree</field>
        <field name="model">ai.execution.log</field>
        <field name="arch" type="xml">
            <tree>
                <field name="create_date"/>
                <field name="prompt_config_id"/>
                <field name="record_model"/>
                <field name="record_id"/>
                <field name="execution_time"/>
                <field name="status" decoration-success="status=='success'" decoration-danger="status=='error'"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="ai_execution_log_view_form" model="ir.ui.view">
        <field name="name">AI Execution Log Form</field>
        <field name="model">ai.execution.log</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>AI Execution Log</h1>
                    </div>
                    <group>
                        <group name="basic_info">
                            <field name="prompt_config_id"/>
                            <field name="record_model"/>
                            <field name="record_id"/>
                            <field name="create_date"/>
                        </group>
                        <group name="execution_info">
                            <field name="execution_time"/>
                            <field name="status"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Input Data" name="input_data">
                            <field name="input_data" widget="text" nolabel="1"/>
                        </page>
                        <page string="Output Data" name="output_data">
                            <field name="output_data" widget="text" nolabel="1"/>
                        </page>
                        <page string="Error Details" name="error_details" invisible="status != 'error'">
                            <field name="error_message" widget="text" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="ai_execution_log_view_search" model="ir.ui.view">
        <field name="name">AI Execution Log Search</field>
        <field name="model">ai.execution.log</field>
        <field name="arch" type="xml">
            <search>
                <field name="prompt_config_id"/>
                <field name="record_model"/>
                <field name="create_date"/>
                <filter string="Success" name="success" domain="[('status','=','success')]"/>
                <filter string="Error" name="error" domain="[('status','=','error')]"/>
                <separator/>
                <filter string="Today" name="today" domain="[('create_date','&gt;=',datetime.datetime.combine(context_today(),datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('create_date','&gt;=',datetime.datetime.combine(context_today()-datetime.timedelta(days=7),datetime.time(0,0,0)))]"/>
                <group expand="0" string="Group By">
                    <filter string="Prompt Configuration" name="group_prompt_config" context="{'group_by':'prompt_config_id'}"/>
                    <filter string="Status" name="group_status" context="{'group_by':'status'}"/>
                    <filter string="Date" name="group_date" context="{'group_by':'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="ai_execution_log_action" model="ir.actions.act_window">
        <field name="name">AI Execution Logs</field>
        <field name="res_model">ai.execution.log</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="ai_execution_log_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No AI executions logged yet.
            </p>
            <p>
                This view shows the history of AI prompt executions and their results.
            </p>
        </field>
    </record>

    <!-- Menu Items - Parent references will be added in menu_actions.xml -->
    <menuitem id="ai_integration_menu"
              name="AI Integration"
              sequence="30"/>

    <menuitem id="ai_prompt_config_menu"
              name="AI Prompt Configurations"
              parent="ai_integration_menu"
              action="ai_prompt_config_action"
              sequence="10"/>

    <menuitem id="ai_execution_log_menu"
              name="AI Execution Logs"
              parent="ai_integration_menu"
              action="ai_execution_log_action"
              sequence="20"/>
</odoo>
