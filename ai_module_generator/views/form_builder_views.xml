<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form Builder Views -->

    <!-- Tree View -->
    <record id="form_builder_view_tree" model="ir.ui.view">
        <field name="name">Form Builder Tree</field>
        <field name="model">form.builder</field>
        <field name="arch" type="xml">
            <tree decoration-info="layout_type=='step_by_step'" decoration-success="enable_payment==True"
                  decoration-warning="not active" create="true" edit="true" delete="true">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="form_type"/>
                <field name="layout_type"/>
                <field name="enable_step_form" widget="boolean_toggle"/>
                <field name="enable_payment" widget="boolean_toggle"/>
                <field name="payment_type"/>
                <field name="website_published" widget="boolean_toggle"/>
                <field name="portal_access" widget="boolean_toggle"/>
                <field name="active" widget="boolean_toggle"/>
                <button name="action_open_form_builder" type="object"
                        string="Configure" class="btn-primary"
                        icon="fa-cogs" title="Open Full Configuration"/>
                <button name="action_configure_fields" type="object"
                        string="Fields" class="btn-secondary"
                        icon="fa-list" title="Configure Fields"/>
                <button name="action_preview_form" type="object"
                        string="Preview" class="btn-info"
                        icon="fa-eye" title="Preview Form"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="form_builder_view_form" model="ir.ui.view">
        <field name="name">Form Builder Form</field>
        <field name="model">form.builder</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_configure_fields" type="object" string="Configure Fields"
                            class="btn-primary"/>
                    <button name="action_preview_form" type="object" string="Preview Form"
                            class="btn-secondary"/>
                    <field name="active" widget="boolean_toggle"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Form Name"/>
                        </h1>
                        <h2>
                            <field name="code" placeholder="form_code"/>
                        </h2>
                    </div>

                    <group>
                        <group name="basic_info">
                            <field name="template_id"/>
                            <field name="form_type"/>
                            <field name="layout_type"/>
                            <field name="sequence"/>
                        </group>
                        <group name="configuration">
                            <field name="model_description"/>
                            <field name="website_published"/>
                            <field name="portal_access"/>
                            <field name="api_enabled"/>
                        </group>
                    </group>

                    <group string="Description">
                        <field name="description" nolabel="1"/>
                    </group>

                    <notebook>
                        <!-- Enhanced Step-by-Step Configuration -->
                        <page string="Step-by-Step Configuration" name="step_config" 
                              invisible="layout_type != 'step_by_step'">
                            <group>
                                <group name="step_settings">
                                    <field name="enable_step_form"/>
                                    <field name="require_login_before_form"/>
                                    <field name="enable_review_step"/>
                                    <field name="enable_edit_in_review"/>
                                </group>
                            </group>
                        </page>

                        <!-- Payment Configuration -->
                        <page string="Payment Configuration" name="payment_config">
                            <group>
                                <group name="payment_settings">
                                    <field name="enable_payment"/>
                                    <field name="payment_required" invisible="not enable_payment"/>
                                    <field name="payment_type" invisible="not enable_payment"/>
                                    <field name="default_payment_rate" invisible="not enable_payment"/>
                                </group>
                                <group name="sales_order">
                                    <field name="create_sales_order"/>
                                    <field name="product_id" invisible="not create_sales_order"/>
                                    <field name="product_price" invisible="not create_sales_order"/>
                                    <field name="payment_currency_id" invisible="not enable_payment"/>
                                </group>
                            </group>
                            <group string="Payment Methods" invisible="not enable_payment">
                                <field name="payment_method_ids" nolabel="1"/>
                            </group>
                        </page>

                        <!-- Available Add-ons -->
                        <page string="Available Add-ons" name="addons">
                            <field name="addon_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="price"/>
                                    <field name="is_required"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>

                        <!-- FAQs -->
                        <page string="FAQs" name="faqs">
                            <field name="faq_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="question"/>
                                    <field name="answer"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>

                        <!-- Website Configuration -->
                        <page string="Website Configuration" name="website_config">
                            <group>
                                <group name="website_settings">
                                    <field name="website_url" readonly="1"/>
                                    <field name="website_menu_name"/>
                                    <field name="website_page_title"/>
                                </group>
                                <group name="portal_settings">
                                    <field name="portal_menu_name"/>
                                    <field name="api_endpoint" readonly="1"/>
                                </group>
                            </group>
                            <group string="SEO">
                                <field name="website_meta_description" nolabel="1"/>
                            </group>
                        </page>

                        <!-- Document Generation -->
                        <page string="Document Generation" name="document_generation">
                            <group>
                                <group name="document_settings">
                                    <field name="enable_document_generation"/>
                                    <field name="document_template_id" invisible="not enable_document_generation"/>
                                </group>
                                <group name="submission_settings">
                                    <field name="auto_confirm_submission"/>
                                    <field name="submission_email_template_id"/>
                                </group>
                            </group>
                        </page>

                        <!-- Notifications -->
                        <page string="Notifications" name="notifications">
                            <group>
                                <group name="notification_settings">
                                    <field name="enable_email_notifications"/>
                                    <field name="enable_whatsapp_notifications"/>
                                </group>
                            </group>
                        </page>

                        <!-- Validation Rules -->
                        <page string="Validation Rules" name="validation">
                            <group>
                                <field name="validation_rules" widget="text" nolabel="1"
                                       placeholder='{"field_name": {"required": true, "min_length": 5}}'/>
                            </group>
                        </page>

                        <!-- Statistics -->
                        <page string="Statistics" name="statistics">
                            <group>
                                <group name="stats">
                                    <field name="submission_count" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="form_builder_view_search" model="ir.ui.view">
        <field name="name">Form Builder Search</field>
        <field name="model">form.builder</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="description"/>
                <field name="template_id"/>
                <filter string="Active" name="active" domain="[('active','=',True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active','=',False)]"/>
                <separator/>
                <filter string="Step-by-Step Forms" name="step_forms" domain="[('layout_type','=','step_by_step')]"/>
                <filter string="Payment Enabled" name="payment_enabled" domain="[('enable_payment','=',True)]"/>
                <filter string="Website Published" name="website_published" domain="[('website_published','=',True)]"/>
                <separator/>
                <filter string="Data Collection" name="data_collection" domain="[('form_type','=','data_collection')]"/>
                <filter string="Application" name="application" domain="[('form_type','=','application')]"/>
                <filter string="Registration" name="registration" domain="[('form_type','=','registration')]"/>
                <group expand="0" string="Group By">
                    <filter string="Template" name="group_template" context="{'group_by':'template_id'}"/>
                    <filter string="Form Type" name="group_form_type" context="{'group_by':'form_type'}"/>
                    <filter string="Layout Type" name="group_layout_type" context="{'group_by':'layout_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="form_builder_action" model="ir.actions.act_window">
        <field name="name">Form Builders</field>
        <field name="res_model">form.builder</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first form builder!
            </p>
            <p>
                Form builders define the structure and behavior of forms in your generated modules.
                Configure step-by-step forms, payment options, add-ons, and FAQs.
            </p>
        </field>
    </record>

    <!-- Form Builder Addon Views -->
    <record id="form_builder_addon_view_tree" model="ir.ui.view">
        <field name="name">Form Builder Addon Tree</field>
        <field name="model">form.builder.addon</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="description"/>
                <field name="price"/>
                <field name="is_required"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="form_builder_addon_view_form" model="ir.ui.view">
        <field name="name">Form Builder Addon Form</field>
        <field name="model">form.builder.addon</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="form_builder_id"/>
                            <field name="name"/>
                            <field name="price"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="is_required"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Form Builder FAQ Views -->
    <record id="form_builder_faq_view_tree" model="ir.ui.view">
        <field name="name">Form Builder FAQ Tree</field>
        <field name="model">form.builder.faq</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="question"/>
                <field name="answer"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="form_builder_faq_view_form" model="ir.ui.view">
        <field name="name">Form Builder FAQ Form</field>
        <field name="model">form.builder.faq</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="form_builder_id"/>
                            <field name="sequence"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <group>
                        <field name="question"/>
                    </group>
                    <group>
                        <field name="answer"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

</odoo>
