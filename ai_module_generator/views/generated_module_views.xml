<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Generated Module Views -->

    <!-- Tree View -->
    <record id="generated_module_view_tree" model="ir.ui.view">
        <field name="name">Generated Module Tree</field>
        <field name="model">generated.module</field>
        <field name="arch" type="xml">
            <tree decoration-info="state=='generated'" decoration-success="state=='installed'" decoration-muted="state=='archived'">
                <field name="name"/>
                <field name="code"/>
                <field name="template_id"/>
                <field name="state" decoration-info="state=='generated'" decoration-success="state=='installed'"/>
                <field name="website_url" widget="url"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="generated_module_view_form" model="ir.ui.view">
        <field name="name">Generated Module Form</field>
        <field name="model">generated.module</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_install_module" type="object" string="Install Module"
                            class="btn-primary" invisible="state != 'generated'"/>
                    <button name="action_uninstall_module" type="object" string="Uninstall Module"
                            class="btn-secondary" invisible="state != 'installed'"/>
                    <button name="action_regenerate_module" type="object" string="Regenerate"
                            class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="generated,installed,active"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_website" type="object"
                                class="oe_stat_button" icon="fa-globe">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Open</span>
                                <span class="o_stat_text">Website</span>
                            </div>
                        </button>
                        <button name="action_view_backend_module" type="object"
                                class="oe_stat_button" icon="fa-cogs">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Backend</span>
                                <span class="o_stat_text">Module</span>
                            </div>
                        </button>
                        <button name="action_view_template" type="object"
                                class="oe_stat_button" icon="fa-file-code-o">
                            <field name="form_count" widget="statinfo" string="Forms"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Module Name"/>
                        </h1>
                        <label for="code" class="oe_edit_only"/>
                        <h2>
                            <field name="code" placeholder="module_code"/>
                        </h2>
                    </div>
                    <group>
                        <group name="basic_info">
                            <field name="description"/>
                            <field name="template_id"/>
                            <field name="version"/>
                        </group>
                        <group name="status_info">
                            <field name="module_path"/>
                            <field name="installation_date"/>
                            <field name="field_count"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Website URLs" name="website_urls">
                            <group>
                                <group name="main_url">
                                    <field name="website_url" widget="url"/>
                                </group>
                            </group>
                            <group string="All Form URLs">
                                <field name="form_urls" nolabel="1" widget="text" readonly="1"/>
                            </group>
                        </page>

                        <page string="Generation Config" name="generation_config">
                            <group>
                                <field name="generation_config" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Installation Log" name="installation_log" invisible="not installation_log">
                            <group>
                                <field name="installation_log" widget="text" nolabel="1"/>
                            </group>
                        </page>

                        <page string="Error Details" name="error_details" invisible="state != 'error'">
                            <group>
                                <field name="error_message" widget="text" nolabel="1"/>
                                <field name="error_traceback" widget="text" nolabel="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="generated_module_view_search" model="ir.ui.view">
        <field name="name">Generated Module Search</field>
        <field name="model">generated.module</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="description"/>
                <field name="template_id"/>
                <filter string="Generated" name="generated" domain="[('state','=','generated')]"/>
                <filter string="Installed" name="installed" domain="[('state','=','installed')]"/>
                <filter string="Active" name="active" domain="[('state','=','active')]"/>
                <filter string="Error" name="error" domain="[('state','=','error')]"/>
                <separator/>
                <filter string="My Modules" name="my_modules" domain="[('create_uid','=',uid)]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by':'state'}"/>
                    <filter string="Template" name="group_template" context="{'group_by':'template_id'}"/>
                    <filter string="Creation Date" name="group_create_date" context="{'group_by':'create_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="generated_module_view_kanban" model="ir.ui.view">
        <field name="name">Generated Module Kanban</field>
        <field name="model">generated.module</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="name"/>
                <field name="code"/>
                <field name="state"/>
                <field name="website_url"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_primary">
                                        <strong><field name="name"/></strong>
                                    </div>
                                    <div class="o_secondary">
                                        <field name="code"/>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_card_content">
                                <div class="o_kanban_card_manage_pane">
                                    <div class="o_kanban_card_manage_section">
                                        <div>Template: <field name="template_id"/></div>
                                        <div t-if="record.website_url.value">
                                            <a t-att-href="record.website_url.value" target="_blank">
                                                <i class="fa fa-globe"/> Website
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_card_footer">
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="state" widget="label_selection"
                                               options="{'classes': {'generated': 'secondary', 'installed': 'success', 'error': 'danger'}}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action -->
    <record id="generated_module_action" model="ir.actions.act_window">
        <field name="name">Generated Modules</field>
        <field name="res_model">generated.module</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="search_view_id" ref="generated_module_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No generated modules yet!
            </p>
            <p>
                Generate your first module using the Module Generator wizard.
                Generated modules will appear here with links to access their
                website forms and backend functionality.
            </p>
        </field>
    </record>
</odoo>
