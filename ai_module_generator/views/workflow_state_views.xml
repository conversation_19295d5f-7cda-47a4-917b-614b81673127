<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Workflow State Views -->

    <!-- Tree View -->
    <record id="workflow_state_view_tree" model="ir.ui.view">
        <field name="name">Workflow State Tree</field>
        <field name="model">workflow.state</field>
        <field name="arch" type="xml">
            <tree decoration-info="state_type=='initial'" decoration-success="state_type=='final'" decoration-warning="state_type=='payment_request'">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="template_id"/>
                <field name="state_type"/>
                <field name="is_default"/>
                <field name="is_readonly"/>
                <field name="is_portal_visible"/>
                <field name="color" widget="color"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="workflow_state_view_form" model="ir.ui.view">
        <field name="name">Workflow State Form</field>
        <field name="model">workflow.state</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_configure_transitions" type="object" 
                            string="Configure Transitions" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="State Name"/>
                        </h1>
                        <label for="code" class="oe_edit_only"/>
                        <h2>
                            <field name="code" placeholder="state_code"/>
                        </h2>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="template_id"/>
                            <field name="state_type"/>
                            <field name="sequence"/>
                            <field name="active"/>
                        </group>
                        <group name="behavior">
                            <field name="is_default"/>
                            <field name="is_readonly"/>
                            <field name="is_portal_visible"/>
                        </group>
                    </group>

                    <group string="Description">
                        <field name="description" nolabel="1" placeholder="Describe what this state represents..."/>
                    </group>

                    <notebook>
                        <page string="Actions &amp; Notifications" name="actions">
                            <group>
                                <group name="entry_actions">
                                    <field name="entry_action"/>
                                    <field name="send_email_notification"/>
                                    <field name="send_whatsapp_notification"/>
                                </group>
                                <group name="exit_actions">
                                    <field name="exit_action"/>
                                </group>
                            </group>
                            
                            <group string="Email Configuration" invisible="not send_email_notification">
                                <field name="email_template_id"/>
                            </group>

                            <group string="WhatsApp Configuration" invisible="not send_whatsapp_notification">
                                <field name="whatsapp_template"/>
                            </group>
                        </page>

                        <page string="Payment Configuration" name="payment" invisible="state_type != 'payment_request'">
                            <group>
                                <group name="payment_settings">
                                    <field name="is_payment_stage"/>
                                    <field name="payment_product_id"/>
                                    <field name="auto_create_sales_order"/>
                                    <field name="auto_send_payment_link"/>
                                </group>
                                <group name="payment_description">
                                    <field name="payment_description" nolabel="1" placeholder="Description for payment request..."/>
                                </group>
                            </group>
                        </page>

                        <page string="UI Configuration" name="ui">
                            <group>
                                <group name="appearance">
                                    <field name="color" widget="color"/>
                                    <field name="icon"/>
                                </group>
                                <group name="permissions">
                                    <field name="required_group_ids" widget="many2many_tags"/>
                                </group>
                            </group>
                        </page>

                        <page string="Transitions" name="transitions">
                            <field name="allowed_transition_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="state_type"/>
                                    <field name="color" widget="color"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="workflow_state_view_kanban" model="ir.ui.view">
        <field name="name">Workflow State Kanban</field>
        <field name="model">workflow.state</field>
        <field name="arch" type="xml">
            <kanban default_group_by="template_id" class="o_kanban_small_column">
                <field name="name"/>
                <field name="code"/>
                <field name="state_type"/>
                <field name="color"/>
                <field name="icon"/>
                <field name="is_default"/>
                <field name="template_id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click" t-attf-style="border-left: 4px solid #{record.color.raw_value};">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <i t-attf-class="fa #{record.icon.raw_value}" t-attf-style="color: #{record.color.raw_value};"/>
                                        <t t-esc="record.name.value"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        <t t-esc="record.code.value"/>
                                    </small>
                                </div>
                                <div class="o_kanban_record_top_right">
                                    <span t-if="record.is_default.raw_value" class="badge badge-success">Default</span>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <span class="badge" t-attf-class="badge-#{record.state_type.raw_value == 'initial' and 'info' or record.state_type.raw_value == 'final' and 'success' or record.state_type.raw_value == 'payment_request' and 'warning' or 'secondary'}">
                                    <t t-esc="record.state_type.value"/>
                                </span>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action -->
    <record id="workflow_state_action" model="ir.actions.act_window">
        <field name="name">Workflow States</field>
        <field name="res_model">workflow.state</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first workflow state!
            </p>
            <p>
                Workflow states define the different stages that records go through in your generated modules.
                Configure initial states, payment stages, final states, and transitions between them.
            </p>
        </field>
    </record>

</odoo>
