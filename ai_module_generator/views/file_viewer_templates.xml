<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- File Viewer Templates -->

    <!-- Main File Browser Template -->
    <template id="file_viewer" name="Module File Viewer">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <style>
                    .file-tree-item {
                        transition: background-color 0.2s ease;
                    }
                    .file-tree-item:hover {
                        background-color: #f8f9fa !important;
                    }
                    .hover-bg-light:hover {
                        background-color: #e9ecef !important;
                    }
                    .file-tree {
                        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                        font-size: 14px;
                    }
                    .file-tree-level-0 {
                        border-left: 2px solid #dee2e6;
                        padding-left: 10px;
                    }
                    .file-tree-level-1 {
                        border-left: 2px solid #ced4da;
                        margin-left: 10px;
                    }
                    .file-tree-level-2 {
                        border-left: 2px solid #adb5bd;
                        margin-left: 10px;
                    }
                </style>
                <div class="container mt-4">
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h1 class="h3">
                                    <i class="fa fa-folder-open-o mr-2"></i>
                                    Module Files: <span t-esc="module.name"/>
                                </h1>
                                <div>
                                    <a t-attf-href="/module_generator/download_module/#{module.id}"
                                       class="btn btn-primary">
                                        <i class="fa fa-download mr-1"></i>
                                        Download Module
                                    </a>
                                    <a href="/web#action=ai_module_generator.generated_module_action"
                                       class="btn btn-secondary ml-2">
                                        <i class="fa fa-arrow-left mr-1"></i>
                                        Back to Modules
                                    </a>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fa fa-info-circle mr-2"></i>
                                        Module Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Code:</strong> <code t-esc="module.code"/></p>
                                            <p><strong>Template:</strong> <span t-esc="module.template_id.name"/></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>State:</strong>
                                                <span t-attf-class="badge badge-#{module.state == 'installed' and 'success' or module.state == 'generated' and 'info' or 'secondary'}"
                                                      t-esc="module.state.title()"/>
                                            </p>
                                            <p><strong>Created:</strong> <span t-esc="module.create_date"/></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fa fa-files-o mr-2"></i>
                                        File Structure
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="file-tree">
                                        <t t-call="ai_module_generator.file_tree_node">
                                            <t t-set="items" t-value="file_structure"/>
                                            <t t-set="level" t-value="0"/>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- File Tree Node Template -->
    <template id="file_tree_node" name="File Tree Node">
        <div t-attf-class="file-tree-level-#{level}">
            <t t-foreach="items" t-as="item">
                <div t-attf-class="file-tree-item mb-2 #{level > 0 and 'ml-4' or ''}" t-attf-style="padding-left: #{level * 20}px;">
                    <t t-if="item['type'] == 'directory'">
                        <div class="d-flex align-items-center p-2 bg-light rounded">
                            <i class="fa fa-folder text-warning mr-2" style="font-size: 16px;"></i>
                            <strong class="text-dark" t-esc="item['name']"/>
                            <small class="text-muted ml-2">(<span t-esc="item['size']"/> items)</small>
                        </div>
                        <t t-if="item.get('children')">
                            <div class="mt-2">
                                <t t-call="ai_module_generator.file_tree_node">
                                    <t t-set="items" t-value="item['children']"/>
                                    <t t-set="level" t-value="level + 1"/>
                                </t>
                            </div>
                        </t>
                    </t>
                    <t t-else="">
                        <div class="d-flex align-items-center justify-content-between p-2 border rounded hover-bg-light">
                            <div class="d-flex align-items-center flex-grow-1">
                                <i t-attf-class="fa #{item.get('is_text') and 'fa-file-text-o text-info' or 'fa-file-o text-secondary'} mr-2" style="font-size: 14px;"></i>
                                <a t-attf-href="/module_generator/view_file/#{module.id}?file_path=#{item['path']}"
                                   t-esc="item['name']"
                                   class="text-decoration-none text-primary flex-grow-1"/>
                                <small class="text-muted ml-2">
                                    <span t-esc="'%.1f KB' % (item['size'] / 1024.0)"/>
                                </small>
                            </div>
                            <div class="ml-2">
                                <a t-attf-href="/module_generator/download_file/#{module.id}?file_path=#{item['path']}"
                                   class="btn btn-sm btn-outline-primary"
                                   title="Download File">
                                    <i class="fa fa-download"></i>
                                </a>
                            </div>
                        </div>
                    </t>
                </div>
            </t>
        </div>
    </template>

    <!-- File Content Viewer Template -->
    <template id="file_content_viewer" name="File Content Viewer">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container-fluid mt-4">
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h1 class="h4">
                                    <i class="fa fa-file-text-o mr-2"></i>
                                    <span t-esc="file_info['name']"/>
                                </h1>
                                <div>
                                    <a t-attf-href="/module_generator/download_file/#{module.id}?file_path=#{file_path}"
                                       class="btn btn-primary">
                                        <i class="fa fa-download mr-1"></i>
                                        Download
                                    </a>
                                    <a t-attf-href="/module_generator/view_files/#{module.id}"
                                       class="btn btn-secondary ml-2">
                                        <i class="fa fa-arrow-left mr-1"></i>
                                        Back to Files
                                    </a>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h6 class="mb-0">
                                                <i class="fa fa-info-circle mr-2"></i>
                                                File: <code t-esc="file_info['path']"/>
                                            </h6>
                                        </div>
                                        <div class="col-md-4 text-right">
                                            <small class="text-muted">
                                                Size: <span t-esc="'%.1f KB' % (file_info['size'] / 1024.0)"/> |
                                                Type: <span t-esc="file_info['mime_type']"/>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <t t-if="file_info['is_binary']">
                                        <div class="p-4 text-center">
                                            <i class="fa fa-file-o fa-3x text-muted mb-3"></i>
                                            <h5>Binary File</h5>
                                            <p class="text-muted">This file contains binary data and cannot be displayed as text.</p>
                                            <a t-attf-href="/module_generator/download_file/#{module.id}?file_path=#{file_path}"
                                               class="btn btn-primary">
                                                <i class="fa fa-download mr-1"></i>
                                                Download File
                                            </a>
                                        </div>
                                    </t>
                                    <t t-else="">
                                        <pre class="mb-0 p-3" style="max-height: 80vh; overflow-y: auto; background-color: #f8f9fa; border: none;"><code t-esc="file_content"/></pre>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Error Template -->
    <template id="file_viewer_error" name="File Viewer Error">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container mt-4">
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">
                                        <i class="fa fa-exclamation-triangle mr-2"></i>
                                        Error Loading Files
                                    </h5>
                                </div>
                                <div class="card-body text-center">
                                    <i class="fa fa-exclamation-circle fa-3x text-danger mb-3"></i>
                                    <h5 t-esc="module_name or 'Unknown Module'"/>
                                    <p class="text-muted" t-esc="error_message"/>
                                    <a href="/web#action=ai_module_generator.generated_module_action"
                                       class="btn btn-primary mt-3">
                                        <i class="fa fa-arrow-left mr-1"></i>
                                        Back to Generated Modules
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
