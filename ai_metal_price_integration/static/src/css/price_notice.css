.price-components-loading {
    padding: 20px;
    color: #666;
}

.price-components-content {
    margin-top: 10px;
    margin-bottom: 20px;
}

.price-components-content table {
    font-size: 0.9rem;
}

.price-lock-notice {
    display: inline-block;
    margin-left: 10px;
    font-size: 0.8rem;
    color: #28a745;
}

.price-lock-notice i {
    margin-right: 3px;
}

.price-volatility-notice {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

.metal-price-updated {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0% { background-color: rgba(255, 243, 205, 0.5); }
    100% { background-color: transparent; }
}

.price-lock-timer {
    display: inline-block;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    margin-left: 10px;
}

.price-lock-timer i {
    margin-right: 5px;
}

.price-lock-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 15px;
}

.price-lock-info {
    display: flex;
    align-items: center;
}

.price-lock-icon {
    font-size: 1.5rem;
    margin-right: 10px;
    color: #28a745;
}

/* Metal rates widget styles */
.metal-rates-widget {
    transition: all 0.3s ease;
}

.metal-rates-widget.refreshing {
    opacity: 0.7;
    pointer-events: none;
}

.metal-rates-widget.updated {
    background-color: rgba(40, 167, 69, 0.1);
}

.metal-rate-item {
    transition: background-color 0.3s ease;
    padding: 10px;
    border-radius: 4px;
}

.metal-rate-item.metal-price-updated {
    background-color: rgba(255, 243, 205, 0.5);
}

.refresh-metal-rates .fa {
    transition: transform 0.3s ease;
}

.refreshing .refresh-metal-rates .fa {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
