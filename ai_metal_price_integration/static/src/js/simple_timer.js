// Simple Price Lock Timer - Standalone version
// This script doesn't rely on Odoo's module system
(function() {
    'use strict';

    // Create a global object to store the timer state
    var PriceLockTimer = {
        timerElement: null,
        expiryTime: null,
        intervalId: null,
        lastRefreshTime: 0,

        init: function() {
            // Find the timer element
            this.timerElement = document.getElementById('price-lock-timer-value');
            if (!this.timerElement) {
                console.log("Price lock timer element not found");
                return;
            }

            console.log("Initializing price lock timer");

            // Get the expiry time from the server
            var expiryStr = this.timerElement.getAttribute('data-expiry');
            if (!expiryStr) {
                // Try to get it from the text content
                var timeStr = this.timerElement.textContent.trim();
                if (timeStr.match(/^\d{2}:\d{2}:\d{2}$/)) {
                    var parts = timeStr.split(':');
                    var hours = parseInt(parts[0]);
                    var minutes = parseInt(parts[1]);
                    var seconds = parseInt(parts[2]);

                    var now = new Date();
                    var expiry = new Date(now.getTime() + (hours * 3600 + minutes * 60 + seconds) * 1000);
                    expiryStr = expiry.toISOString();
                    console.log("Calculated expiry time from text: " + expiryStr);
                } else {
                    console.log("Could not parse timer text: " + timeStr);
                    return;
                }
            } else {
                console.log("Found expiry time in data attribute: " + expiryStr);
            }

            // Parse the expiry time
            this.expiryTime = new Date(expiryStr);
            console.log("Expiry time: " + this.expiryTime);

            // Store the current time as the last refresh time
            this.lastRefreshTime = new Date().getTime();

            // Clear any existing interval
            if (this.intervalId) {
                clearInterval(this.intervalId);
            }

            // Update timer immediately
            this.updateTimer();

            // Update timer every second
            this.intervalId = setInterval(this.updateTimer.bind(this), 1000);
        },

        updateTimer: function() {
            if (!this.timerElement || !this.expiryTime) return;

            var now = new Date();
            var timeDiff = Math.max(0, Math.floor((this.expiryTime - now) / 1000));

            if (timeDiff <= 0) {
                this.timerElement.textContent = 'Updating...';

                // Only refresh if we haven't refreshed recently (within the last 5 seconds)
                var currentTime = new Date().getTime();
                if (currentTime - this.lastRefreshTime > 5000) {
                    console.log("Timer expired, refreshing page");
                    this.lastRefreshTime = currentTime;

                    // Reload the page to get fresh prices
                    window.location.reload();
                } else {
                    console.log("Skipping refresh - too soon since last refresh");

                    // Set a new expiry time 1 minute from now to avoid getting stuck
                    this.expiryTime = new Date(new Date().getTime() + 60 * 1000);

                    // Update the timer immediately
                    this.updateTimer();
                }
                return;
            }

            var hours = Math.floor(timeDiff / 3600);
            var minutes = Math.floor((timeDiff % 3600) / 60);
            var seconds = timeDiff % 60;

            var timeString =
                (hours < 10 ? '0' + hours : hours) + ':' +
                (minutes < 10 ? '0' + minutes : minutes) + ':' +
                (seconds < 10 ? '0' + seconds : seconds);

            this.timerElement.textContent = timeString;
        }
    };

    // Initialize the timer when the DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            PriceLockTimer.init();
        });
    } else {
        // DOM already loaded, initialize immediately
        PriceLockTimer.init();
    }

    // Make the timer object available globally
    window.PriceLockTimer = PriceLockTimer;
})();
