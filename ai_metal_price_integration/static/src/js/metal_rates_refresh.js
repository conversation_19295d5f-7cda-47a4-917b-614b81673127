odoo.define('ai_metal_price_integration.metal_rates_refresh', ['@web/legacy/js/public/public_widget', '@web/core/l10n/translation'], function (require) {
    'use strict';

    var publicWidget = require('@web/legacy/js/public/public_widget');
    var _t = require('@web/core/l10n/translation')._t;

    publicWidget.registry.MetalRatesRefresh = publicWidget.Widget.extend({
        selector: '.oe_website_sale',
        events: {
            'click .refresh-metal-rates': '_onRefreshRates',
        },

        /**
         * @override
         */
        start: function () {
            var self = this;
            this.refreshInterval = null;

            // Set up auto-refresh if on cart page
            if (this.el.querySelector('.metal-rates-widget')) {
                // Update the last refreshed time
                this._updateLastRefreshedTime();

                // Auto-refresh every 5 minutes
                this.refreshInterval = setInterval(function () {
                    self._refreshRates(false); // Silent refresh
                }, 5 * 60 * 1000);
            }

            return this._super.apply(this, arguments);
        },

        /**
         * Handle refresh button click
         * @private
         */
        _onRefreshRates: function (ev) {
            ev.preventDefault();
            this._refreshRates(true); // User-initiated refresh
        },

        /**
         * Refresh metal rates and cart prices
         * @private
         * @param {Boolean} showNotification - Whether to show notification after refresh
         */
        _refreshRates: function (showNotification) {
            var self = this;
            var $widget = this.$('.metal-rates-widget');

            if (!$widget.length) {
                return;
            }

            // Show loading state
            $widget.addClass('refreshing');

            // Use jQuery AJAX instead of _rpc to avoid conflicts
            $.ajax({
                url: '/shop/cart/refresh_rates',
                type: 'POST',
                data: {},
                success: function(result) {
                    if (result.success) {
                        // Update metal rates display
                        self._updateMetalRatesDisplay(result.metal_rates);

                        // Update cart total
                        $('.oe_cart .oe_currency_value').html(result.formatted_total);

                        // Update price lock expiry
                        if (self.$('.price-lock-timer').length) {
                            var timerElement = document.getElementById('price-lock-timer-value');
                            if (timerElement) {
                                // Update the timer text
                                var formattedTime = result.formatted_time || '';
                                timerElement.textContent = formattedTime;

                                // Update the data-expiry attribute
                                timerElement.setAttribute('data-expiry', result.price_lock_expiry);

                                // Reset the timer if PriceLockTimer is available
                                if (window.PriceLockTimer) {
                                    window.PriceLockTimer.expiryTime = new Date(result.price_lock_expiry);
                                    window.PriceLockTimer.wasExpired = false;
                                }
                            }
                        }

                        // Show notification if requested and there are price changes
                        if (showNotification && result.price_changes && result.price_changes.length > 0) {
                            self._showPriceChangeNotification(result.price_changes);
                        }

                        // Highlight the widget briefly to show it was updated
                        $widget.removeClass('refreshing').addClass('updated');
                        setTimeout(function () {
                            $widget.removeClass('updated');
                        }, 2000);
                    } else {
                        // Show error
                        $widget.removeClass('refreshing');
                        if (showNotification) {
                            // Use standard alert for notifications in Odoo 17
                            alert(_t('Error: ') + (result.error || _t('Failed to refresh metal rates')));
                        }
                    }
                },
                error: function(error) {
                    $widget.removeClass('refreshing');
                    if (showNotification) {
                        // Use standard alert for notifications in Odoo 17
                        alert(_t('Error: Failed to refresh metal rates'));
                    }
                }
            });
        },

        /**
         * Update metal rates display
         * @private
         * @param {Array} metalRates - Array of metal rate objects
         */
        _updateMetalRatesDisplay: function (metalRates) {
            var $ratesContainer = this.$('.metal-rates-container');
            if (!$ratesContainer.length || !metalRates || !metalRates.length) {
                return;
            }

            // Update each metal rate
            metalRates.forEach(function (metal) {
                var $metalEl = $ratesContainer.find('.metal-rate-item[data-metal="' + metal.name + '"]');
                if ($metalEl.length) {
                    $metalEl.find('.buy-price').html(metal.currency + ' ' + metal.buy_price.toFixed(2));
                    $metalEl.find('.sell-price').html(metal.currency + ' ' + metal.sell_price.toFixed(2));
                    $metalEl.addClass('metal-price-updated');
                    setTimeout(function () {
                        $metalEl.removeClass('metal-price-updated');
                    }, 2000);
                }
            });

            // Update last refreshed time
            this._updateLastRefreshedTime();
        },

        /**
         * Show notification for price changes
         * @private
         * @param {Array} priceChanges - Array of price change objects
         */
        _showPriceChangeNotification: function (priceChanges) {
            if (!priceChanges || !priceChanges.length) {
                return;
            }

            var message = '<p>' + _t('The following prices have been updated:') + '</p><ul>';
            priceChanges.forEach(function (change) {
                var percentChange = change.percent_change.toFixed(2);
                var direction = percentChange > 0 ? _t('increased') : _t('decreased');
                message += '<li><strong>' + change.product + ':</strong> ' + direction + ' ' + _t('by') + ' ' +
                    Math.abs(percentChange) + '% (' + _t('from') + ' ' + change.old_price.toFixed(2) +
                    ' ' + _t('to') + ' ' + change.new_price.toFixed(2) + ')</li>';
            });
            message += '</ul>';

            // Use standard alert for notifications in Odoo 17
            alert(_t('Price Update') + '\n\n' + message.replace(/<[^>]*>/g, ''));
        },

        /**
         * Update the last refreshed time display
         * @private
         */
        _updateLastRefreshedTime: function () {
            var now = new Date();
            var hours = now.getHours().toString().padStart(2, '0');
            var minutes = now.getMinutes().toString().padStart(2, '0');
            var seconds = now.getSeconds().toString().padStart(2, '0');
            var timeString = hours + ':' + minutes + ':' + seconds;

            this.$('.last-refreshed-time').text(timeString);
        },

        /**
         * @override
         */
        destroy: function () {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
            this._super.apply(this, arguments);
        }
    });
});
