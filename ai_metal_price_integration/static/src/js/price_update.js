odoo.define('ai_metal_price_integration.price_update', ['web.public_widget', 'web.core'], function (require) {
    'use strict';

    var publicWidget = require('web.public_widget');
    var core = require('web.core');
    var _t = core._t;

    publicWidget.registry.MetalPriceUpdate = publicWidget.Widget.extend({
        selector: '.oe_website_sale',

        /**
         * @override
         */
        start: function () {
            var self = this;
            if (this.el.querySelector('.product_price')) {
                this._updateProductPrice();

                // Update price when variant changes
                $(document).on('change', 'input.js_variant_change, select.js_variant_change', function () {
                    self._updateProductPrice();
                });
            }
            return this._super.apply(this, arguments);
        },

        /**
         * Update product price based on current metal rates
         * @private
         */
        _updateProductPrice: function () {
            var self = this;
            var productId = $('input.product_id').val();

            if (!productId) {
                return;
            }

            // Use jQuery AJAX instead of _rpc to avoid conflicts
            $.ajax({
                url: '/shop/product/price',
                type: 'POST',
                data: {
                    'product_id': productId,
                },
                success: function(result) {
                    if (result.success) {
                        // Update price display
                        $('.oe_price').html(result.formatted_price);

                        // Update price components if available
                        if (result.components) {
                            self._updatePriceComponents(result.components);
                        }
                    }
                },
                error: function(error) {
                    console.error("Error updating product price:", error);
                }
            });
        },

        /**
         * Update price components display
         * @private
         * @param {Object} components - Price components data
         */
        _updatePriceComponents: function (components) {
            var $container = $('.price-components-content');
            if (!$container.length) {
                return;
            }

            // Build HTML for components
            var html = '<table class="table table-sm">';
            html += '<thead><tr><th>' + _t('Component') + '</th><th class="text-right">' + _t('Value') + '</th></tr></thead>';
            html += '<tbody>';

            // Add metal components
            var metalComponents = components.components.filter(function (c) { return c.type === 'metal'; });
            if (metalComponents.length > 0) {
                metalComponents.forEach(function (component) {
                    html += '<tr><td>' + component.name + ' (' + component.weight.toFixed(3) + 'g @ ' +
                        component.rate.toFixed(2) + '/g)</td><td class="text-right">' +
                        component.value.toFixed(2) + '</td></tr>';
                });
            }

            // Add diamond components
            var diamondComponents = components.components.filter(function (c) { return c.type === 'diamond'; });
            if (diamondComponents.length > 0) {
                diamondComponents.forEach(function (component) {
                    html += '<tr><td>' + component.name + ' (' + component.weight.toFixed(3) + 'ct @ ' +
                        component.rate.toFixed(2) + '/ct)</td><td class="text-right">' +
                        component.value.toFixed(2) + '</td></tr>';
                });
            }

            // Add labor cost
            if (components.labor_cost > 0) {
                html += '<tr><td>' + _t('Making Charges') + '</td><td class="text-right">' +
                    components.labor_cost.toFixed(2) + '</td></tr>';
            }

            // Add margin
            if (components.margin > 0) {
                html += '<tr><td>' + _t('Other Charges') + '</td><td class="text-right">' +
                    components.margin.toFixed(2) + '</td></tr>';
            }

            // Add subtotal
            html += '<tr class="table-secondary"><td><strong>' + _t('Subtotal') + '</strong></td><td class="text-right"><strong>' +
                components.subtotal.toFixed(2) + '</strong></td></tr>';

            // Add total
            html += '<tr class="table-primary"><td><strong>' + _t('Total') + '</strong></td><td class="text-right"><strong>' +
                components.total.toFixed(2) + '</strong></td></tr>';

            html += '</tbody></table>';

            // Update the container
            $container.html(html).removeClass('d-none');
            $('.price-components-loading').addClass('d-none');
        }
    });
});
