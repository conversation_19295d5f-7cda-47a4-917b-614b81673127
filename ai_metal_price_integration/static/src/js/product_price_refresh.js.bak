// Product Price Auto-Refresh
// This script automatically refreshes product prices on the product description page
// without reloading the page for products that require real-time computation

odoo.define('ai_metal_price_integration.product_price_refresh', [], function (require) {
    'use strict';

    // In Odoo 17, we need to use the correct module paths
    const { Component, useState, useRef } = owl;
    const { xml } = owl.tags;
    const { useListener } = owl.hooks;

    // For translation
    const _t = env._t || ((s) => s);

    publicWidget.registry.ProductPriceRefresh = publicWidget.Widget.extend({
        selector: '.oe_website_sale',
        events: {
            'click .refresh-product-price': '_onRefreshPrice',
        },

        /**
         * @override
         */
        start: function () {
            var self = this;
            this.refreshInterval = null;
            this.productId = this._getProductId();
            this.isJewelry = this._isJewelryProduct();
            this.lastRefreshTime = 0;
            this.refreshing = false;

            // Only set up auto-refresh for jewelry products
            if (this.isJewelry && this.productId) {
                console.log("Setting up auto-refresh for jewelry product:", this.productId);

                // Initialize the refresh timer
                this._initRefreshTimer();

                // Set up auto-refresh every 60 seconds
                this.refreshInterval = setInterval(function () {
                    self._refreshProductPrice(false); // Silent refresh
                }, 60 * 1000);
            }

            return this._super.apply(this, arguments);
        },

        /**
         * Initialize the refresh timer
         * @private
         */
        _initRefreshTimer: function() {
            // Create timer elements if they don't exist
            if (!document.querySelector('.product-price-refresh-timer')) {
                var priceEl = document.querySelector('.oe_price');
                if (priceEl) {
                    // Create container
                    var timerContainer = document.createElement('div');
                    timerContainer.className = 'product-price-refresh-timer mt-2';

                    // Create timer display
                    var timerDisplay = document.createElement('span');
                    timerDisplay.id = 'product-price-timer-value';
                    timerDisplay.textContent = '01:00';

                    // Create refresh icon
                    var refreshIcon = document.createElement('i');
                    refreshIcon.className = 'fa fa-refresh refresh-product-price ml-2';
                    refreshIcon.title = _t('Refresh Price');

                    // Add elements to container
                    timerContainer.appendChild(document.createTextNode(_t('Price updates in: ')));
                    timerContainer.appendChild(timerDisplay);
                    timerContainer.appendChild(refreshIcon);

                    // Insert after price
                    priceEl.parentNode.insertBefore(timerContainer, priceEl.nextSibling);

                    // Start countdown
                    this._startCountdown();
                }
            }
        },

        /**
         * Start countdown timer
         * @private
         */
        _startCountdown: function() {
            var self = this;
            var timerElement = document.getElementById('product-price-timer-value');
            if (!timerElement) return;

            var seconds = 60; // 1 minute countdown

            // Clear any existing interval
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
            }

            // Update immediately
            this._updateCountdown(timerElement, seconds);

            // Set interval to update every second
            this.countdownInterval = setInterval(function() {
                seconds--;

                if (seconds <= 0) {
                    // Time's up, refresh the price
                    clearInterval(self.countdownInterval);
                    self._refreshProductPrice(false);
                    // Restart countdown after refresh
                    setTimeout(function() {
                        self._startCountdown();
                    }, 1000);
                } else {
                    // Update the countdown display
                    self._updateCountdown(timerElement, seconds);
                }
            }, 1000);
        },

        /**
         * Update countdown display
         * @private
         * @param {HTMLElement} element - Timer element
         * @param {Number} seconds - Seconds remaining
         */
        _updateCountdown: function(element, seconds) {
            var minutes = Math.floor(seconds / 60);
            var secs = seconds % 60;
            element.textContent = (minutes < 10 ? '0' + minutes : minutes) + ':' +
                                 (secs < 10 ? '0' + secs : secs);
        },

        /**
         * Get product ID from the page
         * @private
         * @returns {Number} Product ID
         */
        _getProductId: function() {
            var productForm = document.querySelector('form[action="/shop/cart/update"]');
            if (productForm) {
                var productInput = productForm.querySelector('input[name="product_id"]');
                if (productInput) {
                    return parseInt(productInput.value, 10);
                }
            }
            return null;
        },

        /**
         * Check if the current product is a jewelry product
         * @private
         * @returns {Boolean} True if jewelry product
         */
        _isJewelryProduct: function() {
            // We'll check if the product requires real-time price computation
            // This is a more reliable way to identify jewelry products

            // First, check if the product has the requires_realtime_price attribute
            const requiresRealtime = document.querySelector('[data-requires-realtime-price="true"]');
            if (requiresRealtime) {
                return true;
            }

            // As a fallback, check for the jewelry-indicator-container
            const jewelryIndicator = document.querySelector('.jewelry-indicator-container');
            if (jewelryIndicator) {
                // Make an AJAX call to check if this product is a jewelry product
                const productId = this.productId;
                if (productId) {
                    // We'll make a synchronous call for simplicity
                    let isJewelry = false;
                    $.ajax({
                        url: '/shop/is_jewelry_product',
                        type: 'POST',
                        async: false,
                        data: {
                            'product_id': productId
                        },
                        success: function(result) {
                            isJewelry = result.is_jewelry || false;
                        }
                    });
                    return isJewelry;
                }
            }

            return false;
        },

        /**
         * Handle refresh button click
         * @private
         */
        _onRefreshPrice: function(ev) {
            ev.preventDefault();
            this._refreshProductPrice(true); // User-initiated refresh
        },

        /**
         * Refresh product price
         * @private
         * @param {Boolean} showNotification - Whether to show notification after refresh
         */
        _refreshProductPrice: function(showNotification) {
            var self = this;

            // Prevent multiple refreshes
            if (this.refreshing) return;

            // Check if we've refreshed recently (within the last 5 seconds)
            var currentTime = new Date().getTime();
            if (currentTime - this.lastRefreshTime < 5000) {
                console.log("Skipping refresh - too soon since last refresh");
                return;
            }

            this.refreshing = true;
            this.lastRefreshTime = currentTime;

            // Show loading state
            var priceEl = document.querySelector('.oe_price');
            if (priceEl) {
                priceEl.classList.add('refreshing');
            }

            // Add spinning animation to refresh icon
            var refreshIcon = document.querySelector('.refresh-product-price');
            if (refreshIcon) {
                refreshIcon.classList.add('fa-spin');
            }

            // Call the API to refresh the product price
            this._rpc({
                route: '/shop/product/refresh_price',
                params: {
                    'product_id': this.productId
                }
            }).then(function(result) {
                if (result.success) {
                    // Update the product price
                    self._updateProductPrice(result);

                    // Restart the countdown
                    self._startCountdown();

                    // Show notification if requested
                    if (showNotification && result.price_change) {
                        self._showPriceChangeNotification(result.price_change);
                    }
                } else {
                    console.error("Failed to refresh product price:", result.error);
                    if (showNotification) {
                        alert(_t('Error: ') + (result.error || _t('Failed to refresh product price')));
                    }
                }

                // Remove loading state
                if (priceEl) {
                    priceEl.classList.remove('refreshing');
                }

                // Stop spinning animation
                if (refreshIcon) {
                    refreshIcon.classList.remove('fa-spin');
                }

                self.refreshing = false;
            }).guardedCatch(function(error) {
                console.error("Error refreshing product price:", error);

                // Remove loading state
                if (priceEl) {
                    priceEl.classList.remove('refreshing');
                }

                // Stop spinning animation
                if (refreshIcon) {
                    refreshIcon.classList.remove('fa-spin');
                }

                if (showNotification) {
                    alert(_t('Error: Failed to refresh product price'));
                }

                self.refreshing = false;
            });
        },

        /**
         * Update product price display
         * @private
         * @param {Object} result - Price refresh result
         */
        _updateProductPrice: function(result) {
            // Update main price
            var priceEl = document.querySelector('.oe_price .oe_currency_value');
            if (priceEl && result.formatted_price) {
                priceEl.innerHTML = result.formatted_price;

                // Add highlight effect
                priceEl.closest('.oe_price').classList.add('price-updated');
                setTimeout(function() {
                    priceEl.closest('.oe_price').classList.remove('price-updated');
                }, 2000);
            }

            // Update list price if available
            var listPriceEl = document.querySelector('.oe_default_price .oe_currency_value');
            if (listPriceEl && result.formatted_list_price) {
                listPriceEl.innerHTML = result.formatted_list_price;
            }

            // Update price breakup if available
            if (result.price_components && typeof updatePriceBreakup === 'function') {
                updatePriceBreakup(result.price_components);
            }
        },

        /**
         * Show notification for price change
         * @private
         * @param {Object} priceChange - Price change information
         */
        _showPriceChangeNotification: function(priceChange) {
            var percentChange = priceChange.percent_change.toFixed(2);
            var direction = percentChange > 0 ? _t('increased') : _t('decreased');
            var message = _t('Price has been updated: ') + direction + ' ' + _t('by') + ' ' +
                Math.abs(percentChange) + '% (' + _t('from') + ' ' + priceChange.old_price.toFixed(2) +
                ' ' + _t('to') + ' ' + priceChange.new_price.toFixed(2) + ')';

            alert(_t('Price Update') + '\n\n' + message);
        },

        /**
         * @override
         */
        destroy: function() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
            }
            this._super.apply(this, arguments);
        }
    });
});
