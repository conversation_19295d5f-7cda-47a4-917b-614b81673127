odoo.define('ai_metal_price_integration.product_price', ['web.public_widget'], function (require) {
    'use strict';

    var publicWidget = require('web.public_widget');

    publicWidget.registry.ProductPrice = publicWidget.Widget.extend({
        selector: '.oe_website_sale',

        start: function () {
            var self = this;
            // Only initialize if we're on a product page and not in the cart or checkout
            if (this.el.querySelector('.product_price') && !this.el.querySelector('#cart_products') && !this.el.querySelector('.checkout_autoformat')) {
                this._updateProductPrice();

                // Update price when variant changes - use delegated event to avoid conflicts
                this.$el.on('change', 'input.js_variant_change, select.js_variant_change', function () {
                    self._updateProductPrice();
                });
            }
            return this._super.apply(this, arguments);
        },

        _updateProductPrice: function () {
            var self = this;
            var productId = $('input.product_id').val();

            if (!productId) {
                return;
            }

            // Use jQuery AJAX instead of _rpc to avoid conflicts
            $.ajax({
                url: '/shop/product/price',
                type: 'POST',
                data: {
                    'product_id': productId,
                },
                success: function (result) {
                    if (result.success) {
                        // Update price display
                        $('.oe_price').html(result.formatted_price);

                        // Update price components if available
                        if (result.components) {
                            self._updatePriceComponents(result.components);
                        }
                    }
                },
                error: function (error) {
                    console.error("Error updating product price:", error);
                }
            });
        },

        _updatePriceComponents: function (components) {
            var $container = $('.price-components-content');
            if (!$container.length) {
                return;
            }

            // Log components for debugging
            console.log("Price components received:", components);
            console.log("All components:", components.components);

            // Build HTML for components
            var html = '<table class="table table-sm">';
            html += '<thead><tr><th>Component</th><th class="text-right">Value</th></tr></thead>';
            html += '<tbody>';

            // Add metal components
            var metalComponents = components.components.filter(function (c) { return c.type === 'metal'; });
            if (metalComponents.length > 0) {
                metalComponents.forEach(function (component) {
                    html += '<tr><td>' + component.name + ' (' + component.weight.toFixed(3) + 'g @ ' +
                        component.rate.toFixed(2) + '/g)</td><td class="text-right">' +
                        component.value.toFixed(2) + '</td></tr>';
                });
            }

            // Add diamond components
            var diamondComponents = components.components.filter(function (c) { return c.type === 'diamond'; });
            if (diamondComponents.length > 0) {
                diamondComponents.forEach(function (component) {
                    html += '<tr><td>' + component.name + ' (' + component.weight.toFixed(3) + 'ct @ ' +
                        component.rate.toFixed(2) + '/ct)</td><td class="text-right">' +
                        component.value.toFixed(2) + '</td></tr>';
                });
            }

            // Add labor cost
            if (components.labor_cost > 0) {
                html += '<tr><td>Making Charges</td><td class="text-right">' +
                    components.labor_cost.toFixed(2) + '</td></tr>';
            }

            // Add margin components
            var marginComponents = components.components.filter(function (c) { return c.type === 'margin'; });
            console.log("Margin components:", marginComponents);

            // Always display margin components if they exist
            if (marginComponents && marginComponents.length > 0) {
                console.log("Found margin components, adding to display");
                marginComponents.forEach(function (component) {
                    console.log("Adding margin component:", component.name, component.value);
                    // Only display if the value is not 0 or if it's the fixed margin
                    if (component.value !== 0 || component.name === 'Fixed Margin') {
                        html += '<tr><td>' + component.name + '</td><td class="text-right">' +
                            component.value.toFixed(2) + '</td></tr>';
                    }
                });
            } else if (components.margin > 0) {
                // Fallback for backward compatibility
                console.log("No margin components found, using fallback with total margin:", components.margin);
                html += '<tr><td>Other Charges</td><td class="text-right">' +
                    components.margin.toFixed(2) + '</td></tr>';
            } else {
                console.log("No margin components or total margin found");
                // Always show fixed margin even if it's 0
                html += '<tr><td>Fixed Margin</td><td class="text-right">0.00</td></tr>';
            }

            // Add subtotal
            html += '<tr class="table-secondary"><td><strong>Subtotal</strong></td><td class="text-right"><strong>' +
                components.subtotal.toFixed(2) + '</strong></td></tr>';

            // Add taxes (GST)
            if (components.taxes > 0) {
                html += '<tr class="table-info"><td><strong>Taxes (GST)</strong></td><td class="text-right"><strong>' +
                    components.taxes.toFixed(2) + '</strong></td></tr>';

                // Add tax details if available
                if (components.tax_details && components.tax_details.length > 0) {
                    components.tax_details.forEach(function(tax) {
                        html += '<tr class="tax-detail"><td class="pl-4">' + tax.type + ' (' +
                            (tax.amount / tax.base * 100).toFixed(1) + '%)</td><td class="text-right">' +
                            tax.amount.toFixed(2) + '</td></tr>';
                    });
                }
            }

            // Add total
            html += '<tr class="table-primary"><td><strong>Total (Incl. GST)</strong></td><td class="text-right"><strong>' +
                components.total.toFixed(2) + '</strong></td></tr>';

            html += '</tbody></table>';

            // Add GST note for metals
            if (metalComponents.length > 0) {
                html += '<div class="small text-muted mt-2">' +
                    'Note: Precious metals are subject to 3% GST in India (CGST & SGST within state, IGST for interstate).' +
                    '</div>';
            }

            // Update the container
            $container.html(html).removeClass('d-none');
            $('.price-components-loading').addClass('d-none');
        }
    });
});
