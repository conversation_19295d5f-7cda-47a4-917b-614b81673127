// Simple Add to Cart override
console.log("Simple Add to Cart override loading...");

// Wait for the DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, initializing Add to Cart override");

    // Find the Add to Cart button
    var addToCartButton = document.querySelector('#add_to_cart');
    if (!addToCartButton) {
        console.log("Add to Cart button not found, looking for a-submit");
        addToCartButton = document.querySelector('.a-submit');
    }

    if (addToCartButton) {
        console.log("Add to Cart button found, adding click event listener");

        // Add click event listener
        addToCartButton.addEventListener('click', function(event) {
            console.log("Add to Cart button clicked");

            // Prevent the default action
            event.preventDefault();

            // Find the form
            var form = document.querySelector('form[action="/shop/cart/update"]');
            if (!form) {
                console.error("Form not found");
                return;
            }

            // Get the product ID
            var productIdInput = form.querySelector('input[name="product_id"]');
            if (!productIdInput) {
                console.error("Product ID input not found");
                return;
            }

            var productId = productIdInput.value;
            console.log("Product ID: " + productId);

            // Get the quantity
            var quantityInput = form.querySelector('input[name="add_qty"]');
            var quantity = quantityInput ? quantityInput.value : 1;
            console.log("Quantity: " + quantity);

            // Get the CSRF token
            var csrfTokenInput = form.querySelector('input[name="csrf_token"]');
            var csrfToken = csrfTokenInput ? csrfTokenInput.value : '';

            // Show loading indicator
            addToCartButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Adding...';
            addToCartButton.disabled = true;

            // Create a new form and submit it
            var newForm = document.createElement('form');
            newForm.method = 'POST';
            newForm.action = '/shop/cart/update';
            newForm.style.display = 'none';

            // Add product ID
            var productIdField = document.createElement('input');
            productIdField.type = 'hidden';
            productIdField.name = 'product_id';
            productIdField.value = productId;
            newForm.appendChild(productIdField);

            // Add quantity
            var quantityField = document.createElement('input');
            quantityField.type = 'hidden';
            quantityField.name = 'add_qty';
            quantityField.value = quantity;
            newForm.appendChild(quantityField);

            // Add CSRF token
            if (csrfToken) {
                var csrfTokenField = document.createElement('input');
                csrfTokenField.type = 'hidden';
                csrfTokenField.name = 'csrf_token';
                csrfTokenField.value = csrfToken;
                newForm.appendChild(csrfTokenField);
            }

            // Add the form to the body and submit it
            document.body.appendChild(newForm);
            console.log("Submitting form");
            newForm.submit();
        });

        console.log("Add to Cart override initialized successfully");
    } else {
        console.error("Add to Cart button not found");
    }
});
