// Vanilla JavaScript timer for price lock
document.addEventListener('DOMContentLoaded', function() {
    // Find the timer element
    var timerElement = document.getElementById('price-lock-timer-value');
    if (!timerElement) {
        console.log('Timer element not found');
        return;
    }
    
    // Get the expiry time from the data attribute
    var expiryTimeStr = timerElement.getAttribute('data-expiry');
    if (!expiryTimeStr) {
        console.log('No expiry time found');
        return;
    }
    
    // Parse the expiry time
    var expiryTime = new Date(expiryTimeStr);
    
    // Get the initial time
    var initialTimeStr = timerElement.textContent.trim();
    
    // Update the timer immediately
    updateTimer();
    
    // Set interval to update timer every second
    setInterval(updateTimer, 1000);
    
    function updateTimer() {
        var now = new Date();
        var timeDiff = Math.max(0, Math.floor((expiryTime - now) / 1000));
        
        if (timeDiff <= 0) {
            // Timer expired
            timerElement.textContent = 'Expired';
            
            // Reload page after a short delay to refresh prices
            setTimeout(function() {
                window.location.reload();
            }, 3000);
            
            return;
        }
        
        // Calculate hours, minutes, seconds
        var hours = Math.floor(timeDiff / 3600);
        var minutes = Math.floor((timeDiff % 3600) / 60);
        var seconds = timeDiff % 60;
        
        // Format time components
        var hoursStr = hours < 10 ? '0' + hours : hours;
        var minutesStr = minutes < 10 ? '0' + minutes : minutes;
        var secondsStr = seconds < 10 ? '0' + seconds : seconds;
        
        // Update timer display
        timerElement.textContent = hoursStr + ':' + minutesStr + ':' + secondsStr;
    }
});
