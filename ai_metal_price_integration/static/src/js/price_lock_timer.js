odoo.define('ai_metal_price_integration.price_lock_timer', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');
    var _t = require('web.core')._t;

    publicWidget.registry.PriceLockTimer = publicWidget.Widget.extend({
        selector: '.oe_website_sale',

        /**
         * @override
         */
        start: function () {
            var self = this;
            this.timerInterval = null;

            // Initialize timer if price lock element exists
            if (this.el.querySelector('.price-lock-timer')) {
                this._initializeTimer();
                console.log('Price lock timer initialized');
            } else {
                console.log('Price lock timer element not found');
            }

            return this._super.apply(this, arguments);
        },

        /**
         * Initialize countdown timer for price lock
         * @private
         */
        _initializeTimer: function () {
            var self = this;
            var timerElement = this.el.querySelector('.price-lock-timer');
            var expiryTimeStr = timerElement.getAttribute('data-expiry-time');

            console.log('Initializing timer with expiry time:', expiryTimeStr);

            if (!expiryTimeStr) {
                console.error('No expiry time found');
                return;
            }

            // Parse expiry time
            var expiryTime = new Date(expiryTimeStr);
            console.log('Parsed expiry time:', expiryTime);

            // Update timer immediately
            this._updateTimer(timerElement, expiryTime);

            // Set interval to update timer every second
            this.timerInterval = setInterval(function () {
                self._updateTimer(timerElement, expiryTime);
            }, 1000);
        },

        /**
         * Update timer display
         * @private
         * @param {HTMLElement} timerElement - The timer element
         * @param {Date} expiryTime - The expiry time
         */
        _updateTimer: function (timerElement, expiryTime) {
            var now = new Date();
            var timeDiff = expiryTime - now;

            if (timeDiff <= 0) {
                // Timer expired
                clearInterval(this.timerInterval);
                timerElement.innerHTML = '<i class="fa fa-clock-o text-danger"></i> ' + _t('Expired');

                // Reload page after a short delay to refresh prices
                setTimeout(function () {
                    window.location.reload();
                }, 3000);

                return;
            }

            // Calculate hours, minutes, seconds
            var hours = Math.floor(timeDiff / (1000 * 60 * 60));
            var minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            // Format time components
            var hoursStr = hours.toString().padStart(2, '0');
            var minutesStr = minutes.toString().padStart(2, '0');
            var secondsStr = seconds.toString().padStart(2, '0');

            // Update timer display
            var timerHtml = '<i class="fa fa-clock-o"></i> ';

            // Change color based on remaining time
            if (timeDiff < 5 * 60 * 1000) { // Less than 5 minutes
                timerHtml += '<span class="text-danger">';
            } else if (timeDiff < 15 * 60 * 1000) { // Less than 15 minutes
                timerHtml += '<span class="text-warning">';
            } else {
                timerHtml += '<span class="text-success">';
            }

            timerHtml += hoursStr + ':' + minutesStr + ':' + secondsStr + '</span>';
            timerElement.innerHTML = timerHtml;
        },

        /**
         * @override
         */
        destroy: function () {
            if (this.timerInterval) {
                clearInterval(this.timerInterval);
            }
            this._super.apply(this, arguments);
        }
    });
});
