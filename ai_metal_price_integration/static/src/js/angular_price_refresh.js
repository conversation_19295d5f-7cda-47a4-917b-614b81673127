// Angular Price Refresh Integration
// This script integrates with the AngularJS price breakup functionality

document.addEventListener('DOMContentLoaded', function() {
    'use strict';
    
    console.log("Angular Price Refresh Integration loaded");
    
    // Check if Angular is available
    if (typeof angular === 'undefined') {
        console.log("Angular not found, skipping integration");
        return;
    }
    
    // Wait for Angular to initialize
    setTimeout(function() {
        // Try to find the Angular scope
        const element = document.querySelector('[ng-controller="WebsiteSaleController"]');
        if (!element) {
            console.log("No Angular controller found");
            return;
        }
        
        const scope = angular.element(element).scope();
        if (!scope) {
            console.log("No Angular scope found");
            return;
        }
        
        console.log("Found Angular scope:", scope);
        
        // Get the product ID
        const productId = getProductId();
        if (!productId) {
            console.log("No product ID found");
            return;
        }
        
        console.log("Product ID:", productId);
        
        // Set up auto-refresh for the price breakup
        setInterval(function() {
            console.log("Auto-refreshing price breakup for product ID:", productId);
            scope.$apply(function() {
                scope.fetchPriceBreakup(productId);
            });
        }, 15000); // Every 15 seconds
    }, 1000);
    
    // Get the product ID from the URL or form
    function getProductId() {
        // Try to get from the form first
        const productForm = document.querySelector('form[action="/shop/cart/update"]');
        if (productForm) {
            const productInput = productForm.querySelector('input[name="product_id"]');
            if (productInput) {
                return parseInt(productInput.value, 10);
            }
        }
        
        // Try to get from the URL
        const url = window.location.href;
        const match = url.match(/\/shop\/([^\/]+)-(\d+)/);
        if (match && match[2]) {
            return parseInt(match[2], 10);
        }
        
        // Try to get from data attribute
        const productElement = document.querySelector('[data-product-id]');
        if (productElement) {
            return parseInt(productElement.getAttribute('data-product-id'), 10);
        }
        
        return null;
    }
});
