// Cart Price Updater
// This script updates cart prices without reloading the page

document.addEventListener('DOMContentLoaded', function() {
    'use strict';
    
    console.log("Cart Price Updater loaded");
    
    // CartPriceUpdater class
    class CartPriceUpdater {
        constructor() {
            this.refreshInterval = null;
            this.lastRefreshTime = 0;
            this.refreshing = false;
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Check if this is a cart page
            if (this.isCartPage()) {
                console.log("This is a cart page, setting up auto-refresh");
                
                // Force refresh on load to ensure we have the latest prices
                setTimeout(() => {
                    this.refreshCartPrices();
                }, 2000);
                
                // Set up auto-refresh every 30 seconds
                this.refreshInterval = setInterval(() => {
                    this.refreshCartPrices();
                }, 30000);
            }
        }
        
        // Set up event listeners
        setupEventListeners() {
            // Add a manual refresh button if needed
            const cartTotal = document.querySelector('.oe_cart_total');
            if (cartTotal && !document.querySelector('.cart-refresh-button')) {
                const refreshButton = document.createElement('button');
                refreshButton.className = 'btn btn-sm btn-secondary cart-refresh-button ml-2';
                refreshButton.innerHTML = '<i class="fa fa-refresh"></i> Refresh Prices';
                refreshButton.style.display = 'none'; // Hide by default
                
                cartTotal.parentNode.insertBefore(refreshButton, cartTotal.nextSibling);
                
                refreshButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.refreshCartPrices();
                });
            }
        }
        
        // Check if this is a cart page
        isCartPage() {
            return window.location.pathname.includes('/shop/cart') || 
                   document.querySelector('.oe_cart') !== null;
        }
        
        // Refresh cart prices
        refreshCartPrices() {
            // Prevent multiple refreshes
            if (this.refreshing) {
                console.log("Already refreshing cart prices, skipping");
                return;
            }
            
            // Prevent refreshing too frequently
            const currentTime = new Date().getTime();
            if (currentTime - this.lastRefreshTime < 5000) {
                console.log("Skipping refresh - too soon since last refresh");
                return;
            }
            
            console.log("Refreshing cart prices");
            this.refreshing = true;
            this.lastRefreshTime = currentTime;
            
            // Add refreshing class to price elements
            const priceElements = document.querySelectorAll('.oe_currency_value');
            priceElements.forEach(el => el.classList.add('refreshing'));
            
            // Make the AJAX call to refresh cart prices
            fetch('/shop/cart/refresh_prices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                console.log("Cart prices refreshed:", data);
                
                if (data.success) {
                    // Update the cart lines
                    if (data.order_lines) {
                        this.updateCartLines(data.order_lines);
                    }
                    
                    // Update the cart total
                    if (data.formatted_total) {
                        this.updateCartTotal(data.formatted_total);
                    }
                } else {
                    console.error("Failed to refresh cart prices:", data.error);
                }
                
                // Remove refreshing class
                priceElements.forEach(el => el.classList.remove('refreshing'));
                this.refreshing = false;
            })
            .catch(error => {
                console.error("Error refreshing cart prices:", error);
                // Remove refreshing class
                priceElements.forEach(el => el.classList.remove('refreshing'));
                this.refreshing = false;
            });
        }
        
        // Update cart lines
        updateCartLines(orderLines) {
            if (!orderLines || !orderLines.length) {
                console.log("No order lines to update");
                return;
            }
            
            console.log("Updating cart lines:", orderLines);
            
            // Find all order line rows in the cart
            const orderLineRows = document.querySelectorAll('tr.js_product, tr.cart_line, tr[data-product-id]');
            if (!orderLineRows.length) {
                console.log("No order line rows found in the DOM");
                return;
            }
            
            // Track if any prices were updated
            let pricesUpdated = false;
            
            // Update each order line
            orderLines.forEach(line => {
                console.log("Processing line ID:", line.id, "Product:", line.product_name);
                
                // Find the row for this line
                let lineRow = null;
                for (let i = 0; i < orderLineRows.length; i++) {
                    const row = orderLineRows[i];
                    const lineId = row.getAttribute('data-line-id');
                    const productId = row.getAttribute('data-product-id');
                    
                    // Match by line ID if available, otherwise try product ID
                    if ((lineId && parseInt(lineId) === line.id) || 
                        (productId && parseInt(productId) === line.product_id)) {
                        lineRow = row;
                        console.log("Found matching row for line ID:", line.id);
                        break;
                    }
                }
                
                if (!lineRow) {
                    console.log("Could not find row for line ID:", line.id);
                    return;
                }
                
                // Update the subtotal price
                const priceEls = lineRow.querySelectorAll('.text-end .oe_currency_value, td.text-end span.oe_currency_value, .text-right .oe_currency_value');
                priceEls.forEach(priceEl => {
                    if (priceEl && !priceEl.closest('.oe_website_sale_subtotal') && !priceEl.closest('.oe_cart_total')) {
                        const oldPrice = parseFloat(priceEl.textContent.replace(/[^0-9.-]+/g, ''));
                        const newPrice = line.price_subtotal;
                        
                        console.log("Updating price from", oldPrice, "to", newPrice);
                        
                        // Update the price
                        priceEl.textContent = newPrice.toFixed(2);
                        
                        // Add highlight effect if price changed
                        if (oldPrice !== newPrice) {
                            priceEl.classList.add('price-updated');
                            setTimeout(() => {
                                priceEl.classList.remove('price-updated');
                            }, 2000);
                            pricesUpdated = true;
                        }
                    }
                });
                
                // Update the unit price if available
                const unitPriceEls = lineRow.querySelectorAll('.text-center .oe_currency_value, td.text-center span.oe_currency_value');
                unitPriceEls.forEach(unitPriceEl => {
                    if (unitPriceEl && line.price_unit) {
                        const oldUnitPrice = parseFloat(unitPriceEl.textContent.replace(/[^0-9.-]+/g, ''));
                        const newUnitPrice = line.price_unit;
                        
                        console.log("Updating unit price from", oldUnitPrice, "to", newUnitPrice);
                        
                        // Update the unit price
                        unitPriceEl.textContent = newUnitPrice.toFixed(2);
                        
                        // Add highlight effect if price changed
                        if (oldUnitPrice !== newUnitPrice) {
                            unitPriceEl.classList.add('price-updated');
                            setTimeout(() => {
                                unitPriceEl.classList.remove('price-updated');
                            }, 2000);
                            pricesUpdated = true;
                        }
                    }
                });
            });
        }
        
        // Update cart total
        updateCartTotal(formattedTotal) {
            if (!formattedTotal) {
                console.log("No formatted total provided");
                return;
            }
            
            console.log("Updating cart total with:", formattedTotal);
            
            // Update the total amount
            const totalEls = document.querySelectorAll('.oe_website_sale_subtotal .oe_currency_value, .oe_cart_total .oe_currency_value, .oe_total .oe_currency_value');
            totalEls.forEach(totalEl => {
                // Extract just the numeric part for comparison
                const oldTotalText = totalEl.textContent;
                
                // Only update if the content is different
                if (oldTotalText !== formattedTotal) {
                    console.log("Updating total from", oldTotalText, "to", formattedTotal);
                    
                    // Update the total
                    totalEl.innerHTML = formattedTotal;
                    
                    // Add highlight effect
                    totalEl.classList.add('price-updated');
                    setTimeout(() => {
                        totalEl.classList.remove('price-updated');
                    }, 2000);
                }
            });
        }
    }
    
    // Initialize the cart price updater
    const cartPriceUpdater = new CartPriceUpdater();
    
    // Store the instance for potential future use
    window.CartPriceUpdater = cartPriceUpdater;
});
