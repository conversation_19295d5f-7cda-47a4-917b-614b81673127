<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="menu_advocate_registration" model="website.menu">
            <field name="name">Advocate Application</field>
            <field name="url">/advocate-application</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">30</field>
        </record>
         <!-- <record id="menu_advocate_search" model="website.menu">
            <field name="name">Advocate Search</field>
            <field name="url">/advocate-search</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">40</field>
        </record> -->
         <!-- <record id="menu_advocate_details" model="website.menu">
            <field name="name">Advocate Details</field>
            <field name="url">/advocate-details</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">50</field>
        </record>
         <record id="menu_advocate_profile" model="website.menu">
            <field name="name">Advocate Profile</field>
            <field name="url">/advocate-profile</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">60</field>
        </record> -->
         <!-- <record id="menu_advocate_profile_json" model="website.menu">
            <field name="name">Advocate Profile Data</field>
            <field name="url">/advocate-profile-json</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">65</field>
        </record> -->
    </data>
</odoo>