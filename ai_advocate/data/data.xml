<odoo>
    <data noupdate="1">
        <record model="specialization" id="1">
            <field name="name">Adoption</field>
        </record>
        <record model="specialization" id="2">
            <field name="name">Arbitration</field>
        </record>
        <record model="specialization" id="3">
            <field name="name">Armed Forces Tribunal</field>
        </record>
        <record model="specialization" id="4">
            <field name="name">Bail</field>
        </record>
        <record model="specialization" id="5">
            <field name="name">Banking</field>
        </record>
        <record model="specialization" id="6">
            <field name="name">Bankruptcy</field>
        </record>
        <record model="specialization" id="7">
            <field name="name">Cheating &amp; Fraud</field>
        </record>
        <record model="specialization" id="8">
            <field name="name">Cheque Bo<PERSON>ce</field>
        </record>
        <record model="specialization" id="9">
            <field name="name">Child Custody</field>
        </record>
        <record model="specialization" id="10">
            <field name="name">Civil</field>
        </record>
        <record model="specialization" id="11">
            <field name="name">Compliance</field>
        </record>
        <record model="specialization" id="12">
            <field name="name">Consumer Court</field>
        </record>
        <record model="specialization" id="13">
            <field name="name">Contract</field>
        </record>
        <record model="specialization" id="14">
            <field name="name">Conveyancing</field>
        </record>
        <record model="specialization" id="15">
            <field name="name">Copyright</field>
        </record>
        <record model="specialization" id="16">
            <field name="name">Corporate</field>
        </record>
        <record model="specialization" id="17">
            <field name="name">Court Marriage</field>
        </record>
        <record model="specialization" id="18">
            <field name="name">Criminal</field>
        </record>
        <record model="specialization" id="19">
            <field name="name">Cyber Crime</field>
        </record>
         <record model="specialization" id="20">
            <field name="name">Debt Recovery</field>
        </record>
        <record model="specialization" id="21">
            <field name="name">Divorce</field>
        </record>
        <record model="specialization" id="22">
            <field name="name">Domestic Violence</field>
        </record>
        <record model="specialization" id="23">
            <field name="name">Dowry</field>
        </record>
        <record model="specialization" id="24">
            <field name="name">DRT</field>
        </record>
        <record model="specialization" id="25">
            <field name="name">Employment</field>
        </record>
        <record model="specialization" id="26">
            <field name="name">Family</field>
        </record>
        <record model="specialization" id="27">
            <field name="name">GST</field>
        </record>
        <record model="specialization" id="28">
            <field name="name">Immigration</field>
        </record>
        <record model="specialization" id="29">
            <field name="name">Insurance</field>
        </record>
        <record model="specialization" id="30">
            <field name="name">Intellectual Property</field>
        </record>
        <record model="specialization" id="31">
            <field name="name">International</field>
        </record>
        <record model="specialization" id="32">
            <field name="name">Labour</field>
        </record>
        <record model="specialization" id="33">
            <field name="name">Land Dispute</field>
        </record>
        <record model="specialization" id="34">
            <field name="name">Landlord Tenant</field>
        </record>
        <record model="specialization" id="35">
            <field name="name">Legal Documents</field>
        </record>
        <record model="specialization" id="36">
            <field name="name">MACT</field>
        </record>
        <record model="specialization" id="37">
            <field name="name">Money Laundering</field>
        </record>
        <record model="specialization" id="38">
            <field name="name">Muslim Law</field>
        </record>
        <record model="specialization" id="39">
            <field name="name">NDPS</field>
        </record>
        <record model="specialization" id="40">
            <field name="name">Patent</field>
        </record>
        <record model="specialization" id="41">
            <field name="name">Personal Injury</field>
        </record>
        <record model="specialization" id="42">
            <field name="name">PIL</field>
        </record>
        <record model="specialization" id="43">
            <field name="name">POSCO</field>
        </record>
        <record model="specialization" id="44">
            <field name="name">Property</field>
        </record>
        <record model="specialization" id="45">
            <field name="name">RERA</field>
        </record>
        <record model="specialization" id="46">
            <field name="name">Revenue</field>
        </record>
        <record model="specialization" id="47">
            <field name="name">Special Leave Petitions</field>
        </record>
        <record model="specialization" id="48">
            <field name="name">Succession Certificate</field>
        </record>    
        <record model="specialization" id="49">
            <field name="name">Tax</field>
        </record>
        <record model="specialization" id="50">
            <field name="name">Trade</field>
        </record>
        <record model="specialization" id="51">
            <field name="name">Trademark</field>
        </record>
        <record model="specialization" id="52">
            <field name="name">Trust, Society and NGO</field>
        </record>
        <record model="specialization" id="53">
            <field name="name">Trusts and estates</field>
        </record>
        <record model="specialization" id="54">
            <field name="name">Will</field>
        </record>
        <record model="specialization" id="55">
            <field name="name">Writ</field>
        </record>
        <record model="courttype" id="101">
            <field name="name">Supreme Court</field>
        </record>
        <record model="courttype" id="102">
            <field name="name">National Green Tribunal</field>
        </record>
        <record model="courttype" id="103">
            <field name="name">High Court</field>
        </record>
        <record model="courttype" id="104">
            <field name="name">Session Court</field>
        </record>
        <record model="courttype" id="105">
            <field name="name">Metropolitan Magistrate</field>
        </record>
        <record model="courttype" id="106">
            <field name="name">Chief Judicial Magistrate</field>
        </record>
        <record model="courttype" id="107">
            <field name="name">Judicial Magistrate</field>
        </record>
        <record model="courttype" id="108">
            <field name="name">Collector &amp; District Magistrate</field>
        </record>
        <record model="courttype" id="109">
            <field name="name">Deputy Collector &amp; Sub Divisional Magistrate</field>
        </record>
          <record model="courttype" id="110">
            <field name="name">Executive Magistrate &amp; Mamlatdar</field>
        </record>

        <record id="ai_advocate.model_advocatedetails" model="ir.model">
                    <field name="website_form_key">create_advocate_registration</field>
                    <field name="website_form_access">True</field>
                    <field name="website_form_label">Create a Advocate Registration </field>
        </record>
        <!-- <function model="ir.model.fields" name="formbuilder_whitelist">
            <value>x_blog_complains</value>
            <value eval="[
              'name', 'x_phone_no', 'x_email', 'x_blog_url', 'x_reason'
              ]"/>
        </function> -->

    </data>
</odoo> 