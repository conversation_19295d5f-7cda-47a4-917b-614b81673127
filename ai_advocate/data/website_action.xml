<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Define an action for the customer registration page -->
        <record id="action_advocate_form" model="ir.actions.act_url">
            <field name="name">Advocate Application</field>
            <field name="url">/advocate-application</field>
            <field name="target">self</field>
        </record>
        <!-- <record id="action_advocate_serach" model="ir.actions.act_url">
            <field name="name">Advocate Search</field>
            <field name="url">/advocate-search</field>
            <field name="target">self</field>
        </record> -->
        <!-- <record id="action_advocate_details" model="ir.actions.act_url">
            <field name="name">Advocate Details</field>
            <field name="url">/advocate-details</field>
            <field name="target">self</field>
        </record>
        <record id="action_advocate_profile" model="ir.actions.act_url">
            <field name="name">Advocate Profile</field>
            <field name="url">/advocate-</field>
            <field name="target">self</field>
        </record> -->
          <!-- <record id="action_advocate_profile_json" model="ir.actions.act_url">
            <field name="name">Advocate Profile Data</field>
            <field name="url">/advocate-profile-json</field>
            <field name="target">self</field>
        </record> -->
    </data>
</odoo>