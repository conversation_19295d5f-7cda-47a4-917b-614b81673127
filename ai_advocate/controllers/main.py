from odoo import http
from odoo.http import request
from odoo.exceptions import ValidationError
# from datetime import datetime
import json
from werkzeug.utils import redirect
from odoo.addons.portal.controllers.portal import CustomerPortal

import base64
import datetime
import json
import logging
import json
import requests

class AdvocateFormController(http.Controller):
    logger = logging.getLogger(__name__)
      
    @http.route('/advocate-registration', type='http', auth='public', website=True, name='Advocate FORM')
    def advocate_form_page(self, **kwargs):
        try:
            # Define default template
            template_name = 'ai_advocate.website_advocate_registration_page'
            # Render the selected template
            return request.render(template_name, {})
        except ValueError:
            # Handle invalid user type
            return request.render('user.invalid_user_type_template', {})
        except Exception as e:
            self.logger.exception("An error occurred while rendering Advocate form page: %s", str(e))
            return http.request.make_response("An error occurred while rendering Advocate form page.")
   
    # @http.route(['/advocate-search'], type='http', auth="public", website=True)
    # def AdvocateSearch(self):
    #     # advocates = request.env['res.partner'].sudo().search([])
    #     specialization = request.env['specialization'].sudo().search([])
    #     values = {}
    #     values.update({
    #         # 'advocates': advocates,
    #         'specializations' : specialization
    #     })
    #     return request.render("ai_advocate.website_advocate_search", values)
    
    # @http.route(['/advocate-search'], type='http', auth="public", website=True)
    # # def advocate_search(self, specializations=None, districts=None, talukas=None, **kwargs):
    # def advocate_search(self):
    #     specializations = request.env['specialization'].sudo().search([])
    #     # specializations = int(request.httprequest.args.get('specializations', 1))
    #     # districts = int(request.httprequest.args.get('districts', 1))
    #     # talukas = int(request.httprequest.args.get('talukas', 1))
    #     states = request.env['res.country.state'].sudo().search([('country_id', '=', 104)])
    #     districts = request.env['location.district'].sudo().search([('x_state_id', '=', 588)])
    #     talukas = request.env['location.taluka'].sudo().search([])
    #     # _logger.info("Specializations: %s", specializations)  # Add this line to log specialization data
    #     # console.log(specializations)

    #     # domain = []

    #     # # Construct domain based on parameters
    #     # if specializations:
    #     #     domain.append(('x_specialization', '=', int(specializations)))
    #     # if districts:
    #     #     domain.append(('x_district', '=', int(districts)))
    #     # if talukas:
    #     #     domain.append(('x_taluka', '=', int(talukas)))
    #     values = {'specializations': specializations, 'states':states ,'talukas':talukas ,'districts':districts}
    #     return request.render("ai_advocate.website_advocate_search", values)

    #     # advocates = request.env['advocatedetails'].sudo().search(domain)

    #     # # Prepare data to be returned
    #     # advocate_data = []
    #     # for advocate in advocates:
    #     #     advocate_data.append({
    #     #         # 'name': advocate.name,
    #     #         'specializations': advocate.x_specialization,
    #     #         'districts': advocate.x_district,
    #     #         'talukas': advocate.x_taluka,
        
    #     #     })

    #     # # Return JSON response
    #     # return json.dumps({'data': advocate_data})
    
    # @http.route(['/advocate-details'], type='http', auth="public", website=True)
    # def advocate_details(self):
    #     advocates = request.env['advocatedetails'].sudo().search([])
   
    #     values = {'advocates': advocates }
    #     return request.render("ai_advocate.website_advocate_details", values)
    

    # @http.route(['/advocate-listing'], type='http', auth="public", website=True)
    # def advocate_listing(self):
    #     advocates = request.env['advocatedetails'].sudo().search([])
   
    #     values = {'advocates': advocates }
    #     return request.render("ai_advocate.website_advocate_listing", values)
    # def advocate_details(self, specialization=None, district=None, taluka=None, **kwargs):
    #     #  specialization = int(request.httprequest.args.get('specialization', 1))
    #     #  district = int(request.httprequest.args.get('district', 1))
    #     #  taluka = int(request.httprequest.args.get('taluka', 1))
    #     domain = []

    #     # Construct domain based on parameters
    #     if specialization:
    #         domain.append(('x_specialization', '=', int(specialization)))
    #     if district:
    #         domain.append(('x_district', '=', int(district)))
    #     if taluka:
    #         domain.append(('x_taluka', '=', int(taluka)))

    #     # Fetch data from Advocate model
    #     advocates = request.env['advocatedetails'].sudo().search(domain)

    #     # Prepare data to be returned
    #     advocate_data = []
    #     for advocate in advocates:
    #         advocate_data.append({
    #             # 'name': advocate.name,
    #             'specialization': advocate.x_specialization,
    #             'district': advocate.x_district,
    #             'taluka': advocate.x_taluka,
        
    #         })

    #     # Return JSON response
    #     return json.dumps({'data': advocate_data})
    #         # return request.render("ai_advocate.website_advocate_details", advocate_data)
    #     # return request.render("ai_advocate.website_advocate_details", {'advocate_data': advocate_data})

    

    # @http.route(['/advocate-profile/<int:advocateid>'], type='http', auth="public", website=True)
    # @http.route(['/advocate-profile'], type='http', auth="public", website=True)
    # # def advocate_profile(self,advocateid):
    # def advocate_profile(self):
    #     # advocates = request.env['advocatedetails'].sudo().search([advocateid])
    #     advocates = request.env['advocatedetails'].sudo().search([])
   
    #     values = {'advocates': advocates }
    #     return request.render("ai_advocate.website_advocate_profile", values)
    

    
    @http.route(['/advocate-profile/json'], type='http', auth="public", website=True ,method= ['GET','POST'])
    
    def advocate_profile_json(data):
        # url= "/advocate-profile/json"

        # response =request.post(url,json=data)

        # if response.status_code == 200:

        #     return response.json()
        # else:
        #     return None
   

        json_data= { "json":
          [ {"name": {"1":"shrijit"} } ]
            # {"name": "2", "x_first_name": "Juhee"}, 
            # {"name": "Manali ", "x_first_name": "Manali "}, 
            #     {"name": "Dhruv ", "x_first_name": "Dhruv "}
        }  
                
        # result = advocate_profile_json(json_data)

            # Print the result
        # print(result)


     # advocates = request.env['advocatedetails'].sudo().search([])
        # advocate_data=[]
        # for advocate in advocates:
        #     advocate_data.append ({
        #         'name':advocate.name,
        #         'x_first_name':advocate.x_first_name
        #     })
   
        return http.request.make_response(json.dumps(json_data))
        # return json.dumps(advocate_data)
        # values = {'advocates': advocates }
        # return request.render("ai_advocate.website_advocate_profile", values)

    # POST request to create a record
    @http.route('/api/v2/<string:model_name>', type='json', auth='public', website=True, csrf=False, methods=['POST'])
    def create_record_v2(self,model_name, **kwargs):
        try:
            data = json.loads(request.httprequest.data)
            if data:
                record = request.env[model_name].create(data)
                # return Response(json.dumps({"id": record.id}), content_type='application/json')
                return {"name": record.name,
                         "x_staff": {
                             "name":record.x_staff.name
                         }
                         }
                # return {"name": record.name}
        except Exception as e:
            # return {'status': 'error', status=500,  content_type='application/json' , 'error_message': str(e)}
            return {'status': 'error', 'error_message': str(e)}

   
    @http.route(['/advocate-profile-search'], type='json', auth="public", website=True ,method= ['POST'])
    def advocate_post_data(data):

        json_get = {}

        json_data =  json.loads(request.httprequest.get_data(as_text=True))
        

        if json_data and 'json' in json_data:
  
        #  json_data = post.get('data')  # Assuming the JSON data is passed as 'data' parameter
        
        # Process the JSON data as needed
           processed_data = []
        for item in json_data:
                # Example processing: Convert 'false' strings to False
                processed_item = {key: False if value.lower() == 'false' else value for key, value in item.items()}
                processed_data.append(processed_item)
        
        # Example: Return a response with the processed data
        return {
            'status': 'success',
            'message': 'Data received and processed successfully',
            'processed_data': processed_data
        }


    # @http.route('/submit_advocate_form', type='http', auth='public', website=True)
    # def submit_advocate_details(self, **post):
    #     try:
    #         # Perform server-side validation for required fields
    #         # required_fields = ['x_first_name', 'x_whatsapp_number']  # Add other required field names here
    #         # for field_name in required_fields:
    #         #     if not post.get(field_name):
    #         #         return http.request.make_response(f"{field_name.replace('_', ' ').title()} is required. Please fill it out.")

    #         # Process dynamic fields
    #         dynamic_fields = {
    #             'many2one': [],
    #             'many2many': {},
    #             'one2many': {}
    #         }

    #         for key, value in post.items():
    #             if key.startswith('x_'):
    #                 field_name_parts = key.split('_')
    #                 field_type = field_name_parts[1]  # Extract field type (many2one, many2many, one2many)
    #                 if field_type == 'many2one':
    #                     dynamic_fields[field_type].append((field_name_parts[2], int(value)))
    #                 elif field_type == 'many2many':
    #                     dynamic_fields[field_type].setdefault(field_name_parts[2], []).append(int(value))
    #                 elif field_type == 'one2many':
    #                     field_index = field_name_parts[3]  # Extract index of the field
    #                     field_name = '_'.join(field_name_parts[4:])  # Extract field name
    #                     dynamic_fields[field_type].setdefault(field_name, {})
    #                     dynamic_fields[field_type][field_name].setdefault(int(field_index), {})[field_name_parts[-1]] = value

    #         # Handle Many2one fields
    #         for field_name, field_value in dynamic_fields['many2one']:
    #             post[field_name] = field_value

    #         # Handle Many2many fields
    #         for field_name, field_values in dynamic_fields['many2many'].items():
    #             post[field_name] = [(6, 0, field_values)]

    #         # Handle One2many fields
    #         one2many_data = []
    #         for field_name, field_values in dynamic_fields['one2many'].items():
    #             for index, data in field_values.items():
    #                 one2many_data.append((0, index, data))
    #         post.update(one2many_data)

    #         # If all validations pass, proceed with record creation
    #         new_record = request.env['advocatedetails'].sudo().create(post)
    #         return redirect('/thankyou')

    #     except ValidationError as e:
    #         self.logger.exception("An error occurred while submitting Advocate details: %s", str(e))
    #         return http.request.make_response("An error occurred while submitting Advocates details.")
        

    
    