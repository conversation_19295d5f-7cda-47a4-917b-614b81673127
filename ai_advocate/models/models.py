from odoo import models, fields, api

class Specialization(models.Model):
    _name = 'specialization'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Specialization model to store various specializations of advocates'
    
    name = fields.Char(string='Name', help='Name of the specialization.')

class CourtType(models.Model):
    _name = 'courttype'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Various types of courts listed here'
    
    name = fields.Char(string='Name', help='Name of the court type.')

class Advocatedetails(models.Model):
    _name = 'advocatedetails'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Advocate Registration'
    
    name = fields.Char(string='Name', compute='_compute_full_name', help='Full name of the advocate.')
    x_first_name = fields.Char(string='First Name', help='First name of the advocate.')
    x_father_name = fields.Char(string='Father Name/ Husband Name', help='Father’s name or husband’s name of the advocate.')
    x_surname = fields.Char(string='Surname', help='Surname of the advocate.')
    x_age = fields.Integer(string='Age', help='Age of the advocate.')
    x_profile_picture = fields.Binary(string='Profile Picture', help='Profile picture of the advocate.')
    x_email = fields.Char(string='Email', help='Email address of the advocate.')
    x_password = fields.Char(string='Password', help='Password for the advocate’s account.')
    x_mobile_number = fields.Char(string='Mobile Number', help='Mobile number of the advocate.')
    x_whatsapp_number = fields.Char(string='Whatsapp Number', help='Whatsapp number of the advocate.')
    x_building_no_name = fields.Char(string='Building No - Name', help='Building number and name of the advocate’s address.')
    x_street = fields.Char(string='Street', help='Street address of the advocate.')
    x_landmark = fields.Char(string='Landmark', help='Landmark near the advocate’s address.')
    x_state = fields.Many2one('res.country.state', string='State', help='State where the advocate resides.')
    x_district = fields.Many2one('location.district', string='District', help='District where the advocate resides.')
    x_taluka = fields.Many2one('location.taluka', string='Taluka/City', help='Taluka or city where the advocate resides.')
    x_village = fields.Many2one('location.village', string='Village/Area', help='Village or area where the advocate resides.')
    x_pincode = fields.Char(string='Pincode', help='Postal code for the advocate’s address.')
    x_education = fields.Text(string='Education', help='Educational qualifications of the advocate.')
    x_experience = fields.Float(string='Experience (in years)', help='Total years of experience of the advocate.')
    x_license_number = fields.Char(string='License Number', help='License number of the advocate.')
    x_specialization = fields.Many2many('specialization', 'x_specialization_x_advocates_rel', 'x_advocates_id', 'x_specialization_id', string='Specialization', help='Specializations of the advocate.')
    x_awards = fields.Text(string='Awards and Recognitions', help='Awards and recognitions received by the advocate.')
    x_blogs_info = fields.Text(string='Blogs and Information', help='Blogs and other informational content about the advocate.')
    x_staff = fields.One2many('staffdetails', 'x_advocate_id', string='Staff', help='Staff members associated with the advocate.')
    x_bar_association = fields.Char(string='Bar Association', help='Bar association to which the advocate belongs.')
    x_basic_info_link = fields.Char(string='Basic Info Link', help='Link to the basic information about the advocate.')
    x_practice_district = fields.Many2many('location.district', 'x_district_x_advocates_rel', 'x_advocates_id', 'x_district_id', string='Practice District', help='Districts where the advocate practices.')
    x_practice_taluka = fields.Many2many('location.taluka', 'x_taluka_x_advocates_rel', 'x_advocates_id', 'x_taluka_id', string='Practice Taluka', help='Talukas where the advocate practices.')
    x_court_type = fields.Many2many('courttype', 'x_court_type_x_advocates_rel', 'x_advocates_id', 'x_court_type_id', string='Practice Court Type', help='Types of courts where the advocate practices.')
    user_id = fields.Many2one('res.users', string='User', help='User associated with the advocate.')
    company_id = fields.Many2one('res.company', string='Company', help='Company associated with the advocate.')

    @api.depends('x_first_name', 'x_father_name', 'x_surname')
    def _compute_full_name(self):
        for record in self:
            full_name = ' '.join(filter(None, [record.x_first_name, record.x_father_name, record.x_surname]))
            record.name = full_name

class Staffdetails(models.Model):
    _name = 'staffdetails'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Advocates Staff Details'
    
    name = fields.Char(string='Full Name', help='Full name of the staff member.')
    x_phone = fields.Char(string='Phone', help='Phone number of the staff member.')
    x_email = fields.Char(string='Email', help='Email address of the staff member.')
    x_address = fields.Text(string='Address', help='Address of the staff member.')
    x_position = fields.Char(string='Position', help='Position of the staff member within the advocate’s office.')
    x_department = fields.Char(string='Department', help='Department in which the staff member works.')
    x_join_date = fields.Date(string='Join Date', help='Date when the staff member joined.')
    x_leave_date = fields.Date(string='Leave Date', help='Date when the staff member left (if applicable).')
    x_experience = fields.Float(string='Experience (in years)', help='Total years of experience of the staff member.')
    x_education = fields.Text(string='Education', help='Educational qualifications of the staff member.')
    x_advocate_id = fields.Many2one('advocatedetails', string='Advocate', help='Advocate to whom the staff member is associated.')
    x_whatsapp_number = fields.Char(string='Whatsapp Number', help='Whatsapp number of the staff member.')
