# -*- coding: utf-8 -*-
{
    'name': 'Advocate Registration',
    'version': '1.0',
    'category': 'Administration',
    'summary': 'Advocate module to help advocates registeration',
    'description': 'Advocate module to help user search Advocates and register as one ',
    'author': '<PERSON><PERSON><PERSON>',
    'website': 'https://arihantai.com/',
    'depends': [
        'base',
        'website',
        'ai_location',
       
    ],
    'data': [
        'security/advocate_security.xml',
        'security/ir_rule.xml',
        'security/ir.model.access.csv',
        # 'views/view.xml',
        'views/advocate_view.xml',
        'views/staff_view.xml',
        # 'views/advocate_client_views.xml',
        'views/specialization_view.xml',
        'views/courttype_view.xml',
        'views/website_advocateform_template.xml',
        # 'views/website_advocate_search.xml',
        # 'views/website_advocate_details.xml',
        # 'views/website_advocate_listing.xml',
        # 'views/website_advocate_profile.xml',
        # 'views/website_advocate_profile_json.xml',

        'data/data.xml',
        'data/website_menu.xml',
        'data/website_action.xml',
        'data/mail_template_data.xml',
        'data/mail_template_advocate.xml',
        'views/page_record.xml',
    ],

    'installable': True,
    'auto_install': False,
    'application': True,

    'license': 'LGPL-3',

}
