<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_advocate_tree" model="ir.ui.view">
            <field name="name">advocate.tree</field>
            <field name="model">advocatedetails</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="x_first_name"/>
                    <field name="x_surname"/>
                    <field name="x_email"/>
                    <field name="x_age"/>
                </tree>
            </field>
        </record>

        <record id="view_advocate_form" model="ir.ui.view">
            <field name="name">advocate.form</field>
            <field name="model">advocatedetails</field>
            <field name="arch" type="xml">
                <form string="Advocates">
                    <sheet>
                        <field name="x_profile_picture" widget="image" class="oe_avatar"/>
                        <group>
                            <group string="Personal Information">
                                <field name="name"/>
                                <field name="x_first_name"/>
                                <field name="x_father_name"/>
                                <field name="x_surname"/>
                                <field name="x_email"/>
                                <field name="x_age"/>
                                <field name="x_mobile_number"/>
                                <field name="x_whatsapp_number"/>
                            </group>

                            <group string="Residential Information">
                                <field name="x_building_no_name"/>
                                <field name="x_landmark"/>
                                <field name="x_street"/>
                                <field name="x_state" domain="[('country_id','=','IN')]"/>
                                <field name="x_district" domain="[('x_state_id', '=', x_state)]"/>
                                <field name="x_taluka" domain="[('x_district_id', '=', x_district)]"/>
                                <field name="x_village" domain="[('x_taluka_id', '=', x_taluka)]"/>
                            </group>
                        </group>
                        <group string="Professional Details">
                            <group>
                                <field name="x_education"/>
                                <field name="x_specialization" widget="many2many_tags"/>
                                <field name="x_license_number"/>
                                <field name="x_experience"/>
                                <field name="x_court_type" widget="many2many_tags"/>
                                <field name="x_basic_info_link"/>
                            </group>
                            <group>
                                <field name="x_awards"/>
                                <field name="x_blogs_info"/>
                                <field name="x_bar_association"/>
                                <field name="x_practice_district" domain="[('x_state_id', '=', x_state)]" widget="many2many_tags"/>
                                <field name="x_practice_taluka" domain="[('x_district_id', 'in', x_practice_district)]" widget="many2many_tags"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Staff Management">
                                <field name="x_staff"/>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids" options="{'post_refresh': 'recipients'}"/>
                    </div>
                </form>
            </field>
        </record>

       <record id="view_advocate_search" model="ir.ui.view">
    <field name="name">advocate.search</field>
    <field name="model">advocatedetails</field>
    <field name="arch" type="xml">
        <search string="Advocates">
            <field name="name" string="Name"/>
            <field name="x_first_name" string="First Name"/>
            <field name="x_father_name" string="Father Name"/>
            <field name="x_surname" string="Surname"/>
            <field name="x_mobile_number" string="Mobile Number"/>
            <field name="x_whatsapp_number" string="Whatsapp Number"/>
            <field name="x_email" string="Email"/>
            <!-- <filter string="Has Profile Picture" domain="[('x_profile_picture', '!=', False)]"/>
            <filter string="Without Profile Picture" domain="[('x_profile_picture', '=', False)]"/> -->
        </search>
    </field>
</record>


        <record id="view_advocatedetails_kanban" model="ir.ui.view">
            <field name="name">advocatedetails.kanban</field>
            <field name="model">advocatedetails</field>
            <field name="arch" type="xml">
                <kanban string="Advocates">
                    <templates>
                        <t t-name="kanban-box">
                            <div class="o_kanban_card">
                                <div class="o_kanban_image">
                                    <img t-if="record.x_profile_picture.raw_value" t-att-src="'data:image/png;base64,%s' % record.x_profile_picture.raw_value" class="img img-fluid"/>
                                    <t t-if="not record.x_profile_picture.raw_value">
                                        <div class="o_kanban_image_placeholder">No Image</div>
                                    </t>
                                </div>
                                <div class="o_kanban_details">
                                    <strong>
                                        <field name="name"/>
                                    </strong>
                                    <div>
                                        <field name="x_mobile_number" placeholder="Mobile Number"/>
                                    </div>
                                    <div>
                                        <field name="x_whatsapp_number" placeholder="Whatsapp Number"/>
                                    </div>
                                    <div>
                                        <field name="x_email" placeholder="Email"/>
                                    </div>
                                    <div>
                                        <field name="x_specialization" placeholder="Specializations"/>
                                    </div>
                                    <div>
                                        <field name="x_experience" placeholder="Experience (years)"/>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
       
        <menuitem id="menu_advocate_master" name="Advocate Master" sequence="10"/>
        
        <record id="action_advocate" model="ir.actions.act_window">
            <field name="name">Advocate Details</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">advocatedetails</field>
            <field name="view_mode">tree,form,kanban</field>
            <field name="search_view_id" ref="view_advocate_search"/>
        </record>

        <menuitem id="menu_advocate"
                  name="Advocate Details"
                  parent="menu_advocate_master"
                  action="action_advocate"
                  sequence="15"/>
    </data>
</odoo>
