<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data> 
 <record id="view_courttype_tree" model="ir.ui.view">
            <field name="name">courttype.tree</field>
            <field name="model">courttype</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>
 
 
 <record id="view_courttype_form" model="ir.ui.view">
                <field name="name">courttype.form</field>
                <field name="model">courttype</field>
                <field name="arch" type="xml">
                    <form>
                     <sheet>  
                <group>     
            <field name="name" string="Name" />        
                </group>  
                    </sheet>
                    <div class="oe_chatter">
                               <field name="message_follower_ids"/>
                               <field name="activity_ids"/>
                               <field name="message_ids"
                                      options="{'post_refresh': 'recipients'}"/>
                            </div>  
                    </form>
                </field>
            </record>


  <record id="action_courttype" model="ir.actions.act_window">
            <field name="name">Court Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">courttype</field>
            <field name="view_mode">tree,form</field>
        </record>
        <menuitem id="menu_configuration" parent="menu_advocate_master" name="Configuration" sequence="100" />
        <menuitem id="menu_courttype"
                      name="Court Type"
                      parent="menu_configuration"
                      action="ai_advocate.action_courttype"
                      sequence="50"/>          

    </data>
</odoo>

                  