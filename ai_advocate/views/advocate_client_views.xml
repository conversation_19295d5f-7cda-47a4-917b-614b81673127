<odoo>
    <record id="view_advocate_client_form" model="ir.ui.view">
        <field name="name">advocate.client.form</field>
        <field name="model">advocate.client</field>
        <field name="arch" type="xml">
            <form string="Advocate Client">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="contact_details"/>
                        <field name="advocate_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="action_advocate_client" model="ir.actions.act_window">
            <field name="name">Advocate Clients</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">advocate.client</field>
            <field name="view_mode">tree,form</field>
        </record>
        
        <menuitem id="menu_advocate"
                      name="Advocate Clients"
                      parent="menu_advocate_master"
                      action="ai_advocate.action_advocate_client"
                      sequence="40"/>        
</odoo>