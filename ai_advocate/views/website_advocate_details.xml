<?xml version="1.0" encoding="utf-8"?>
<odoo>
   <template id="ai_advocate.website_advocate_details" name="Advocate Details">
       <t t-call="website.layout">
           <div id="wrap" class="oe_structure oe_empty">
                           <h3 style="margin-left:100px;"> Advocate Details </h3>


             <t t-set="specialization" t-value="request.httprequest.args.get('specialization')"/>
             <t t-set="district" t-value="request.httprequest.args.get('district')"/>
             <t t-set="taluka" t-value="request.httprequest.args.get('taluka')"/>              

             <section style="background-image: none; position: relative;" class="o_colored_level pb0 pt32"
                data-original-title="" title="" aria-describedby="tooltip264081"
                data-oe-shape-data="{&quot;shape&quot;:&quot;web_editor/Origins/04_001&quot;,&quot;flip&quot;:[]}">
                <div class="o_we_shape o_web_editor_Origins_04_001" style="" />
                <style>
               
                .card {
                    display: flex;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                    max-width: 800px;
                    margin: 0 auto;
                    border-color: #ffb6c1;
                    cursor: pointer; /* Added cursor pointer */
                }

                .column {
                    flex: 1;
                    padding: 20px;
                }

                .column:first-child {
                    flex: 0 0 150px;
                }

                .column img {
                    max-width: 50%;
                    height: auto;
                    margin-right: 20px;
                    /* Define the clip path for the rounded shape */
                    clip-path: circle(50% at 50% 50%);
                }

                .buttons {
                    display: flex;
                    gap: 10px;
                    margin-top: 20px;
                }

                .buttons button {
                    padding: 10px 15px;
                    border: none;
                    border-radius: 4px;
                    background-color: #007bff;
                    color: white;
                    cursor: pointer;
                    width: 100%;
                    transition: background-color 0.3s ease;
                }

                .buttons button:hover {
                    background-color: #0056b3;
                }


                .card-container {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 15px;
                }

                .card {
                    width: 300px;
                    min-height: 1px;
                    margin-bottom: 15px;
                }

            </style>
                <!-- <t t-set="advocatedetails" t-value="env['advocatedetails'].sudo().search([])" /> -->
                 <t t-if="specialization == '0' or taluka == '0' or district == '0' or not (specialization and taluka and district)">
                  <t t-set="advocatedetails" t-value="env['advocatedetails'].sudo().search([])"/>
               </t>
               <t t-else="">
                    <t t-set="advocatedetails" t-value="env['advocatedetails'].sudo().search([ '|',  '|',  ['x_specialization.id', '=', int(specialization)],['x_district.id', '=', district],['x_taluka.id', '=', taluka]])"/>
                  
               </t>
                <div class="card-container">
                    <t t-foreach="advocatedetails" t-as="a">
                      <div class="card">
                      <a t-attf-href="/advocate-profile?advocateid={{ a.id }}">
                      <!-- <a t-attf-href="/advocate-profile"> -->
                       <!--<div class="card" data-id="<t t-esc='a.id' >">-->
                        <!--<div class="card" t-attf-onclick="openPage(<t t-esc='a.id' />)">-->
                                                  <!--<div class="card" t-attf-onclick="openPage(<t t-esc='a.id' />)">-->
                                                  
                            <!--<div class="card" t-attf-onclick="openPage('<t t-esc='a.id' />')">-->
                        <!--<div class="card" t-attf-onclick="openPage(&quot;<t t-esc='a.id' />&quot;)">-->
                            <div class="column" style="line-height: 1.5;" data-original-id="390"
                                data-original-src="/web/image/390-90929bf7/website.s_image_text_default_image"
                                data-original-title="" title="" aria-describedby="tooltip666329">
                                <p data-original-title="" title="" aria-describedby="tooltip361225">
                                    <img class="img-fluid o_we_custom_image mr-auto float-left rounded img-thumbnail"
                                        src="/web/image/1527-ff72ef4c/image.jpg" alt="" data-original-title=""
                                        title="" aria-describedby="tooltip913269" style="width: 25% !important;"
                                        loading="lazy" data-original-id="1516" data-original-src="/web/image/1516-5bba4b2f/image.jpg"
                                        data-mimetype="image/jpeg" data-resize-width="172" />
                                    &amp;nbsp;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;
                                    <span style="font-size: 18px;">&amp;nbsp;</span>
                                    <strong><span style="font-size: 18px;"><t t-esc="a.x_first_name" /> <t
                                                t-esc="a.x_surname" /></span></strong>
                                </p>
                                <p data-original-title="" title="" aria-describedby="tooltip361225">&amp;nbsp; &amp;nbsp;&amp;nbsp;&amp;nbsp; <span
                                        style="font-size: 14px;">&amp;nbsp; </span><span class="fa fa-briefcase" /><span
                                        style="font-size: 14px;">&amp;nbsp;<t t-esc="a.x_experience" /></span></p>
                                <p data-original-title="" title="" aria-describedby="tooltip361225">
                                    <span
                                        style="color: rgb(33, 37, 41); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); font-size: 14px;"
                                        data-original-title="" title="" aria-describedby="tooltip621555">&amp;nbsp; &amp;nbsp;&amp;nbsp;&amp;nbsp; &amp;nbsp;&amp;nbsp;</span>
                                    <span style="color: rgb(33, 37, 41);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(255, 255, 255)"
                                        data-original-title="" title="" aria-describedby="tooltip621555">
                                        <span class="fa fa-lightbulb-o" />
                                    </span>
                                    <span
                                        style="color: rgb(33, 37, 41); font-style: normal; font-weight: 400; background-color: rgb(255, 255, 255); font-size: 14px;"
                                        data-original-title="" title="" aria-describedby="tooltip621555">&amp;nbsp;<t
                                            t-esc="a.x_specialization.name" /></span>
                                </p>
                                <p data-original-title="" title="" aria-describedby="tooltip882125">
                                    <span style="font-size: 14px;">
                                        &amp;nbsp;&amp;nbsp; &amp;nbsp;&amp;nbsp;&amp;nbsp; &amp;nbsp;</span>
                                    <span class="fa fa-location-arrow" data-original-title="" title=""
                                        aria-describedby="tooltip472808" />
                                    <span style="font-size: 14px;">&amp;nbsp<t t-esc="a.x_taluka.name" /> , <t
                                            t-esc="a.x_street" /> , <t t-esc="a.x_state.name" /></span>
                                </p>
                                <p data-original-title="" title="" aria-describedby="tooltip882125">&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&amp;nbsp; &amp;nbsp;&amp;nbsp;<a
                                        href="#" class="btn btn-fill-primary rounded-circle" data-original-title=""
                                        title="" style="background-color: #0056b3;">Book Consultant</a></p>
                            </div>
                        </a>
                        </div>
                    </t>
                </div>
            </section>
           </div>

           <!-- <script>
    // Event listener for search button
    document.getElementById('search_button').addEventListener('click', function() {
        // Gather selected values
        var specialization = document.getElementById('specSelect').value;
        var district = document.getElementById('disSelect').value;
        var taluka = document.getElementById('talSelect').value;
        alert(taluka);

        // Create request object
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '/advocate-details?specialization=' + specialization + '&amp;district=' + district + '&amp;taluka=' + taluka, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    // Handle successful response
                    var response = JSON.parse(xhr.responseText);
                    // Update search results on the page
                    // Example: display search results in a div with id="search_results"
                    console.log(response.data)
                    document.getElementById('search_results').innerHTML = response.data;
                } else {
                    // Handle error
                    console.error('Error:', xhr.statusText);
                }
            }
        };

        // Send request
        xhr.send();
    });
</script> -->
       </t>

        
   </template>
</odoo>