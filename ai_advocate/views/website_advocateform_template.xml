<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
    <template id="ai_advocate.website_advocate_registration_page" name="Advocate Registration">
      <t t-call="website.layout">
        <t t-set="user" t-value="env['res.users'].sudo().search([('id','=',uid)])" />
        <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css" />
        <style>
          /* Form */
          .oe_form {
            padding: 20px;
            border-radius: 5px;
            width: auto;
          }

          .nice-form-group {
            flex: 1;
            margin-right: 10px;
            /* Adjust this value as needed */
            --nf-input-focus-border-color: #ffc107 !important;
            --nf-input-focus-background-color: #ffc107 !important;
            padding-bottom: 0.5rem;
            padding-top: 0.5rem;
          }

          .nice-form-group {
            --nf-input-focus-border-color: #ffc107 !important;
            --nf-input-focus-background-color: #ffc107 !important;
            padding-bottom: 0.5rem;
            padding-top: 0.5rem;
          }

          .form-box {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #fff;
            /* White background for form container */
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border: 1px solid #ccc;
            /* Border around form box */
          }

          /* Form headings */
          h3 {
            color: #333;
          }

          /* Responsive Styles */
          @media (max-width: 768px) {
            .container {
              width: 100%;
            }
          }
        </style>
        <xpath expr="//div[@class='container']" position="inside">
          <div class="form-box">
            <form action="/website/form/" method="post" enctype="multipart/form-data" data-mark="*" data-pre-fill="true" data-success-mode="message" data-success-page="/contactus-thank-you" data-model_name="advocatedetails">
              <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()" />
              <div class="row">
                  <div class="s_allow_columns container"><h1 style="text-align: center;">Advocate Registration</h1></div>
                  <div class="col-md-12">
                    <h3>Personal Details</h3>
                    <div style="display: flex;">
                      <div class="nice-form-group">
                        <input type="text" name="x_first_name" placeholder="First Name" required="1" />
                      </div>
                      <div class="nice-form-group">
                        <input type="text" name="x_father_name" placeholder="Father's Name/Husband's Name" />
                      </div>
                      <div class="nice-form-group">
                        <input type="text" name="x_surname" placeholder="Surname" required="1" />
                      </div>
                    </div>
                    <div style="display: flex;">
                      <div class="nice-form-group">
                        <input type="email" name="x_email" class="icon-right" placeholder="Email" required="1" />
                      </div>
                      <div class="nice-form-group">
                        <input type="password" name="x_password" placeholder="Password" class="icon-right" required="1" />
                      </div>
                    </div>
                    <div style="display: flex;">
                      <div class="nice-form-group">
                        <input type="tel" name="x_mobile_number" class="icon-right " id="mobileno" placeholder="Mobile Number" required="1" />
                      </div>
                      <div class="nice-form-group">
                        <input type="checkbox" class="switch" name="x_is_whatsapp_no" id="mobilec" />
                        <span> WhatsApp Number Same As Mobile Number ? </span>
                      </div>
                      <div class="nice-form-group">
                        <input type="tel" name="x_whatsapp_number" id="whatno" class="icon-right" placeholder="WhatsApp Number" />
                      </div>
                    </div>
                    <div style="display: flex;">
                      <div class="nice-form-group" style="flex: 1; ">
                        <input type="date" name="x_date_of_birth" id="dateOfBirthInput" placeholder="Date of Birth" required="1" style="width: 100%;" />
                      </div>
                      <div class="nice-form-group">
                        <input type="number" name="x_age" style="width: 100%;" placeholder="Age" />
                      </div>
                    </div>
                  </div>
                  <div class="col-md-12">
                    <h3 style="margin-top:10px;">Address Details</h3>
                    <div style="display: flex;">
                      <div class="nice-form-group">
                        <input type="text" name="x_building_no_name" placeholder="Building No - Name" />
                      </div>
                      <div class="nice-form-group">
                        <input type="text" name="x_landmark" placeholder="Landmark" />
                      </div>
                      <div class="nice-form-group">
                        <input type="text" name="x_street" placeholder="Street" />
                      </div>
                    </div>
                    <div class="nice-form-group col">
                      <select class="form-control form-select s_website_form_input" id="stateSelect" autocomplete="off" name="x_district" required="1">
                        <option value="0">Select A State</option>
                      </select>
                    </div>
                  </div>
                  <div class="nice-form-group col">
                    <select class="form-control form-select s_website_form_input" id="districtSelect" autocomplete="off" name="x_district" required="1">
                      <option value="0">Select A District</option>
                    </select>
                  </div>
                </div>
                <div class="row ">
                  <div class="nice-form-group col">
                    <select class="form-control form-select s_website_form_input" id="talukaSelect" autocomplete="off" name="x_taluka" required="1">
                      <option value="0">Select A Taluka/City</option>
                    </select>
                  </div>
                  <div class="nice-form-group col">
                    <select class="form-control form-select s_website_form_input" id="villageSelect" autocomplete="off" name="x_village" required="1">
                      <option value="0">Select Village/Area</option>
                    </select>
                  </div>
                </div>
                <!-- </div> -->
                <div class="nice-form-group">
                  <input type="number" name="x_pincode" style="width: 100%;" placeholder="Pincode" />
                </div>
                <div class="col-md-12">
                  <h3 style="margin-top:15px;">Professional Information</h3>
                  <div class="nice-form-group" style="display: flex;">
                    <div class="nice-form-group">
                      <input type="text" name="x_education" placeholder="Education" />
                    </div>
                    <div class="nice-form-group">
                      <input type="number" name="x_experience" style="width: 100%;" placeholder="Experience (in years)" />
                    </div>
                  </div>
                  <div class="nice-form-group" style="display: flex;">
                    <div class="nice-form-group">
                      <input type="text" name="x_license_number" placeholder="License Number" />
                    </div>
               
                      <div class="nice-form-group">
                        <select name="x_specialization">
                          <option value="0">Select Specialization</option>
                          <option value="1" selected="selected">Adoption</option>
                          <option value="2">Arbitration</option>
                          <option value="3">Armed Forces Tribunal</option>
                          <option value="4">Bail</option>
                          <option value="5">Banking</option>
                          <option value="6">Bankruptcy</option>
                          <option value="7">Cheating &amp; Fraud</option>
                          <option value="8">Cheque Bounce</option>
                          <option value="9">Child Custody</option>
                          <option value="10">Civil</option>
                          <option value="11">Compliance</option>
                          <option value="12">Consumer Court</option>
                          <option value="13">Contract</option>
                          <option value="14">Conveyancing</option>
                          <option value="15">Copyright</option>
                          <option value="16">Corporate</option>
                          <option value="17">Court Marriage</option>
                          <option value="18">Criminal</option>
                          <option value="19">Cyber Crime</option>
                          <option value="20">Debt Recovery</option>
                          <option value="21">Divorce</option>
                          <option value="22">Domestic Violence</option>
                          <option value="23">Dowry</option>
                          <option value="24">DRT</option>
                          <option value="25">Employment</option>
                          <option value="26">Family</option>
                          <option value="27">GST</option>
                          <option value="28">Immigration</option>
                          <option value="29">Insurance</option>
                          <option value="30">Intellectual Property</option>
                          <option value="31">International</option>
                          <option value="32">Labour</option>
                          <option value="33">Land Dispute</option>
                          <option value="34">Landlord Tenant</option>
                          <option value="35">Legal Documents</option>
                          <option value="36">MACT</option>
                          <option value="37">Money Laundering</option>
                          <option value="38">Muslim Law</option>
                          <option value="39">NDPS</option>
                          <option value="40">Patent</option>
                          <option value="41">Personal Injury</option>
                          <option value="42">PIL</option>
                          <option value="43">POSCO</option>
                          <option value="44">Property</option>
                          <option value="45">RERA</option>
                          <option value="46">Revenue</option>
                          <option value="47">Special Leave Petitions</option>
                          <option value="48">Succession Certificate</option>
                          <option value="49">Tax</option>
                          <option value="50">Trade</option>
                          <option value="51">Trademark</option>
                          <option value="52">Trust, Society and NGO</option>
                          <option value="53">Trusts and estates</option>
                          <option value="54">Will</option>
                          <option value="55">Writ</option>
                        </select>
                      </div>
                </div>
                <div class="nice-form-group">
                  <textarea name="x_awards" placeholder="Awards and Recognitions" />
                </div>
                <div class="nice-form-group">
                  <textarea name="x_blogs_info" placeholder="Blogs and Information" />
                </div>
                <div class="nice-form-group">
                  <input type="text" name="x_bar_association" placeholder="Bar Association" />
                </div>
                <div class="nice-form-group">
                  <select multiple="multiple" style="height: auto;" name="x_practice_district" id="districtsSelected">
                    <option value="0">Select Practice District</option>
                    <!-- Add options dynamically from Odoo -->
                  </select>
                </div>
                <div class="nice-form-group">
                  <select multiple="multiple" style="height: auto;" name="x_practice_taluka" id="talukasSelected">
                    <option value="0">Select Practice Taluka</option>
                    <!-- Add options dynamically from Odoo -->
                  </select>
                </div>
                <br />
                <button type="submit" class="btn btn-primary" style="text-align: center;margin-left:300px;">Submit</button>
         
              </div>
            </form>
          </div>
        </xpath>
        <script>
          document.getElementById('mobilec').addEventListener('change', function() {
            if (this.checked) {
              var mobileNumber = document.getElementById('mobileno').value;
              document.getElementById('whatno').value = mobileNumber;
            } else {
              document.getElementById('whatno').value = '';
            }
          });
          document.addEventListener("DOMContentLoaded", function() {
            var stateSelect = document.getElementById("stateSelect");
            var districtSelect = document.querySelector("select[name='x_district']");
            var talukaSelect = document.querySelector("select[name='x_taluka']");
            var villageSelect = document.querySelector("select[name='x_village']");
            var selectedStateId = 0;
            var selectedDistrictId = 0;
            var selectedTalukaId = 0;

            function populateStates() {
              stateSelect.innerHTML = ' &lt; option value = "0" &gt; Select A State &lt; /option&gt;';
              var countryId = 104; // Assuming 104 is the ID for India in your database, change it if needed.
              fetch('/ai_get_states/' + countryId).then(response = &gt; response.json()).then(data = &gt; {
                data.forEach(state = &gt; {
                  var option = document.createElement("option");
                  option.value = state.id;
                  option.textContent = state.name;
                  stateSelect.appendChild(option);
                });
              });
            }

            function populateDistrictsSingle() {
              districtSelect.innerHTML = '';
              var selectOption = document.createElement("option");
              selectOption.value = '0';
              selectOption.textContent = 'Select A District';
              districtSelect.appendChild(selectOption);
              if (selectedStateId === 0) {
                return;
              }
              fetch('/ai_get_districts/' + selectedStateId).then(response = &gt; response.json()).then(data = &gt; {
                data.forEach(district = &gt; {
                  var option = document.createElement("option");
                  option.value = district.id;
                  option.textContent = district.name;
                  districtSelect.appendChild(option);
                });
              }).catch(error = &gt; {
                console.error('Error fetching districts:', error);
              });
            }

            function populateTalukasSingle() {
              talukaSelect.innerHTML = '';
              var selectOption = document.createElement("option");
              selectOption.value = '0';
              selectOption.textContent = 'Select A Taluka/City';
              talukaSelect.appendChild(selectOption);
              if (selectedDistrictId === 0) {
                return;
              }
              fetch('/ai_get_talukas/' + selectedDistrictId).then(response = &gt; response.json()).then(data = &gt; {
                data.forEach(taluka = &gt; {
                  var option = document.createElement("option");
                  option.value = taluka.id;
                  option.textContent = taluka.name;
                  talukaSelect.appendChild(option);
                });
              }).catch(error = &gt; {
                console.error('Error fetching talukas:', error);
              });
            }

            function populateVillagesSingle() {
              villageSelect.innerHTML = '';
              var selectOption = document.createElement("option");
              selectOption.value = '0';
              selectOption.textContent = 'Select Village/Area';
              villageSelect.appendChild(selectOption);
              if (selectedTalukaId === 0) {
                return;
              }
              fetch('/ai_get_villages/' + selectedTalukaId).then(response = &gt; response.json()).then(data = &gt; {
                data.forEach(village = &gt; {
                  var option = document.createElement("option");
                  option.value = village.id;
                  option.textContent = village.name;
                  villageSelect.appendChild(option);
                });
              }).catch(error = &gt; {
                console.error('Error fetching villages:', error);
              });
            }
            stateSelect.addEventListener("change", function() {
              selectedStateId = this.value;
              populateDistrictsSingle();
              // Call function to populate multiple districts
              populateMultiDistricts();
            });
            districtSelect.addEventListener("change", function() {
              selectedDistrictId = this.value;
              populateTalukasSingle();
            });
            talukaSelect.addEventListener("change", function() {
              selectedTalukaId = this.value;
              populateVillagesSingle();
            });
            // Function to populate multi-districts
            function populateMultiDistricts() {
              var selectedStates = stateSelect.value;
              var districtsSelected = document.getElementById("districtsSelected");
              districtsSelected.innerHTML = '  &lt;option value = "0" &gt; Select Practice District  &lt; /option&gt;';
              fetch('/ai_get_multi_districts?state_ids=' + selectedStates).then(response = &gt; response.json()).then(data = &gt; {
                data.forEach(district = &gt; {
                  var option = document.createElement("option");
                  option.value = district.id;
                  option.textContent = district.name;
                  districtsSelected.appendChild(option);
                });
              });
            }
            // Function to populate multi-talukas
            function populateMultiTalukas(selectedDistricts) {
              var talukaSelect = document.getElementById("talukasSelected");
              talukaSelect.innerHTML = ' &lt; option value = "0" &gt; Select Practice Talukas &lt; /option&gt;';
              if (selectedDistricts &amp; &amp; selectedDistricts.length &gt; 0) {
                fetch('/ai_get_multi_talukas?district_ids=' + selectedDistricts.join(',')).then(response = &gt; response.json()).then(data = &gt; {
                  data.forEach(taluka = &gt; {
                    var option = document.createElement("option");
                    option.value = taluka.id;
                    option.textContent = taluka.name;
                    talukaSelect.appendChild(option);
                  });
                });
              }
            }
            populateStates();
          });
        </script>
      </t>
    </template>
  </data>

</odoo>