<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data> 
 <record id="view_specialization_tree" model="ir.ui.view">
            <field name="name">specialization.tree</field>
            <field name="model">specialization</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>
 
 
 <record id="view_specialization_form" model="ir.ui.view">
                <field name="name">specialization.form</field>
                <field name="model">specialization</field>
                <field name="arch" type="xml">
                    <form>
                     <sheet>  
                <group>     
            <field name="name" string="Name" />        
                </group>  
                    </sheet> 
                    <div class="oe_chatter">
                               <field name="message_follower_ids"/>
                               <field name="activity_ids"/>
                               <field name="message_ids"
                                      options="{'post_refresh': 'recipients'}"/>
                            </div> 
                    </form>
                </field>
            </record>


  <record id="action_specialization" model="ir.actions.act_window">
            <field name="name">Specialization</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">specialization</field>
            <field name="view_mode">tree,form</field>
        </record>
        
        <menuitem id="menu_specialization"
                      name="Specialization"
                      parent="menu_advocate_master"
                      action="ai_advocate.action_specialization"
                      sequence="30"/>          

    </data>
</odoo>

                  