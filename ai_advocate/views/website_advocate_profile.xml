<?xml version="1.0" encoding="utf-8"?>
<odoo>
   <template id="ai_advocate.website_advocate_profile" name="Advocate Profile">
       <t t-call="website.layout">
           <div id="wrap" class="oe_structure oe_empty">

            <style>
                    
                    .eltdf-listing-single-holder .eltdf-ls-sidebar>* {
                    margin: 25px 0 0;
                    border-radius: 5px;
                    -webkit-box-shadow: 0 5px 35px 0 rgba(158, 127, 103, .12);
                    box-shadow: 0 5px 35px 0 rgba(158, 127, 103, .12);
                }


                .eltdf-listing-single-holder .eltdf-ls-location .eltdf-ls-location-contact-info {
                    padding: 28px 33px 24px;
                }

                eltdf-ls-location-contact-info{
                  padding-top:10px;
                }



                .page{
                
                <!--width:80%;-->
                padding:30px;
                }

                .header{
                
                    position: relative;
                    display: inline-block;
                    width: 100%;
                    vertical-align: middle;
                    -webkit-box-shadow: 0 5px 35px 0 rgba(158, 127, 103, .12);
                    box-shadow: 0 5px 35px 0 rgba(158, 127, 103, .12);
                    -webkit-box-sizing: border-box;
                    box-sizing: border-box;
                    
                    .row{
                    position: relative;
                    padding: 55px 0;
                    -webkit-box-sizing: border-box;
                    box-sizing: border-box;
                    width:1100px;
                    margin:0 auto;
                }
                    .list{
                    
                    
                    position: relative;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    margin-left: 25px;
                    
                    }
                    
                    .left-list{
                    
                        position: relative;
                    width: 70%;
                    top: -6px;
                    }
                    
                    .logo{
                          position: absolute;
                        top: 5px;
                        left: 0;
                        width: 90px;
                    
                    }
                    .title{
                    
                    padding: 0 20px 0 125px; 
                    position: relative;
                    display: inline-block;
                    width: 100%;
                    vertical-align: middle;
                    -webkit-box-sizing: border-box;
                    box-sizing: border-box;
                    }
                    
                    .title-info{
                    
                    
                    position: relative;
                    display: inline-block;
                    width: 100%;
                    vertical-align: middle;
                    <!--margin: 4px 0 0;-->
                    -webkit-box-sizing: border-box;
                    box-sizing: border-box;
                    margin-right: 6px;
                    padding-right: 11px;
                    border-right: 1px solid #e4e4e4;
                    
                    }
                    

                }

                .body{
                    margin-top: 50px;
                    margin-bottom: 20px;
                <!--background: transperant;-->
                    background-color: #f7f5ec;
                font-weight:400;
                color: #848484;
                }


                .info{


                  margin-left: -15px;
                    margin-right: -15px;

                    <!--margin-left: -15px;-->
                    <!--margin-right: -15px;-->
                }


                .t-1{

                <!--padding-left: 60px;-->
                <!--    padding-right: 60px;-->
                  position: relative;
                    float: left;
                    <!--width: 66.66667%;-->
                    min-height: 1px;
                    padding-left: 15px;
                    padding-right: 15px;
                    <!---webkit-box-sizing: border-box;-->
                    box-sizing: border-box;

                }

                .ispan{
                display: inline-block;
                    width: 25%;
                    margin-right:10px;
                }

                .content{
                    width: 75%;
                    padding-bottom: 28px;
                    border-bottom: 1px solid #f5f5f5;
                    margin-left: 105px;
                    margin-top: -50px;
                }

                eltdf-ls-title-holder{
                display:inline-block;
                margin-left:20px;
                width:25%
                }

                eltdf-ls-tags-items
                {
                display:inline-block;
                margin-left:20px;
                <!--margin-top:10px;-->
                }


                .half-section{

                position: relative;
                    float: left;
                    width: 66.66677%;
                    min-height: 1px;
                    padding-left: 40px;
                    padding-right: 40px;
                    -webkit-box-sizing: border-box;
                    box-sizing: border-box;
                    }
                    
                .half-section-2{

                position: relative;
                    float: left;
                    width: 33.33333%;
                    min-height: 1px;
                    padding-left: 30px;
                    padding-right: 40px;
                    -webkit-box-sizing: border-box;
                    box-sizing: border-box;
                    box-shadow: 0 5px 35px 0 rgba(158, 127, 103, .12);
                    }

                  .locations{
                    
                    
                        display: inline-block;
                        margin-left: 20px;
                        
                        
                    a{
                        border: 1px solid rgb(253, 206, 77);
                        padding: 4px 10px 2px;
                        margin-right: 5px;
                        color: rgb(253, 206, 77);
                        }
                        
                        
                  }
                  
                  .spec{
                  
                    margin-left:345px;
                    margin-top:-65px;
                  }
                  
                  .flex{
                  display:flex;
                  }
                  
                  .img-2{
                  
                    width: 250px;
                    height: 140px;
                    margin-top: 5px;
                    margin-right: 15px;
                  
                  }
          
        
                      
                      .tags{
                        color:#848484;
                        font-weight:400;
                        margin-left:160px;
                      }
                      
                      .bar-tags{
                        color:#848484;
                        font-weight:400;
                        margin-left:100px;
                      }
                      
                      
                      
                      
                      .about {
                      
                      margin-left:20px;
                      position: relative;
                      display: flex;
                      }
                      
                      
                      @media only screen and (max-width: 1024px) {
                    .half-section{

                    position: relative;
                        float: left;
                        min-height: 1px;
                        padding-left: 40px;
                        padding-right: 0px;
                        -webkit-box-sizing: border-box;
                        width:90%
                        }
                    }
                      
                        @media only screen and (max-width: 1024px) {
                      .header .list{
                        
                        
                        position: relative;
                      
                        margin-left: 8px;
                        
                        }
                    }

                      @media only screen and (max-width: 1024px) {
                        .header .left-list{
                        
                            position: relative;
                        width: 87%;
                        top: -6px;
                        }
                    }

                      @media only screen and (max-width: 1024px) {
                      
                      .header .title{
                        
                        padding: 0 20px 0 65px; 
                        position: relative;
                        display: inline-block;
                        width: 100%;
                        vertical-align: middle;
                        -webkit-box-sizing: border-box;
                        box-sizing: border-box;
                        }
                    }

                      @media only screen and (max-width: 1024px) {
                        .tags{
                        color:#848484;
                        font-weight:400;
                        <!--margin-left:103px;-->
                      }
                    }

                      @media only screen and (max-width: 1024px) {
                        .ispan{
                    display: inline-block;
                        width: 80%;
                        margin-right:10px;
                    }
                    }
                      
                      
                      @media only screen and (max-width: 1024px) {
                      .header .logo{
                              position: absolute;
                            top: 10px;
                            left: -20px;
                            width: 90px;
                        
                        }
                      
                    }
                      
                      
                      
                      
                      
                      
                      @media only screen and (max-width: 1024px) {
                      .half-section-2{

                    position: relative;
                        float: left;
                        min-height: 1px;
                        padding-left: 15px;
                        padding-right: 15px;
                        width:135%;
                        -webkit-box-sizing: border-box;
                        box-sizing: border-box;
                        box-shadow: 0 5px 35px 0 rgba(158, 127, 103, .12);
                        }
                    }
                      
                      
                      @media only screen and (max-width: 1024px) {
                        .about{
                        <!--color:#848484;-->
                        <!--font-weight:400;-->
                        margin-left:30px;
                        margin-top:36px;
                        <!--padding:5px;-->
                      }
                    }

                    @media only screen and (max-width: 1024px) {
                        .bar-tags{
                        color:#848484;
                        font-weight:400;
                        margin-left:85px;
                      }
                    }
          
          
                        @media only screen and (max-width: 1024px) {
                        .tags{
                        color:#848484;
                        font-weight:400;
                        margin-left:103px;
                      }
                    }
                      
                        @media only screen and (max-width: 1024px) {
                        .spec{
                              margin-left: 25px;
                            margin-top: -30px;
                        }
                      }
                    

                    @media only screen and (max-width: 1024px) {
                      
                      .body {
                        margin-top: 40px;
                        margin-left: 68px;
                    }
                      
                    }

                    @media only screen and (max-width: 1024px) {
                        .name{
                      
                              margin-top: 10px;
                            margin-left: 55px;
                      }
                    }

                    @media only screen and (max-width: 1024px) {
                        .img{
                      
                            margin-right: 12px;
                            padding-top: 14px;
                            padding-left: 50px;
                            margin-left: 0px;
                            padding-bottom: 3px;
                        height:110px;
                      }
                    }
                      
                    @media only screen and (max-width: 1024px) {
                        .eltdf-listing-single-holder .eltdf-ls-title-area-wrapper>.eltdf-grid {
                            padding: 32px 0;
                        }
                    }




                    @media only screen and (max-width: 480px) {
                        .eltdf-container-inner, .eltdf-grid, .eltdf-row-grid-section {
                            width: 300px;
                        }
                    }



                    @media only screen and (max-width: 1366px) {
                        .single-listing-item .eltdf-grid-huge-gutter {
                            margin-left: -25px;
                            margin-right: -25px;
                        }
                    }

                    @media only screen and (max-width: 1440px) {
                        .single-listing-item .eltdf-grid-huge-gutter>div {
                            padding: 0 25px;
                        }
                    }

                    @media only screen and (max-width: 1024px) {
                        .eltdf-grid-col-8 {
                            width: 100%;
                            float: none;
                        }
                    }

                    @media (max-width: 1200px) {
                        h1, .h1 {
                            font-size: calc(1.5125rem + 3.15vw);
                            margin-left: 62px;
                        }
                    }

          </style>
                
              <t t-set="record_id" t-value="request.params.get('advocateid')"/>
              <t t-set="y" t-value="env['advocatedetails'].sudo().search([('id','=',int(record_id))])"/>
          <div class="page">  
            <div class="header">
                <div class="flex">
                <!--    <div class="col-md-4">-->
                <!-- <t t-if="x_profile_picture">-->
                <!--    <img t-att-src="x_profile_picture" style="margin-right: 20px;padding-top: 5px;padding-left: 210px;margin-left: 10px;padding-bottom:2px;"/>-->
                <!--</t>-->
                <!--<t t-else="">-->
                <div class="list">
                    <div class="left-list">
                        <div class="logo">
                           <!-- <img id="logo" src="https://findall.qodeinteractive.com/wp-content/uploads/2019/07/listing-1-title-img-1.png"  style="margin-right: 20px;padding-top: 5px;/* border-radius: 20%; */padding-left: 40px;margin-left: 10px;padding-bottom:2px;"/> -->
                            <img t-attf-src="data:image/jpeg;base64,{{ y.x_profile_picture }}"

          style="    width: 90px;
            height: 90px;
            margin-top: 10px;"/>
                        </div>
                        <div class="title">
                            <h1 class="name"> Advocate : 
                            <t t-esc="y.x_first_name"/> <t t-esc="y.x_surname"/> 
                            </h1>

                        </div>
                        <div class="title-info">
                            <span style="" class="tags"><i class="fa fa-briefcase"></i> Specialization :
                             <t t-esc="y.x_specialization.name"/>  
                             </span>    
                            <!--<span style="" class="bar-tags"><i class="fa fa-tag"></i> Bar Association : <t t-esc="y.x_bar_association"/> </span>-->
                            <span style="" class="bar-tags"><i class="fa fa-file"></i> License Number : 
                            <t t-esc="y.x_license_number"/> 
                            </span>
                            <span style="" class="bar-tags">
                                <i class="fa fa-clock-o"></i> <strong> Expeirence: </strong><t t-esc="y.x_experience"/>years
                                </span>
                        </div>
                    </div>
                  </div>

        </div>
        <!--</p>-->
        <!--</div>-->
        </div>
          
                

        <div class="body" style="">
            <!--<div class="row" style="margin-top:45px;margin-left:172px;">-->
            <div class="info">
            <div class="t-1" style="padding-left:40px;padding-right:40px;padding-bottom:30px;">
            <div class="row" >
            <div class="half-section">
                <!--<div class="row">     -->
                    <div class="ispan">
                        <!--<div class="eltdf-ls-title-holder">-->
        <i class="eltdf-icon-ion-icon ion-ios-information-outline "></i> 
        <i class="fa fa-briefcase" style="margin-top: 8px;"></i>
        <!--<i class="fa fa-info"  style="margin-top:5px;"></i>-->
        <h4 class="eltdf-ls-parts-title " style="margin-top: 10px;display: inline-block;margin-left: 5px;">About</h4> </div>
            <!--        <span style="" class="about"><i class="fa fa-info"  style="margin-top:5px;"></i> <h4 style="margin-left:10px;  font-weight:500;-->
            <!--color:black;">About </h4></span>-->
                    
            <!--    </div>-->
            <!--<-->
                <div class="content">
                  <p>  </p>
                  <p>  </p>
            
                  <p>
                      <!--With a Juris Doctor (JD) from Harvard Law School, honored with the prestigious "Legal Eagle Award" for excellence in advocacy, recognized by Chambers and Partners as a leading litigation attorney, specializing in civil rights cases in federal and appellate courts nationwide.-->
                    Possessing a 
                    <t t-esc="y.x_education"/>
                     from Harvard Law School, celebrated with the esteemed "
                     <t t-esc="y.x_awards"/>
                     " for exceptional advocacy, acknowledged by
                      <t t-esc="y.x_court_type.name"/> 
                      as a top litigator, specializing in federal and appellate civil rights cases.
                  
                  </p>
    
                </div> 
          </div>
          <div class="half-section-2">
                
                
                
                <div class="contact">
        <div class="eltdf-ls-location-contact-info">
            
            <div class="logo">
                            <!--<img src="https://findall.qodeinteractive.com/wp-content/uploads/2019/07/h1-custom-icon-10.png"  class="img" style="width:100px;height:100px;"/>-->
                            <!--<img src="https://www.discover-india.in/img/map-image.png"  class="img-2" style=" "/>-->
            </div>

              <h4 class="eltdf-ls-parts-title " style="margin-top:5px;">Contact info</h4> <div class="eltdf-ls-notice">

              <div class="eltdf-ls-location-cid-holder">
              <p class="eltdf-ls-location-phone">
              <span aria-hidden="true" class="eltdf-icon-font-elegant icon_mobile "></span> <i class="fa fa-mobile"></i> <a itemprop="url" class="eltdf-ls-location-phone-link" href="tel:0052366852"> Phone: 
              <t t-esc="y.x_mobile_number"/>
              </a>
              </p>
              <p class="eltdf-ls-location-email">
              <span aria-hidden="true" class="eltdf-icon-font-elegant icon_mail_alt "></span><i class="fa fa-envelope"></i> <a itemprop="url" class="eltdf-ls-location-email-link" href="mailto: <EMAIL>">  Email:
               <t t-esc="y.x_email"/>
               </a>
              </p>

              <p class="eltdf-ls-location-email">
              <span aria-hidden="true" class="eltdf-icon-font-elegant icon_mail_alt "></span><i class="fa fa-home"></i> 
              <t t-esc="y.x_landmark"/>, <t t-esc="y.x_street"/>, <t t-esc="y.x_taluka.name"/>, <t t-esc="y.x_district.name"/>, <t t-esc="y.x_state.name"/>
              </p>
              </div>
              <!--<button type="submit" class="eltdf-btn eltdf-btn-huge eltdf-btn-solid eltdf-btn-icon eltdf-enquiry-opener"> <span class="eltdf-btn-text">Contact Business</span> <i class="eltdf-icon-ion-icon icon ion-android-person "></i> </button> <div class="eltdf-ls-business-hours">-->

              </div>
              </div>
        <!--</div>-->
        </div>
        </div>

        </div>
        <!--<div class="row">-->
            
        <!--<div class="eltdf-ls-tags" style="margin-left:12px;margin-top:10px;">-->
        <!--<div class="eltdf-ls-title-holder" style="display:inline-block;">-->
        <!--<i class="fa fa-map "></i> <h4 class="eltdf-ls-parts-title " style="display: inline-block;">Locations</h4> </div>-->
        <!--<div class="eltdf-ls-tags-items">-->
        
        <!--</div>-->
        <!--</div>-->


            </div>


            <!--</div>-->
            </div>
          </div>
        </div>
           
           </div>
       </t>

        
   </template>
</odoo>