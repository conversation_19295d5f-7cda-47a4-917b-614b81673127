<?xml version="1.0" encoding="utf-8"?>
<odoo>
   <template id="ai_advocate.website_advocate_listing" name="Advocate Listing">
       <t t-call="website.layout">
         

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.2.23/angular.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.1/js/select2.full.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.1/css/select2.min.css" />

<style>

                body {
                font-family: tahoma;
                height: 100vh;
                background-size: cover;
                background-position: center;
                display: flex;
                <!--align-items: center;-->
                }

                .name {
                        margin-top: -40px; /* Adjust the margin as needed to move the text upward */
                    }
                    
                .our-team-wrapper {
                border-radius:16px; /* Adding border radius to the wrapper */
                overflow: hidden; /* Ensure the border radius doesn't affect the inner content */
                }
                .our-team {
                padding: 30px 0 40px;
                margin-bottom: 30px;
                background-color: #f7f5ec;
                text-align: center;
                overflow: hidden;
                position: relative;
                border-radius:16px;
                height:400px;
                <!--border-bottom: 2px solid transparent; /* Ensure that the border doesn't affect the layout */-->
                <!--border-image: linear-gradient(151deg, rgba(240,167,141,1) 0%, rgba(249,197,122,0.9635562843301383) 100%);-->
                <!--border-image-slice: 1; /* Ensure that the gradient covers the entire border */-->
                }
                .our-team .picture {
                display: inline-block;
                height: 130px;
                width: 130px;
                margin-bottom: 50px;
                z-index: 1;
                position: relative;
                }

                .our-team .picture::before {
                content: "";
                width: 100%;
                height: 0;
                border-radius: 50%;
                <!--background: linear-gradient(135deg, rgba(178,232,241,1) 15%, rgba(236,236,226,1) 30%, rgba(240,238,233,1) 44%, rgba(237,217,185,1) 70%, rgba(232,198,217,1) 88%, rgba(246,165,222,1) 100%);-->
                <!--background: linear-gradient(151deg, rgba(240,167,141,1) 0%, rgba(249,197,122,0.9635562843301383) 100%);-->
                position: absolute;
                bottom: 135%;
                right: 0;
                left: 0;
                opacity: 0.9;
                transform: scale(3);
                transition: all 0.3s linear 0s;
                }

                .our-team:hover .picture::before {
                width:100%;
                height: 100%;
                }

                .our-team .picture::after {
                content: "";
                width: 100%;
                height: 100%;
                border-radius: 50%;
                <!--background: linear-gradient(135deg, rgba(178,232,241,1) 15%, rgba(236,236,226,1) 30%, rgba(240,238,233,1) 44%, rgba(237,217,185,1) 70%, rgba(232,198,217,1) 88%, rgba(246,165,222,1) 100%);-->
                background: linear-gradient(151deg, rgba(240,167,141,1) 0%, rgba(249,197,122,0.9635562843301383) 100%);
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
                
                }

                .our-team .picture img {
                width: 170px;
                height: auto;
                border-radius: 50%;
                transform: scale(1);
                transition: all 0.9s ease 0s;
                }

                .our-team:hover .picture img {
                box-shadow: 0 0 0 14px #f7f5ec;
                transform: scale(0.75);
                }

                .our-team .title {
                display: block;
                font-size: 15px;
                color: #4e5052;
                text-transform: capitalize;
                }

                .our-team .social {
                width: 100%;
                padding: 0;
                margin: 0;
                <!--background: linear-gradient(135deg, rgba(178,232,241,1) 15%, rgba(236,236,226,1) 30%, rgba(240,238,233,1) 44%, rgba(237,217,185,1) 70%, rgba(232,198,217,1) 88%, rgba(246,165,222,1) 100%);-->
                background: linear-gradient(151deg, rgba(240,167,141,1) 0%, rgba(249,197,122,0.9635562843301383) 100%);
                position: absolute;
                bottom: -100px;
                left: 0;
                transition: all 0.5s ease 0s;
                }

                .our-team .social {
                bottom: 0;
                }

                .our-team .social li {
                display: inline-block;
                }

                .our-team .social li a {
                display: block;
                padding: 10px;
                font-size: 17px;
                color:#222;
                transition: all 0.3s ease 0s;
                text-decoration: none;
                }

                .our-team .social li a:hover {
                color: #fff;
                
                }
                .icon-container {
                    display: flex;
                    <!--align-items:start; /* Align items vertically */-->
                    justify-content: center; /* Vertically center the content */
                    align-items:start; /* Horizontally center the content */
                }

                .icon-container img {
                    width: 30px; /* Adjust width as needed */
                    height:30px; /* Maintain aspect ratio */
                    <!--margin-right: 5px; /* Adjust spacing between icons and text */-->
                    <!--margin-left: 35px;-->
                    margin-right: 5px;
                    margin-bottom: 10px;
                    margin-left: 20px;
                }

                .icon-container span {
                    margin-right: 10px;
                }


                    @media only screen and (max-width: 1024px) {
                    .col-4{
                    width:100%;
                    max-width:100%;
                    padding-left:15px;
                    padding-right:15px;
                }
                }
</style>
      
    <div id="wrap" class="oe_structure oe_empty"  ng-app="myApp" ng-controller="SearchController">
      
      <section class="s_searchbar bg-200 pt48 pb48 o_colored_level" data-snippet="s_searchbar" data-name="Search" style="background-image: none;">
          <div class="container-fluid">
             <form action="/website/form/" method="post" enctype="multipart/form-data" class="o_mark_required container" data-mark="*" data-pre-fill="true" data-success-mode="redirect" data-success-page="/contactus-thank-you" data-model_name="mail.mail">
              <div class="s_website_form_rows row s_col_no_bgcolor">
            

                    <div data-visibility-condition="" data-visibility-between="" class="form-group s_website_form_field col-12 s_website_form_custom col-lg-2" data-type="char" data-name="Field">
                        <label class="s_website_form_label" style="width: 200px" for="ou0wr89q4z1">
                        <span class="s_website_form_label_content"></span>
                        </label>
                        <input type="text" class="form-control s_website_form_input" ng-model="x_first_name"   name="Your Name" placeholder="Enter Name" id="ou0wr89q4z1" data-fill-with="name" style="height:30px !important"/>
                    </div>
                    <div data-visibility-condition="" data-visibility-between="" class="form-group s_website_form_field col-12 col-lg-2 s_website_form_custom" data-type="many2one" data-name="Field">
                        

                        <label class="s_website_form_label " style="width: 200px" for="o4rhgmabto9">
                        <span class="s_website_form_label_content"/>
                        </label>
                            
                            <select class="form-control s_website_form_input" name="" id="o4rhgmabto9"  placeholder="Search By Taluka" ng-model="x_taluka" style="">
                            <!--<option value="taluka" selected="selected">Taluka</option>-->
                            <!--<option value="Deesa">Deesa</option>-->
                            <!--<option value="abc">abc</option>-->
                            <!--<option value="dd">dd</option>-->
                            <t t-set="advocatedetails" t-value="env['location.taluka'].sudo().search([])"/>

                            <t t-foreach="advocatedetails" t-as="a"> 
                            <option t-att-value="a.name" > <t t-esc="a.name"/>  </option>
                            </t>
                            </select>
                            
                            <!--</t>-->
                    </div>
                        <div data-visibility-condition="" data-visibility-between="" class="form-group s_website_form_field col-12 col-lg-2 s_website_form_custom" data-type="many2one" data-name="Field">
                            <label class="s_website_form_label " style="width: 200px" for="o4rhgmabto9">
                            <span class="s_website_form_label_content"/>
                            </label>
                                <t t-set="advocatedetails" t-value="env['courttype'].sudo().search([])"/>
                                <select class="form-control s_website_form_input" name="courtType" ng-model="x_court_type" id="courtType">
                                    <!-- <option value="">Select Court Type</option> -->
                                    
                                    <t t-foreach="advocatedetails" t-as="a">
                                        
                                        <!--<option value="<t t-esc="a.id"/>"> <t t-esc="a.x_name"/></option>-->
                                    <option t-att-value="a.name">
                                        <!--<t t-esc="a.id"/>-->
                                    <t t-esc="a.name"/></option>
                                                <!--<option value="{{ a.x_court_type}}">{{ a.x_court_type}}</option>-->

                                    </t>
                                </select>
                        </div>
                <div data-visibility-condition="" data-visibility-between="" class="form-group s_website_form_field col-12 col-lg-2 s_website_form_custom" data-type="many2one" data-name="Field">
                    <label class="s_website_form_label " style="width: 200px" for="o4rhgmabto9">
                    <span class="s_website_form_label_content"/>
                    </label>
                    <select class="form-control s_website_form_input" ng-model="x_specialization" placeholder="Search By Specialization"  name="" id="o4rhgmabto9" style="">
                    <!--<option value="Specializaion" selected="selected">Specializaion</option>-->
                    <!--<option value="Bail">Bail</option>-->
                    <!--<option value="Fraud">Fraud</option>-->
                    <!--<option value="Fraud">Criminal</option>-->
                    
                    <t t-set="advocatedetails" t-value="env['specialization'].sudo().search([])"/>

                                    <t t-foreach="advocatedetails" t-as="a"> 
                                    <option t-att-value="a.name"><t t-esc="a.name"/> </option>
                                    </t>
                    </select>
                </div>
        
         
                <div class="form-group d-flex align-items-center col-12 s_website_form_submit col-lg-2 text-center s_website_form_no_submit_label" data-name="Submit Button" data-original-title="" title="" aria-describedby="tooltip196811">
                    <div style="width: 200px;" class="s_website_form_label"/>
                    <a href="#" role="button"  ng-click="search(1)"  class="btn btn-primary d-flex align-items-center s_website_form_send" data-original-title="" title="" aria-describedby="popover251623" style="margin-top: 18px;">Search</a>
                    <span id="s_website_form_result"/>
                </div>
              </div>
          </form>
        </div>

      
    
    <!--<div class="container" style="margin-top:20px;" ng-repeat="record in allData">-->
            <div class="container" style="margin-top:20px;" >
            <!--<div ng-repeat="advocate in ctrl.filteredAdvocates">-->
            <!--<div ng-repeat="record in allData">-->
            <!--<t t-set="advocatedetails" t-value="env['advocatedetails'].sudo().search([])"/>-->
            
            <!--<h3>{{ advocate.name }}</h3>-->
            <!--      <p>Court Type: {{ advocate.courtType }}</p>-->
            <!--      <p>Taluka: {{ advocate.taluka }}</p>-->
            <div class="row">
                <!--<div class="cardsgap">-->
                <!--<t t-foreach="advocatedetails" t-as="a">-->
                <!--<div class="col-12 col-sm-6 col-md-6 col-lg-4">-->
                <!--<div class="col-4 " ng-repeat="record in allData">-->
                <div class="col-12 col-sm-6 col-md-6 col-lg-4" ng-repeat="record in allData">
                    <!--{{record.id}}-->
                    
                    <div class="our-team-wrapper">
                                    <!--<a t-attf-href="/ui-advocate-details?advocateid={{ record.id }}">-->
                                    <!--<a t-attf-href="/ui-advocate-details?advocateid={{ record.id }}">-->
                        <a ng-href="/advocate-profile?advocateid={{ record.id }}">
                        <div class="our-team">
                        <div class="picture">
                        <img class="img-fluid" src="https://picsum.photos/130/130?image=1027"/>
                        <!--<t t-esc="a.x_profile_picture"/>-->
                        </div>
                        <!--<div class="team-content">-->
                        <!--  <h3 class="name">Michele Miller</h3>-->
                        <!--  <h4 class="title">Web Developer</h4>-->
                        <!--</div>-->
                        <div class="team-content">
                        <!--<h5 class="name">Michele Miller</h5>-->
                        <h5 class="name">
                            <strong>
                                {{record.x_first_name}}
                            <!--<t t-esc="a.x_first_name"/>-->
                            <!--<t t-esc="a.x_surname"/>-->
                            </strong>
                        </h5>
                        <div class="icon-container">
                            <img src="https://cdn-icons-png.flaticon.com/128/15763/15763670.png" alt="Icon 1"/>
                            <span style=" font-size:15px;margin-top: 5px;">  {{record.x_expeirence}} Years</span>
                            <img src="https://cdn-icons-png.flaticon.com/128/3425/3425077.png" alt="Icon 2"/>
                                {{record.x_taluka[0].name}}
                            <!--<span style=" font-size:15px;margin-top: 5px;"><t t-esc="a.x_taluka.name"/>  , <t t-esc="a.x_state.name"/></span>-->
                            <!--<span style=" font-size:15px;margin-top: 5px;"><t t-esc="a.x_taluka.name"/>  , <t t-esc="a.x_state.name"/></span>-->
                        </div>
                        <div class="icon-container">
                            <img src="https://cdn-icons-png.flaticon.com/128/8728/8728601.png" alt="Icon 3"/>
                            <!--<span style=" font-size:15px;margin-top: 5px;">Bail, Cheating &amp; Fraud, Adoption</span>-->
                            <span style=" font-size:15px;margin-top: 5px;">
                            <!--<t t-esc="a.x_specialization.name"/>-->
                            {{record.x_specialization[0].name}}
                            </span>
                        </div>
                        <div class="icon-container">
                            <img src="https://cdn-icons-png.freepik.com/256/5380/5380552.png?ga=GA1.1.505986701.1707730991&amp;semt=ais_hybrid" alt="Icon 3"/>
                            <!--<span style=" font-size:15px;margin-top: 5px;">Supreme Court &amp; High Court</span>-->
                            <span style=" font-size:15px;margin-top: 5px;">
                                {{record.x_court_type[0].name}}
                            <!--<t t-esc="a.x_court_type.x_name"/>-->
                            </span>
                        </div>
                        <!--<div class="button">-->
                        <!--  <a class="btn-1" href="#">-->
                        <!--    <span>View More Details!</span>-->
                        <!--  </a>-->
                        <!--</div>-->
                        </div>
                        <ul class="social">
                        <li>
                            <a href="https://cdn-icons-png.flaticon.com/128/1077/1077041.png" class="fa fa-facebook" aria-hidden="true"/>
                        </li>
                        <li>
                            <a href="https://cdn-icons-png.flaticon.com/128/160/160194.png" class="fa fa-twitter" aria-hidden="true"/>
                        </li>
                        <li>
                            <a href="https://cdn-icons-png.flaticon.com/128/220/220381.png" class="fa fa-google-plus" aria-hidden="true"/>
                        </li>
                        <li>
                            <a href="https://cdn-icons-png.flaticon.com/128/160/160168.png" class="fa fa-linkedin" aria-hidden="true"/>
                        </li>
                        </ul>
                    </div>
                    </a>
                    <!--</t>-->
                    <!--</div>-->
                    <!--</t>-->
                </div>
                <!--</t>-->
                </div>
            <!--</div>-->
            </div >
            </div>
            
    
    
    
                <script>
                  angular.module('myApp', []).controller('SearchController', function($scope, $http) {
                    
                    
                      // Dropdown options
                      // $scope.dropdownOptions = {'person' : 'Person Text', 'company' : 'Company Text'};
                      $scope.domainFilter = [];
                      $scope.allData = [];
                      
                      <!-- console.log($scope.allData) -->
                      $scope.currentPage = 1;
                      $scope.batch_size = 10;
                      $scope.current_total_records = 0;
                      $scope.total_pages = 1;
                      
                      
                      
                      
                      // Model name
                      var modelName = 'advocatedetails';
                                              <!-- alert(modelName) -->

                  
                       
                       <t t-if="x_first_name">
                            $scope.x_first_name = <t t-esc="x_first_name"/>;
                        </t>
                        
                        <!-- alert($scope.x_first_name) -->
                        <!--alert($scope.x_court_type)-->
                        <!--alert($scope.x_taluka)-->
                       <t t-if="x_specialization">
                            $scope.x_specialization[0] = <t t-esc="x_specialization.name"/>;
                        </t>
                        
                        <t t-if="x_court_type">
                            $scope.x_court_type[0] = <t t-esc="x_court_type.name"/>;
                        </t>
                        
                        <t t-if="x_taluka">
                            $scope.x_taluka[0] = <t t-esc="x_taluka.name"/>;
                        </t>
                        
                        <!-- <t t-if="x_taluka">-->
                        <!--    $scope.x_taluka[0] = "abc";-->
                        <!--</t>-->
                        
                    
                      
                      // Function to decode base64 data
                      $scope.decodeBase64 = function(base64Data) {
                          // Decode base64 data
                          var decodedData = atob(base64Data);
                          
                          // Return data URI for image
                          return 'data:image/jpg;base64,' + decodedData;
                      };
                       $scope.get_current_page_total_records = function() {
                        if ($scope.allData &amp;&amp; $scope.batch_size){
                          return Math.min($scope.batch_size, $scope.allData.length);
                        }
                        else{
                          return 20;
                        }
                      };
                      
                      // Function to perform search
                      $scope.search = function(is_manual = 0) {
                          // Construct domain filter
                          $scope.domainFilter = [];
                          <!--console.log($scope.x_name)-->
                          <!--console.log($scope.x_first_name)-->
                          <!--console.log($scope.x_specialization)-->
                          if ($scope.x_first_name) {
                              $scope.domainFilter.push(["x_first_name", "ilike", $scope.x_first_name]);
                          }
                            if ($scope.x_specialization) {
                              $scope.domainFilter.push(["x_specialization", "ilike", $scope.x_specialization]);
                          }
                           if ($scope.x_taluka) {
                              $scope.domainFilter.push(["x_taluka", "ilike", $scope.x_taluka]);
                          }
                           if ($scope.x_court_type) {
                              $scope.domainFilter.push(["x_court_type", "ilike", $scope.x_court_type]);
                          }
                          // if ($scope.company_type &amp;&amp; $scope.company_type != 'Select') {
                          //    $scope.domainFilter.push(["company_type", "=", $scope.company_type]); // Replace "field_name" with actual field name
                          // }
                          if(is_manual == 1){
                            $scope.currentPage = 1;
                          }
                          // Prepare payload
                          var payload = {
                              data: {
                                  domain_filter: JSON.stringify($scope.domainFilter),
                                  args: {
                                      order: "create_date DESC"
                                  }
                              }
                          };
                  
                          <!-- $http.post('/api/v1_domain/' + modelName + ) -->
                           <!-- $http.post('/api/v1_domain/' + modelName ,payload ) -->
                           $http.post('/api/v1_domain/advocatedetails',payload )
                          .then(function(response) {
                              // Handle response
                              <!--console.log('Response:', response.data);-->
                              $scope.allData = response.data.result.records;
                              console.log($scope.allData)
                              $scope.total_records = response.data.result.total_records;
                              $scope.current_total_records = $scope.get_current_page_total_records();
                              $scope.total_pages = Math.ceil($scope.total_records / $scope.batch_size);
                          })
                          .catch(function(error) {
                              // Handle error
                              console.error('Error:', error);
                          });
                          // POST data to ERP controller
                      };
                      
                      $scope.search();
                  });
                  
                  
                  $(function () {
                    setTimeout(function () {
                    $("select").select2();
                    }, 100);
                    });
                </script>
                      
             
        </section>         
      </div>
    
     <script>
                function openPage(id) {
                    <!--window.location.href = 'https://oneclickvakil.com/advocatedemo?id=' + id;-->
                    window.location.href = 'https://oneclickvakil.com/ui-advocate-details?id=' + id;
                }
            </script>
       </t>

        
   </template>
</odoo>