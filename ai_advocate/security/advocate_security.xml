<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record model="ir.module.category" id="module_category_advocate">
            <field name="name">Advocate</field>
            <field name="description">User access levels for Advocate module</field>
            <field name="sequence">10</field>
        </record>

        <record id="group_advocate_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_category_advocate"/>
        </record>
       
        <record id="group_advocate_admin" model="res.groups">
            <field name="name">Administrator</field>
            <field name="category_id" ref="module_category_advocate"/>
            <field name="implied_ids" eval="[(4, ref('group_advocate_user'))]"/>
        </record>
    </data>
</odoo>
