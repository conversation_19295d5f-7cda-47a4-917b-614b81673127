<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <!-- Record rule for Advocate Administrators: see all records within the same company -->
        <record id="ir_rule_advocate_admin_rule" model="ir.rule">
            <field name="name">Administrator: See all records in company</field>
            <field name="model_id" ref="model_advocatedetails"/>
            <field name="global" eval="False"/>
            <field name="domain_force">[('company_id', '=', user.company_id.id)]</field>
            <field name="groups" eval="[(4, ref('ai_advocate.group_advocate_admin'))]"/>
        </record>

        <!-- Record rule for Advocate Users: see only own records within the same company -->
        <record id="ir_rule_advocate_user_rule" model="ir.rule">
            <field name="name">User: See own records in company</field>
            <field name="model_id" ref="model_advocatedetails"/>
            <field name="global" eval="False"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('ai_advocate.group_advocate_user'))]"/>
        </record>
    </data>
</odoo>
