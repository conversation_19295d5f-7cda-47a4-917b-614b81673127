# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* advocates
#
msgid ""
msgstr "
"Project-Id-Version: Odoo Server 15.0-20231018\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-26 07:42+0000\n"
"PO-Revision-Date: 2023-12-26 07:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__address
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Address"
msgstr "સરનામું"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__age
msgid "Age"
msgstr "ઉંમર"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__awards
msgid "Awards and Recognitions"
msgstr "પુરસ્કારો અને અભિજ્ઞાન "

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__bar_association
msgid "Bar Association"
msgstr "બાર એસોસિએશન"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__basic_info_link
msgid "Basic Info Link"
msgstr "મૂળભૂત માહિતી લિંક"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__blogs_info
msgid "Blogs and Information"
msgstr "બ્લોગ્સ અને માહિતી"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__building_no_name
msgid "Building No - Name"
msgstr "બિલ્ડિંગ નંબર - નામ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__name
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Client Name"
msgstr "ગ્રાહકનું નામ"

#. module: advocates
#: model:ir.ui.menu,name:advocates.menu_clients
msgid "Clients"
msgstr "ગ્રાહકો"

#. module: advocates
#: model:ir.actions.act_window,name:advocates.action_client
msgid "Clients Details"
msgstr "ગ્રાહકોની વિગતો"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__create_uid
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__create_uid
#: model:ir.model.fields,field_description:advocates.field_specialization__create_uid
#: model:ir.model.fields,field_description:advocates.field_staffdetails__create_uid
msgid "Created by"
msgstr "ના દ્વારા બનેલુ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__create_date
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__create_date
#: model:ir.model.fields,field_description:advocates.field_specialization__create_date
#: model:ir.model.fields,field_description:advocates.field_staffdetails__create_date
msgid "Created on"
msgstr "ના રોજ બનાવ્યું"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__date_of_birth
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Date of Birth"
msgstr "જન્મ તારીખ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__department
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Department"
msgstr "વિભાગ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__display_name
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__display_name
#: model:ir.model.fields,field_description:advocates.field_specialization__display_name
#: model:ir.model.fields,field_description:advocates.field_staffdetails__display_name
msgid "Display Name"
msgstr "પ્રદર્શન નામ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__district
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__district
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "District"
msgstr "જીલ્લો"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__education
#: model:ir.model.fields,field_description:advocates.field_staffdetails__education
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Education"
msgstr "શિક્ષણ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__email
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__email
#: model:ir.model.fields,field_description:advocates.field_staffdetails__email
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Email"
msgstr "ઇમેઇલ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__experience
#: model:ir.model.fields,field_description:advocates.field_staffdetails__experience
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Experience (in years)"
msgstr "અનુભવ (વર્ષોમાં)"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__father_name
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__father_name
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Father Name/ Husband Name"
msgstr "પિતાનું નામ/ પતિનું નામ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__first_name
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__first_name
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "First Name"
msgstr "પ્રથમ નામ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__name
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Full Name"
msgstr "પૂરું નામ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__id
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__id
#: model:ir.model.fields,field_description:advocates.field_specialization__id
#: model:ir.model.fields,field_description:advocates.field_staffdetails__id
msgid "ID"
msgstr "આઈડી"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__join_date
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Join Date"
msgstr "જોડાવાની તારીખ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__landmark
msgid "Landmark"
msgstr સીમાચિહ્ન"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails____last_update
#: model:ir.model.fields,field_description:advocates.field_advocatedetails____last_update
#: model:ir.model.fields,field_description:advocates.field_specialization____last_update
#: model:ir.model.fields,field_description:advocates.field_staffdetails____last_update
msgid "Last Modified on"
msgstr "પર છેલ્લે સંશોધિત"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__write_uid
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__write_uid
#: model:ir.model.fields,field_description:advocates.field_specialization__write_uid
#: model:ir.model.fields,field_description:advocates.field_staffdetails__write_uid
msgid "Last Updated by"
msgstr "દ્વારા છેલ્લે અપડેટ કરાયેલ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__write_date
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__write_date
#: model:ir.model.fields,field_description:advocates.field_specialization__write_date
#: model:ir.model.fields,field_description:advocates.field_staffdetails__write_date
msgid "Last Updated on"
msgstr તેની પર છેલ્લે સુધારેલ:"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__advocate_id
msgid "Advocate"
msgstr "વકીલ"

#. module: advocates
#: model:ir.actions.act_window,name:advocates.action_advocate
#: model:ir.ui.menu,name:advocates.menu_advocate
msgid "Advocate Details"
msgstr "વકીલની વિગતો"

#. module: advocates
#: model:ir.ui.menu,name:advocates.menu_advocate_master
msgid "Advocate Master"
msgstr "વકીલ માસ્ટર"

#. module: advocates
#: model:ir.model,name:advocates.model_advocatedetails
msgid "Advocate Registration"
msgstr "વકીલ નોંધણી"

#. module: advocates
#: model_terms:ir.ui.view,arch_db:advocates.view_advocate_form
msgid "Advocates"
msgstr "વકીલો"

#. module: advocates
#: model:ir.model,name:advocates.model_clientdetails
msgid "Advocates Client Registration"
msgstr "વકીલો ક્લાયન્ટ નોંધણી"

#. module: advocates
#: model:ir.model,name:advocates.model_staffdetails
msgid "Advocates Staff Details"
msgstr "વકીલોના સ્ટાફની વિગતો"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__leave_date
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Leave Date"
msgstr "રજાની તારીખ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__license_number
msgid "License Number"
msgstr "લાઇસન્સ નંબર"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__mobile_number
msgid "Mobile Number"
msgstr "મોબાઈલ ફોન નંબર"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__name
#: model:ir.model.fields,field_description:advocates.field_specialization__name
#: model_terms:ir.ui.view,arch_db:advocates.view_specialization_form
msgid "Name"
msgstr "નામ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__password
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__password
msgid "Password"
msgstr "પાસવર્ડ"

#. module: advocates
#: model_terms:ir.ui.view,arch_db:advocates.view_advocate_form
msgid "Personal Information"
msgstr "અંગત જાણકારી"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__phone
#: model:ir.model.fields,field_description:advocates.field_staffdetails__phone
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Phone"
msgstr "ફોન"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__pincode
msgid "Pincode"
msgstr "પિનકોડ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_staffdetails__position
#: model_terms:ir.ui.view,arch_db:advocates.view_staff_form
msgid "Position"
msgstr "પોઝીશન"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__practice_district
msgid "Practice District"
msgstr "પ્રેક્ટિસ ડિસ્ટ્રિક્ટ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__practice_taluka
msgid "Practice Taluka"
msgstr "પ્રેક્ટિસ તાલુકા"

#. module: advocates
#: model_terms:ir.ui.view,arch_db:advocates.view_advocate_form
msgid "Professional Details"
msgstr "વ્યવસાયિક વિગતો"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__profile_picture
msgid "Profile Picture"
msgstr "પ્રોફાઇલ ચિત્ર"

#. module: advocates
#: model_terms:ir.ui.view,arch_db:advocates.view_advocate_form
msgid "Residential Information"
msgstr "રહેણાંક માહિતી"

#. module: advocates
#: model:ir.actions.act_window,name:advocates.action_specialization
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__specialization
#: model:ir.ui.menu,name:advocates.menu_specialization
msgid "Specialization"
msgstr "વિશેષતા"

#. module: advocates
#: model:ir.model,name:advocates.model_specialization
msgid "Specialization model to store various specializations of advocates"
msgstr "વકીલોની વિવિધ વિશેષતાઓને સંગ્રહિત કરવા માટે વિશેષતા મોડેલ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__staff
msgid "Staff"
msgstr "સ્ટાફ"

#. module: advocates
#: model:ir.actions.act_window,name:advocates.action_staff
#: model:ir.ui.menu,name:advocates.menu_staff
msgid "Staff Details"
msgstr "સ્ટાફની વિગતો"

#. module: advocates
#: model_terms:ir.ui.view,arch_db:advocates.view_advocate_form
msgid "Staff Management"
msgstr "સ્ટાફ મેનેજમેન્ટ"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__state
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__state
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "State"
msgstr "રાજ્ય"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__street
msgid "Street"
msgstr "શેરી"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__streetline_1
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Street line 1"
msgstr "શેરી લાઇન 1"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__streetline_2
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Street line 2"
msgstr "શેરી લાઇન 2"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__surname
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__surname
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Surname"
msgstr "અટક"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__taluka
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__taluka
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Taluka/City"
msgstr "તાલુકો/શહેર"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_clientdetails__village
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__village
#: model_terms:ir.ui.view,arch_db:advocates.view_client_form
msgid "Village/Area"
msgstr "ગામ/વિસ્તાર"

#. module: advocates
#: model:ir.model.fields,field_description:advocates.field_advocatedetails__whatsapp_number
msgid "Whatsapp Number"
msgstr "વોટ્સએપ નંબર"

