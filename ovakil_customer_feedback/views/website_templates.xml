<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Index Template -->
        <template id="index" name="Customer Feedback System Index">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <h1 class="mt-4">Customer Feedback System</h1>
                                <p class="lead">Complete customer feedback collection and management system with AI analysis and payment integration</p>

                                <div class="row mt-4">
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">Customer Feedback Form</h5>
                                                <p class="card-text">Main customer feedback collection form with rating and comments</p>
                                                <a href="/customer-feedback-main" class="btn btn-primary">Access Form</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Feedback Form Website Template -->
        <template id="customer_feedback_main_website_form" name="Customer Feedback Form Form">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Enhanced Form Styling -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"/>
                    <link rel="stylesheet" href="/ovakil_customer_feedback/static/src/css/enhanced_forms.css"/>
                    <style>
                        .step-form-container {
                            max-width: 900px;
                            margin: 0 auto;
                            padding: 20px;
                        }
                        .step-indicator {
                            display: flex;
                            justify-content: center;
                            margin-bottom: 30px;
                            position: relative;
                        }
                        .step-indicator::before {
                            content: '';
                            position: absolute;
                            top: 20px;
                            left: 25%;
                            right: 25%;
                            height: 2px;
                            background: #e9ecef;
                            z-index: 1;
                        }
                        .step {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            position: relative;
                            z-index: 2;
                            background: white;
                            padding: 0 15px;
                        }
                        .step-number {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            background: #e9ecef;
                            color: #6c757d;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                            margin-bottom: 8px;
                            transition: all 0.3s ease;
                        }
                        .step.active .step-number {
                            background: #007bff;
                            color: white;
                        }
                        .step.completed .step-number {
                            background: #28a745;
                            color: white;
                        }
                        .step-title {
                            font-size: 12px;
                            color: #6c757d;
                            text-align: center;
                            font-weight: 500;
                        }
                        .step.active .step-title {
                            color: #007bff;
                            font-weight: 600;
                        }
                        .form-section {
                            background: white;
                            border-radius: 12px;
                            padding: 30px;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            margin-bottom: 20px;
                        }
                        .form-section h3 {
                            color: #2c3e50;
                            font-weight: 600;
                            margin-bottom: 20px;
                            padding-bottom: 10px;
                            border-bottom: 2px solid #e9ecef;
                        }
                        .form-group {
                            margin-bottom: 20px;
                        }
                        .form-control {
                            border-radius: 8px;
                            border: 2px solid #e9ecef;
                            padding: 12px 15px;
                            transition: all 0.3s ease;
                        }
                        .form-control:focus {
                            border-color: #007bff;
                            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                        }
                        .btn-step {
                            padding: 12px 30px;
                            border-radius: 8px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                        }
                        .login-required-section {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            border-radius: 12px;
                            padding: 25px;
                            text-align: center;
                            margin: 20px 0;
                        }
                        .review-section {
                            background: #f8f9fa;
                            border-radius: 12px;
                            padding: 25px;
                            margin: 20px 0;
                        }
                        .review-item {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 10px 0;
                            border-bottom: 1px solid #e9ecef;
                        }
                        .review-item:last-child {
                            border-bottom: none;
                        }
                        .review-label {
                            font-weight: 600;
                            color: #495057;
                        }
                        .review-value {
                            color: #6c757d;
                        }
                        .faq-section {
                            background: white;
                            border-radius: 12px;
                            padding: 25px;
                            margin: 20px 0;
                        }
                        .faq-item {
                            border-bottom: 1px solid #e9ecef;
                            padding: 15px 0;
                        }
                        .faq-question {
                            font-weight: 600;
                            color: #2c3e50;
                            cursor: pointer;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }
                        .faq-answer {
                            color: #6c757d;
                            margin-top: 10px;
                            display: none;
                        }
                        .faq-answer.show {
                            display: block;
                        }
                    </style>
                </t>
                <div id="wrap" class="enhanced-form-wrapper">
                    <!-- Progress Steps -->
                    <div class="container">
                        <div class="form-progress-steps">
                            <div class="step-item active">
                                <div class="step-number">1</div>
                                <div class="step-label">Form Details</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-label">Review</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-label">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-lg-10 offset-lg-1">
                                <!-- Form Header Card -->
                                <div class="form-header-card">
                                    <div class="form-header-content">
                                        <h1 class="form-title">Customer Feedback System</h1>
                                        <p class="form-description">Share your valuable feedback and help us improve our services</p>
                                        <div class="form-meta">
                                            <span class="form-meta-item">
                                                <i class="fas fa-clock"></i>
                                                Estimated time: 5-10 minutes
                                            </span>
                                            <span class="form-meta-item">
                                                <i class="fas fa-shield-alt"></i>
                                                Secure &amp; Confidential
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Form Card -->
                                <div class="main-form-card">
                                    <form id="enhanced-form" action="/customer-feedback-main/submit" method="post" class="enhanced-form" novalidate="novalidate">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                        <!-- Basic Information Section -->
                                        <div class="form-section">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-user"></i>
                                                Basic Information
                                            </h3>

                                            <div class="nice-form-group">
                                                <label for="name" class="required">Name</label>
                                                <input type="text" class="form-control" name="name" required="required"
                                                       data-field-type="char" data-required="true" placeholder="Enter your full name"/>
                                                <div class="invalid-feedback">Please provide a valid name.</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="customer_name" class="required">Customer Name</label>
                                                <input type="text" class="form-control" name="customer_name" required="required" placeholder="Enter customer name"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid customer name.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="customer_email" class="required">Email Address</label>
                                                <input type="text" class="form-control" name="customer_email" required="required" placeholder="Enter email address"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                            </div>
                                        </div>

                                        <!-- Additional Details Section -->
                                        <div class="form-section">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-info-circle"></i>
                                                Additional Details
                                            </h3>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="overall_rating" class="required">Overall Rating</label>
                                                <select class="form-control" name="overall_rating" required="required"
                                                        data-field-type="selection" data-required="true">
                                                    <option value="">Select...</option>
                                                    <option value="1">1 Star</option>
                                                    <option value="2">2 Stars</option>
                                                    <option value="3">3 Stars</option>
                                                    <option value="4">4 Stars</option>
                                                    <option value="5">5 Stars</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="assigned_user" class="">Assigned User</label>
                                                <select class="form-control" name="assigned_user"
                                                        data-widget="many2one_dropdown" data-model="res.users"
                                                        data-field-type="many2one" data-required="false"
                                                        data-label="Assigned User">
                                                    <option value="">Select Assigned User...</option>
                                                    <t t-foreach="users" t-as="user">
                                                        <option t-att-value="user.id"><t t-esc="user.name"/></option>
                                                    </t>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid assigned user.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="priority" class="">Priority Level</label>
                                                <select class="form-control" name="priority" 
                                                        data-field-type="selection" data-required="false">
                                                    <option value="">Select...</option>
                                                    <option value="low">Low Priority</option>
                                                    <option value="medium">Medium Priority</option>
                                                    <option value="high">High Priority</option>
                                                    <option value="urgent">Urgent</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Feedback Tags</label>
                                                <select name="feedback_tags" class="form-control" multiple="multiple"
                                                        data-widget="many2many_tags" data-model="project.tags"
                                                        data-field-type="many2many" data-required="false">
                                                    <t t-foreach="project_tags" t-as="tag">
                                                        <option t-att-value="tag.id"><t t-esc="tag.name"/></option>
                                                    </t>
                                                </select>
                                                <div class="invalid-feedback">Please select at least one feedback tags.</div>
                                                </div>
                                                <div class="valid-feedback">Selections confirmed!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="feedback_comments" class="">Comments &amp; Suggestions</label>
                                                <textarea class="form-control" name="feedback_comments"  placeholder="Enter comments &amp; suggestions" rows="4"
                                                          data-field-type="text" data-required="false"></textarea>
                                                <div class="invalid-feedback">Please provide a valid comments &amp; suggestions.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6"></div>
                                            </div>

                                        </div>

                                        <!-- Submit Section -->
                                        <div class="form-submit-section">
                                            <button type="submit" class="btn btn-submit-enhanced" data-original-text="Submit Customer Feedback Form">
                                                <i class="fas fa-paper-plane"></i>
                                                Submit Customer Feedback Form
                                            </button>
                                            <p class="form-text mt-3">
                                                <i class="fas fa-lock"></i>
                                                Your information is secure and will be processed confidentially.
                                            </p>
                                        </div>
                                </form>

                                </div>

                                <!-- Success Section (Hidden initially) -->
                                <div class="main-form-card" id="success-section" style="display: none;">
                                    <div class="text-center py-5">
                                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        <h2 class="mt-3 mb-3">Thank You!</h2>
                                        <p class="lead">Your customer feedback form has been submitted successfully.</p>
                                        <p class="text-muted">You will receive a confirmation email shortly.</p>
                                        <a href="/customer_feedback" class="btn btn-primary mt-3">
                                            <i class="fas fa-home"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Form JavaScript -->
                    <script src="/ovakil_customer_feedback/static/src/js/enhanced_forms.js"></script>
                </div>
            </t>
        </template>

        <!-- Customer Feedback Form Success Template -->
        <template id="customer_feedback_main_success" name="Customer Feedback Form Success">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8 offset-lg-2">
                                <h1 class="mt-4 text-success">Thank You!</h1>
                                <p class="lead">Your customer feedback form has been submitted successfully.</p>
                                <a href="/customer_feedback" class="btn btn-primary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Portal Templates for Customer Feedback Form -->
        <!-- Customer Portal List Template -->
        <template id="portal_customer_feedback_main_list" name="Customer Feedback Form Portal List">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Customer Feedback Form</t>
                </t>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Your Customer Feedback Form Records</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="customer_feedback_main_records">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Payment Status</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="customer_feedback_main_records" t-as="record">
                                                    <tr>
                                                        <td><a t-attf-href="/my/customer-feedback-main/#{record.id}"><t t-esc="record.name"/></a></td>
                                                        <td><span class="badge bg-primary text-white" t-field="record.state"/></td>
                                                        <td>
                                                            <span t-attf-class="badge #{record.payment_status == 'paid' and 'bg-success text-white' or record.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                                <span t-field="record.payment_status"/>
                                                            </span>
                                                        </td>
                                                        <td><t t-esc="record.create_date" t-options="{'widget': 'date'}"/></td>
                                                        <td>
                                                            <a t-attf-href="/my/customer-feedback-main/#{record.id}" class="btn btn-sm btn-primary">View</a>
                                                            <t t-if="record.state == 'feedback_submitted'">
                                                                <a t-attf-href="/my/customer-feedback-main/#{record.id}/edit" class="btn btn-sm btn-secondary">Edit</a>
                                                            </t>
                                                            <t t-if="record.payment_status == 'pending' and record.payment_url">
                                                                <a t-att-href="record.payment_url" class="btn btn-sm btn-warning">Pay Now</a>
                                                            </t>
                                                            <t t-elif="record.payment_status == 'pending'">
                                                                <span class="btn btn-sm btn-outline-warning disabled">Payment Pending</span>
                                                            </t>
                                                            <t t-if="record.payment_status == 'paid'">
                                                                <a t-attf-href="/my/customer-feedback-main/#{record.id}/download-pdf" class="btn btn-sm btn-success">
                                                                    <i class="fa fa-download"/> Download PDF
                                                                </a>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        <p>You don't have any customer feedback form records yet.</p>
                                        <a href="/customer-feedback-main" class="btn btn-primary">Create New Record</a>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Portal Detail Template -->
        <template id="portal_customer_feedback_main_detail" name="Customer Feedback Form Portal Detail">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4><t t-esc="customer_feedback_main.name"/></h4>
                                <div>
                                    <t t-if="customer_feedback_main.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/edit" class="btn btn-secondary">Edit</a>
                                    </t>
                                    <t t-if="customer_feedback_main.payment_status == 'paid'">
                                        <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/download-pdf" class="btn btn-success">
                                            <i class="fa fa-download"/> Download PDF
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Basic Information</h5>
                                        <p><strong>Name:</strong> <t t-esc="customer_feedback_main.name"/></p>
                                        <p><strong>Status:</strong> <span class="badge bg-primary text-white" t-field="customer_feedback_main.state"/></p>
                                        <p><strong>Created:</strong> <t t-esc="customer_feedback_main.create_date" t-options="{'widget': 'datetime'}"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Rate:</strong> $<t t-esc="customer_feedback_main.payment_rate"/></p>
                                        <p><strong>Payment Amount:</strong> $<t t-esc="customer_feedback_main.payment_amount"/></p>
                                        <p><strong>Payment Status:</strong>
                                            <span t-attf-class="badge #{customer_feedback_main.payment_status == 'paid' and 'bg-success text-white' or customer_feedback_main.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                <span t-field="customer_feedback_main.payment_status"/>
                                            </span>
                                        </p>
                                        <t t-if="customer_feedback_main.payment_method">
                                            <p><strong>Payment Method:</strong>
                                                <span class="badge bg-info text-white" t-field="customer_feedback_main.payment_method"/>
                                            </p>
                                        </t>
                                        <t t-if="customer_feedback_main.payment_url and customer_feedback_main.payment_status == 'pending'">
                                            <p><a t-att-href="customer_feedback_main.payment_url" class="btn btn-warning">Pay Now</a></p>
                                        </t>
                                    </div>
                                </div>

                                <!-- AI Response Section (only for paid customers) -->
                                <t t-if="customer_feedback_main.payment_status == 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">AI Response</h5>
                                                    <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/download-pdf" class="btn btn-light btn-sm">
                                                        <i class="fa fa-download"/> Download PDF
                                                    </a>
                                                </div>
                                                <div class="card-body">
                                                    <t t-raw="customer_feedback_main.ai_response"/>

                                                    <!-- PDF Download Section -->
                                                    <div class="text-center mt-4 pt-3 border-top">
                                                        <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/download-pdf" class="btn btn-success btn-lg">
                                                            <i class="fa fa-download"/> Download PDF
                                                        </a>
                                                        <p class="text-muted mt-2">
                                                            <small>Click to download the AI response as PDF document</small>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                <t t-elif="customer_feedback_main.payment_status != 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <h5>AI Response Available After Payment</h5>
                                                <p>Complete your payment to access the AI-generated response and download the PDF report.</p>
                                                <t t-if="customer_feedback_main.payment_url">
                                                    <a t-att-href="customer_feedback_main.payment_url" class="btn btn-warning">Complete Payment</a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
        <!-- Enhanced Step-by-Step Form Template -->
        <template id="customer_feedback_main_step_form" name="Customer Feedback Step Form">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Enhanced Form Styling -->
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"/>
                    <link rel="stylesheet" href="/ovakil_customer_feedback/static/src/css/enhanced_forms.css"/>
                    <style>
                        .step-form-container {
                            max-width: 900px;
                            margin: 0 auto;
                            padding: 20px;
                            background: #f8f9fa;
                            min-height: 100vh;
                        }
                        .step-indicator {
                            display: flex;
                            justify-content: center;
                            margin-bottom: 40px;
                            position: relative;
                            background: white;
                            padding: 30px;
                            border-radius: 15px;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                        }
                        .step-indicator::before {
                            content: '';
                            position: absolute;
                            top: 50px;
                            left: 20%;
                            right: 20%;
                            height: 3px;
                            background: linear-gradient(90deg, #e9ecef 0%, #007bff 50%, #e9ecef 100%);
                            z-index: 1;
                        }
                        .step {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            position: relative;
                            z-index: 2;
                            background: white;
                            padding: 0 20px;
                            min-width: 120px;
                        }
                        .step-number {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            background: #e9ecef;
                            color: #6c757d;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                            font-size: 18px;
                            margin-bottom: 10px;
                            transition: all 0.3s ease;
                            border: 3px solid #e9ecef;
                        }
                        .step.active .step-number {
                            background: #007bff;
                            color: white;
                            border-color: #007bff;
                            transform: scale(1.1);
                        }
                        .step.completed .step-number {
                            background: #28a745;
                            color: white;
                            border-color: #28a745;
                        }
                        .step-title {
                            font-size: 14px;
                            color: #6c757d;
                            text-align: center;
                            font-weight: 500;
                            line-height: 1.3;
                        }
                        .step.active .step-title {
                            color: #007bff;
                            font-weight: 600;
                        }
                        .step.completed .step-title {
                            color: #28a745;
                            font-weight: 600;
                        }
                        .form-section {
                            background: white;
                            border-radius: 15px;
                            padding: 40px;
                            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                            margin-bottom: 30px;
                            border: 1px solid #e9ecef;
                        }
                        .form-section h2 {
                            color: #2c3e50;
                            font-weight: 700;
                            margin-bottom: 25px;
                            padding-bottom: 15px;
                            border-bottom: 3px solid #007bff;
                            font-size: 28px;
                        }
                        .form-section h3 {
                            color: #495057;
                            font-weight: 600;
                            margin-bottom: 20px;
                            font-size: 20px;
                        }
                        .form-group {
                            margin-bottom: 25px;
                        }
                        .form-label {
                            font-weight: 600;
                            color: #495057;
                            margin-bottom: 8px;
                            display: block;
                        }
                        .form-control {
                            border-radius: 10px;
                            border: 2px solid #e9ecef;
                            padding: 15px 20px;
                            transition: all 0.3s ease;
                            font-size: 16px;
                        }
                        .form-control:focus {
                            border-color: #007bff;
                            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
                            transform: translateY(-2px);
                        }
                        .btn-step {
                            padding: 15px 35px;
                            border-radius: 10px;
                            font-weight: 600;
                            font-size: 16px;
                            transition: all 0.3s ease;
                            border: none;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                        }
                        .btn-step:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                        }
                        .btn-primary {
                            background: linear-gradient(135deg, #007bff, #0056b3);
                        }
                        .btn-secondary {
                            background: linear-gradient(135deg, #6c757d, #495057);
                        }
                        .btn-success {
                            background: linear-gradient(135deg, #28a745, #1e7e34);
                        }
                        .login-required-section {
                            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                            border: 2px solid #ffc107;
                            border-radius: 15px;
                            padding: 40px;
                            text-align: center;
                            margin: 30px 0;
                        }
                        .review-section {
                            background: #f8f9fa;
                            border-radius: 15px;
                            padding: 30px;
                            margin: 25px 0;
                            border: 2px solid #e9ecef;
                        }
                        .review-item {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 15px 0;
                            border-bottom: 1px solid #dee2e6;
                        }
                        .review-item:last-child {
                            border-bottom: none;
                        }
                        .review-label {
                            font-weight: 600;
                            color: #495057;
                            flex: 1;
                        }
                        .review-value {
                            color: #6c757d;
                            flex: 2;
                            text-align: right;
                        }
                        .faq-section {
                            background: white;
                            border-radius: 15px;
                            padding: 30px;
                            margin: 25px 0;
                            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                        }
                        .faq-item {
                            border-bottom: 1px solid #e9ecef;
                            padding: 20px 0;
                        }
                        .faq-question {
                            font-weight: 600;
                            color: #2c3e50;
                            cursor: pointer;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            font-size: 16px;
                        }
                        .faq-question:hover {
                            color: #007bff;
                        }
                        .faq-answer {
                            color: #6c757d;
                            margin-top: 15px;
                            display: none;
                            line-height: 1.6;
                        }
                        .faq-answer.show {
                            display: block;
                        }
                        .addon-section {
                            background: white;
                            border-radius: 15px;
                            padding: 30px;
                            margin: 25px 0;
                            border: 2px solid #e9ecef;
                        }
                        .addon-item {
                            border: 2px solid #e9ecef;
                            border-radius: 10px;
                            padding: 20px;
                            margin: 15px 0;
                            transition: all 0.3s ease;
                        }
                        .addon-item:hover {
                            border-color: #007bff;
                            transform: translateY(-2px);
                            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                        }
                        .addon-item.selected {
                            border-color: #28a745;
                            background: #f8fff9;
                        }
                        .row {
                            margin: 0 -15px;
                        }
                        .col-md-6 {
                            padding: 0 15px;
                        }
                    </style>
                </t>

                <div class="step-form-container">
                    <!-- Step Indicator -->
                    <div class="step-indicator">
                        <div t-attf-class="step #{current_step == 'login' and 'active' or (current_step in ['form_fill', 'review', 'payment', 'complete'] and 'completed' or '')}">
                            <div class="step-number">
                                <i t-if="current_step in ['form_fill', 'review', 'payment', 'complete']" class="fas fa-check"></i>
                                <span t-else="">1</span>
                            </div>
                            <div class="step-title">Login</div>
                        </div>
                        <div t-attf-class="step #{current_step == 'form_fill' and 'active' or (current_step in ['review', 'payment', 'complete'] and 'completed' or '')}">
                            <div class="step-number">
                                <i t-if="current_step in ['review', 'payment', 'complete']" class="fas fa-check"></i>
                                <span t-else="">2</span>
                            </div>
                            <div class="step-title">Fill Form</div>
                        </div>
                        <div t-attf-class="step #{current_step == 'review' and 'active' or (current_step in ['payment', 'complete'] and 'completed' or '')}">
                            <div class="step-number">
                                <i t-if="current_step in ['payment', 'complete']" class="fas fa-check"></i>
                                <span t-else="">3</span>
                            </div>
                            <div class="step-title">Review</div>
                        </div>
                        <div t-if="form_config.payment_required" t-attf-class="step #{current_step == 'payment' and 'active' or (current_step == 'complete' and 'completed' or '')}">
                            <div class="step-number">
                                <i t-if="current_step == 'complete'" class="fas fa-check"></i>
                                <span t-else="">4</span>
                            </div>
                            <div class="step-title">Payment</div>
                        </div>
                        <div t-attf-class="step #{current_step == 'complete' and 'active' or ''}">
                            <div class="step-number">
                                <i t-if="current_step == 'complete'" class="fas fa-check"></i>
                                <span t-else=""><t t-if="form_config.payment_required">5</t><t t-else="">4</t></span>
                            </div>
                            <div class="step-title">Complete</div>
                        </div>
                    </div>

                    <!-- Step Content -->
                    <t t-if="current_step == 'login'">
                        <t t-call="ovakil_customer_feedback.login_step_content"/>
                    </t>
                    <t t-elif="current_step == 'form_fill'">
                        <t t-call="ovakil_customer_feedback.form_fill_step_content"/>
                    </t>
                    <t t-elif="current_step == 'review'">
                        <t t-call="ovakil_customer_feedback.review_step_content"/>
                    </t>
                    <t t-elif="current_step == 'payment'">
                        <t t-call="ovakil_customer_feedback.payment_step_content"/>
                    </t>
                    <t t-elif="current_step == 'complete'">
                        <t t-call="ovakil_customer_feedback.complete_step_content"/>
                    </t>
                </div>

                <!-- Enhanced JavaScript -->
                <script src="/ovakil_customer_feedback/static/src/js/enhanced_forms.js"></script>
                <script>
                    // FAQ Toggle functionality
                    document.addEventListener('DOMContentLoaded', function() {
                        const faqQuestions = document.querySelectorAll('.faq-question');
                        faqQuestions.forEach(question => {
                            question.addEventListener('click', function() {
                                const answer = this.nextElementSibling;
                                const icon = this.querySelector('i');

                                if (answer.classList.contains('show')) {
                                    answer.classList.remove('show');
                                    icon.classList.remove('fa-chevron-up');
                                    icon.classList.add('fa-chevron-down');
                                } else {
                                    answer.classList.add('show');
                                    icon.classList.remove('fa-chevron-down');
                                    icon.classList.add('fa-chevron-up');
                                }
                            });
                        });

                        // Addon selection functionality
                        const addonItems = document.querySelectorAll('.addon-item');
                        addonItems.forEach(item => {
                            const checkbox = item.querySelector('input[type="checkbox"]');
                            if (checkbox) {
                                checkbox.addEventListener('change', function() {
                                    if (this.checked) {
                                        item.classList.add('selected');
                                    } else {
                                        item.classList.remove('selected');
                                    }
                                });
                            }
                        });
                    });
                </script>
            </t>
        </template>

        <!-- Login Step Content -->
        <template id="login_step_content" name="Login Step Content">
            <div class="form-section">
                <h2><i class="fas fa-sign-in-alt"></i> Login Required</h2>
                <div class="login-required-section">
                    <i class="fas fa-lock fa-3x mb-3" style="color: #ffc107;"></i>
                    <h3>Please Login to Continue</h3>
                    <p class="mb-4">To ensure your data is saved and you can review your submission, please login to your account.</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="/web/login?redirect=/customer-feedback-main/step/form_fill" class="btn btn-primary btn-step">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                        <a href="/web/signup?redirect=/customer-feedback-main/step/form_fill" class="btn btn-secondary btn-step">
                            <i class="fas fa-user-plus"></i> Sign Up
                        </a>
                    </div>
                </div>
            </div>
        </template>

        <!-- Form Fill Step Content -->
        <template id="form_fill_step_content" name="Form Fill Step Content">
            <form method="post" t-attf-action="/customer-feedback-main/step/form_fill#{record and '?record_id=' + str(record.id) or ''}">
                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                <div class="form-section">
                    <h2><i class="fas fa-edit"></i> Customer Feedback Form</h2>

                    <!-- Basic Information -->
                    <h3><i class="fas fa-user"></i> Basic Information</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="name">Name *</label>
                                <input type="text" class="form-control" name="name" id="name"
                                       t-att-value="record and record.name or ''" required="required"/>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="customer_name">Customer Name *</label>
                                <input type="text" class="form-control" name="customer_name" id="customer_name"
                                       t-att-value="record and record.customer_name or ''" required="required"/>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customer_email">Email Address *</label>
                        <input type="email" class="form-control" name="customer_email" id="customer_email"
                               t-att-value="record and record.customer_email or ''" required="required"/>
                    </div>

                    <!-- Additional Details -->
                    <h3><i class="fas fa-star"></i> Feedback Details</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="overall_rating">Overall Rating *</label>
                                <select class="form-control" name="overall_rating" id="overall_rating" required="required">
                                    <option value="">Select Rating...</option>
                                    <option value="1" t-att-selected="record and record.overall_rating == '1' and 'selected' or ''">⭐ 1 Star</option>
                                    <option value="2" t-att-selected="record and record.overall_rating == '2' and 'selected' or ''">⭐⭐ 2 Stars</option>
                                    <option value="3" t-att-selected="record and record.overall_rating == '3' and 'selected' or ''">⭐⭐⭐ 3 Stars</option>
                                    <option value="4" t-att-selected="record and record.overall_rating == '4' and 'selected' or ''">⭐⭐⭐⭐ 4 Stars</option>
                                    <option value="5" t-att-selected="record and record.overall_rating == '5' and 'selected' or ''">⭐⭐⭐⭐⭐ 5 Stars</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="assigned_user">Assigned User</label>
                                <select class="form-control" name="assigned_user" id="assigned_user">
                                    <option value="">Select Assigned User...</option>
                                    <t t-foreach="users" t-as="user">
                                        <option t-att-value="user.id"
                                                t-att-selected="record and record.assigned_user.id == user.id and 'selected' or ''">
                                            <t t-esc="user.name"/>
                                        </option>
                                    </t>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="priority">Priority Level</label>
                                <select class="form-control" name="priority" id="priority">
                                    <option value="">Select Priority...</option>
                                    <option value="low" t-att-selected="record and record.priority == 'low' and 'selected' or ''">🟢 Low Priority</option>
                                    <option value="medium" t-att-selected="record and record.priority == 'medium' and 'selected' or ''">🟡 Medium Priority</option>
                                    <option value="high" t-att-selected="record and record.priority == 'high' and 'selected' or ''">🟠 High Priority</option>
                                    <option value="urgent" t-att-selected="record and record.priority == 'urgent' and 'selected' or ''">🔴 Urgent</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label" for="feedback_tags">Feedback Tags</label>
                                <select class="form-control" name="feedback_tags" id="feedback_tags" multiple="multiple">
                                    <t t-foreach="project_tags" t-as="tag">
                                        <option t-att-value="tag.id"
                                                t-att-selected="record and tag in record.feedback_tags and 'selected' or ''">
                                            <t t-esc="tag.name"/>
                                        </option>
                                    </t>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="feedback_comments">Comments &amp; Suggestions</label>
                        <textarea class="form-control" name="feedback_comments" id="feedback_comments" rows="4"
                                  placeholder="Please share your detailed feedback..."><t t-if="record" t-esc="record.feedback_comments"/></textarea>
                    </div>

                    <!-- Available Addons -->
                    <t t-if="addons">
                        <h3><i class="fas fa-plus-circle"></i> Available Add-ons</h3>
                        <div class="addon-section">
                            <t t-foreach="addons" t-as="addon">
                                <div class="addon-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="flex-grow-1">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="selected_addons"
                                                       t-att-value="addon.id" t-att-id="'addon_' + str(addon.id)"
                                                       t-att-checked="record and addon in record.selected_addon_ids and 'checked' or ''"/>
                                                <label class="form-check-label" t-att-for="'addon_' + str(addon.id)">
                                                    <strong><t t-esc="addon.name"/></strong>
                                                    <t t-if="addon.is_required"> <span class="badge bg-warning">Required</span></t>
                                                </label>
                                            </div>
                                            <p class="text-muted mb-0 mt-1"><t t-esc="addon.description"/></p>
                                        </div>
                                        <div class="text-end">
                                            <span class="h5 mb-0">
                                                <t t-if="addon.price > 0">₹<t t-esc="addon.price"/></t>
                                                <t t-else="">Free</t>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </t>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="/customer-feedback-main" class="btn btn-secondary btn-step">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary btn-step">
                            <i class="fas fa-arrow-right"></i> Continue to Review
                        </button>
                    </div>
                </div>
            </form>
        </template>

        <!-- Review Step Content -->
        <template id="review_step_content" name="Review Step Content">
            <div class="form-section">
                <h2><i class="fas fa-eye"></i> Review Your Submission</h2>

                <div class="review-section">
                    <h3><i class="fas fa-check-circle"></i> Please Review Your Information</h3>

                    <div class="review-item">
                        <span class="review-label">Name:</span>
                        <span class="review-value"><t t-esc="record.name"/></span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Customer Name:</span>
                        <span class="review-value"><t t-esc="record.customer_name"/></span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Email:</span>
                        <span class="review-value"><t t-esc="record.customer_email"/></span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">Overall Rating:</span>
                        <span class="review-value">
                            <t t-if="record.overall_rating == '1'">⭐ 1 Star</t>
                            <t t-elif="record.overall_rating == '2'">⭐⭐ 2 Stars</t>
                            <t t-elif="record.overall_rating == '3'">⭐⭐⭐ 3 Stars</t>
                            <t t-elif="record.overall_rating == '4'">⭐⭐⭐⭐ 4 Stars</t>
                            <t t-elif="record.overall_rating == '5'">⭐⭐⭐⭐⭐ 5 Stars</t>
                        </span>
                    </div>
                    <t t-if="record.assigned_user">
                        <div class="review-item">
                            <span class="review-label">Assigned User:</span>
                            <span class="review-value"><t t-esc="record.assigned_user.name"/></span>
                        </div>
                    </t>
                    <t t-if="record.priority">
                        <div class="review-item">
                            <span class="review-label">Priority:</span>
                            <span class="review-value">
                                <t t-if="record.priority == 'low'">🟢 Low Priority</t>
                                <t t-elif="record.priority == 'medium'">🟡 Medium Priority</t>
                                <t t-elif="record.priority == 'high'">🟠 High Priority</t>
                                <t t-elif="record.priority == 'urgent'">🔴 Urgent</t>
                            </span>
                        </div>
                    </t>
                    <t t-if="record.feedback_tags">
                        <div class="review-item">
                            <span class="review-label">Feedback Tags:</span>
                            <span class="review-value">
                                <t t-foreach="record.feedback_tags" t-as="tag">
                                    <span class="badge bg-primary me-1"><t t-esc="tag.name"/></span>
                                </t>
                            </span>
                        </div>
                    </t>
                    <t t-if="record.selected_addon_ids">
                        <div class="review-item">
                            <span class="review-label">Selected Add-ons:</span>
                            <span class="review-value">
                                <t t-foreach="record.selected_addon_ids" t-as="addon">
                                    <div><t t-esc="addon.name"/>
                                        <t t-if="addon.price > 0">(₹<t t-esc="addon.price"/>)</t>
                                        <t t-else="">(Free)</t>
                                    </div>
                                </t>
                            </span>
                        </div>
                    </t>
                    <t t-if="record.feedback_comments">
                        <div class="review-item">
                            <span class="review-label">Comments:</span>
                            <span class="review-value"><t t-esc="record.feedback_comments"/></span>
                        </div>
                    </t>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between mt-4">
                    <form method="post" t-attf-action="/customer-feedback-main/step/review?record_id=#{record.id}" class="d-inline">
                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                        <input type="hidden" name="edit" value="1"/>
                        <button type="submit" class="btn btn-secondary btn-step">
                            <i class="fas fa-edit"></i> Edit Information
                        </button>
                    </form>
                    <form method="post" t-attf-action="/customer-feedback-main/step/review?record_id=#{record.id}" class="d-inline">
                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                        <button type="submit" class="btn btn-success btn-step">
                            <i class="fas fa-check"></i> Confirm &amp; Continue
                        </button>
                    </form>
                </div>
            </div>
        </template>

        <!-- Payment Step Content -->
        <template id="payment_step_content" name="Payment Step Content">
            <div class="form-section">
                <h2><i class="fas fa-credit-card"></i> Payment</h2>

                <t t-if="form_config.payment_type == 'auto'">
                    <div class="alert alert-info">
                        <h4><i class="fas fa-info-circle"></i> Payment Required</h4>
                        <p>Please complete your payment to finalize your submission.</p>
                        <p><strong>Amount: ₹<t t-esc="record.payment_amount"/></strong></p>
                    </div>

                    <div class="text-center">
                        <a t-if="record.payment_url" t-att-href="record.payment_url" class="btn btn-primary btn-step btn-lg">
                            <i class="fas fa-credit-card"></i> Pay Now
                        </a>
                        <p class="mt-3 text-muted">You will be redirected to a secure payment page.</p>
                    </div>
                </t>

                <t t-else="">
                    <div class="alert alert-success">
                        <h4><i class="fas fa-check-circle"></i> Payment Request Submitted</h4>
                        <p>Your form has been submitted successfully. Our admin will review your submission and send you a payment link if required.</p>
                        <p><strong>Estimated Amount: ₹<t t-esc="record.payment_amount"/></strong></p>
                    </div>

                    <div class="text-center">
                        <a href="/customer-feedback-main/step/complete?record_id=#{record.id}" class="btn btn-success btn-step">
                            <i class="fas fa-arrow-right"></i> Continue
                        </a>
                    </div>
                </t>
            </div>
        </template>

        <!-- Complete Step Content -->
        <template id="complete_step_content" name="Complete Step Content">
            <div class="form-section">
                <h2><i class="fas fa-check-circle text-success"></i> Submission Complete!</h2>

                <div class="text-center mb-4">
                    <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                    <h3>Thank You for Your Feedback!</h3>
                    <p class="lead">Your submission has been received and is being processed.</p>
                    <p><strong>Reference ID:</strong> <span class="badge bg-primary"><t t-esc="record.name"/></span></p>
                </div>

                <!-- Payment Status -->
                <t t-if="form_config.payment_required">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Payment Status</h5>
                        <t t-if="form_config.payment_type == 'admin_approval'">
                            <p>Our admin will review your submission and contact you with payment details if required.</p>
                        </t>
                        <t t-else="">
                            <p>Payment Status: <span class="badge bg-warning"><t t-esc="record.payment_status"/></span></p>
                        </t>
                    </div>
                </t>

                <!-- FAQs Section -->
                <t t-if="faqs">
                    <div class="faq-section">
                        <h3><i class="fas fa-question-circle"></i> Frequently Asked Questions</h3>
                        <t t-foreach="faqs" t-as="faq">
                            <div class="faq-item">
                                <div class="faq-question">
                                    <span><t t-esc="faq.question"/></span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="faq-answer">
                                    <t t-esc="faq.answer"/>
                                </div>
                            </div>
                        </t>
                    </div>
                </t>

                <!-- Next Steps -->
                <div class="alert alert-success">
                    <h5><i class="fas fa-lightbulb"></i> What's Next?</h5>
                    <ul class="mb-0">
                        <li>You will receive an email confirmation shortly</li>
                        <li>Our team will review your feedback within 24-48 hours</li>
                        <li t-if="form_config.payment_required and form_config.payment_type == 'admin_approval'">You will be contacted regarding payment if applicable</li>
                        <li>You can track the status of your submission in your account</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mt-4">
                    <a href="/my/home" class="btn btn-primary btn-step me-3">
                        <i class="fas fa-user"></i> My Account
                    </a>
                    <a href="/customer-feedback-main" class="btn btn-secondary btn-step">
                        <i class="fas fa-plus"></i> Submit Another
                    </a>
                </div>
            </div>
        </template>

    </data>
</odoo>