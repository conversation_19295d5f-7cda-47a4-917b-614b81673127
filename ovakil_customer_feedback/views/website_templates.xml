<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Index Template -->
        <template id="index" name="Customer Feedback System Index">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-12">
                                <h1 class="mt-4">Customer Feedback System</h1>
                                <p class="lead">Complete customer feedback collection and management system with AI analysis and payment integration</p>

                                <div class="row mt-4">
                                    <div class="col-md-6 col-lg-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">Customer Feedback Form</h5>
                                                <p class="card-text">Main customer feedback collection form with rating and comments</p>
                                                <a href="/customer-feedback-main" class="btn btn-primary">Access Form</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Feedback Form Website Template -->
        <template id="customer_feedback_main_website_form" name="Customer Feedback Form Form">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Enhanced Form Styling -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
                    <link rel="stylesheet" href="/ovakil_customer_feedback/static/src/css/enhanced_forms.css"/>
                </t>
                <div id="wrap" class="enhanced-form-wrapper">
                    <!-- Progress Steps -->
                    <div class="container">
                        <div class="form-progress-steps">
                            <div class="step-item active">
                                <div class="step-number">1</div>
                                <div class="step-label">Form Details</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-label">Review</div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-label">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-lg-10 offset-lg-1">
                                <!-- Form Header Card -->
                                <div class="form-header-card">
                                    <div class="form-header-content">
                                        <h1 class="form-title">Customer Feedback Form</h1>
                                        <p class="form-description">Main customer feedback collection form with rating and comments</p>
                                        <div class="form-meta">
                                            <span class="form-meta-item">
                                                <i class="fas fa-clock"></i>
                                                Estimated time: 5-10 minutes
                                            </span>
                                            <span class="form-meta-item">
                                                <i class="fas fa-shield-alt"></i>
                                                Secure &amp; Confidential
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Form Card -->
                                <div class="main-form-card">
                                    <form id="enhanced-form" action="/customer-feedback-main/submit" method="post" class="enhanced-form" novalidate="novalidate">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                        <!-- Basic Information Section -->
                                        <div class="form-section">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-user"></i>
                                                Basic Information
                                            </h3>

                                            <div class="nice-form-group">
                                                <label for="name" class="required">Name</label>
                                                <input type="text" class="form-control" name="name" required="required"
                                                       data-field-type="char" data-required="true" placeholder="Enter your full name"/>
                                                <div class="invalid-feedback">Please provide a valid name.</div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="customer_name" class="required">Customer Name</label>
                                                <input type="text" class="form-control" name="customer_name" required="required" placeholder="Enter customer name"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid customer name.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="customer_email" class="required">Email Address</label>
                                                <input type="text" class="form-control" name="customer_email" required="required" placeholder="Enter email address"
                                                       data-field-type="char" data-required="true" />
                                                <div class="invalid-feedback">Please provide a valid email address.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                            </div>
                                        </div>

                                        <!-- Additional Details Section -->
                                        <div class="form-section" style="display: none;">
                                            <h3 class="form-section-title">
                                                <i class="fas fa-info-circle"></i>
                                                Additional Details
                                            </h3>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="overall_rating" class="required">Overall Rating</label>
                                                <select class="form-control" name="overall_rating" required="required"
                                                        data-field-type="selection" data-required="true">
                                                    <option value="">Select...</option>
                                                    <option value="1">1 Star</option>
                                                    <option value="2">2 Stars</option>
                                                    <option value="3">3 Stars</option>
                                                    <option value="4">4 Stars</option>
                                                    <option value="5">5 Stars</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="assigned_user" class="">Assigned User</label>
                                                <select class="form-control" name="assigned_user" 
                                                        data-widget="many2one_dropdown" data-model="res.users"
                                                        data-field-type="many2one" data-required="false"
                                                        data-label="Assigned User">
                                                    <option value="">Select Assigned User...</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="invalid-feedback">Please select a valid assigned user.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="priority" class="">Priority Level</label>
                                                <select class="form-control" name="priority" 
                                                        data-field-type="selection" data-required="false">
                                                    <option value="">Select...</option>
                                                    <option value="low">Low Priority</option>
                                                    <option value="medium">Medium Priority</option>
                                                    <option value="high">High Priority</option>
                                                    <option value="urgent">Urgent</option>
                                                </select>
                                                <div class="invalid-feedback">Please select a valid option.</div>
                                                </div>
                                                <div class="valid-feedback">Selection confirmed!</div>
                                            </div>
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label class="">Feedback Tags</label>
                                                <select name="feedback_tags"  style="display: none;" multiple="multiple"
                                                        data-widget="many2many_tags" data-model="project.tags"
                                                        data-field-type="many2many" data-required="false">
                                                </select>
                                                <div class="invalid-feedback">Please select at least one feedback tags.</div>
                                                </div>
                                                <div class="valid-feedback">Selections confirmed!</div>
                                            </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="nice-form-group">
                                                <label for="feedback_comments" class="">Comments &amp; Suggestions</label>
                                                <textarea class="form-control" name="feedback_comments"  placeholder="Enter comments &amp; suggestions" rows="4"
                                                          data-field-type="text" data-required="false"></textarea>
                                                <div class="invalid-feedback">Please provide a valid comments &amp; suggestions.</div>
                                                </div>
                                                <div class="valid-feedback">Looks good!</div>
                                            </div>
                                                <div class="col-md-6"></div>
                                            </div>

                                        </div>

                                        <!-- Submit Section -->
                                        <div class="form-submit-section">
                                            <button type="submit" class="btn btn-submit-enhanced" data-original-text="Submit Customer Feedback Form">
                                                <i class="fas fa-paper-plane"></i>
                                                Submit Customer Feedback Form
                                            </button>
                                            <p class="form-text mt-3">
                                                <i class="fas fa-lock"></i>
                                                Your information is secure and will be processed confidentially.
                                            </p>
                                        </div>
                                </form>

                                </div>

                                <!-- Success Section (Hidden initially) -->
                                <div class="main-form-card" id="success-section" style="display: none;">
                                    <div class="text-center py-5">
                                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                                        <h2 class="mt-3 mb-3">Thank You!</h2>
                                        <p class="lead">Your customer feedback form has been submitted successfully.</p>
                                        <p class="text-muted">You will receive a confirmation email shortly.</p>
                                        <a href="/customer_feedback" class="btn btn-primary mt-3">
                                            <i class="fas fa-home"></i>
                                            Back to Home
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Form JavaScript -->
                    <script src="/ovakil_customer_feedback/static/src/js/enhanced_forms.js"></script>
                </div>
            </t>
        </template>

        <!-- Customer Feedback Form Success Template -->
        <template id="customer_feedback_main_success" name="Customer Feedback Form Success">
            <t t-call="website.layout">
                <div id="wrap">
                    <div class="container">
                        <div class="row">
                            <div class="col-lg-8 offset-lg-2">
                                <h1 class="mt-4 text-success">Thank You!</h1>
                                <p class="lead">Your customer feedback form has been submitted successfully.</p>
                                <a href="/customer_feedback" class="btn btn-primary">Back to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Portal Templates for Customer Feedback Form -->
        <!-- Customer Portal List Template -->
        <template id="portal_customer_feedback_main_list" name="Customer Feedback Form Portal List">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Customer Feedback Form</t>
                </t>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Your Customer Feedback Form Records</h4>
                            </div>
                            <div class="card-body">
                                <t t-if="customer_feedback_main_records">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Payment Status</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="customer_feedback_main_records" t-as="record">
                                                    <tr>
                                                        <td><a t-attf-href="/my/customer-feedback-main/#{record.id}"><t t-esc="record.name"/></a></td>
                                                        <td><span class="badge bg-primary text-white" t-field="record.state"/></td>
                                                        <td>
                                                            <span t-attf-class="badge #{record.payment_status == 'paid' and 'bg-success text-white' or record.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                                <span t-field="record.payment_status"/>
                                                            </span>
                                                        </td>
                                                        <td><t t-esc="record.create_date" t-options="{'widget': 'date'}"/></td>
                                                        <td>
                                                            <a t-attf-href="/my/customer-feedback-main/#{record.id}" class="btn btn-sm btn-primary">View</a>
                                                            <t t-if="record.state == 'feedback_submitted'">
                                                                <a t-attf-href="/my/customer-feedback-main/#{record.id}/edit" class="btn btn-sm btn-secondary">Edit</a>
                                                            </t>
                                                            <t t-if="record.payment_status == 'pending' and record.payment_url">
                                                                <a t-att-href="record.payment_url" class="btn btn-sm btn-warning">Pay Now</a>
                                                            </t>
                                                            <t t-elif="record.payment_status == 'pending'">
                                                                <span class="btn btn-sm btn-outline-warning disabled">Payment Pending</span>
                                                            </t>
                                                            <t t-if="record.payment_status == 'paid'">
                                                                <a t-attf-href="/my/customer-feedback-main/#{record.id}/download-pdf" class="btn btn-sm btn-success">
                                                                    <i class="fa fa-download"/> Download PDF
                                                                </a>
                                                            </t>
                                                        </td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>
                                </t>
                                <t t-else="">
                                    <div class="alert alert-info">
                                        <p>You don't have any customer feedback form records yet.</p>
                                        <a href="/customer-feedback-main" class="btn btn-primary">Create New Record</a>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Customer Portal Detail Template -->
        <template id="portal_customer_feedback_main_detail" name="Customer Feedback Form Portal Detail">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4><t t-esc="customer_feedback_main.name"/></h4>
                                <div>
                                    <t t-if="customer_feedback_main.state == 'feedback_submitted'">
                                        <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/edit" class="btn btn-secondary">Edit</a>
                                    </t>
                                    <t t-if="customer_feedback_main.payment_status == 'paid'">
                                        <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/download-pdf" class="btn btn-success">
                                            <i class="fa fa-download"/> Download PDF
                                        </a>
                                    </t>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Basic Information</h5>
                                        <p><strong>Name:</strong> <t t-esc="customer_feedback_main.name"/></p>
                                        <p><strong>Status:</strong> <span class="badge bg-primary text-white" t-field="customer_feedback_main.state"/></p>
                                        <p><strong>Created:</strong> <t t-esc="customer_feedback_main.create_date" t-options="{'widget': 'datetime'}"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Rate:</strong> $<t t-esc="customer_feedback_main.payment_rate"/></p>
                                        <p><strong>Payment Amount:</strong> $<t t-esc="customer_feedback_main.payment_amount"/></p>
                                        <p><strong>Payment Status:</strong>
                                            <span t-attf-class="badge #{customer_feedback_main.payment_status == 'paid' and 'bg-success text-white' or customer_feedback_main.payment_status == 'pending' and 'bg-warning text-dark' or 'bg-secondary text-white'}">
                                                <span t-field="customer_feedback_main.payment_status"/>
                                            </span>
                                        </p>
                                        <t t-if="customer_feedback_main.payment_method">
                                            <p><strong>Payment Method:</strong>
                                                <span class="badge bg-info text-white" t-field="customer_feedback_main.payment_method"/>
                                            </p>
                                        </t>
                                        <t t-if="customer_feedback_main.payment_url and customer_feedback_main.payment_status == 'pending'">
                                            <p><a t-att-href="customer_feedback_main.payment_url" class="btn btn-warning">Pay Now</a></p>
                                        </t>
                                    </div>
                                </div>

                                <!-- AI Response Section (only for paid customers) -->
                                <t t-if="customer_feedback_main.payment_status == 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                                    <h5 class="mb-0">AI Response</h5>
                                                    <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/download-pdf" class="btn btn-light btn-sm">
                                                        <i class="fa fa-download"/> Download PDF
                                                    </a>
                                                </div>
                                                <div class="card-body">
                                                    <t t-raw="customer_feedback_main.ai_response"/>

                                                    <!-- PDF Download Section -->
                                                    <div class="text-center mt-4 pt-3 border-top">
                                                        <a t-attf-href="/my/customer-feedback-main/#{customer_feedback_main.id}/download-pdf" class="btn btn-success btn-lg">
                                                            <i class="fa fa-download"/> Download PDF
                                                        </a>
                                                        <p class="text-muted mt-2">
                                                            <small>Click to download the AI response as PDF document</small>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                <t t-elif="customer_feedback_main.payment_status != 'paid'">
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <h5>AI Response Available After Payment</h5>
                                                <p>Complete your payment to access the AI-generated response and download the PDF report.</p>
                                                <t t-if="customer_feedback_main.payment_url">
                                                    <a t-att-href="customer_feedback_main.payment_url" class="btn btn-warning">Complete Payment</a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>