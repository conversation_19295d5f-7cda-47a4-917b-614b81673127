<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Customer Feedback Form Tree View -->
    <record id="customer_feedback_main_tree_view" model="ir.ui.view">
        <field name="name">Customer Feedback Form Tree</field>
        <field name="model">ovakil_customer_feedback.customer_feedback_main</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
<field name="customer_name" optional="show"/>
<field name="customer_email" optional="show"/>
<field name="overall_rating" optional="show" widget="badge"/>
<field name="assigned_user" optional="hide"/>
<field name="feedback_tags" optional="hide" widget="many2many_tags"/>
<field name="state"/>
<field name="create_date"/>
            </tree>
        </field>
    </record>
    <!-- Customer Feedback Form Form View -->
    <record id="customer_feedback_main_form_view" model="ir.ui.view">
        <field name="name">Customer Feedback Form Form</field>
        <field name="model">ovakil_customer_feedback.customer_feedback_main</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_feedback_submitted_to_feedback_under_review" type="object" string="Feedback Submitted → Under Review"
                            class="btn-primary" invisible="state != 'feedback_submitted'"/>
                    <button name="action_feedback_under_review_to_feedback_responded" type="object" string="Under Review → Responded"
                            class="btn-primary" invisible="state != 'feedback_under_review'"/>
                    <button name="action_request_payment" type="object" string="Request Payment"
                            class="btn-warning" invisible="payment_status != 'none'"/>
                    <button name="action_record_cash_payment" type="object" string="Record Cash Payment"
                            class="btn-success" invisible="payment_status != 'pending'"/>
                    <button name="action_refresh_payment_status" type="object" string="Refresh Payment Status"
                            class="btn-info" invisible="payment_status not in ['pending', 'none']"/>
                    <button name="action_view_sales_order" type="object" string="View Sales Order"
                            class="btn-info" invisible="not sales_order_id"/>
                    <button name="action_open_payment_portal" type="object" string="Open Payment Portal"
                            class="btn-secondary" invisible="not payment_url"/>
                    <!-- ✨ AI Response Buttons -->
                    <button name="action_get_ai_response" type="object" string="Get AI Response"
                            class="btn-secondary" icon="fa-magic"
                            invisible="ai_response_generated == True"/>
                    <button name="action_generate_ai_pdf" type="object" string="Regenerate PDF"
                            class="btn-info" icon="fa-file-pdf-o"
                            invisible="ai_response == False"
                            help="Generate fresh PDF from current AI response content"
                            groups="base.group_user"/>
                    <field name="state" widget="statusbar" statusbar_visible="feedback_submitted,feedback_under_review,feedback_responded"/>
                </header>
                <sheet>
                    <group>
                        <group name="basic_info">
                            <field name="name"/>
                        <field name="customer_name"/>
                        <field name="customer_email"/>
                        <field name="overall_rating"/>
                        <field name="priority"/>
                        </group>
                        <group name="relational_info">
                        <field name="assigned_user" widget="many2one_dropdown" domain="[(&apos;active&apos;, &apos;=&apos;, True)]" options="{'no_create': False, 'no_open': False}"/>
                        <field name="feedback_tags" widget="many2many_tags" options="{'no_create': False, 'no_open': False}"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <group name="payment_info" string="Payment Information">
                        <group>
                            <field name="payment_rate"/>
                            <field name="payment_amount" readonly="1"/>
                            <field name="payment_status"/>
                        </group>
                        <group>
                            <field name="rate_changed_by" readonly="1"/>
                            <field name="rate_changed_date" readonly="1"/>
                            <field name="sales_order_id" readonly="1"/>
                            <field name="payment_url" readonly="1"/>
                        </group>
                    </group>
                        <field name="feedback_comments" widget="text"/>

                    <!-- ✨ AI Response Section -->
                    <!-- Hidden fields for AI functionality -->
                    <field name="ai_response_generated" invisible="1"/>
                    <field name="ai_response_pdf" invisible="1"/>
                    <field name="ai_response_pdf_filename" invisible="1"/>

                    <notebook>
                        <page string="AI Response &amp; Document Draft">
                            <group>
                                <div class="alert alert-info" invisible="ai_response_generated == True">
                                    <p><strong>AI Response &amp; Document Draft</strong></p>
                                    <p>Use this section to:</p>
                                    <ul>
                                        <li>Generate AI response using the "Get AI Response" button above</li>
                                        <li>Manually create or edit document content</li>
                                        <li>Format content using the rich text editor</li>
                                        <li>Generate PDF for customer download</li>
                                    </ul>
                                </div>
                                <field name="ai_response" widget="html" nolabel="1"
                                       placeholder="Click 'Get AI Response' button to generate AI content, or manually type your document content here..."/>
                                <separator string="AI Response Details" invisible="ai_response_generated == False"/>
                                <group invisible="ai_response_generated == False">
                                    <field name="ai_last_prompt" readonly="1" string="Last AI Prompt Used"/>
                                    <field name="ai_response_pdf" filename="ai_response_pdf_filename"
                                           invisible="ai_response_pdf == False"/>
                                    <field name="ai_response_pdf_filename" invisible="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <!-- Customer Feedback Form Kanban View -->
    <record id="customer_feedback_main_kanban_view" model="ir.ui.view">
        <field name="name">Customer Feedback Form Kanban</field>
        <field name="model">ovakil_customer_feedback.customer_feedback_main</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column" quick_create="false">
                <field name="id"/>
                <field name="name"/>
                <field name="state"/>
                <field name="color"/>
                <field name="assigned_user"/>
                <field name="priority"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="{{kanban_color(record.color.raw_value)}} oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                </div>
                                <div class="o_dropdown_kanban dropdown">
                                    <a class="dropdown-toggle o-no-caret btn" role="button" data-toggle="dropdown" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                        <span class="fa fa-ellipsis-v"/>
                                    </a>
                                    <div class="dropdown-menu" role="menu">
                                        <a t-if="widget.editable" role="menuitem" type="edit" class="dropdown-item">Edit</a>
                                        <a t-if="widget.deletable" role="menuitem" type="delete" class="dropdown-item">Delete</a>
                                        <div role="separator" class="dropdown-divider"/>
                                        <div class="dropdown-item-text text-muted">
                                            <small>ID: <field name="id"/></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                        <div class="o_kanban_record_subtitle">
                            <field name="customer_name"/>
                        </div>
                        <div class="o_kanban_record_subtitle">
                            <field name="customer_email"/>
                        </div>
                            </div>
                        <div class="oe_kanban_bottom_left">
                            <field name="priority" widget="priority"/>
                        </div>
                        <div class="oe_kanban_bottom_right">
                            <field name="assigned_user" widget="many2one_avatar_user"/>
                        </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Customer Feedback Form Action -->
    <record id="customer_feedback_main_action" model="ir.actions.act_window">
        <field name="name">Customer Feedback Form</field>
        <field name="res_model">ovakil_customer_feedback.customer_feedback_main</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
                                      (0, 0, {'view_mode': 'kanban', 'view_id': ref('customer_feedback_main_kanban_view')}),
                                      (0, 0, {'view_mode': 'tree', 'view_id': ref('customer_feedback_main_tree_view')}),
                                      (0, 0, {'view_mode': 'form', 'view_id': ref('customer_feedback_main_form_view')})]"/>
        <field name="context">{'group_by': 'state'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first customer feedback form!
            </p>
            <p>
                Click the "New" button to create a new customer feedback form.
            </p>
        </field>
    </record>

    <!-- Form Configuration Views -->
    <record id="form_configuration_tree_view" model="ir.ui.view">
        <field name="name">Form Configuration Tree</field>
        <field name="model">ovakil_customer_feedback.form_configuration</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="payment_required"/>
                <field name="payment_type"/>
                <field name="default_payment_rate"/>
                <field name="require_login"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="form_configuration_form_view" model="ir.ui.view">
        <field name="name">Form Configuration Form</field>
        <field name="model">ovakil_customer_feedback.form_configuration</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="active"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Payment Configuration">
                            <group>
                                <group>
                                    <field name="payment_required"/>
                                    <field name="payment_type" invisible="not payment_required"/>
                                    <field name="default_payment_rate"/>
                                </group>
                            </group>
                        </page>

                        <page string="Form Behavior">
                            <group>
                                <group>
                                    <field name="require_login"/>
                                    <field name="enable_review_step"/>
                                    <field name="enable_edit_capability"/>
                                </group>
                            </group>
                        </page>

                        <page string="Available Add-ons">
                            <field name="addon_ids">
                                <tree editable="bottom">
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="price"/>
                                    <field name="is_required"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>

                        <page string="FAQs">
                            <field name="faq_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="question"/>
                                    <field name="answer"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="form_configuration_action" model="ir.actions.act_window">
        <field name="name">Form Configuration</field>
        <field name="res_model">ovakil_customer_feedback.form_configuration</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first form configuration!
            </p>
            <p>
                Configure payment options, add-ons, and FAQs for your forms.
            </p>
        </field>
    </record>

    <!-- Customer Feedback Form Menu -->
    <menuitem id="customer_feedback_main_menu" name="Customer Feedback Form" action="customer_feedback_main_action" sequence="10"/>

    <!-- Form Configuration Menu -->
    <menuitem id="form_configuration_menu"
              name="Form Configuration"
              parent="customer_feedback_main_menu"
              action="form_configuration_action"
              sequence="20"/>
</odoo>