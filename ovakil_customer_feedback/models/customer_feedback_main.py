from odoo import api, fields, models, _


class CustomerFeedbackMain(models.Model):
    """
    Main customer feedback collection form with rating and comments Model
    Generated from template: Customer Feedback System
    """
    _name = 'ovakil_customer_feedback.customer_feedback_main'
    _description = 'Main customer feedback collection form with rating and comments'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char(string='Name', required=True, tracking=True)
    customer_name = fields.Char(string='Customer Name', required=True, help='Please enter your full name')
    customer_email = fields.Char(string='Email Address', required=True, help="We'll use this to send you updates")
    overall_rating = fields.Selection([('1', '1 Star'), ('2', '2 Stars'), ('3', '3 Stars'), ('4', '4 Stars'), ('5', '5 Stars')], string='Overall Rating', required=True, help='Rate your overall experience')
    feedback_comments = fields.Text(string='Comments & Suggestions', help='Please share your detailed feedback')
    recommend_service = fields.Boolean(string='Would you recommend our service?', required=True, default=True, help='Help us understand if you\'d recommend us to others')
    assigned_user = fields.Many2one('res.users', string='Assigned User', help='User responsible for handling this feedback', domain=[('active', '=', True)])
    priority = fields.Selection([('low', 'Low Priority'), ('medium', 'Medium Priority'), ('high', 'High Priority'), ('urgent', 'Urgent')], string='Priority Level', default='medium', help='Priority level for handling this feedback')
    feedback_tags = fields.Many2many('project.tags', string='Feedback Tags', relation='ovakil_customer_feedback_t_project_tags_rel', help='Tags to categorize this feedback')
    state = fields.Selection([
        ('feedback_submitted', 'Feedback Submitted'),
        ('feedback_under_review', 'Under Review'),
        ('feedback_responded', 'Responded')
    ], string='State', default='feedback_submitted', tracking=True)
    active = fields.Boolean(string='Active', default=True)
    color = fields.Integer(string='Color', default=0, help='Color for kanban view')
    partner_id = fields.Many2one('res.partner', string='Customer', help='Related customer partner')
    # Payment Fields
    payment_rate = fields.Float(string='Payment Rate', default=450.0, tracking=True, help='Rate for this record (can be overridden)')
    payment_amount = fields.Float(string='Payment Amount', compute='_compute_payment_amount', store=True, help='Computed payment amount based on rate')
    rate_changed_by = fields.Many2one('res.users', string='Rate Changed By', readonly=True, help='User who last changed the rate')
    rate_changed_date = fields.Datetime(string='Rate Changed Date', readonly=True, help='When the rate was last changed')
    sales_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True, help='Related sales order for payment')
    payment_status = fields.Selection([
        ('none', 'No Payment Required'),
        ('pending', 'Payment Pending'),
        ('paid', 'Payment Completed'),
        ('failed', 'Payment Failed'),
    ], string='Payment Status', default='none', tracking=True, compute='_compute_payment_status', store=True)
    payment_url = fields.Char(string='Payment URL', readonly=True, help='Customer portal payment link')
    payment_method = fields.Selection([
        ('online', 'Online Payment'),
        ('cash', 'Cash Payment'),
        ('bank_transfer', 'Bank Transfer'),
        ('check', 'Check'),
    ], string='Payment Method', readonly=True, help='Method used for payment')
    cash_collected_by = fields.Many2one('res.users', string='Cash Collected By', readonly=True, help='User who collected cash payment')
    cash_collection_date = fields.Datetime(string='Cash Collection Date', readonly=True, help='When cash was collected')
    # ✨ AI Response Fields
    ai_response = fields.Html(string='AI Response', help='AI-generated response based on record data')
    ai_response_generated = fields.Boolean(string='AI Response Generated', default=False)
    ai_response_pdf = fields.Binary(string='AI Response PDF')
    ai_response_pdf_filename = fields.Char(string='AI Response PDF Filename')
    ai_last_prompt = fields.Text(string='Last AI Prompt', help='Last prompt sent to AI')
    ai_response_last_updated = fields.Datetime(string='AI Response Last Updated', help='When AI response was last modified')


    @api.depends('payment_rate')
    def _compute_payment_amount(self):
        """Compute payment amount based on rate"""
        for record in self:
            record.payment_amount = record.payment_rate

    def _compute_payment_status(self):
        """Compute payment status based on sales order state"""
        for record in self:
            # Store old payment status to detect changes
            old_payment_status = record.payment_status

            if not record.sales_order_id:
                record.payment_status = 'none'
                continue

            # Get all posted invoices from the sales order
            invoices = record.sales_order_id.invoice_ids.filtered(lambda inv: inv.state == 'posted' and inv.move_type == 'out_invoice')

            if not invoices:
                # No invoices yet
                if record.sales_order_id.state in ['sale', 'done']:
                    record.payment_status = 'pending'
                else:
                    record.payment_status = 'none'
            else:
                # Check payment status of invoices
                total_amount = sum(invoices.mapped('amount_total'))
                paid_amount = sum(invoices.mapped('amount_residual_signed'))

                if paid_amount <= 0:  # Fully paid (residual is 0 or negative)
                    record.payment_status = 'paid'
                elif paid_amount < total_amount:  # Partially paid
                    record.payment_status = 'pending'
                else:  # Not paid
                    record.payment_status = 'pending'

            # Auto-generate AI response and PDF when payment becomes "paid"
            if old_payment_status != 'paid' and record.payment_status == 'paid':
                record._auto_generate_ai_response_and_pdf()

    def _auto_generate_ai_response_and_pdf(self):
        """Automatically generate AI response and PDF when payment is completed"""
        try:
            # Generate AI response if not already generated
            if not self.ai_response or not self.ai_response_generated:
                self.action_get_ai_response()

            # Generate PDF if not already generated or if AI response was updated
            if not self.ai_response_pdf or self.ai_response:
                self.action_generate_ai_pdf()

        except Exception as e:
            # Log error but don't fail the payment process
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Failed to auto-generate AI response and PDF for {self.name}: {str(e)}")

    @api.model
    def create(self, vals):
        """Override create to automatically link customer"""
        # Auto-link customer based on email if not already set
        if not vals.get('partner_id') and vals.get('customer_email'):
            partner = self._find_or_create_partner(vals.get('customer_email'), vals.get('customer_name'))
            if partner:
                vals['partner_id'] = partner.id

        return super().create(vals)

    def _find_or_create_partner(self, email, name=None):
        """Find existing partner by email or create new one"""
        if not email:
            return False

        # Search for existing partner
        partner = self.env['res.partner'].search([('email', '=', email)], limit=1)

        if not partner and name:
            # Create new partner if not found
            partner = self.env['res.partner'].create({
                'name': name or email,
                'email': email,
                'is_company': False,
                'customer_rank': 1,  # Mark as customer
            })

        return partner

    def _create_sales_order(self):
        """Create sales order for payment request"""
        if self.sales_order_id:
            return self.sales_order_id

        # Ensure we have a partner
        if not self.partner_id:
            partner = self._find_or_create_partner(self.customer_email, self.customer_name)
            if partner:
                self.partner_id = partner.id

        if not self.partner_id:
            raise ValueError("Cannot create sales order without customer information")

        # Create sales order
        order_vals = {
            'partner_id': self.partner_id.id,
            'origin': self.name,
            'order_line': [(0, 0, {
                'name': f'Service for {self.name}',
                'product_uom_qty': 1,
                'price_unit': self.payment_amount,
            })],
        }

        # Try to find a service product, or create a generic one
        service_product = self.env['product.product'].search([
            ('type', '=', 'service'),
            ('sale_ok', '=', True)
        ], limit=1)

        if not service_product:
            # Create a generic service product
            service_product = self.env['product.product'].create({
                'name': 'Generic Service',
                'type': 'service',
                'sale_ok': True,
                'purchase_ok': False,
                'list_price': 0.0,
            })

        order_vals['order_line'][0][2]['product_id'] = service_product.id

        sales_order = self.env['sale.order'].create(order_vals)
        self.sales_order_id = sales_order.id

        return sales_order

    @api.onchange('payment_rate')
    def _onchange_payment_rate(self):
        """Track rate changes"""
        if self.payment_rate != self._origin.payment_rate:
            self.rate_changed_by = self.env.user
            self.rate_changed_date = fields.Datetime.now()

    def write(self, vals):
        """Override write to track rate changes"""
        if 'payment_rate' in vals:
            vals.update({
                'rate_changed_by': self.env.user.id,
                'rate_changed_date': fields.Datetime.now(),
            })
        return super().write(vals)
    def action_feedback_submitted_to_feedback_under_review(self):
        """Move from Feedback Submitted to Under Review"""
        self.state = 'feedback_under_review'
    def action_feedback_under_review_to_feedback_responded(self):
        """Move from Under Review to Responded"""
        self.state = 'feedback_responded'

    def action_request_payment(self):
        """Request payment from customer"""
        self.ensure_one()

        # Validate payment rate is set (can be 0)
        if not hasattr(self, 'payment_rate'):
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Rate Not Set',
                    'message': 'Payment rate must be set before requesting payment.',
                    'type': 'warning',
                }
            }

        # Find payment stage in workflow (if workflow module is available)
        try:
            if 'workflow.state' in self.env:
                payment_states = self.env['workflow.state'].search([
                    ('template_id.name', '=', 'Customer Feedback System'),
                    ('is_payment_stage', '=', True)
                ])

                if payment_states:
                    payment_state = payment_states[0]
                    # Execute payment request with current rate
                    payment_state._request_payment(self)
                else:
                    # Fallback: Create sales order directly
                    self._create_sales_order()
            else:
                # Fallback: Create sales order directly
                self._create_sales_order()
        except Exception:
            # Fallback: Create sales order directly
            self._create_sales_order()

        # Update payment status
        self.payment_status = 'pending'

        # Generate payment URL
        if self.sales_order_id:
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            self.payment_url = base_url + "/my/orders/" + str(self.sales_order_id.id)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Payment Requested',
                'message': 'Payment request sent for amount: ' + str(self.payment_amount),
                'type': 'success',
            }
        }

    def action_view_sales_order(self):
        """View related sales order"""
        self.ensure_one()

        if not self.sales_order_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Sales Order',
                    'message': 'No sales order found for this record.',
                    'type': 'warning',
                }
            }

        return {
            'name': 'Sales Order',
            'type': 'ir.actions.act_window',
            'res_model': 'sale.order',
            'res_id': self.sales_order_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_open_payment_portal(self):
        """Open customer payment portal"""
        self.ensure_one()

        if not self.payment_url:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Payment URL',
                    'message': 'Payment URL not available. Please request payment first.',
                    'type': 'warning',
                }
            }

        return {
            'type': 'ir.actions.act_url',
            'url': self.payment_url,
            'target': 'new',
        }

    def action_record_cash_payment(self):
        """Record cash payment for this record"""
        self.ensure_one()

        if not self.sales_order_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Sales Order',
                    'message': 'Please request payment first to create a sales order.',
                    'type': 'warning',
                }
            }

        if self.payment_status == 'paid':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Already Paid',
                    'message': 'This order has already been paid.',
                    'type': 'warning',
                }
            }

        # Confirm sales order if not already confirmed
        if self.sales_order_id.state == 'draft':
            self.sales_order_id.action_confirm()

        # For service products, mark as delivered to enable invoicing
        for line in self.sales_order_id.order_line:
            if line.product_id.type == 'service':
                line.qty_delivered = line.product_uom_qty

        # Create invoice if not already created
        if not self.sales_order_id.invoice_ids:
            # Check if we can create invoice
            if self.sales_order_id._get_invoiceable_lines():
                invoice = self.sales_order_id._create_invoices()
                if invoice:
                    invoice.action_post()
                else:
                    # If invoice creation fails, create manual invoice
                    invoice = self._create_manual_invoice()
            else:
                # Create manual invoice if no invoiceable lines
                invoice = self._create_manual_invoice()
        else:
            invoice = self.sales_order_id.invoice_ids[0]
            if invoice.state == 'draft':
                invoice.action_post()

        # Register cash payment
        payment_method_line = self._get_cash_payment_method()

        payment_vals = {
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': self.sales_order_id.partner_id.id,
            'amount': self.sales_order_id.amount_total,
            'currency_id': self.sales_order_id.currency_id.id,
            'payment_method_line_id': payment_method_line.id,
            'journal_id': payment_method_line.journal_id.id,  # ✅ CRITICAL FIX
            'ref': f'Cash payment for {self.name}',
            'date': fields.Date.context_today(self),
        }

        payment = self.env['account.payment'].create(payment_vals)
        payment.action_post()

        # Reconcile payment with invoice
        invoice = self.sales_order_id.invoice_ids[0]
        payment_lines = payment.line_ids.filtered(lambda line: line.account_id == payment.destination_account_id)
        invoice_lines = invoice.line_ids.filtered(lambda line: line.account_id == payment.destination_account_id)
        (payment_lines + invoice_lines).reconcile()

        # Update payment tracking fields
        self.write({
            'payment_method': 'cash',
            'cash_collected_by': self.env.user.id,
            'cash_collection_date': fields.Datetime.now(),
        })

        # Integrate with AI Cash Management System
        cash_collection_record = self._integrate_with_cash_management(payment)

        message = f'Cash payment of {self.sales_order_id.amount_total} {self.sales_order_id.currency_id.name} has been recorded successfully.'
        if cash_collection_record:
            message += f' Cash collection record {cash_collection_record.name} created in Cash Management System.'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Cash Payment Recorded',
                'message': message,
                'type': 'success',
            }
        }

    def _get_cash_payment_method(self):
        """Get cash payment method"""
        # First try to find existing cash journal
        cash_journal = self.env['account.journal'].search([
            ('type', '=', 'cash'),
            ('company_id', '=', self.env.company.id)
        ], limit=1)

        if not cash_journal:
            # Create a cash journal if none exists
            cash_journal = self.env['account.journal'].create({
                'name': 'Cash',
                'type': 'cash',
                'code': 'CSH1',
                'company_id': self.env.company.id,
            })

        # Ensure the journal is properly configured
        if not cash_journal.default_account_id:
            # Create cash account if it doesn't exist
            cash_account = self.env['account.account'].search([
                ('account_type', '=', 'asset_cash'),
                ('company_id', '=', self.env.company.id)
            ], limit=1)

            if not cash_account:
                cash_account = self.env['account.account'].create({
                    'name': 'Cash',
                    'code': '101001',
                    'account_type': 'asset_cash',
                    'company_id': self.env.company.id,
                })

            cash_journal.default_account_id = cash_account.id

        # Get or create the payment method line for cash
        payment_method_line = self.env['account.payment.method.line'].search([
            ('journal_id', '=', cash_journal.id),
            ('payment_method_id.payment_type', '=', 'inbound')
        ], limit=1)

        if not payment_method_line:
            # Get the manual payment method
            manual_in = self.env.ref('account.account_payment_method_manual_in', raise_if_not_found=False)
            if not manual_in:
                # Create manual payment method if it doesn't exist
                manual_in = self.env['account.payment.method'].create({
                    'name': 'Manual',
                    'code': 'manual',
                    'payment_type': 'inbound',
                })

            payment_method_line = self.env['account.payment.method.line'].create({
                'journal_id': cash_journal.id,
                'payment_method_id': manual_in.id,
                'name': 'Manual',
            })

        return payment_method_line

    def _integrate_with_cash_management(self, payment):
        """Integrate cash payment with AI Cash Management System"""
        try:
            # Check if AI Cash Management System is installed
            if 'ai.cash.management.cash.collection.simple' not in self.env:
                return False

            cash_collection_model = self.env['ai.cash.management.cash.collection.simple']
            cash_collector_model = self.env['ai.cash.management.cash.collector.simple']

            # Get or create a cash collector for the current user
            collector = cash_collector_model.search([('user_id', '=', self.env.user.id)], limit=1)
            if not collector:
                # Create a collector for the current user
                collector = cash_collector_model.create({
                    'name': self.env.user.name,
                    'user_id': self.env.user.id,
                    'phone': self.env.user.phone or '',
                    'state': 'active',
                })

            # Create cash collection record
            collection_vals = {
                'collector_id': collector.id,
                'customer_name': self.sales_order_id.partner_id.name,
                'customer_phone': self.sales_order_id.partner_id.phone or '',
                'customer_email': self.sales_order_id.partner_id.email or '',
                'amount': payment.amount,
                'payment_method': 'cash',
                'source_reference': f"{self.name} - {self.sales_order_id.name}",
                'description': f"Cash payment for {self.name}",
                'notes': f"Collected via Record Cash Payment feature. Payment ID: {payment.id}",
                'collection_date': fields.Datetime.now(),
                'state': 'confirmed',  # Auto-confirm since payment is already recorded
            }

            cash_collection = cash_collection_model.create(collection_vals)

            # Auto-submit to manager if collector is not a manager
            try:
                if not self.env.user.has_group('ai_cash_management.group_branch_manager'):
                    cash_collection.action_submit()
            except:
                # If group doesn't exist, just continue
                pass

            return cash_collection

        except Exception as e:
            # Log the error but don't fail the payment process
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"Failed to integrate with Cash Management System: {str(e)}")
            return False

    def _create_manual_invoice(self):
        """Create manual invoice when automatic creation fails"""
        invoice_vals = {
            'move_type': 'out_invoice',
            'partner_id': self.sales_order_id.partner_id.id,
            'invoice_origin': self.sales_order_id.name,
            'invoice_line_ids': [(0, 0, {
                'name': f'Payment for {self.name}',
                'quantity': 1,
                'price_unit': self.payment_amount,
                'account_id': self._get_income_account().id,
            })],
        }

        invoice = self.env['account.move'].create(invoice_vals)
        invoice.action_post()

        # Link invoice to sales order
        self.sales_order_id.invoice_ids = [(4, invoice.id)]

        return invoice

    def _get_income_account(self):
        """Get income account for manual invoice"""
        # Try to get income account from company
        company = self.env.company
        income_account = company.account_sale_tax_id.invoice_repartition_line_ids.filtered(
            lambda line: line.repartition_type == 'base'
        ).account_id

        if not income_account:
            # Fallback to finding any income account
            income_account = self.env['account.account'].search([
                ('account_type', '=', 'income'),
                ('company_id', '=', company.id)
            ], limit=1)

        if not income_account:
            # Create a basic income account if none exists
            income_account = self.env['account.account'].create({
                'name': 'Service Income',
                'code': '400001',
                'account_type': 'income',
                'company_id': company.id,
            })

        return income_account

    def action_refresh_payment_status(self):
        """Manually trigger payment status computation"""
        # Force refresh by invalidating cache and recomputing
        self.invalidate_recordset(['payment_status'])
        self._compute_payment_status()

        # Get human-readable status
        status_dict = dict(self._fields["payment_status"].selection)
        current_status = status_dict.get(self.payment_status, self.payment_status)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Payment Status Refreshed',
                'message': f'Payment status updated to: {current_status}',
                'type': 'success',
            }
        }

    @api.model
    def _cron_update_payment_status(self):
        """Cron job to update payment status for pending payments"""
        pending_records = self.search([('payment_status', 'in', ['pending', 'none'])])
        for record in pending_records:
            old_status = record.payment_status
            # Force refresh by invalidating cache
            record.invalidate_recordset(['payment_status'])
            record._compute_payment_status()
            if record.payment_status != old_status:
                status_dict = dict(record._fields["payment_status"].selection)
                old_status_label = status_dict.get(old_status, old_status)
                new_status_label = status_dict.get(record.payment_status, record.payment_status)
                record.message_post(
                    body=f"Payment status automatically updated from {old_status_label} to {new_status_label}",
                    subject="Payment Status Update"
                )

    # ✨ Generic Display Methods for Selection and Many2one Fields
    def get_field_display(self, field_name):
        """Get human-readable value for any selection or many2one field"""
        if not hasattr(self, field_name):
            return ''

        field_value = getattr(self, field_name)
        if not field_value:
            return ''

        field_obj = self._fields.get(field_name)
        if not field_obj:
            return str(field_value)

        # Handle Selection fields
        if hasattr(field_obj, 'selection') and field_obj.selection:
            if callable(field_obj.selection):
                # Dynamic selection
                selection_list = field_obj.selection(self)
            else:
                # Static selection
                selection_list = field_obj.selection

            selection_dict = dict(selection_list)
            return selection_dict.get(field_value, str(field_value))

        # Handle Many2one fields
        elif hasattr(field_obj, 'comodel_name'):
            if hasattr(field_value, 'name'):
                return field_value.name
            elif hasattr(field_value, 'display_name'):
                return field_value.display_name
            else:
                return str(field_value)

        # Default: return string representation
        return str(field_value)

    def get_state_display(self):
        """Get human-readable state value"""
        return self.get_field_display('state')

    def get_payment_status_display(self):
        """Get human-readable payment status value"""
        return self.get_field_display('payment_status')

    # ✨ AI Response Auto-Regeneration Methods
    @api.onchange('ai_response')
    def _onchange_ai_response(self):
        """Clear PDF when AI response is manually updated"""
        if self.ai_response and self.ai_response_pdf:
            # Mark that PDF needs regeneration
            self.ai_response_pdf = False
            self.ai_response_pdf_filename = False

    def write(self, vals):
        """Override write to regenerate PDF when AI response is updated"""
        result = super().write(vals)

        # If AI response was updated, regenerate PDF automatically
        if 'ai_response' in vals and vals.get('ai_response'):
            for record in self:
                if record.ai_response:
                    try:
                        # Update the last updated timestamp
                        record.ai_response_last_updated = fields.Datetime.now()
                        # Regenerate PDF with current AI response content
                        record.action_generate_ai_pdf()
                    except Exception as e:
                        # Log error but don't fail the write operation
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.warning(f"Failed to auto-regenerate PDF for {record.name}: {str(e)}")

        return result

    # ✨ AI Response Methods
    def action_get_ai_response(self):
        """Get AI response for this record"""
        self.ensure_one()

        # Get AI prompt configuration
        ai_prompt_configs = self.env['ai.prompt.config'].search([
            ('form_builder_id.code', '=', 'customer_feedback_main'),
            ('prompt_type', '=', 'system_prompt'),
            ('active', '=', True)
        ], limit=1)

        if not ai_prompt_configs:
            # Create a fallback AI response if no configuration is found
            fallback_response = self._generate_fallback_ai_response()
            self.write({
                'ai_response': fallback_response,
                'ai_response_generated': True,
                'ai_last_prompt': 'Fallback response - no AI configuration found',
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'AI Response Generated',
                    'message': 'AI response has been generated using fallback method.',
                    'type': 'success',
                }
            }

        ai_config = ai_prompt_configs[0]

        try:
            # Prepare record data for AI prompt
            record_data = self._prepare_ai_data()

            # Execute AI prompt
            ai_response = ai_config.execute_prompt(record_data)

            # Store AI response
            self.write({
                'ai_response': ai_response,
                'ai_response_generated': True,
                'ai_last_prompt': ai_config._format_prompt(record_data),
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'AI Response Generated',
                    'message': 'AI response has been generated successfully.',
                    'type': 'success',
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'AI Error',
                    'message': f'Failed to generate AI response: {str(e)}',
                    'type': 'danger',
                }
            }

    def _prepare_ai_data(self):
        """Prepare record data for AI prompt"""
        self.ensure_one()

        data = {
            'name': self.name or '',
            'state': dict(self._fields['state'].selection).get(self.state, self.state),
            'create_date': self.create_date.strftime('%Y-%m-%d %H:%M:%S') if self.create_date else '',
        }

        # Add all form fields dynamically
        for field_name in ['customer_name', 'customer_email', 'overall_rating', 'assigned_user', 'priority', 'feedback_tags', 'feedback_comments']:
            if hasattr(self, field_name):
                field_value = getattr(self, field_name, '')
                if field_value:
                    # Handle different field types
                    if hasattr(field_value, 'name'):  # Many2one
                        data[field_name] = field_value.name
                    elif hasattr(field_value, 'mapped'):  # Many2many/One2many
                        data[field_name] = ', '.join(field_value.mapped('name'))
                    else:
                        data[field_name] = str(field_value)
                else:
                    data[field_name] = ''

        return data

    def _generate_fallback_ai_response(self):
        """Generate a fallback AI response when no AI configuration is available"""
        self.ensure_one()

        # Create a professional response based on the form data
        response_parts = [
            f"<h2>Thank you for your submission!</h2>",
            "<p>We have received your form submission and appreciate you taking the time to provide this information.</p>",
            "<h3>Submission Summary:</h3>",
            "<ul>"
        ]

        # Add form field data dynamically
        for field_name in ['customer_name', 'customer_email', 'overall_rating', 'assigned_user', 'priority', 'feedback_tags', 'feedback_comments']:
            if hasattr(self, field_name):
                field_value = getattr(self, field_name, '')
                if field_value:
                    field_label = field_name.replace('_', ' ').title()
                    # Handle different field types
                    if hasattr(field_value, 'name'):  # Many2one
                        response_parts.append(f"<li><strong>{field_label}:</strong> {field_value.name}</li>")
                    elif hasattr(field_value, 'mapped'):  # Many2many/One2many
                        values_str = ', '.join(field_value.mapped('name'))
                        response_parts.append(f"<li><strong>{field_label}:</strong> {values_str}</li>")
                    else:
                        response_parts.append(f"<li><strong>{field_label}:</strong> {str(field_value)}</li>")

        response_parts.extend([
            "</ul>",
            "<h3>Our Response:</h3>",
            "<p>Your submission is valuable to us and helps us improve our services. We are committed to addressing your needs and will take appropriate action based on your input.</p>",
            "<p>If you have any urgent matters or need immediate assistance, please don't hesitate to contact our support team.</p>",
            "<p><strong>Thank you for choosing our services!</strong></p>",
            "<hr>",
            f"<p><small>Reference: {self.name} | Date: {self.create_date.strftime('%Y-%m-%d') if self.create_date else 'N/A'}</small></p>"
        ])

        return '\n'.join(response_parts)

    def action_generate_ai_pdf(self):
        """Generate PDF from AI response"""
        self.ensure_one()

        if not self.ai_response:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No AI Response',
                    'message': 'Please generate AI response first.',
                    'type': 'warning',
                }
            }

        try:
            # Generate PDF from AI response
            pdf_content = self._generate_pdf_from_html(self.ai_response)
            filename = f"ai_response_{self.name or 'record'}.pdf"

            self.write({
                'ai_response_pdf': pdf_content,
                'ai_response_pdf_filename': filename,
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'PDF Generated',
                    'message': f'PDF has been generated: {filename}',
                    'type': 'success',
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'PDF Error',
                    'message': f'Failed to generate PDF: {str(e)}',
                    'type': 'danger',
                }
            }

    def _generate_pdf_from_html(self, html_content):
        """Generate PDF from HTML content using Odoo's built-in report system"""
        import base64
        import logging
        import tempfile
        import os
        _logger = logging.getLogger(__name__)

        try:
            # Create a simple HTML template for PDF generation
            html_template = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI Response Document</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .content {
            margin: 20px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        h2, h3 {
            color: #007bff;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI Response Document</h1>
    </div>
    <div class="content">
        {html_content}
    </div>
    <div class="footer">
        <p>Generated on: {self.create_date.strftime('%Y-%m-%d %H:%M:%S') if self.create_date else 'N/A'}</p>
        <p>Reference: {self.name}</p>
        <p>© AI Generated Document</p>
    </div>
</body>
</html>
            '''

            # Use Odoo's report system to generate PDF
            IrActionsReport = self.env['ir.actions.report']

            try:
                # Generate PDF using wkhtmltopdf
                pdf_content = IrActionsReport._run_wkhtmltopdf(
                    [html_template],
                    landscape=False,
                    specific_paperformat_args={
                        'data-report-margin-top': 15,
                        'data-report-margin-bottom': 15,
                        'data-report-margin-left': 10,
                        'data-report-margin-right': 10,
                        'data-report-page-size': 'A4',
                        'data-report-orientation': 'Portrait'
                    }
                )

                if pdf_content:
                    return base64.b64encode(pdf_content)
                else:
                    raise Exception("PDF content is empty")

            except Exception as e:
                raise e

        except Exception as e1:
            _logger.error(f"Odoo PDF generation failed: {str(e1)}")

            # Fallback: Create a very simple PDF using basic method
            try:
                import subprocess
                import tempfile
                import os

                # Create simple HTML content
                simple_html = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>AI Response Document</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 20px;">
    <h1 style="color: #007bff; text-align: center;">AI Response Document</h1>
    <hr>
    <div>
        {html_content.replace('<', '&lt;').replace('>', '&gt;') if '<script' in html_content.lower() else html_content}
    </div>
    <hr>
    <p style="text-align: center; font-size: 12px; color: #666;">
        Generated: {self.create_date.strftime('%Y-%m-%d') if self.create_date else 'N/A'} |
        Reference: {self.name}
    </p>
</body>
</html>
                '''

                # Write to temporary file
                with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as html_file:
                    html_file.write(simple_html)
                    html_file_path = html_file.name

                # Generate PDF using wkhtmltopdf command directly
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as pdf_file:
                    pdf_file_path = pdf_file.name

                try:
                    # Run wkhtmltopdf
                    cmd = [
                        'wkhtmltopdf',
                        '--page-size', 'A4',
                        '--margin-top', '15mm',
                        '--margin-bottom', '15mm',
                        '--margin-left', '10mm',
                        '--margin-right', '10mm',
                        '--encoding', 'UTF-8',
                        '--quiet',
                        html_file_path,
                        pdf_file_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                    if result.returncode == 0 and os.path.exists(pdf_file_path):
                        # Read the generated PDF
                        with open(pdf_file_path, 'rb') as pdf_file:
                            pdf_content = pdf_file.read()

                        # Clean up
                        os.unlink(html_file_path)
                        os.unlink(pdf_file_path)

                        if pdf_content:
                            return base64.b64encode(pdf_content)
                        else:
                            raise Exception("Generated PDF is empty")
                    else:
                        raise Exception(f"wkhtmltopdf failed: {result.stderr}")

                except Exception as e:
                    # Clean up on error
                    if os.path.exists(html_file_path):
                        os.unlink(html_file_path)
                    if os.path.exists(pdf_file_path):
                        os.unlink(pdf_file_path)
                    raise e

            except Exception as e2:
                _logger.error(f"Fallback PDF generation failed: {str(e2)}")

                # Last resort: Create a minimal text-based response
                error_html = f'''
                <h1>PDF Generation Error</h1>
                <p>We apologize, but there was an issue generating your PDF document.</p>
                <p>Please contact support with reference: {self.name}</p>
                <p>Error details: PDF generation system temporarily unavailable</p>
                '''

                # Return a simple base64 encoded error message instead of failing completely
                return base64.b64encode(error_html.encode('utf-8'))
