from odoo import http
from odoo.http import request
import json


class CustomerFeedbackSystemController(http.Controller):

    @http.route('/customer_feedback', type='http', auth='public', website=True)
    def index(self, **kwargs):
        """Main page for Customer Feedback System"""
        return request.render('ovakil_customer_feedback.index', {})

    @http.route(['/customer-feedback-main', '/customer-feedback-main/step/<string:step>'], type='http', auth='public', website=True, methods=['GET', 'POST'])
    def customer_feedback_main_form(self, step='form_fill', record_id=None, **kwargs):
        """Enhanced step-by-step form page"""
        # Handle POST requests (form submissions)
        if request.httprequest.method == 'POST':
            return self._handle_step_submission(step, record_id, **kwargs)
        # Get form configuration
        form_config = request.env['ovakil_customer_feedback.form_configuration'].sudo().search([], limit=1)
        if not form_config:
            form_config = request.env['ovakil_customer_feedback.form_configuration'].sudo().create({
                'name': 'Customer Feedback Form',
                'payment_required': True,
                'payment_type': 'admin_approval',
                'require_login': True,
                'enable_review_step': True,
            })

        # Check if user is logged in
        current_user = request.env.user
        is_logged_in = current_user and current_user.id != request.env.ref('base.public_user').id

        # Handle login requirement
        if form_config.require_login and not is_logged_in and step != 'login':
            step = 'login'

        # Get existing record if editing
        record = None
        if record_id:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo().browse(int(record_id))
            if not record.exists():
                record = None

        # Get form data
        users = request.env['res.users'].sudo().search([
            ('active', '=', True),
            ('share', '=', False)
        ])

        project_tags = request.env['project.tags'].sudo().search([])
        if not project_tags:
            default_tags = [
                'Service Quality', 'Product Quality', 'Customer Support',
                'Delivery', 'Pricing', 'User Experience'
            ]
            for tag_name in default_tags:
                request.env['project.tags'].sudo().create({'name': tag_name})
            project_tags = request.env['project.tags'].sudo().search([])

        # Get FAQs
        faqs = form_config.faq_ids.filtered('active')

        # Get available addons
        addons = form_config.addon_ids.filtered('active')

        return request.render('ovakil_customer_feedback.customer_feedback_main_step_form', {
            'form_config': form_config,
            'current_step': step,
            'users': users,
            'project_tags': project_tags,
            'faqs': faqs,
            'addons': addons,
            'is_logged_in': is_logged_in,
            'current_user': current_user,
            'record': record,
        })

    @http.route('/customer-feedback-main/submit', type='http', auth='public', website=True, methods=['POST'], csrf=False)
    def customer_feedback_main_submit(self, **kwargs):
        """Submit Customer Feedback Form form"""
        try:
            # Create record from form data
            customer_feedback_main_model = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo()
            values = {}

            # Extract and process form fields
            if 'customer_name' in kwargs and kwargs['customer_name']:
                values['customer_name'] = kwargs['customer_name']
            if 'customer_email' in kwargs and kwargs['customer_email']:
                values['customer_email'] = kwargs['customer_email']
            if 'overall_rating' in kwargs and kwargs['overall_rating']:
                values['overall_rating'] = kwargs['overall_rating']
            if 'assigned_user' in kwargs and kwargs['assigned_user']:
                try:
                    values['assigned_user'] = int(kwargs['assigned_user'])
                except (ValueError, TypeError):
                    pass  # Skip invalid values
            if 'priority' in kwargs and kwargs['priority']:
                values['priority'] = kwargs['priority']
            if 'feedback_tags' in kwargs and kwargs['feedback_tags']:
                try:
                    if isinstance(kwargs['feedback_tags'], list):
                        values['feedback_tags'] = [(6, 0, [int(x) for x in kwargs['feedback_tags']])]
                    else:
                        ids = [int(x.strip()) for x in kwargs['feedback_tags'].split(',') if x.strip()]
                        values['feedback_tags'] = [(6, 0, ids)]
                except (ValueError, TypeError):
                    pass  # Skip invalid values
            if 'feedback_comments' in kwargs and kwargs['feedback_comments']:
                values['feedback_comments'] = kwargs['feedback_comments']

            # Handle missing selection fields with defaults
            if 'overall_rating' not in values or not values.get('overall_rating'):
                if 'rating' in 'overall_rating'.lower():
                    values['overall_rating'] = '3'  # Default to 3 stars
                elif 'priority' in 'overall_rating'.lower():
                    values['overall_rating'] = 'medium'  # Default to medium priority
                elif 'status' in 'overall_rating'.lower() or 'state' in 'overall_rating'.lower():
                    values['overall_rating'] = 'draft'  # Default to draft status
                else:
                    values['overall_rating'] = 'option1'  # Default to first option
            if 'priority' not in values or not values.get('priority'):
                if 'rating' in 'priority'.lower():
                    values['priority'] = '3'  # Default to 3 stars
                elif 'priority' in 'priority'.lower():
                    values['priority'] = 'medium'  # Default to medium priority
                elif 'status' in 'priority'.lower() or 'state' in 'priority'.lower():
                    values['priority'] = 'draft'  # Default to draft status
                else:
                    values['priority'] = 'option1'  # Default to first option

            # Add required name field if not present
            if 'name' in kwargs:
                values['name'] = kwargs['name']
            elif 'customer_name' in values:
                values['name'] = values['customer_name']
            else:
                values['name'] = 'Anonymous' 

            # Create the record
            record = customer_feedback_main_model.create(values)

            return request.render('ovakil_customer_feedback.customer_feedback_main_success', {
                'record': record
            })
        except Exception as e:
            # Return simple error response
            return request.make_response(
                f'<html><body><h1>Error</h1><p>Form submission failed: {str(e)}</p><a href="/customer-feedback-main">Back to Form</a></body></html>',
                status=500
            )



    def _handle_step_submission(self, step, record_id=None, **kwargs):
        """Handle form submission for each step"""
        try:
            current_user = request.env.user
            is_logged_in = current_user and current_user.id != request.env.ref('base.public_user').id

            if step == 'form_fill':
                # Save form data and move to review
                if not is_logged_in:
                    return request.redirect('/web/login?redirect=/customer-feedback-main/step/form_fill')

                # Create or update record
                if record_id:
                    record = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo().browse(int(record_id))
                    if record.exists():
                        record.write(self._extract_form_values(kwargs))
                    else:
                        record = self._create_form_record(kwargs)
                else:
                    record = self._create_form_record(kwargs)

                # Move to review step
                record.form_step = 'review'
                return request.redirect(f'/customer-feedback-main/step/review?record_id={record.id}')

            elif step == 'review':
                # Handle review step - either edit or proceed to payment
                record = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo().browse(int(record_id))

                if 'edit' in kwargs:
                    # Go back to form fill for editing
                    record.form_step = 'form_fill'
                    return request.redirect(f'/customer-feedback-main/step/form_fill?record_id={record.id}')
                else:
                    # Proceed to payment/completion
                    form_config = record.form_config_id
                    if form_config.payment_required:
                        record.form_step = 'payment'
                        if form_config.payment_type == 'auto':
                            # Auto payment - create sales order immediately
                            record._create_sales_order()
                            return request.redirect(f'/customer-feedback-main/step/payment?record_id={record.id}')
                        else:
                            # Admin approval required
                            record.form_step = 'complete'
                            return request.redirect(f'/customer-feedback-main/step/complete?record_id={record.id}')
                    else:
                        # No payment required
                        record.form_step = 'complete'
                        return request.redirect(f'/customer-feedback-main/step/complete?record_id={record.id}')

            elif step == 'payment':
                # Handle payment step
                record = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo().browse(int(record_id))
                # Payment will be handled by the payment provider
                record.form_step = 'complete'
                return request.redirect(f'/customer-feedback-main/step/complete?record_id={record.id}')

            else:
                return request.redirect('/customer-feedback-main')

        except Exception as e:
            return request.make_response(
                f'<html><body><h1>Error</h1><p>Step processing failed: {str(e)}</p><a href="/customer-feedback-main">Back to Form</a></body></html>',
                status=500
            )

    def _extract_form_values(self, kwargs):
        """Extract form values from kwargs"""
        values = {}

        # Extract and process form fields
        if 'customer_name' in kwargs and kwargs['customer_name']:
            values['customer_name'] = kwargs['customer_name']
        if 'customer_email' in kwargs and kwargs['customer_email']:
            values['customer_email'] = kwargs['customer_email']
        if 'overall_rating' in kwargs and kwargs['overall_rating']:
            values['overall_rating'] = kwargs['overall_rating']
        if 'assigned_user' in kwargs and kwargs['assigned_user']:
            try:
                values['assigned_user'] = int(kwargs['assigned_user'])
            except (ValueError, TypeError):
                pass
        if 'priority' in kwargs and kwargs['priority']:
            values['priority'] = kwargs['priority']
        if 'feedback_tags' in kwargs and kwargs['feedback_tags']:
            try:
                if isinstance(kwargs['feedback_tags'], list):
                    values['feedback_tags'] = [(6, 0, [int(x) for x in kwargs['feedback_tags']])]
                else:
                    ids = [int(x.strip()) for x in kwargs['feedback_tags'].split(',') if x.strip()]
                    values['feedback_tags'] = [(6, 0, ids)]
            except (ValueError, TypeError):
                pass
        if 'feedback_comments' in kwargs and kwargs['feedback_comments']:
            values['feedback_comments'] = kwargs['feedback_comments']

        # Handle selected addons
        if 'selected_addons' in kwargs:
            addon_ids = []
            if isinstance(kwargs['selected_addons'], list):
                addon_ids = [int(x) for x in kwargs['selected_addons'] if x.isdigit()]
            elif kwargs['selected_addons']:
                addon_ids = [int(kwargs['selected_addons'])]
            if addon_ids:
                values['selected_addon_ids'] = [(6, 0, addon_ids)]

        # Add required name field if not present
        if 'name' in kwargs:
            values['name'] = kwargs['name']
        elif 'customer_name' in values:
            values['name'] = values['customer_name']
        else:
            values['name'] = 'Anonymous'

        return values

    def _create_form_record(self, kwargs):
        """Create new form record"""
        values = self._extract_form_values(kwargs)

        # Set form step
        values['form_step'] = 'review'

        # Create the record
        record = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo().create(values)
        return record
