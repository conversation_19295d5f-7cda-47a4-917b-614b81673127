from odoo import http
from odoo.http import request
import json


class CustomerFeedbackSystemController(http.Controller):

    @http.route('/customer_feedback', type='http', auth='public', website=True)
    def index(self, **kwargs):
        """Main page for Customer Feedback System"""
        return request.render('ovakil_customer_feedback.index', {})

    @http.route('/customer-feedback-main', type='http', auth='public', website=True)
    def customer_feedback_main_form(self, **kwargs):
        """Form page for Customer Feedback Form"""
        # Check if user is logged in for review functionality
        current_user = request.env.user
        is_logged_in = current_user and current_user.id != request.env.ref('base.public_user').id

        # Get users for the assigned_user dropdown (only internal users)
        users = request.env['res.users'].sudo().search([
            ('active', '=', True),
            ('share', '=', False)  # Only internal users
        ])

        # Get or create project tags for feedback_tags dropdown
        project_tags = request.env['project.tags'].sudo().search([])
        if not project_tags:
            # Create default tags if none exist
            default_tags = [
                'Service Quality', 'Product Quality', 'Customer Support',
                'Delivery', 'Pricing', 'User Experience'
            ]
            for tag_name in default_tags:
                request.env['project.tags'].sudo().create({'name': tag_name})
            project_tags = request.env['project.tags'].sudo().search([])

        return request.render('ovakil_customer_feedback.customer_feedback_main_website_form', {
            'users': users,
            'project_tags': project_tags,
            'is_logged_in': is_logged_in,
            'current_user': current_user,
        })

    @http.route('/customer-feedback-main/submit', type='http', auth='public', website=True, methods=['POST'], csrf=False)
    def customer_feedback_main_submit(self, **kwargs):
        """Submit Customer Feedback Form form"""
        try:
            # Create record from form data
            customer_feedback_main_model = request.env['ovakil_customer_feedback.customer_feedback_main'].sudo()
            values = {}

            # Extract and process form fields
            if 'customer_name' in kwargs and kwargs['customer_name']:
                values['customer_name'] = kwargs['customer_name']
            if 'customer_email' in kwargs and kwargs['customer_email']:
                values['customer_email'] = kwargs['customer_email']
            if 'overall_rating' in kwargs and kwargs['overall_rating']:
                values['overall_rating'] = kwargs['overall_rating']
            if 'assigned_user' in kwargs and kwargs['assigned_user']:
                try:
                    values['assigned_user'] = int(kwargs['assigned_user'])
                except (ValueError, TypeError):
                    pass  # Skip invalid values
            if 'priority' in kwargs and kwargs['priority']:
                values['priority'] = kwargs['priority']
            if 'feedback_tags' in kwargs and kwargs['feedback_tags']:
                try:
                    if isinstance(kwargs['feedback_tags'], list):
                        values['feedback_tags'] = [(6, 0, [int(x) for x in kwargs['feedback_tags']])]
                    else:
                        ids = [int(x.strip()) for x in kwargs['feedback_tags'].split(',') if x.strip()]
                        values['feedback_tags'] = [(6, 0, ids)]
                except (ValueError, TypeError):
                    pass  # Skip invalid values
            if 'feedback_comments' in kwargs and kwargs['feedback_comments']:
                values['feedback_comments'] = kwargs['feedback_comments']

            # Handle missing selection fields with defaults
            if 'overall_rating' not in values or not values.get('overall_rating'):
                if 'rating' in 'overall_rating'.lower():
                    values['overall_rating'] = '3'  # Default to 3 stars
                elif 'priority' in 'overall_rating'.lower():
                    values['overall_rating'] = 'medium'  # Default to medium priority
                elif 'status' in 'overall_rating'.lower() or 'state' in 'overall_rating'.lower():
                    values['overall_rating'] = 'draft'  # Default to draft status
                else:
                    values['overall_rating'] = 'option1'  # Default to first option
            if 'priority' not in values or not values.get('priority'):
                if 'rating' in 'priority'.lower():
                    values['priority'] = '3'  # Default to 3 stars
                elif 'priority' in 'priority'.lower():
                    values['priority'] = 'medium'  # Default to medium priority
                elif 'status' in 'priority'.lower() or 'state' in 'priority'.lower():
                    values['priority'] = 'draft'  # Default to draft status
                else:
                    values['priority'] = 'option1'  # Default to first option

            # Add required name field if not present
            if 'name' in kwargs:
                values['name'] = kwargs['name']
            elif 'customer_name' in values:
                values['name'] = values['customer_name']
            else:
                values['name'] = 'Anonymous' 

            # Create the record
            record = customer_feedback_main_model.create(values)

            return request.render('ovakil_customer_feedback.customer_feedback_main_success', {
                'record': record
            })
        except Exception as e:
            # Return simple error response
            return request.make_response(
                f'<html><body><h1>Error</h1><p>Form submission failed: {str(e)}</p><a href="/customer-feedback-main">Back to Form</a></body></html>',
                status=500
            )
