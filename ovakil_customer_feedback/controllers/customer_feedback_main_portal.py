from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal
from odoo.exceptions import AccessError, MissingError
import logging

_logger = logging.getLogger(__name__)


class CustomerFeedbackMainPortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        """Add Customer Feedback Form count to portal home"""
        values = super()._prepare_home_portal_values(counters)

        if 'customer_feedback_main' in counters:
            partner = request.env.user.partner_id
            customer_feedback_main_count = request.env['ovakil_customer_feedback.customer_feedback_main'].search_count([
                '|', '|',
                ('customer_email', '=', partner.email),
                ('partner_id', '=', partner.id),
                ('create_uid', '=', request.env.user.id)
            ]) if partner.email else 0
            values['customer_feedback_main_count'] = customer_feedback_main_count

        return values

    @http.route(['/my/customer-feedback-main'], type='http', auth="user", website=True)
    def portal_customer_feedback_main_list(self, **kw):
        """Display Customer Feedback Form list in customer portal"""
        partner = request.env.user.partner_id

        if not partner.email:
            return request.render('portal.portal_my_home')

        # Base domain for user's records
        domain = [
            '|', '|',
            ('customer_email', '=', partner.email),
            ('partner_id', '=', partner.id),
            ('create_uid', '=', request.env.user.id)
        ]

        # Handle search functionality
        search_query = kw.get('search', '').strip()
        if search_query:
            search_domain = [
                '|', '|', '|',
                ('name', 'ilike', search_query),
                ('customer_name', 'ilike', search_query),
                ('customer_email', 'ilike', search_query),
                ('state', 'ilike', search_query)
            ]
            domain = ['&'] + domain + search_domain

        # Handle state filter
        state_filter = kw.get('state', '').strip()
        if state_filter:
            domain.append(('state', '=', state_filter))

        customer_feedback_main_records = request.env['ovakil_customer_feedback.customer_feedback_main'].search(domain, order='create_date desc')

        # Get available states for filter dropdown
        all_states = request.env['ovakil_customer_feedback.customer_feedback_main']._fields['state'].selection

        values = {
            'customer_feedback_main_records': customer_feedback_main_records,
            'page_name': 'Customer Feedback Form',
            'search_query': search_query,
            'state_filter': state_filter,
            'available_states': all_states,
        }

        return request.render('ovakil_customer_feedback.portal_customer_feedback_main_list', values)

    @http.route(['/my/customer-feedback-main/<int:record_id>'], type='http', auth="user", website=True)
    def portal_customer_feedback_main_detail(self, record_id, **kw):
        """Display Customer Feedback Form detail in customer portal"""
        try:
            customer_feedback_main_sudo = self._document_check_access('ovakil_customer_feedback.customer_feedback_main', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {
            'customer_feedback_main': customer_feedback_main_sudo,
            'page_name': 'Customer Feedback Form',
        }

        return request.render('ovakil_customer_feedback.portal_customer_feedback_main_detail', values)

    @http.route(['/my/customer-feedback-main/<int:record_id>/edit'], type='http', auth="user", website=True)
    def portal_customer_feedback_main_edit(self, record_id, **kw):
        """Edit Customer Feedback Form in customer portal (only for draft state)"""
        try:
            customer_feedback_main_sudo = self._document_check_access('ovakil_customer_feedback.customer_feedback_main', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if record is in initial state (editable) - feedback_submitted is the initial state
        editable_state = 'feedback_submitted'

        if customer_feedback_main_sudo.state != editable_state:
            return request.redirect('/my/customer-feedback-main/' + str(record_id))

        values = {
            'customer_feedback_main': customer_feedback_main_sudo,
            'page_name': 'Customer Feedback Form',
            'is_edit_mode': True,
        }

        return request.render('ovakil_customer_feedback.portal_customer_feedback_main_edit', values)

    @http.route(['/my/customer-feedback-main/<int:record_id>/update'], type='http', auth="user", website=True, methods=['POST'], csrf=False)
    def portal_customer_feedback_main_update(self, record_id, **kw):
        """Update Customer Feedback Form from customer portal"""
        try:
            customer_feedback_main_sudo = self._document_check_access('ovakil_customer_feedback.customer_feedback_main', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if record is in initial state (editable) - feedback_submitted is the initial state
        editable_state = 'feedback_submitted'

        if customer_feedback_main_sudo.state != editable_state:
            return request.redirect('/my/customer-feedback-main/' + str(record_id))

        # Extract form data and update record
        update_values = {}

        # Process form fields
        for field_name, field_value in kw.items():
            if field_name.startswith('csrf_token') or field_name in ['submit']:
                continue
            if field_value:
                update_values[field_name] = field_value

        # Update the record
        if update_values:
            customer_feedback_main_sudo.sudo().write(update_values)

        return request.redirect('/my/customer-feedback-main/' + str(record_id) + '?updated=1')

    @http.route(['/my/customer-feedback-main/<int:record_id>/download-pdf'], type='http', auth="user", website=True)
    def portal_customer_feedback_main_download_pdf(self, record_id, **kw):
        """Download AI Response PDF for paid customers"""
        try:
            customer_feedback_main_sudo = self._document_check_access('ovakil_customer_feedback.customer_feedback_main', record_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        # Check if customer has paid
        if customer_feedback_main_sudo.payment_status != 'paid':
            return request.render('portal.portal_error', {
                'error_title': _('Access Denied'),
                'error_message': _('PDF download is only available for paid customers. Please complete your payment first.'),
            })

        # ✨ ALWAYS regenerate PDF based on current AI response content
        try:
            # If no AI response exists, generate it first
            if not customer_feedback_main_sudo.ai_response:
                _logger.info(f"Generating AI response for portal download - record {record_id}")
                customer_feedback_main_sudo.action_get_ai_response()
                # Refresh the record to get updated data
                customer_feedback_main_sudo = customer_feedback_main_sudo.browse(record_id)

            # ALWAYS generate fresh PDF based on current AI response content
            _logger.info(f"Generating fresh PDF from current AI response for portal download - record {record_id}")
            customer_feedback_main_sudo.action_generate_ai_pdf()
            # Refresh the record to get updated data
            customer_feedback_main_sudo = customer_feedback_main_sudo.browse(record_id)

        except Exception as e:
            _logger.error(f"Error generating AI response/PDF for record {record_id}: {str(e)}")
            return request.render('portal.portal_error', {
                'error_title': _('Generation Error'),
                'error_message': _('Failed to generate AI response or PDF. Please try again later or contact support.'),
            })

        # Final check if PDF is available after generation attempts
        if not customer_feedback_main_sudo.ai_response_pdf:
            return request.render('portal.portal_error', {
                'error_title': _('PDF Not Available'),
                'error_message': _('AI Response PDF could not be generated. Please contact support.'),
            })

        # Return the PDF file
        pdf_data_base64 = customer_feedback_main_sudo.ai_response_pdf
        filename = customer_feedback_main_sudo.ai_response_pdf_filename or f'customer_feedback_main_ai_response_{record_id}.pdf'

        # Decode base64 PDF data to binary
        import base64
        try:
            pdf_data = base64.b64decode(pdf_data_base64)
        except Exception as e:
            _logger.error(f"Error decoding PDF data for record {record_id}: {str(e)}")
            return request.render('portal.portal_error', {
                'error_title': _('PDF Decode Error'),
                'error_message': _('Failed to decode PDF data. Please try regenerating the PDF.'),
            })

        return request.make_response(
            pdf_data,
            headers=[
                ('Content-Type', 'application/pdf'),
                ('Content-Disposition', f'attachment; filename="{filename}"'),
                ('Content-Length', len(pdf_data)),
            ]
        )
