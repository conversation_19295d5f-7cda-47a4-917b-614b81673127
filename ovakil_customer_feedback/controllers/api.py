from odoo import http
from odoo.http import request
import json


class CustomerFeedbackSystemApiController(http.Controller):
    """
    REST API Controller for Customer Feedback System
    Provides CRUD operations for all forms in the module
    """

    # Customer Feedback Form API Endpoints

    @http.route('/api/customer_feedback_main', type='json', auth='user', methods=['GET'])
    def customer_feedback_main_list(self, **kwargs):
        """List Customer Feedback Form records"""
        try:
            domain = kwargs.get('domain', [])
            limit = kwargs.get('limit', 100)
            offset = kwargs.get('offset', 0)

            records = request.env['ovakil_customer_feedback.customer_feedback_main'].search(domain, limit=limit, offset=offset)

            return {
                'success': True,
                'data': [{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                } for record in records],
                'total': request.env['ovakil_customer_feedback.customer_feedback_main'].search_count(domain)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main/<int:record_id>', type='json', auth='user', methods=['GET'])
    def customer_feedback_main_get(self, record_id, **kwargs):
        """Get specific Customer Feedback Form record"""
        try:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main', type='json', auth='user', methods=['POST'])
    def customer_feedback_main_create(self, **kwargs):
        """Create new Customer Feedback Form record"""
        try:
            values = kwargs.get('values', {})
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].create(values)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main/<int:record_id>', type='json', auth='user', methods=['PUT'])
    def customer_feedback_main_update(self, record_id, **kwargs):
        """Update Customer Feedback Form record"""
        try:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            values = kwargs.get('values', {})
            record.write(values)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main/<int:record_id>', type='json', auth='user', methods=['DELETE'])
    def customer_feedback_main_delete(self, record_id, **kwargs):
        """Delete Customer Feedback Form record"""
        try:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            record.unlink()

            return {'success': True, 'message': 'Record deleted successfully'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
