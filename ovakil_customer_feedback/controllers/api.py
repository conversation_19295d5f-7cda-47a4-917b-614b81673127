from odoo import http
from odoo.http import request
import json


class CustomerFeedbackSystemApiController(http.Controller):
    """
    REST API Controller for Customer Feedback System
    Provides CRUD operations for all forms in the module
    """

    # Customer Feedback Form API Endpoints

    @http.route('/api/customer_feedback_main', type='json', auth='user', methods=['GET'])
    def customer_feedback_main_list(self, **kwargs):
        """List Customer Feedback Form records"""
        try:
            domain = kwargs.get('domain', [])
            limit = kwargs.get('limit', 100)
            offset = kwargs.get('offset', 0)

            records = request.env['ovakil_customer_feedback.customer_feedback_main'].search(domain, limit=limit, offset=offset)

            return {
                'success': True,
                'data': [{
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                } for record in records],
                'total': request.env['ovakil_customer_feedback.customer_feedback_main'].search_count(domain)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main/<int:record_id>', type='json', auth='user', methods=['GET'])
    def customer_feedback_main_get(self, record_id, **kwargs):
        """Get specific Customer Feedback Form record"""
        try:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                    'create_date': record.create_date.isoformat() if record.create_date else None,
                    # Add more fields as needed
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main', type='json', auth='user', methods=['POST'])
    def customer_feedback_main_create(self, **kwargs):
        """Create new Customer Feedback Form record"""
        try:
            values = kwargs.get('values', {})
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].create(values)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main/<int:record_id>', type='json', auth='user', methods=['PUT'])
    def customer_feedback_main_update(self, record_id, **kwargs):
        """Update Customer Feedback Form record"""
        try:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            values = kwargs.get('values', {})
            record.write(values)

            return {
                'success': True,
                'data': {
                    'id': record.id,
                    'name': record.name,
                    'state': record.state,
                }
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    @http.route('/api/customer_feedback_main/<int:record_id>', type='json', auth='user', methods=['DELETE'])
    def customer_feedback_main_delete(self, record_id, **kwargs):
        """Delete Customer Feedback Form record"""
        try:
            record = request.env['ovakil_customer_feedback.customer_feedback_main'].browse(record_id)
            if not record.exists():
                return {'success': False, 'error': 'Record not found'}

            record.unlink()

            return {'success': True, 'message': 'Record deleted successfully'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    # Helper API Endpoints for Form Fields

    @http.route('/api/users/search', type='http', auth='public', methods=['GET'], csrf=False)
    def search_users(self, **kwargs):
        """Search users for many2one fields"""
        try:
            query = kwargs.get('query', '')
            limit = int(kwargs.get('limit', 10))

            domain = [('active', '=', True)]
            if query:
                domain.append('|')
                domain.append(('name', 'ilike', query))
                domain.append(('email', 'ilike', query))

            users = request.env['res.users'].sudo().search(domain, limit=limit)

            result = {
                'success': True,
                'data': [{
                    'id': user.id,
                    'name': user.name,
                    'email': user.email or '',
                    'display_name': f"{user.name} ({user.email})" if user.email else user.name
                } for user in users]
            }

            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/users/list', type='http', auth='public', methods=['GET'], csrf=False)
    def list_users(self, **kwargs):
        """List all active users for dropdown"""
        try:
            limit = int(kwargs.get('limit', 50))

            users = request.env['res.users'].sudo().search([
                ('active', '=', True)
            ], limit=limit, order='name')

            result = {
                'success': True,
                'data': [{
                    'id': user.id,
                    'name': user.name,
                    'email': user.email or '',
                    'display_name': f"{user.name} ({user.email})" if user.email else user.name
                } for user in users]
            }

            return request.make_response(
                json.dumps(result),
                headers=[('Content-Type', 'application/json')]
            )
        except Exception as e:
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )
