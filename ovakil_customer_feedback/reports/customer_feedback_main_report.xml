<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- PDF Report Action -->
        <record id="action_report_customer_feedback_main" model="ir.actions.report">
            <field name="name">Customer Feedback Form AI Response</field>
            <field name="model">ovakil_customer_feedback.customer_feedback_main</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ovakil_customer_feedback.report_customer_feedback_main</field>
            <field name="report_file">ovakil_customer_feedback.report_customer_feedback_main</field>
            <field name="binding_model_id" ref="model_ovakil_customer_feedback_customer_feedback_main"/>
            <field name="binding_type">report</field>
            <field name="print_report_name">'Customer Feedback Form AI Response - %s' % (object.name)</field>
        </record>

        <!-- PDF Report Template -->
        <template id="report_customer_feedback_main">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <!-- ✨ AI Response / Document Content Section (Only Content) -->
                            <div t-if="doc.ai_response">
                                <!-- Raw HTML content from the ai_response field -->
                                <t t-raw="doc.ai_response"/>
                            </div>

                            <!-- Placeholder when no AI response -->
                            <div t-if="not doc.ai_response" class="text-center text-muted" style="padding: 50px;">
                                <h3><em>No AI response or document content has been generated yet.</em></h3>
                                <p><small>Use the "Get AI Response" button or manually add content in the backend to populate this document.</small></p>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>