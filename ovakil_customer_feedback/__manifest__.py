{
    'name': 'Customer Feedback Form',
    'version': '********.0',
    'category': 'Website/Website',
    'summary': 'Customer Feedback Form - AI Generated Module',
    'description': """
        Customer Feedback Form Module
        =============================
        
        This module provides a comprehensive customer feedback form system with:
        
        Features:
        ---------
        • Customer feedback collection form
        • AI-powered response generation
        • Payment integration for premium services
        • Portal access for customers
        • PDF report generation
        • Email notifications
        • Mobile-responsive design
        
        Form Fields:
        -----------
        • Customer Name (required)
        • Email Address (required)
        • Overall Rating (1-5 stars)
        • Service Category
        • Assigned User
        • Priority Level
        • Feedback Tags
        • Comments & Suggestions
        
        Technical Features:
        ------------------
        • RESTful API endpoints
        • Enhanced form validation
        • Real-time user data loading
        • Fallback mechanisms for offline mode
        • Bootstrap responsive design
        • Font Awesome icons
        • Professional styling
        
        Generated by AI Module Generator v17.0
    """,
    'author': 'AI Module Generator',
    'website': 'https://oneclickvakil.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'web',
        'website',
        'portal',
        'mail',
        'payment',
        'website_payment',
        'project',  # For project.tags
    ],
    'data': [
        # Security
        'security/ir.model.access.csv',
        
        # Data
        'data/data.xml',
        'data/cron_jobs.xml',
        
        # Reports
        'reports/customer_feedback_main_report.xml',
        
        # Views
        'views/views.xml',
        'views/website_templates.xml',
        'views/portal/customer_feedback_main_portal.xml',
        'views/portal/customer_feedback_main_portal_menu.xml',
    ],
    'demo': [
        'demo/demo.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'ovakil_customer_feedback/static/src/css/enhanced_forms.css',
            'ovakil_customer_feedback/static/src/js/enhanced_forms.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 100,
}
