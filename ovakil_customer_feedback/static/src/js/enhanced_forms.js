/**
 * Enhanced Forms JavaScript
 * Provides advanced form interactions, validation, and user experience enhancements
 */

(function() {
    'use strict';
    
    class EnhancedFormHandler {
        constructor() {
            this.form = null;
            this.currentStep = 1;
            this.totalSteps = 3;
            this.validationRules = new Map();
            this.fieldDependencies = new Map();
            
            this.init();
        }
        
        init() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setupForm());
            } else {
                this.setupForm();
            }
        }
        
        setupForm() {
            this.form = document.getElementById('enhanced-form');
            if (!this.form) return;
            
            this.setupProgressSteps();
            this.setupFieldValidation();
            this.setupFormSections();
            this.setupSubmitHandler();
            this.setupFieldEnhancements();
            this.setupAutoSave();
            this.initializeAssignedUserDropdown();
        }
        
        setupProgressSteps() {
            const steps = document.querySelectorAll('.step-item');
            steps.forEach((step, index) => {
                step.addEventListener('click', () => {
                    if (index + 1 <= this.currentStep) {
                        this.goToStep(index + 1);
                    }
                });
            });
        }
        
        goToStep(stepNumber) {
            if (stepNumber < 1 || stepNumber > this.totalSteps) return;
            
            // Update step indicators
            const steps = document.querySelectorAll('.step-item');
            steps.forEach((step, index) => {
                if (index + 1 <= stepNumber) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
            
            this.currentStep = stepNumber;
            this.updateFormVisibility();
        }
        
        updateFormVisibility() {
            const sections = document.querySelectorAll('.form-section');
            sections.forEach((section, index) => {
                if (index + 1 === this.currentStep) {
                    section.style.display = 'block';
                    section.classList.add('animate-in');
                } else {
                    section.style.display = 'none';
                    section.classList.remove('animate-in');
                }
            });
        }
        
        setupFieldValidation() {
            const fields = this.form.querySelectorAll('input, select, textarea');
            
            fields.forEach(field => {
                // Real-time validation
                field.addEventListener('blur', () => this.validateField(field));
                field.addEventListener('input', () => this.clearFieldError(field));
                
                // Setup field-specific validation rules
                this.setupFieldRules(field);
            });
        }
        
        setupFieldRules(field) {
            const fieldType = field.dataset.fieldType;
            const isRequired = field.dataset.required === 'true';
            
            const rules = [];
            
            if (isRequired) {
                rules.push({
                    test: (value) => value.trim() !== '',
                    message: 'This field is required'
                });
            }
            
            switch (fieldType) {
                case 'email':
                    rules.push({
                        test: (value) => !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                        message: 'Please enter a valid email address'
                    });
                    break;
                case 'phone':
                    rules.push({
                        test: (value) => !value || /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/\s/g, '')),
                        message: 'Please enter a valid phone number'
                    });
                    break;
                case 'integer':
                    rules.push({
                        test: (value) => !value || /^\d+$/.test(value),
                        message: 'Please enter a valid whole number'
                    });
                    break;
                case 'float':
                    rules.push({
                        test: (value) => !value || /^\d*\.?\d+$/.test(value),
                        message: 'Please enter a valid number'
                    });
                    break;
            }
            
            this.validationRules.set(field, rules);
        }
        
        validateField(field) {
            const rules = this.validationRules.get(field);
            if (!rules) return true;
            
            const value = field.value;
            
            for (const rule of rules) {
                if (!rule.test(value)) {
                    this.showFieldError(field, rule.message);
                    return false;
                }
            }
            
            this.showFieldSuccess(field);
            return true;
        }
        
        showFieldError(field, message) {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            
            let feedback = field.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = message;
            }
        }
        
        showFieldSuccess(field) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
        
        clearFieldError(field) {
            field.classList.remove('is-invalid', 'is-valid');
        }
        
        setupFormSections() {
            // Group fields into logical sections
            const fields = Array.from(this.form.querySelectorAll('.nice-form-group'));
            const sectionsConfig = this.getFormSectionsConfig();
            
            sectionsConfig.forEach((config, index) => {
                const section = this.createFormSection(config, index + 1);
                const sectionFields = fields.slice(config.startIndex, config.endIndex);
                
                sectionFields.forEach(field => {
                    section.appendChild(field);
                });
                
                this.form.appendChild(section);
            });
        }
        
        getFormSectionsConfig() {
            const totalFields = this.form.querySelectorAll('.nice-form-group').length;
            const fieldsPerSection = Math.ceil(totalFields / 2);
            
            return [
                {
                    title: 'Basic Information',
                    icon: 'fas fa-user',
                    startIndex: 0,
                    endIndex: fieldsPerSection
                },
                {
                    title: 'Additional Details',
                    icon: 'fas fa-info-circle',
                    startIndex: fieldsPerSection,
                    endIndex: totalFields
                }
            ];
        }
        
        createFormSection(config, stepNumber) {
            const section = document.createElement('div');
            section.className = 'form-section';
            section.style.display = stepNumber === 1 ? 'block' : 'none';
            
            const title = document.createElement('h3');
            title.className = 'form-section-title';
            title.innerHTML = `<i class="${config.icon}"></i> ${config.title}`;
            
            section.appendChild(title);
            return section;
        }
        
        setupSubmitHandler() {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
        }
        
        async handleSubmit() {
            // Validate all fields
            const isValid = this.validateAllFields();
            
            if (!isValid) {
                this.showValidationErrors();
                return;
            }
            
            // Show loading state
            this.setLoadingState(true);
            
            try {
                // Prepare form data
                const formData = new FormData(this.form);
                
                // Submit form
                const response = await fetch(this.form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    this.handleSubmitSuccess();
                } else {
                    throw new Error('Submission failed');
                }
                
            } catch (error) {
                this.handleSubmitError(error);
            } finally {
                this.setLoadingState(false);
            }
        }
        
        validateAllFields() {
            const fields = this.form.querySelectorAll('input, select, textarea');
            let isValid = true;
            
            fields.forEach(field => {
                if (!this.validateField(field)) {
                    isValid = false;
                }
            });
            
            return isValid;
        }
        
        showValidationErrors() {
            const firstInvalid = this.form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
            
            this.showNotification('Please correct the errors below', 'error');
        }
        
        handleSubmitSuccess() {
            this.goToStep(3); // Go to success step
            this.showNotification('Form submitted successfully!', 'success');
            
            // Redirect after a delay
            setTimeout(() => {
                window.location.href = this.form.dataset.successUrl || '/';
            }, 2000);
        }
        
        handleSubmitError(error) {
            console.error('Form submission error:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        }
        
        setLoadingState(loading) {
            if (loading) {
                this.form.classList.add('form-loading');
                const submitBtn = this.form.querySelector('.btn-submit-enhanced');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
                }
            } else {
                this.form.classList.remove('form-loading');
                const submitBtn = this.form.querySelector('.btn-submit-enhanced');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = submitBtn.dataset.originalText || 'Submit';
                }
            }
        }
        
        setupFieldEnhancements() {
            // Auto-format phone numbers
            const phoneFields = this.form.querySelectorAll('[data-field-type="phone"]');
            phoneFields.forEach(field => {
                field.addEventListener('input', this.formatPhoneNumber.bind(this));
            });

            // Auto-capitalize names
            const nameFields = this.form.querySelectorAll('[name*="name"]');
            nameFields.forEach(field => {
                field.addEventListener('blur', this.capitalizeName.bind(this));
            });

            // Setup widget toggles
            this.setupWidgetToggles();

            // Setup relational fields
            this.setupRelationalFields();
        }

        setupRelationalFields() {
            // Setup Many2one fields
            this.setupMany2oneFields();

            // Setup Many2many fields
            this.setupMany2manyFields();

            // Setup One2many fields
            this.setupOne2manyFields();
        }

        setupMany2oneFields() {
            // Searchable dropdowns
            const searchableFields = this.form.querySelectorAll('[data-widget="many2one_searchable"]');
            searchableFields.forEach(field => {
                this.initMany2oneSearchable(field);
            });

            // Radio button lists
            const radioFields = this.form.querySelectorAll('[data-widget="many2one_radio"]');
            radioFields.forEach(field => {
                this.initMany2oneRadio(field);
            });

            // Autocomplete inputs
            const autocompleteFields = this.form.querySelectorAll('[data-widget="many2one_autocomplete"]');
            autocompleteFields.forEach(field => {
                this.initMany2oneAutocomplete(field);
            });
        }

        setupMany2manyFields() {
            // Tags
            const tagFields = this.form.querySelectorAll('[data-widget="many2many_tags"]');
            tagFields.forEach(field => {
                this.initMany2manyTags(field);
            });

            // Checkboxes
            const checkboxFields = this.form.querySelectorAll('[data-widget="many2many_checkboxes"]');
            checkboxFields.forEach(field => {
                this.initMany2manyCheckboxes(field);
            });

            // Multi-select
            const multiselectFields = this.form.querySelectorAll('[data-widget="many2many_multiselect"]');
            multiselectFields.forEach(field => {
                this.initMany2manyMultiselect(field);
            });
        }

        setupOne2manyFields() {
            // List views
            const listFields = this.form.querySelectorAll('[data-widget="one2many_list"]');
            listFields.forEach(field => {
                this.initOne2manyList(field);
            });

            // Inline forms
            const inlineFields = this.form.querySelectorAll('[data-widget="one2many_inline"]');
            inlineFields.forEach(field => {
                this.initOne2manyInline(field);
            });
        }
        
        formatPhoneNumber(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 10) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            }
            e.target.value = value;
        }
        
        capitalizeName(e) {
            const value = e.target.value;
            e.target.value = value.replace(/\b\w/g, l => l.toUpperCase());
        }
        
        setupAutoSave() {
            // Auto-save form data to localStorage
            const fields = this.form.querySelectorAll('input, select, textarea');
            const formId = this.form.id || 'enhanced-form';
            
            fields.forEach(field => {
                // Load saved value
                const savedValue = localStorage.getItem(`${formId}_${field.name}`);
                if (savedValue && !field.value) {
                    field.value = savedValue;
                }
                
                // Save on change
                field.addEventListener('change', () => {
                    localStorage.setItem(`${formId}_${field.name}`, field.value);
                });
            });
        }
        
        // ===== MANY2ONE FIELD IMPLEMENTATIONS =====

        initMany2oneSearchable(field) {
            const container = field.closest('.nice-form-group');
            const model = field.dataset.model || 'res.partner';

            // Create searchable input
            const input = document.createElement('input');
            input.type = 'text';
            input.className = field.className;
            input.name = field.name;
            input.placeholder = `Search ${field.dataset.label || 'records'}...`;
            input.dataset.fieldType = 'many2one_searchable';
            input.dataset.model = model;

            // Create dropdown container
            const dropdown = document.createElement('div');
            dropdown.className = 'many2one-dropdown-results';
            dropdown.style.display = 'none';

            // Replace original field
            const wrapper = document.createElement('div');
            wrapper.className = 'many2one-searchable';
            wrapper.appendChild(input);
            wrapper.appendChild(dropdown);

            // Add search icon
            const icon = document.createElement('i');
            icon.className = 'fas fa-search search-icon';
            wrapper.appendChild(icon);

            field.parentNode.replaceChild(wrapper, field);

            // Setup search functionality
            let searchTimeout;
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchMany2oneRecords(input, dropdown, model, e.target.value);
                }, 300);
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!wrapper.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        async searchMany2oneRecords(input, dropdown, model, query) {
            if (!query || query.length < 2) {
                dropdown.style.display = 'none';
                return;
            }

            try {
                // Mock search results - in real implementation, this would call Odoo API
                const mockResults = this.getMockMany2oneResults(model, query);

                dropdown.innerHTML = '';

                if (mockResults.length === 0) {
                    dropdown.innerHTML = '<div class="many2one-dropdown-item">No results found</div>';
                } else {
                    mockResults.forEach(result => {
                        const item = document.createElement('div');
                        item.className = 'many2one-dropdown-item';
                        item.textContent = result.name;
                        item.dataset.id = result.id;

                        item.addEventListener('click', () => {
                            input.value = result.name;
                            input.dataset.selectedId = result.id;
                            dropdown.style.display = 'none';

                            // Create hidden input for form submission
                            let hiddenInput = input.parentNode.querySelector('input[type="hidden"]');
                            if (!hiddenInput) {
                                hiddenInput = document.createElement('input');
                                hiddenInput.type = 'hidden';
                                hiddenInput.name = input.name;
                                input.parentNode.appendChild(hiddenInput);
                                input.removeAttribute('name'); // Remove name from visible input
                            }
                            hiddenInput.value = result.id;
                        });

                        dropdown.appendChild(item);
                    });
                }

                dropdown.style.display = 'block';

            } catch (error) {
                console.error('Error searching records:', error);
                dropdown.innerHTML = '<div class="many2one-dropdown-item">Error loading results</div>';
                dropdown.style.display = 'block';
            }
        }

        getMockMany2oneResults(model, query) {
            // Mock data for different models
            const mockData = {
                'res.partner': [
                    { id: 1, name: 'John Doe' },
                    { id: 2, name: 'Jane Smith' },
                    { id: 3, name: 'Bob Johnson' },
                    { id: 4, name: 'Alice Brown' },
                    { id: 5, name: 'Charlie Wilson' }
                ],
                'product.product': [
                    { id: 1, name: 'Product A' },
                    { id: 2, name: 'Product B' },
                    { id: 3, name: 'Service X' },
                    { id: 4, name: 'Service Y' }
                ],
                'project.project': [
                    { id: 1, name: 'Website Development' },
                    { id: 2, name: 'Mobile App' },
                    { id: 3, name: 'Data Migration' }
                ]
            };

            const data = mockData[model] || mockData['res.partner'];
            return data.filter(item =>
                item.name.toLowerCase().includes(query.toLowerCase())
            ).slice(0, 5);
        }

        initMany2oneRadio(field) {
            const container = field.closest('.nice-form-group');
            const model = field.dataset.model || 'res.partner';

            // Create radio group container
            const radioGroup = document.createElement('div');
            radioGroup.className = 'many2one-radio-group';

            // Get mock options
            const options = this.getMockMany2oneResults(model, '').slice(0, 4);

            options.forEach(option => {
                const radioItem = document.createElement('div');
                radioItem.className = 'many2one-radio-item';

                const radio = document.createElement('input');
                radio.type = 'radio';
                radio.name = field.name;
                radio.value = option.id;
                radio.id = `${field.name}_${option.id}`;

                const label = document.createElement('label');
                label.htmlFor = radio.id;
                label.textContent = option.name;
                label.style.cursor = 'pointer';
                label.style.margin = '0';

                radioItem.appendChild(radio);
                radioItem.appendChild(label);

                // Make entire item clickable
                radioItem.addEventListener('click', (e) => {
                    if (e.target !== radio) {
                        radio.checked = true;
                        // Update visual state
                        radioGroup.querySelectorAll('.many2one-radio-item').forEach(item => {
                            item.classList.remove('selected');
                        });
                        radioItem.classList.add('selected');
                    }
                });

                radio.addEventListener('change', () => {
                    radioGroup.querySelectorAll('.many2one-radio-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    radioItem.classList.add('selected');
                });

                radioGroup.appendChild(radioItem);
            });

            // Replace original field
            field.parentNode.replaceChild(radioGroup, field);
        }

        initMany2oneAutocomplete(field) {
            // Similar to searchable but with different styling
            this.initMany2oneSearchable(field);
        }

        // ===== MANY2MANY FIELD IMPLEMENTATIONS =====

        initMany2manyTags(field) {
            const container = field.closest('.nice-form-group');
            const model = field.dataset.model || 'res.partner';

            // Create tags container
            const tagsContainer = document.createElement('div');
            tagsContainer.className = 'many2many-tags-container';

            // Create input for adding new tags
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'many2many-input';
            input.placeholder = 'Type to search and add...';

            tagsContainer.appendChild(input);

            // Store selected values
            const selectedValues = new Set();

            // Create hidden input for form submission
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = field.name;

            // Setup search and selection
            let searchTimeout;
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchMany2manyRecords(input, tagsContainer, model, e.target.value, selectedValues, hiddenInput);
                }, 300);
            });

            // Handle enter key
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                }
            });

            // Replace original field
            const wrapper = document.createElement('div');
            wrapper.appendChild(tagsContainer);
            wrapper.appendChild(hiddenInput);
            field.parentNode.replaceChild(wrapper, field);
        }

        async searchMany2manyRecords(input, container, model, query, selectedValues, hiddenInput) {
            if (!query || query.length < 2) {
                this.hideMany2manyDropdown(container);
                return;
            }

            const results = this.getMockMany2oneResults(model, query);
            this.showMany2manyDropdown(container, results, selectedValues, hiddenInput, input);
        }

        showMany2manyDropdown(container, results, selectedValues, hiddenInput, input) {
            // Remove existing dropdown
            this.hideMany2manyDropdown(container);

            if (results.length === 0) return;

            const dropdown = document.createElement('div');
            dropdown.className = 'many2one-dropdown-results many2many-dropdown';

            results.forEach(result => {
                if (selectedValues.has(result.id)) return; // Skip already selected

                const item = document.createElement('div');
                item.className = 'many2one-dropdown-item';
                item.textContent = result.name;
                item.dataset.id = result.id;

                item.addEventListener('click', () => {
                    this.addMany2manyTag(container, result, selectedValues, hiddenInput);
                    input.value = '';
                    this.hideMany2manyDropdown(container);
                });

                dropdown.appendChild(item);
            });

            container.appendChild(dropdown);
        }

        addMany2manyTag(container, record, selectedValues, hiddenInput) {
            selectedValues.add(record.id);

            const tag = document.createElement('div');
            tag.className = 'many2many-tag';
            tag.innerHTML = `
                ${record.name}
                <span class="remove-tag" data-id="${record.id}">&times;</span>
            `;

            // Add remove functionality
            tag.querySelector('.remove-tag').addEventListener('click', () => {
                selectedValues.delete(record.id);
                tag.remove();
                this.updateMany2manyHiddenInput(selectedValues, hiddenInput);
            });

            // Insert before input
            const input = container.querySelector('.many2many-input');
            container.insertBefore(tag, input);

            this.updateMany2manyHiddenInput(selectedValues, hiddenInput);
        }

        updateMany2manyHiddenInput(selectedValues, hiddenInput) {
            hiddenInput.value = Array.from(selectedValues).join(',');
        }

        hideMany2manyDropdown(container) {
            const dropdown = container.querySelector('.many2many-dropdown');
            if (dropdown) {
                dropdown.remove();
            }
        }

        initMany2manyCheckboxes(field) {
            const container = field.closest('.nice-form-group');
            const model = field.dataset.model || 'res.partner';

            // Create checkboxes container
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'many2many-checkboxes';

            // Get options
            const options = this.getMockMany2oneResults(model, '').slice(0, 8);

            // Create hidden input for form submission
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = field.name;

            const selectedValues = new Set();

            options.forEach(option => {
                const checkboxItem = document.createElement('div');
                checkboxItem.className = 'many2many-checkbox-item';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.value = option.id;
                checkbox.id = `${field.name}_${option.id}`;

                const label = document.createElement('label');
                label.htmlFor = checkbox.id;
                label.textContent = option.name;
                label.style.cursor = 'pointer';
                label.style.margin = '0';

                checkbox.addEventListener('change', () => {
                    if (checkbox.checked) {
                        selectedValues.add(option.id);
                    } else {
                        selectedValues.delete(option.id);
                    }
                    hiddenInput.value = Array.from(selectedValues).join(',');
                });

                checkboxItem.appendChild(checkbox);
                checkboxItem.appendChild(label);
                checkboxContainer.appendChild(checkboxItem);
            });

            // Replace original field
            const wrapper = document.createElement('div');
            wrapper.appendChild(checkboxContainer);
            wrapper.appendChild(hiddenInput);
            field.parentNode.replaceChild(wrapper, field);
        }

        initMany2manyMultiselect(field) {
            const container = field.closest('.nice-form-group');
            const model = field.dataset.model || 'res.partner';

            // Create multiselect container
            const multiselectContainer = document.createElement('div');
            multiselectContainer.className = 'many2many-multiselect';

            // Create header
            const header = document.createElement('div');
            header.className = 'many2many-multiselect-header';
            header.innerHTML = `
                <span class="selected-text">Select items...</span>
                <i class="fas fa-chevron-down"></i>
            `;

            // Create dropdown
            const dropdown = document.createElement('div');
            dropdown.className = 'many2many-multiselect-dropdown';
            dropdown.style.display = 'none';

            // Get options
            const options = this.getMockMany2oneResults(model, '').slice(0, 6);
            const selectedValues = new Set();

            // Create hidden input
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = field.name;

            options.forEach(option => {
                const item = document.createElement('div');
                item.className = 'many2many-checkbox-item';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.value = option.id;

                const label = document.createElement('label');
                label.textContent = option.name;
                label.style.margin = '0';

                checkbox.addEventListener('change', () => {
                    if (checkbox.checked) {
                        selectedValues.add(option.id);
                    } else {
                        selectedValues.delete(option.id);
                    }

                    // Update header text
                    const count = selectedValues.size;
                    const selectedText = header.querySelector('.selected-text');
                    if (count === 0) {
                        selectedText.textContent = 'Select items...';
                    } else if (count === 1) {
                        selectedText.textContent = '1 item selected';
                    } else {
                        selectedText.textContent = `${count} items selected`;
                    }

                    hiddenInput.value = Array.from(selectedValues).join(',');
                });

                item.appendChild(checkbox);
                item.appendChild(label);
                dropdown.appendChild(item);
            });

            // Toggle dropdown
            header.addEventListener('click', () => {
                const isVisible = dropdown.style.display === 'block';
                dropdown.style.display = isVisible ? 'none' : 'block';
                header.querySelector('i').className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-up';
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!multiselectContainer.contains(e.target)) {
                    dropdown.style.display = 'none';
                    header.querySelector('i').className = 'fas fa-chevron-down';
                }
            });

            multiselectContainer.appendChild(header);
            multiselectContainer.appendChild(dropdown);

            // Replace original field
            const wrapper = document.createElement('div');
            wrapper.appendChild(multiselectContainer);
            wrapper.appendChild(hiddenInput);
            field.parentNode.replaceChild(wrapper, field);
        }

        // ===== ONE2MANY FIELD IMPLEMENTATIONS =====

        initOne2manyList(field) {
            const container = field.closest('.nice-form-group');

            // Create list container
            const listContainer = document.createElement('div');
            listContainer.className = 'one2many-field';

            // Create header
            const header = document.createElement('div');
            header.className = 'one2many-list-header';
            header.innerHTML = `
                <span>Items</span>
                <button type="button" class="one2many-add-button">
                    <i class="fas fa-plus"></i> Add Item
                </button>
            `;

            // Create list
            const list = document.createElement('div');
            list.className = 'one2many-list';

            // Create hidden input for form submission
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = field.name;

            const items = [];

            // Add button functionality
            header.querySelector('.one2many-add-button').addEventListener('click', () => {
                this.addOne2manyItem(list, items, hiddenInput);
            });

            listContainer.appendChild(header);
            listContainer.appendChild(list);

            // Replace original field
            const wrapper = document.createElement('div');
            wrapper.appendChild(listContainer);
            wrapper.appendChild(hiddenInput);
            field.parentNode.replaceChild(wrapper, field);
        }

        addOne2manyItem(list, items, hiddenInput) {
            const itemId = Date.now(); // Simple ID generation
            const itemData = {
                id: itemId,
                name: `Item ${items.length + 1}`,
                description: ''
            };

            items.push(itemData);

            const listItem = document.createElement('div');
            listItem.className = 'one2many-list-item';
            listItem.innerHTML = `
                <div>
                    <strong>${itemData.name}</strong>
                    <br>
                    <small class="text-muted">Click to edit</small>
                </div>
                <button type="button" class="one2many-remove-button" data-id="${itemId}">
                    <i class="fas fa-trash"></i>
                </button>
            `;

            // Remove functionality
            listItem.querySelector('.one2many-remove-button').addEventListener('click', () => {
                const index = items.findIndex(item => item.id === itemId);
                if (index > -1) {
                    items.splice(index, 1);
                    listItem.remove();
                    this.updateOne2manyHiddenInput(items, hiddenInput);
                }
            });

            // Edit functionality (simplified)
            listItem.addEventListener('click', (e) => {
                if (!e.target.closest('.one2many-remove-button')) {
                    const newName = prompt('Enter item name:', itemData.name);
                    if (newName) {
                        itemData.name = newName;
                        listItem.querySelector('strong').textContent = newName;
                        this.updateOne2manyHiddenInput(items, hiddenInput);
                    }
                }
            });

            list.appendChild(listItem);
            this.updateOne2manyHiddenInput(items, hiddenInput);
        }

        updateOne2manyHiddenInput(items, hiddenInput) {
            hiddenInput.value = JSON.stringify(items);
        }

        initOne2manyInline(field) {
            // Similar to list but with inline editing
            this.initOne2manyList(field);
        }

        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // ===== WIDGET TOGGLE FUNCTIONALITY =====

        setupWidgetToggles() {
            // Handle widget toggles for many2one fields
            const widgetToggles = this.form.querySelectorAll('.widget-toggle-group');
            widgetToggles.forEach(toggleGroup => {
                const radios = toggleGroup.querySelectorAll('input[type="radio"]');
                radios.forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        this.handleWidgetToggle(e.target);
                    });
                });
            });

            // Initialize default widgets
            this.initializeDefaultWidgets();
        }

        handleWidgetToggle(radio) {
            const fieldName = radio.name.replace('_widget', '');
            const widgetType = radio.value;
            const formGroup = radio.closest('.nice-form-group');

            // Hide all widget variants
            const allWidgets = formGroup.querySelectorAll('[id*="' + fieldName + '"]');
            allWidgets.forEach(widget => {
                if (widget.id.includes('_dropdown') || widget.id.includes('_searchable')) {
                    widget.style.display = 'none';
                }
            });

            // Show selected widget
            const selectedWidget = formGroup.querySelector('#' + fieldName + '_' + widgetType);
            if (selectedWidget) {
                selectedWidget.style.display = 'block';

                // Initialize widget if needed
                if (widgetType === 'searchable') {
                    this.initWidgetSearchable(fieldName, formGroup);
                } else if (widgetType === 'dropdown') {
                    this.populateWidgetDropdown(fieldName, formGroup);
                }
            }
        }

        initializeDefaultWidgets() {
            // Initialize dropdown widgets by default
            const dropdownWidgets = this.form.querySelectorAll('[data-widget="many2one_dropdown"]');
            dropdownWidgets.forEach(widget => {
                const fieldName = widget.name;
                const formGroup = widget.closest('.nice-form-group');
                this.populateWidgetDropdown(fieldName, formGroup);
            });
        }

        async populateWidgetDropdown(fieldName, formGroup) {
            const dropdown = formGroup.querySelector('#' + fieldName + '_dropdown');
            if (!dropdown) return;

            const model = dropdown.dataset.model;
            if (!model) return;

            try {
                // Determine API endpoint based on model
                let endpoint = '/api/model/search';
                if (model === 'res.users') {
                    endpoint = '/api/users/list';
                } else if (model === 'res.partner') {
                    endpoint = '/api/partners/list';
                }

                const response = await fetch(endpoint + '?limit=50' + (model !== 'res.users' && model !== 'res.partner' ? '&model=' + model : ''));
                const result = await response.json();

                if (result.success && result.data) {
                    // Clear existing options except the first one
                    dropdown.innerHTML = '<option value="">Select ' + (dropdown.dataset.label || 'option') + '...</option>';

                    // Add options
                    result.data.forEach(item => {
                        const option = document.createElement('option');
                        option.value = item.id;
                        option.textContent = item.display_name || item.name;
                        dropdown.appendChild(option);
                    });
                } else {
                    console.error('Failed to fetch data:', result.error);
                    this.showFallbackOptions(dropdown, fieldName);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                this.showFallbackOptions(dropdown, fieldName);
            }
        }

        showFallbackOptions(dropdown, fieldName) {
            // Fallback options in case API fails
            const fallbackOptions = this.getFallbackOptions(fieldName);

            dropdown.innerHTML = '<option value="">Select ' + (dropdown.dataset.label || 'option') + '...</option>';
            fallbackOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.id;
                optionElement.textContent = option.display_name;
                dropdown.appendChild(optionElement);
            });
        }

        getFallbackOptions(fieldName) {
            // Return appropriate fallback options based on field name
            if (fieldName.includes('user')) {
                return [
                    { id: 1, display_name: 'Administrator' },
                    { id: 2, display_name: 'Support Team' },
                    { id: 3, display_name: 'Customer Service' }
                ];
            } else if (fieldName.includes('partner')) {
                return [
                    { id: 1, display_name: 'Sample Partner 1' },
                    { id: 2, display_name: 'Sample Partner 2' },
                    { id: 3, display_name: 'Sample Partner 3' }
                ];
            }
            return [
                { id: 1, display_name: 'Option 1' },
                { id: 2, display_name: 'Option 2' },
                { id: 3, display_name: 'Option 3' }
            ];
        }

        initWidgetSearchable(fieldName, formGroup) {
            const searchableContainer = formGroup.querySelector('#' + fieldName + '_searchable');
            const input = searchableContainer.querySelector('input');
            const dropdown = searchableContainer.querySelector('.many2one-dropdown-results');

            if (!input || !dropdown) return;

            const model = input.dataset.model;

            let searchTimeout;
            input.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchWidgetRecords(input, dropdown, model, e.target.value, fieldName);
                }, 300);
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!searchableContainer.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        async searchWidgetRecords(input, dropdown, model, query, fieldName) {
            if (!query || query.length < 2) {
                dropdown.style.display = 'none';
                return;
            }

            try {
                // Determine API endpoint based on model
                let endpoint = '/api/model/search';
                if (model === 'res.users') {
                    endpoint = '/api/users/search';
                } else if (model === 'res.partner') {
                    endpoint = '/api/partners/search';
                }

                const response = await fetch(endpoint + '?query=' + encodeURIComponent(query) + '&limit=10' + (model !== 'res.users' && model !== 'res.partner' ? '&model=' + model : ''));
                const result = await response.json();

                dropdown.innerHTML = '';

                if (result.success && result.data && result.data.length > 0) {
                    result.data.forEach(item => {
                        const itemElement = document.createElement('div');
                        itemElement.className = 'many2one-dropdown-item';
                        itemElement.textContent = item.display_name || item.name;
                        itemElement.dataset.itemId = item.id;
                        itemElement.dataset.itemName = item.name;

                        itemElement.addEventListener('click', () => {
                            input.value = item.name;
                            input.dataset.selectedId = item.id;
                            dropdown.style.display = 'none';

                            // Create hidden input for form submission
                            let hiddenInput = input.parentNode.querySelector('input[type="hidden"]');
                            if (!hiddenInput) {
                                hiddenInput = document.createElement('input');
                                hiddenInput.type = 'hidden';
                                hiddenInput.name = fieldName;
                                input.parentNode.appendChild(hiddenInput);
                            }
                            hiddenInput.value = item.id;
                        });

                        dropdown.appendChild(itemElement);
                    });
                } else {
                    const noResults = document.createElement('div');
                    noResults.className = 'many2one-dropdown-item';
                    noResults.textContent = 'No results found';
                    noResults.style.fontStyle = 'italic';
                    noResults.style.color = '#6c757d';
                    dropdown.appendChild(noResults);
                }

                dropdown.style.display = 'block';
            } catch (error) {
                console.error('Error searching records:', error);
                dropdown.innerHTML = '<div class="many2one-dropdown-item" style="color: #dc3545;">Error loading data</div>';
                dropdown.style.display = 'block';
            }
        }

        // ===== ASSIGNED USER DROPDOWN FUNCTIONALITY =====

        initializeAssignedUserDropdown() {
            // Initialize assigned user dropdown with real data
            this.populateAssignedUserDropdown();
        }

        async populateAssignedUserDropdown() {
            const dropdown = document.querySelector('select[name="assigned_user"]');
            if (!dropdown) return;

            try {
                // Try to fetch real users from Odoo API
                const response = await fetch('/api/users/list?limit=50');

                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();

                        if (data.success && data.data && Array.isArray(data.data)) {
                            dropdown.innerHTML = '<option value="">Select Assigned User...</option>';
                            data.data.forEach(user => {
                                const option = document.createElement('option');
                                option.value = user.id;
                                option.textContent = user.display_name;
                                dropdown.appendChild(option);
                            });

                            console.log('Loaded', data.data.length, 'users from API');
                            return;
                        }
                    }
                }

                // If we reach here, API didn't work properly
                console.log('API not available, using fallback users');
                this.showFallbackUsers(dropdown);

            } catch (error) {
                console.error('Error fetching users:', error);
                console.log('Using fallback users due to error');
                this.showFallbackUsers(dropdown);
            }
        }

        showFallbackUsers(dropdown) {
            // Realistic fallback users in case API fails
            const fallbackUsers = [
                { id: 1, display_name: 'Administrator (<EMAIL>)' },
                { id: 2, display_name: 'Customer Support Team' },
                { id: 3, display_name: 'Legal Advisor - Arihant' },
                { id: 4, display_name: 'Document Processing Team' },
                { id: 5, display_name: 'Quality Assurance Team' }
            ];

            dropdown.innerHTML = '<option value="">Select Assigned User...</option>';
            fallbackUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = user.display_name;
                dropdown.appendChild(option);
            });

            console.log('Loaded', fallbackUsers.length, 'fallback users');
        }
    }

    // Initialize when DOM is ready
    new EnhancedFormHandler();

})();
