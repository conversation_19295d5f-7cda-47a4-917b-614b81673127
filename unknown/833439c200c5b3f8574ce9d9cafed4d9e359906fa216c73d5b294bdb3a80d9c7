id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_module_template_user,module.template user,model_module_template,group_module_generator_user,1,1,1,0
access_module_template_manager,module.template manager,model_module_template,group_module_generator_manager,1,1,1,1
access_module_template_admin,module.template admin,model_module_template,group_module_generator_admin,1,1,1,1
access_generated_module_user,generated.module user,model_generated_module,group_module_generator_user,1,1,1,0
access_generated_module_manager,generated.module manager,model_generated_module,group_module_generator_manager,1,1,1,1
access_generated_module_admin,generated.module admin,model_generated_module,group_module_generator_admin,1,1,1,1
access_form_builder_user,form.builder user,model_form_builder,group_module_generator_user,1,1,1,1
access_form_builder_manager,form.builder manager,model_form_builder,group_module_generator_manager,1,1,1,1
access_form_builder_admin,form.builder admin,model_form_builder,group_module_generator_admin,1,1,1,1
access_field_definition_user,field.definition user,model_field_definition,group_module_generator_user,1,1,1,1
access_field_definition_manager,field.definition manager,model_field_definition,group_module_generator_manager,1,1,1,1
access_field_definition_admin,field.definition admin,model_field_definition,group_module_generator_admin,1,1,1,1
access_workflow_state_user,workflow.state user,model_workflow_state,group_module_generator_user,1,1,1,1
access_workflow_state_manager,workflow.state manager,model_workflow_state,group_module_generator_manager,1,1,1,1
access_workflow_state_admin,workflow.state admin,model_workflow_state,group_module_generator_admin,1,1,1,1
access_ai_prompt_config_user,ai.prompt.config user,model_ai_prompt_config,group_module_generator_user,1,1,1,1
access_ai_prompt_config_manager,ai.prompt.config manager,model_ai_prompt_config,group_module_generator_manager,1,1,1,1
access_ai_prompt_config_admin,ai.prompt.config admin,model_ai_prompt_config,group_module_generator_admin,1,1,1,1
access_ai_prompt_test_wizard_user,ai.prompt.test.wizard user,model_ai_prompt_test_wizard,group_module_generator_user,1,1,1,1
access_ai_prompt_test_wizard_manager,ai.prompt.test.wizard manager,model_ai_prompt_test_wizard,group_module_generator_manager,1,1,1,1
access_ai_prompt_test_wizard_admin,ai.prompt.test.wizard admin,model_ai_prompt_test_wizard,group_module_generator_admin,1,1,1,1
access_ai_execution_log_user,ai.execution.log user,model_ai_execution_log,group_module_generator_user,1,0,0,0
access_ai_execution_log_manager,ai.execution.log manager,model_ai_execution_log,group_module_generator_manager,1,1,1,1
access_ai_execution_log_admin,ai.execution.log admin,model_ai_execution_log,group_module_generator_admin,1,1,1,1
access_module_generator_wizard_user,module.generator.wizard user,model_module_generator_wizard,group_module_generator_user,1,1,1,1
access_module_generator_wizard_manager,module.generator.wizard manager,model_module_generator_wizard,group_module_generator_manager,1,1,1,1
access_module_generator_wizard_admin,module.generator.wizard admin,model_module_generator_wizard,group_module_generator_admin,1,1,1,1
access_form_builder_addon_user,form.builder.addon user,model_form_builder_addon,group_module_generator_user,1,1,1,1
access_form_builder_addon_manager,form.builder.addon manager,model_form_builder_addon,group_module_generator_manager,1,1,1,1
access_form_builder_addon_admin,form.builder.addon admin,model_form_builder_addon,group_module_generator_admin,1,1,1,1
access_form_builder_faq_user,form.builder.faq user,model_form_builder_faq,group_module_generator_user,1,1,1,1
access_form_builder_faq_manager,form.builder.faq manager,model_form_builder_faq,group_module_generator_manager,1,1,1,1
access_form_builder_faq_admin,form.builder.faq admin,model_form_builder_faq,group_module_generator_admin,1,1,1,1

