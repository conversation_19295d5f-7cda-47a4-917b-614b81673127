{
    'name': 'Medical Second Opinion Portal',
    'version': '********.0',
    'category': 'Website',
    'summary': 'Medical Second Opinion Management System',
    'description': '''
        This module provides a portal for patients to request second opinions
        from qualified doctors worldwide.
    ''',
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'website',
        'portal',
        'payment',
        'web',
        'website_sale',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/data.xml',
        'data/ir_sequence_data.xml',
        'data/product_data.xml',
        'data/payment_data.xml',
        'data/mail_templates.xml',
        'views/web_menu.xml',
        'views/view_second_opinion.xml',
        'views/website_templates.xml',
        'views/page_record.xml',
        'views/doctor_views.xml',
        'views/product_views.xml',
        'views/payment_views.xml',
        'views/portal_page.xml',        
    ],
    'assets': {
        'web.assets_frontend': [
            'ai_doctor_second_opinion/static/src/css/nice-forms.css',
            'ai_doctor_second_opinion/static/src/css/select2.css',
            'web/static/lib/jquery/jquery.js',
            'ai_doctor_second_opinion/static/src/js/doctor_selection.js',
        ],
    },
    'demo': [],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}