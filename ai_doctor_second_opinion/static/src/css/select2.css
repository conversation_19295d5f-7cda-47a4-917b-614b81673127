.select2-container--bootstrap-5 .select2-selection {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    min-height: 38px;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
    background-color: #B4E7CE;
    border: none;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    color: white;
    margin: 0;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
    color: white;
    margin-right: 0.25rem;
    border: none;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.select2-container--bootstrap-5 .select2-dropdown {
    border-color: #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
    background-color: #B4E7CE;
}
