// odoo.define('ai_doctor_second_opinion.select2', function (require) {
//     'use strict';

//     var publicWidget = require('web.public.widget');

//     publicWidget.registry.DoctorRegistrationForm = publicWidget.Widget.extend({
//         selector: '.o_website_form',
//         events: {
//             'change select.select2': '_onSelect2Change',
//         },

//         start: function () {
//             var def = this._super.apply(this, arguments);
//             this.$('select.select2').select2({
//                 theme: 'bootstrap-5',
//                 width: '100%',
//                 placeholder: 'Select options',
//                 allowClear: true,
//                 closeOnSelect: false
//             });
//             return def;
//         },

//         _onSelect2Change: function (ev) {
//             var $target = $(ev.currentTarget);
//             $target.trigger('input');
//         },

//         destroy: function () {
//             this.$('select.select2').select2('destroy');
//             this._super.apply(this, arguments);
//         }
//     });

//     return publicWidget.registry.DoctorRegistrationForm;
// });
