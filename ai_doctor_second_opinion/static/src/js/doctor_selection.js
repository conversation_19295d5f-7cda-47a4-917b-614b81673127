// Ensure jQuery is loaded
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded. Doctor selection functionality will not work.');
} else {
    (function($) {
        'use strict';
        
        $(document).ready(function() {
            // Check if we're on the doctor selection page
            if (!window.location.pathname.includes('/select/doctor')) {
                console.log('Not on doctor selection page. Skipping doctor selection script.');
                return;
            }

            let searchTimeout = null;
            let isSearching = false;

            // Initialize values from URL params
            const urlParams = new URLSearchParams(window.location.search);
            $('#doctorSort').val(urlParams.get('sort') || 'best_match');
            $('#doctorSearch').val(urlParams.get('search') || '');

            // Handle search input with debounce
            $('#doctorSearch').on('input', function() {
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                const searchQuery = $(this).val().trim();
                searchTimeout = setTimeout(function() {
                    if (!isSearching) {
                        updateDoctorList();
                    }
                }, 500);
            });

            // Handle search button click
            $('#searchButton').on('click', function() {
                if (!isSearching) {
                    updateDoctorList();
                }
            });

            // Handle Enter key in search input
            $('#doctorSearch').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    if (!isSearching) {
                        updateDoctorList();
                    }
                }
            });

            // Handle sort change
            $('#doctorSort').on('change', function() {
                if (!isSearching) {
                    updateDoctorList();
                }
            });

            function updateDoctorList() {
                if (isSearching) return;
                isSearching = true;

                const $doctorCards = $('#doctorCards');
                const $searchInput = $('#doctorSearch');
                const $sortSelect = $('#doctorSort');
                const $searchButton = $('#searchButton');
                const $pagination = $('.pagination');

                // More flexible element checking
                const requiredElements = [
                    { selector: '#doctorCards', name: 'Doctor Cards' },
                    { selector: '#doctorSearch', name: 'Search Input' },
                    { selector: '#doctorSort', name: 'Sort Select' },
                    { selector: '#searchButton', name: 'Search Button' }
                ];

                const missingElements = requiredElements.filter(el => !$(el.selector).length);

                if (missingElements.length) {
                    console.warn('Missing required elements:', 
                        missingElements.map(el => el.name).join(', '));
                    
                    // Attempt a more graceful fallback
                    if (window.location.pathname.includes('/select/doctor')) {
                        console.error('Critical elements missing on doctor selection page');
                        isSearching = false;
                        return;
                    }
                }

                // Show loading state (only if elements exist)
                if ($doctorCards.length) $doctorCards.addClass('loading');
                if ($searchInput.length) $searchInput.prop('disabled', true);
                if ($sortSelect.length) $sortSelect.prop('disabled', true);
                if ($searchButton.length) {
                    $searchButton.prop('disabled', true);
                    $searchButton.html('<i class="fa fa-spinner fa-spin me-2"></i>Searching...');
                }

                // Prepare parameters with safe checks
                const searchQuery = $searchInput.length ? 
                    ($searchInput.val() ? $searchInput.val().trim() : '') : '';
                const sortValue = $sortSelect.length ? 
                    ($sortSelect.val() || 'best_match') : 'best_match';
                const currentUrl = new URL(window.location.href);
                const requestId = currentUrl.searchParams.get('request_id') || '';

                const params = new URLSearchParams({
                    request_id: requestId,
                    sort: sortValue,
                    search: searchQuery,
                    page: '1'
                });

                const newUrl = '/select/doctor?' + params.toString();

                // Make AJAX request
                $.ajax({
                    url: newUrl,
                    method: 'GET',
                    dataType: 'html',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                }).done(function(html) {
                    const $html = $(html);
                    const $newDoctorCards = $html.find('.doctor-cards-container');
                    const $newPagination = $html.find('.pagination');

                    // Update doctor cards
                    if ($doctorCards.length && $newDoctorCards.length) {
                        $doctorCards.html($newDoctorCards.html());
                    } else if ($doctorCards.length) {
                        $doctorCards.html(`
                            <div class="col-12 text-center py-5">
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    No doctors found matching your criteria.
                                </div>
                            </div>
                        `);
                    }

                    // Update pagination
                    if ($pagination.length && $newPagination.length) {
                        $pagination.html($newPagination.html());
                    }

                    // Update URL without page reload
                    window.history.pushState({}, '', newUrl);

                }).fail(function(jqXHR, textStatus, error) {
                    console.error('Error fetching doctors:', error);
                    if ($doctorCards.length) {
                        $doctorCards.html(`
                            <div class="col-12 text-center py-5">
                                <div class="alert alert-danger">
                                    <i class="fa fa-exclamation-circle me-2"></i>
                                    An error occurred while searching. Please try again.
                                </div>
                            </div>
                        `);
                    }
                }).always(function() {
                    isSearching = false;
                    if ($doctorCards.length) $doctorCards.removeClass('loading');
                    if ($searchInput.length) $searchInput.prop('disabled', false);
                    if ($sortSelect.length) $sortSelect.prop('disabled', false);
                    if ($searchButton.length) {
                        $searchButton.prop('disabled', false);
                        $searchButton.html('<i class="fa fa-search me-2"></i>Search');
                    }
                });
            }

            // Handle pagination clicks
            $(document).on('click', '.pagination .page-link', function(e) {
                e.preventDefault();
                if (!isSearching) {
                    const pageUrl = $(this).attr('href');
                    if (pageUrl) {
                        window.history.pushState({}, '', pageUrl);
                        updateDoctorList();
                    }
                }
            });
        });
    })(jQuery);
}
