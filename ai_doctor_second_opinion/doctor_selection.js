// Ensure jQuery is loaded
if (typeof jQuery === 'undefined') {
    console.error('jQuery is not loaded. Doctor selection functionality will not work.');
} else {
    (function($) {
        'use strict';
        
        $(document).ready(function() {
            let searchTimeout = null;
            let isSearching = false;

            // Initialize values from URL params
            const urlParams = new URLSearchParams(window.location.search);
            $('#doctorSort').val(urlParams.get('sort') || 'best_match');
            $('#doctorSearch').val(urlParams.get('search') || '');

            // Handle search input with debounce
            $('#doctorSearch').on('input', function() {
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                const searchQuery = $(this).val().trim();
                searchTimeout = setTimeout(function() {
                    if (!isSearching) {
                        updateDoctorList();
                    }
                }, 500);
            });

            // Handle search button click
            $('#searchButton').on('click', function() {
                if (!isSearching) {
                    updateDoctorList();
                }
            });

            // Handle Enter key in search input
            $('#doctorSearch').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    if (!isSearching) {
                        updateDoctorList();
                    }
                }
            });

            // Handle sort change
            $('#doctorSort').on('change', function() {
                if (!isSearching) {
                    updateDoctorList();
                }
            });

            function updateDoctorList() {
                if (isSearching) return;
                isSearching = true;

                const $doctorCards = $('#doctorCards');
                const $searchInput = $('#doctorSearch');
                const $sortSelect = $('#doctorSort');
                const $searchButton = $('#searchButton');
                const $pagination = $('.pagination');

                // Show loading state
                $doctorCards.addClass('loading');
                $searchInput.prop('disabled', true);
                $sortSelect.prop('disabled', true);
                $searchButton.prop('disabled', true);
                $searchButton.html('<i class="fa fa-spinner fa-spin me-2"></i>Searching...');

                // Prepare parameters
                const searchQuery = $searchInput.val().trim();
                const sortValue = $sortSelect.val();
                const currentUrl = new URL(window.location.href);
                const requestId = currentUrl.searchParams.get('request_id');
                const issueType = $('#issueTypeSelect').val();

                const params = new URLSearchParams({
                    request_id: requestId || '',
                    sort: sortValue || 'best_match',
                    search: searchQuery,
                    page: '1',
                    issue_type: issueType || ''
                });

                if (issueType) {
                    // If issue type is selected, filter doctors by specialty
                    params.set('filter_by_specialty', issueType);
                } else {
                    // If no issue type, proceed with symptom-based search
                    params.set('filter_by_symptoms', searchQuery);
                }

                const newUrl = '/select/doctor?' + params.toString();

                // Make AJAX request
                $.ajax({
                    url: newUrl,
                    method: 'GET',
                    dataType: 'html',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                }).done(function(html) {
                    const $html = $(html);
                    const $newDoctorCards = $html.find('.doctor-cards-container');
                    const $newPagination = $html.find('.pagination');

                    // Update doctor cards
                    if ($newDoctorCards.length) {
                        $doctorCards.html($newDoctorCards.html());
                    } else {
                        $doctorCards.html(`
                            <div class="col-12 text-center py-5">
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    No doctors found matching your criteria.
                                </div>
                            </div>
                        `);
                    }

                    // Update pagination
                    if ($newPagination.length) {
                        $pagination.html($newPagination.html());
                    }

                    // Update URL without page reload
                    window.history.pushState({}, '', newUrl);

                }).fail(function(jqXHR, textStatus, error) {
                    console.error('Error fetching doctors:', error);
                    $doctorCards.html(`
                        <div class="col-12 text-center py-5">
                            <div class="alert alert-danger">
                                <i class="fa fa-exclamation-circle me-2"></i>
                                An error occurred while searching. Please try again.
                            </div>
                        </div>
                    `);
                }).always(function() {
                    isSearching = false;
                    $doctorCards.removeClass('loading');
                    $searchInput.prop('disabled', false);
                    $sortSelect.prop('disabled', false);
                    $searchButton.prop('disabled', false);
                    $searchButton.html('<i class="fa fa-search me-2"></i>Search');
                });
            }

            // Handle pagination clicks
            $(document).on('click', '.pagination .page-link', function(e) {
                e.preventDefault();
                if (!isSearching) {
                    const pageUrl = $(this).attr('href');
                    if (pageUrl) {
                        window.history.pushState({}, '', pageUrl);
                        updateDoctorList();
                    }
                }
            });
        });
    })(jQuery);
}
