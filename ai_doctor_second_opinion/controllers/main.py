from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.addons.payment.controllers.portal import PaymentPortal
import base64
from odoo.exceptions import AccessError, ValidationError
import logging
import traceback
from datetime import datetime
import json
import werkzeug

from odoo.addons.website_payment.controllers import portal as payment_portal
from odoo.addons.portal.controllers.portal import CustomerPortal

_logger = logging.getLogger(__name__)

class SecondOpinionPortal(CustomerPortal):
    # def _prepare_home_portal_values(self, counters):
    #     """
    #     Prepare values for the portal home page
    #     Add counters and details for second opinion requests
    #     """
    #     values = super(SecondOpinionPortal, self)._prepare_home_portal_values(counters)
        
    #     # Only process for logged-in users
    #     if request.env.user._is_public():
    #         # Ensure default values for counts
    #         values.update({
    #             'draft_count': 0,
    #             'pending_payment_count': 0,
    #             'paid_count': 0,
    #             'in_progress_count': 0,
    #             'completed_count': 0,
    #             'second_opinion_requests': []
    #         })
    #         return values

    #     # Count second opinion requests
    #     SecondOpinionRequest = request.env['second.opinion.request']
    #     partner_id = request.env.user.partner_id.id
        
    #     # Define states to count
    #     request_states = {
    #         'draft': 'draft_count',
    #         'pending_payment': 'pending_payment_count', 
    #         'paid': 'paid_count', 
    #         'in_progress': 'in_progress_count',
    #         'completed': 'completed_count'
    #     }
        
    #     # Count requests for each state
    #     for state, counter_name in request_states.items():
    #         values[counter_name] = SecondOpinionRequest.search_count([
    #             ('patient_id', '=', partner_id),
    #             ('state', '=', state)
    #         ]) or 0
        
    #     # Get recent second opinion requests
    #     values['second_opinion_requests'] = SecondOpinionRequest.search([
    #         ('patient_id', '=', partner_id)
    #     ], limit=3, order='create_date desc')
        
    #     # Log for debugging
    #     _logger.info(f"Portal Home Values: {values}")
        
    #     return values

    @http.route(['/my/requests'], type='http', auth="user", website=True)
    def portal_my_requests(self, **kw):
        """Display customer's second opinion requests"""
        values = self._prepare_portal_layout_values()
        
        # Get the logged-in user's partner
        partner = request.env.user.partner_id
        
        # Search for requests where the patient is the current user
        requests = request.env['second.opinion.request'].search([
            ('patient_id', '=', partner.id)
        ])
        
        values.update({
            'requests': requests,
            'page_name': 'my_requests',
        })
        
        return request.render("website.my-consultations", values)
        # return request.render("ai_doctor_second_opinion.portal_my_requests", values)


    @http.route(['/my/requests/<int:request_id>/pay'], type='http', auth="user", website=True)
    def payment_provider_selection(self, request_id, **post):
        """Initialize payment for second opinion request"""
        try:
            # Get the second opinion request
            second_opinion = request.env['second.opinion.request'].sudo().browse(request_id)
            if not second_opinion.exists():
                return request.redirect('/my/home')

            # Check if doctor is assigned
            if not second_opinion.doctor_id:
                return request.redirect(f'/select/doctor?request_id={request_id}&is_followup={1 if second_opinion.is_followup else 0}')

            # Get the invoice
            invoice = second_opinion.invoice_id
            if not invoice or invoice.state != 'draft':
                return request.redirect('/my/home')

            # Store payment info in session
            request.session['second_opinion_payment'] = {
                'request_id': request_id,
                'invoice_id': invoice.id,
                'amount': invoice.amount_total,
                'currency_id': invoice.currency_id.id,
                'partner_id': invoice.partner_id.id,
                'reference': f"SO-{second_opinion.name}-{invoice.name}",
            }

            # Redirect to donation payment page
            return request.redirect('/donation/pay')

        except Exception as e:
            _logger.error(f"Error in payment process: {str(e)}\n{traceback.format_exc()}")
            return request.redirect('/my/home')

    @http.route(['/payment/transaction/<int:tx_id>'], type='json', auth='public')
    def payment_transaction(self, tx_id, access_token=None, **kwargs):
        """Link transaction with second opinion request"""
        try:
            tx_sudo = request.env['payment.transaction'].sudo().browse(tx_id)
            if not tx_sudo.exists():
                return False
            
            # Get second opinion request from session
            payment_info = request.session.get('second_opinion_payment', {})
            if payment_info and payment_info.get('request_id'):
                request_id = payment_info.get('request_id')
                tx_sudo.write({
                    'second_opinion_request_id': request_id,
                })
            
            return tx_sudo
        except Exception as e:
            _logger.error(f"Error updating transaction: {str(e)}\n{traceback.format_exc()}")
            return None

    @http.route(['/my/requests/<int:request_id>/payment/success'], type='http', auth="user", website=True)
    def payment_success(self, request_id, **post):
        """Handle successful payment"""
        try:
            second_opinion = request.env['second.opinion.request'].sudo().browse(request_id)
            if not second_opinion.exists():
                return request.redirect('/my/home')

            # Update request status
            second_opinion.write({
                'state': 'paid',
                'payment_status': 'paid'
            })

            # Post and mark invoice as paid
            invoice = second_opinion.invoice_id
            if invoice and invoice.state == 'draft':
                invoice.sudo().action_post()
                invoice.sudo().action_invoice_paid()

            return request.render('ai_doctor_second_opinion.payment_success', {
                'second_opinion': second_opinion
            })

        except Exception as e:
            _logger.error(f"Error in payment success: {str(e)}\n{traceback.format_exc()}")
            return request.redirect('/my/home')

    @http.route(['/my/requests/<int:request_id>/payment/error'], type='http', auth="user", website=True)
    def payment_error(self, request_id, **post):
        """Handle payment error"""
        values = {
            'error': post.get('error_msg', 'An error occurred during payment processing.'),
            'request_id': request_id
        }
        return request.render('website_payment.donation_pay', values)

    @http.route(['/requests/add'], type='http', auth="user", website=True)
    def second_opinion_request(self, **post):
        symptoms = request.env['medical.symptom'].sudo().search([])
        issue_types = request.env['medical.speciality'].sudo().search([])
        
        # Get previous completed requests for follow-up selection
        previous_requests = request.env['second.opinion.request'].sudo().search([
            ('state', 'in', ['paid', 'assigned', 'completed']),
            ('patient_id', '=', request.env.user.partner_id.id)
        ], order='create_date desc')
        
        values = {
            'symptoms': symptoms if symptoms else [],
            # 'issue_types': issue_types if issue_types else [],
            'previous_requests': previous_requests if previous_requests else [],
        }
        if not request.env.user._is_public():
            values.update({
                'user_name': request.env.user.partner_id.name,
                'is_logged_in': True
            })
        else:
            values.update({
                'user_name': '',
                'is_logged_in': False
            })
            
        return request.render("website.get-second-opinion", values)

    @http.route(['/second-opinion/submit'], type='http', auth="public", website=True)
    def submit_request(self, **post):
        try:
            # Convert is_followup to boolean
            is_followup = post.get('is_followup') == '1'
            
            # Basic validation
            if is_followup and not post.get('previous_request_id'):
                return request.render('website.get-second-opinion', {
                    'error': 'Please select a previous request for follow-up consultation'
                })

            # Handle symptoms separately
            symptoms = post.get('symptoms[]', [])
            if isinstance(symptoms, str):
                symptoms = [symptoms]
            
            vals = {
                'patient_id': request.env.user.partner_id.id,
                'age': post.get('age'),
                'gender': post.get('gender'),
                'symptom_ids': [(6, 0, [int(symptom) for symptom in symptoms if symptom])],
                # 'issue_type': int(post.get('issue_type')),
                'custom_symptoms': post.get('custom_symptoms'),
                'description': post.get('description'),
                'is_followup': is_followup,
            }
            
            if is_followup:
                vals['previous_request_id'] = int(post.get('previous_request_id'))
                # Get doctor from previous request
                prev_request = request.env['second.opinion.request'].sudo().browse(int(post.get('previous_request_id')))
                if prev_request.exists():
                    vals['doctor_id'] = prev_request.doctor_id.id

            # Create request
            second_opinion = request.env['second.opinion.request'].sudo().create(vals)
            
            # Handle file uploads
            for field in ['lab_report_ids', 'medicine_photo_ids', 'prescription_ids', 
                         'xray_ids', 'ct_scan_ids', 'mri_ids', 'doppler_ids', 'sonography_ids']:
                files = request.httprequest.files.getlist(field)
                attachments = []
                for upload in files:
                    if upload:
                        attachment = request.env['ir.attachment'].sudo().create({
                            'name': upload.filename,
                            'type': 'binary',
                            'datas': base64.b64encode(upload.read()),
                            'res_model': 'second.opinion.request',
                            'res_id': second_opinion.id
                        })
                        attachments.append(attachment.id)
                if attachments:
                    second_opinion.write({field: [(6, 0, attachments)]})
            if not is_followup:
                return request.redirect('/select/doctor?request_id=%s' % second_opinion.id)
            else:
                return request.redirect('/my/requests/%s' % second_opinion.id)
            
        except Exception as e:
            _logger.error(f"Error in submit request: {str(e)}\n{traceback.format_exc()}")
            return request.render('website.get-second-opinion', {
                'error': 'An error occurred while processing your request. Please try again.'
            })

    @http.route(['/select/doctor/<int:doctor_id>'], type='http', auth="public", website=True)
    def select_doctor(self, doctor_id, **post):
        request_id = request.params.get('request_id')
        if not request_id:
            return request.redirect('/my/home')
            
        second_opinion_request = request.env['second.opinion.request'].sudo().browse(int(request_id))
        doctor = request.env['medical.doctor'].sudo().browse(doctor_id)
        
        if second_opinion_request and doctor:
            # Determine fee based on request type
            fee = doctor.followup_case_consultation_fee if second_opinion_request.is_followup else doctor.new_case_consultation_fee
            
            # Update request with selected doctor and their consultation fee
            second_opinion_request.write({
                'doctor_id': doctor.id,
                'amount': fee,
                'state': 'pending_payment'
            })
            
            # Create or update invoice
            if not second_opinion_request.invoice_id:
                # Create invoice
                invoice_vals = {
                    'partner_id': request.env.user.partner_id.id,
                    'move_type': 'out_invoice',
                    'invoice_line_ids': [(0, 0, {
                        'product_id': request.env.ref('ai_doctor_second_opinion.consultation_fee_product').id,
                        'name': f"Consultation Fee - Dr. {doctor.name} ({'Follow-up' if second_opinion_request.is_followup else 'New Case'})",
                        'quantity': 1.0,
                        'price_unit': fee,
                    })],
                }
                invoice = request.env['account.move'].sudo().create(invoice_vals)
                second_opinion_request.write({'invoice_id': invoice.id})
            else:
                # Update existing invoice
                invoice = second_opinion_request.invoice_id.sudo()
                if invoice.state == 'draft':
                    for line in invoice.invoice_line_ids:
                        if line.product_id.is_consultation_fee:
                            line.write({
                                'price_unit': fee,
                                'name': f"Consultation Fee - Dr. {doctor.name} ({'Follow-up' if second_opinion_request.is_followup else 'New Case'})"
                            })
                    invoice._compute_amount()
            
            return request.redirect(f'/my/requests/{second_opinion_request.id}')
        return request.redirect('/my/home')

    @http.route(['/assign/doctor/<int:request_id>/<int:doctor_id>'], type='http', auth="public", website=True)
    def assign_doctor(self, request_id, doctor_id, **kwargs):
        second_opinion = request.env['second.opinion.request'].sudo().browse(request_id)
        doctor = request.env['medical.doctor'].sudo().browse(doctor_id)
        
        if second_opinion and doctor:
            # Determine fee based on request type
            fee = doctor.followup_case_consultation_fee if second_opinion.is_followup else doctor.new_case_consultation_fee
            
            # Update request with selected doctor
            second_opinion.write({
                'doctor_id': doctor.id,
                'amount': fee,
                'state': 'assigned'
            })
            
            # Update the invoice with doctor's consultation fee
            if second_opinion.invoice_id:
                invoice = second_opinion.invoice_id.sudo()
                if invoice.state == 'draft':
                    for line in invoice.invoice_line_ids:
                        if line.product_id.is_consultation_fee:
                            line.write({
                                'name': f"Consultation Fee - Dr. {doctor.name} ({'Follow-up' if second_opinion.is_followup else 'New Case'})",
                                'price_unit': fee,
                            })
                    invoice._compute_amount()
            
            return request.redirect('/my/requests')
        return request.redirect('/my/home')

    @http.route(['/select/doctor'], type='http', auth="public", website=True)
    def doctor_selection(self, **post):
        request_id = int(post.get('request_id'))
        page = int(post.get('page', 1))
        sort = post.get('sort', 'best_match')
        search = post.get('search', '')
        
        domain = [('state', '=', 'approved')]
        order = 'name ASC'  # default ordering

        # Apply search if provided
        if search:
            domain += [
                '|', '|',
                ('name', 'ilike', search),
                ('speciality_ids.name', 'ilike', search),
                ('qualification', 'ilike', search)
            ]

        # Apply sorting
        if sort == 'name_asc':
            order = 'name ASC'
        elif sort == 'name_desc':
            order = 'name DESC'
        elif sort == 'fee_asc':
            order = 'new_case_consultation_fee ASC'
        elif sort == 'fee_desc':
            order = 'new_case_consultation_fee DESC'
        elif sort == 'best_match':
            # Get the request's specialities and symptoms
            second_opinion_request = request.env['second.opinion.request'].sudo().browse(request_id)
            if second_opinion_request.issue_type:
                domain += [('speciality_ids', 'in', [second_opinion_request.issue_type.id])]
            order = 'experience DESC'

        # Count total doctors
        total = request.env['medical.doctor'].sudo().search_count(domain)
        
        # Configure pager
        url = '/select/doctor?request_id=%s' % request_id
        pager = request.website.pager(
            url=url,
            total=total,
            page=page,
            step=6,  # 6 doctors per page
            scope=5,
        )

        # Get doctors for current page
        offset = (page - 1) * 6
        doctors = request.env['medical.doctor'].sudo().search(
            domain,
            order=order,
            limit=6,
            offset=offset
        )

        second_opinion_request = request.env['second.opinion.request'].sudo().browse(request_id)
        
        values = {
            'doctors': doctors,
            'pager': pager,
            'request_id': request_id,
            'current_sort': sort,
            'search_query': search,
            'is_followup': second_opinion_request.is_followup
        }
        
        return request.render("ai_doctor_second_opinion.doctor_selection", values)

    @http.route(['/new/request'], type='http', auth="public", website=True)
    def new_request(self, **kw):
        symptoms = request.env['medical.symptom'].sudo().search([])
        specialities = request.env['medical.speciality'].sudo().search([])
        
        values = {
            'symptoms': symptoms,
            'specialities': specialities,
            'page_name': 'new_request',
        }
        
        if not request.env.user._is_public():
            values.update({
                'user_name': request.env.user.partner_id.name,
                'is_logged_in': True
            })
        else:
            values.update({
                'user_name': '',
                'is_logged_in': False
            })
            
        return request.render("ai_doctor_second_opinion.new_request_form", values)

    def _prepare_portal_layout_values(self):
        """Prepare values for portal layout"""
        return {
            'page_name': 'second_opinion_request',
        }

    def _get_second_opinion_product(self):
        """Get the medical second opinion product"""
        return request.env['product.product'].sudo().search(
            [('name', '=', 'Medical Second Opinion')], limit=1)

    def _create_sale_order(self, second_opinion_request):
        """Create a sale order for the second opinion request"""
        product = self._get_second_opinion_product()
        if not product:
            raise ValueError(_("Medical Second Opinion product not found"))

        # Create a new sale order
        sale_order = request.env['sale.order'].sudo().create({
            'partner_id': request.env.user.partner_id.id,
            'second_opinion_request_id': second_opinion_request.id,  # Custom field to link the request
            'order_line': [(0, 0, {
                'product_id': product.id,
                'product_uom_qty': 1.0,
                'name': f"Second Opinion Request: {second_opinion_request.name}",
                'price_unit': product.list_price,
            })],
            'website_id': request.website.id,  # Set website to ensure it's available in e-commerce
        })
        return sale_order

    @http.route(['/get/doctor/fee'], type='json', auth="public", website=True)
    def get_doctor_fee(self, doctor_id, is_followup=False):
        doctor = request.env['medical.doctor'].sudo().browse(int(doctor_id))
        if doctor.exists():
            return {
                'success': True,
                'amount': doctor.followup_case_consultation_fee if is_followup else doctor.new_case_consultation_fee,
                'currency': doctor.currency_id.symbol
            }
        return {'success': False}

    def _prepare_sale_order_values(self, doctor, request_id=False):
        sale_order = request.website.sale_get_order(force_create=True)
        if sale_order.state in ['draft', 'sent']:
            order_lines = [(5, 0, 0)]
            if request_id:
                second_request = request.env['second.opinion.request'].sudo().browse(request_id)
                fee = doctor.followup_case_consultation_fee if second_request.is_followup else doctor.new_case_consultation_fee
                order_lines.append(
                    (0, 0, {
                        'product_id': request.env.ref('ai_doctor_second_opinion.consultation_fee_product').id,
                        'name': f"Consultation Fee for {doctor.name}",
                        'price_unit': fee,
                        'product_uom_qty': 1.0
                    })
                )
            sale_order.write({'order_line': order_lines})
        return sale_order

    def _update_sale_order(self, sale_order, doctor):
        for line in sale_order.order_line:
            if line.product_id.is_consultation_fee:
                line.write({
                    'name': f"Consultation Fee for {doctor.name}",
                    'price_unit': doctor.followup_case_consultation_fee if request.is_followup else doctor.new_case_consultation_fee,
                })

    @http.route(['/my/requests/<int:request_id>'], type='http', auth="user", website=True)
    def second_opinion_detail(self, request_id, **kw):
        try:
            request_sudo = request.env['second.opinion.request'].sudo().browse(request_id)

            if not request_sudo.exists():
                raise NotFound()

            if request_sudo.patient_id != request.env.user.partner_id:
                raise Forbidden()

            _logger.info(
                "Portal access to second opinion request ID %s by user ID %s",
                request_id,
                request.env.user.id
            )

            values = self._prepare_portal_layout_values()

            # Add payment-related information
            values.update({
                'results': request_sudo,
                'page_name': 'second_opinion_detail',
                'default_url': f'/my/requests/{request_id}',
                'show_pay_button': request_sudo.state in ['draft','pending_payment'],
                'product': self._get_second_opinion_product(),
            })

            return request.render(
                "ai_doctor_second_opinion.portal_request_detail",
                values
            )

        except (AccessError, NotFound):
            _logger.warning(
                "Access attempt to non-existent second opinion request ID %s by user ID %s",
                request_id,
                request.env.user.id,
                exc_info=True
            )
            return request.redirect('/my')

        except Forbidden:
            _logger.warning(
                "Unauthorized access attempt to second opinion request ID %s by user ID %s",
                request_id,
                request.env.user.id,
                exc_info=True
            )
            return request.redirect('/my')

        except Exception as e:
            _logger.error(
                "Error displaying second opinion request ID %s: %s",
                request_id,
                str(e),
                exc_info=True
            )
            values = {
                'error_message': _("An error occurred while processing your request. Please try again later.")
            }
            return request.render("website.404", values)

    @http.route(['/my/requests/search'], type='json', auth="user", website=True)
    def search_requests(self, **kw):
        domain = [('patient_id', '=', request.env.user.partner_id.id)]
        
        # Apply search filter
        search = kw.get('search', '')
        if search:
            domain += [
                '|', '|', '|',
                ('name', 'ilike', search),
                ('symptom_ids.name', 'ilike', search),
                ('custom_symptoms', 'ilike', search),
                ('description', 'ilike', search)
            ]
        
        # Apply status filter
        status = kw.get('status')
        if status:
            domain.append(('state', '=', status))
            
        # Apply sorting
        sort = kw.get('sort', 'date_desc')
        order = {
            'date_desc': 'create_date desc',
            'date_asc': 'create_date asc',
            'ref_desc': 'name desc',
            'ref_asc': 'name asc'
        }.get(sort, 'create_date desc')
        
        # Get total count for pagination
        total = request.env['second.opinion.request'].search_count(domain)
        
        # Apply pagination
        page = int(kw.get('page', 1))
        limit = int(kw.get('limit', 10))
        offset = (page - 1) * limit
        
        # Get requests
        requests = request.env['second.opinion.request'].search(
            domain,
            order=order,
            limit=limit,
            offset=offset
        )
        
        # Render table content
        values = {
            'requests': requests
        }
        content = request.env['ir.ui.view']._render_template(
            'ai_doctor_second_opinion.portal_my_requests_table_content',
            values
        )
        
        return {
            'html': content,
            'total': total
        }


class CustomPaymentPortal(payment_portal.PaymentPortal):

    @http.route(['/donation/transaction/<minimum_amount>'], type='json', auth='public')
    def donation_transaction(self, amount, currency_id, partner_id, access_token, minimum_amount=0, **kwargs):
        """Create payment transaction for second opinion request"""
        # Validate transaction kwargs
        allowed_keys = ['provider_id', 'metal_id']  # Added metal_id to allowed keys
        self._validate_transaction_kwargs(kwargs, allowed_keys)

        tx_sudo = super(CustomPaymentPortal, self).donation_transaction(
            amount, currency_id, partner_id, access_token, minimum_amount, **kwargs
        )
        payment_info = request.session.get('second_opinion_payment', {})
        if payment_info:
            second_opinion = request.env['second.opinion.request'].sudo().browse(payment_info.get('request_id'))
            if second_opinion:                
                payment_transaction = request.env['payment.transaction'].sudo().search(
                    [('reference', '=', tx_sudo['reference']), ('provider_id', '=', tx_sudo['provider_id']),
                    ('partner_id', '=', tx_sudo['partner_id']), ('amount', '=', tx_sudo['amount'])], limit=1,
                    order='create_date desc')
                _logger.info("Callback Model ID: %s", str(request.env['ir.model']._get_id('second.opinion.request')))
                model_id = int(request.env['ir.model']._get_id('second.opinion.request'))
                payment_transaction.sudo().write({
                    'second_opinion_request_id': second_opinion.id,
                    'callback_model_id': model_id,
                    'callback_res_id': int(second_opinion.id),
                    'callback_method': '_handle_payment_success',
                    'callback_hash' : payment_transaction._generate_callback_hash(model_id, int(second_opinion.id), '_handle_payment_success')
                })
                _logger.info("Creating transaction for second opinion Payment Provider %s", str(payment_transaction.state))
                request.session.pop('second_opinion_payment', None)
                # second_opinion._handle_payment_success(payment_transaction)

        return tx_sudo

    @staticmethod
    def _validate_transaction_kwargs(kwargs, additional_allowed_keys=()):
        """ Verify that the keys of a transaction route's kwargs are all whitelisted.

        The whitelist consists of all the keys that are expected to be passed to a transaction
        route, plus optional contextually allowed keys.

        This method must be called in all transaction routes to ensure that no undesired kwarg can
        be passed as param and then injected in the create values of the transaction.

        :param dict kwargs: The transaction route's kwargs to verify.
        :param tuple additional_allowed_keys: The keys of kwargs that are contextually allowed.
        :return: None
        :raise ValidationError: If some kwargs keys are rejected.
        """
        whitelist = {
            'provider_id',
            'payment_method_id',
            'token_id',
            'amount',
            'flow',
            'tokenization_requested',
            'landing_route',
            'is_validation',
            'csrf_token',
            'metal_id',
            # Donation-specific keys
            'donation_comment',
            'donation_recipient_email',
            'partner_details',
            'reference_prefix',
        }
        whitelist.update(additional_allowed_keys)
        rejected_keys = set(kwargs.keys()) - whitelist
        if rejected_keys:
            raise ValidationError(
                _("The following kwargs are not whitelisted: %s", ', '.join(rejected_keys))
            )

    def _get_payment_success_url(self, **kwargs):
        """Override to handle second opinion payment success"""
        second_opinion_id = kwargs.get('second_opinion_request_id')
        if second_opinion_id:
            return f'/my/second-opinion-requests/{second_opinion_id}'
        return super()._get_payment_success_url(**kwargs)

    def _get_payment_error_url(self, **kwargs):
        """Override to handle second opinion payment error"""
        second_opinion_id = kwargs.get('second_opinion_request_id')
        if second_opinion_id:
            return f'/my/second-opinion-requests/{second_opinion_id}?error=payment'
        return super()._get_payment_error_url(**kwargs)

    # def _handle_payment_success(self, transaction):
    #     """
    #     Handle successful payment for second opinion request
    #     Override to add custom success handling
    #     """
    #     try:
    #         # Ensure transaction is in a successful state
    #         if transaction.state != 'done':
    #             _logger.warning("Transaction %s not in done state", transaction.reference)
    #             return False
    #         _logger.info("$$$Transaction %s in done state", transaction.reference)
    #         # Retrieve the second opinion request
    #         second_opinion = self.env['second.opinion.request'].sudo().browse(transaction.second_opinion_request_id.id)
    #         if not second_opinion:
    #             _logger.error("No second opinion request found for transaction %s", transaction.reference)
    #             return False

    #         # Update request status
    #         second_opinion.sudo().write({
    #             'state': 'payment_received',
    #             'payment_transaction_id': transaction.id,
    #         })

    #         # Send confirmation email
    #         try:
    #             template = self.env.ref('ai_doctor_second_opinion.second_opinion_payment_confirmation_email')
    #             template.sudo().send_mail(second_opinion.id, force_send=True)
    #         except Exception as email_error:
    #             _logger.error("Failed to send payment confirmation email: %s", str(email_error))

    #         return True
    #     except Exception as e:
    #         _logger.error("Error in payment success handling: %s", str(e))
    #         return False
