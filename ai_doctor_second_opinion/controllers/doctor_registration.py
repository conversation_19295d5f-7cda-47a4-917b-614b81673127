from odoo import http, _
from odoo.http import request, json
import base64
from odoo.exceptions import ValidationError
import logging

from odoo.exceptions import UserError  # Import UserError
from odoo.http import request
_logger = logging.getLogger(__name__)

class DoctorRegistration(http.Controller):
    
    @http.route(['/doctor/registration'], type='http', auth='public', website=True)
    def doctor_registration_form(self, **kw):
        specialities = request.env['medical.speciality'].sudo().search([])
        
        # Fetch previous requests with specific states
        # previous_requests = request.env['second.opinion.request'].sudo().search([
        #     ('state', 'in', ['paid', 'assigned', 'completed'])
        # ])
        
        values = {
            'specialities': specialities,
            # 'previous_requests': previous_requests,
        }
        
        return request.render('website.register-as-doctor', values)
        # return request.render('ai_doctor_second_opinion.doctor_registration_form', values)

    @http.route(['/doctor/register'], type='http', auth='public', website=True, methods=['POST'])
    def doctor_register(self, **post):
        _logger.info("Received registration data: %s", post)  # Log incoming data

        if post:
            # Validate required fields
            required_fields = ['name']
            for field in required_fields:
                if field not in post or not post[field]:
                    _logger.warning("Missing required field: %s", field)  # Log missing field
                    raise UserError(f"{field.replace('_', ' ').capitalize()} is required.")

            # Check if the email already exists
            existing_doctor = request.env['medical.doctor'].sudo().search([('email', '=', post.get('email'))], limit=1)
            if existing_doctor:
                _logger.warning("Email already exists: %s", post.get('email'))  # Log existing email
                return json.dumps({
                    'error': True,
                    'message': 'A registration request with this email already exists.'
                })

            try:
                # Process file uploads
                resume = post.get('resume')
                license_doc = post.get('license_doc')
                photo = post.get('photo')

                # Prepare values for the new doctor record
                values = {
                    'name': post.get('name'),
                    'email': post.get('email'),
                    'phone': post.get('phone'),
                    'registration_number': post.get('registration_number',''),
                    'experience': int(post.get('experience', 0)),
                    'qualification': post.get('qualification'),
                    'address': post.get('address'),
                    'speciality_ids': [(6, 0, post.get('speciality_ids', []))],
                    'state': 'pending',
                    'new_case_consultation_fee': float(post.get('new_case_consultation_fee', 0.0)),
                    'followup_case_consultation_fee': float(post.get('followup_case_consultation_fee', 0.0)),
                }

                _logger.info("Prepared values for doctor record: %s", values)  # Log prepared values

                # Handle file uploads
                if resume:
                    values.update({
                        'resume': base64.b64encode(resume.read()),
                        'resume_filename': resume.filename
                    })

                if license_doc:
                    values.update({
                        'license_doc': base64.b64encode(license_doc.read()),
                        'license_filename': license_doc.filename
                    })

                if photo:
                    values.update({
                        'photo': base64.b64encode(photo.read()),
                        'photo_filename': photo.filename
                    })

                # Handle specialities
                if post.get('speciality_ids'):
                    if isinstance(post.get('speciality_ids'), str):
                        speciality_ids = [int(post.get('speciality_ids'))]
                    else:
                        speciality_ids = [int(id) for id in post.getlist('speciality_ids')]
                        values['speciality_ids'] = [(6, 0, speciality_ids)]

                # Create doctor record
                doctor = request.env['medical.doctor'].sudo().create(values)
                _logger.info("Doctor record created with ID: %s", doctor.id)  # Log successful creation

                # Send notification email to admin
                template = request.env.ref('ai_doctor_second_opinion.doctor_registration_notification_template')
                if template:
                    template.sudo().send_mail(doctor.id, force_send=True)

                return request.render('ai_doctor_second_opinion.doctor_registration_success')

            except Exception as e:
                _logger.error("Error during doctor registration: %s", str(e))  # Log the error
                return json.dumps({
                    'error': True,
                    'message': 'An error occurred during registration. Please try again.'
                })

        # If no post data, render the registration form
        specialities = request.env['medical.speciality'].sudo().search([])
        values = {
            'specialities': specialities
        }
        return request.render("website.register-as-doctor", values)
        # return request.render("ai_doctor_second_opinion.doctor_registration_form", values)