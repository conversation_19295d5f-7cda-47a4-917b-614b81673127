# from odoo import http, _
# from odoo.http import request
# from odoo.addons.payment.controllers.portal import PaymentPortal
# import logging
# import werkzeug
# from odoo.fields import Datetime

# _logger = logging.getLogger(__name__)

# class SecondOpinionPayment(PaymentPortal):

#     def _get_payment_success_url(self, **kwargs):
#         """Override success URL for second opinion payments"""
#         is_second_opinion = request.session.get('second_opinion_payment', {})
#         if is_second_opinion:
#             request_id = is_second_opinion.get('request_id')
#             return f'/my/requests/{request_id}/payment/success'
#         return super()._get_payment_success_url(**kwargs)

#     def _get_payment_error_url(self, **kwargs):
#         """Override error URL for second opinion payments"""
#         is_second_opinion = request.session.get('second_opinion_payment', {})
#         if is_second_opinion:
#             request_id = is_second_opinion.get('request_id')
#             return f'/my/requests/{request_id}/payment/error'
#         return super()._get_payment_error_url(**kwargs)

#     def _handle_payment_success(self, transaction):
#         """
#         Handle successful payment for second opinion request
#         Override to add custom success handling
#         """
#         try:
#             # Ensure transaction is in a successful state
#             if transaction.state != 'done':
#                 _logger.warning("Transaction %s not in done state", transaction.reference)
#                 return False

#             # Retrieve the second opinion request
#             second_opinion = self.env['second.opinion.request'].sudo().browse(transaction.second_opinion_request_id.id)
#             if not second_opinion:
#                 _logger.error("No second opinion request found for transaction %s", transaction.reference)
#                 return False

#             # Update request status
#             second_opinion.sudo().write({
#                 'state': 'payment_received',
#                 'payment_transaction_id': transaction.id,
#             })

#             # Send confirmation email
#             try:
#                 template = self.env.ref('ai_doctor_second_opinion.second_opinion_payment_confirmation_email')
#                 template.sudo().send_mail(second_opinion.id, force_send=True)
#             except Exception as email_error:
#                 _logger.error("Failed to send payment confirmation email: %s", str(email_error))

#             return True
#         except Exception as e:
#             _logger.error("Error in payment success handling: %s", str(e))
#             return False

#     def _execute_callback(self):
#         """
#         Override callback execution to handle potential hash validation issues
#         """
#         try:
#             # Attempt to execute the original callback
#             return super()._execute_callback()
#         except TypeError as e:
#             # Log the specific error
#             _logger.error("Callback execution error: %s", str(e))
            
#             # If callback hash validation fails, try alternative approach
#             if self.callback_model_id and self.callback_res_id and self.callback_method:
#                 try:
#                     callback_model = self.env[self.callback_model_id.model]
#                     callback_record = callback_model.sudo().browse(self.callback_res_id)
                    
#                     # Directly call the callback method
#                     callback_method = getattr(callback_record, self.callback_method, None)
#                     if callback_method:
#                         result = callback_method(self)
#                         return result
#                 except Exception as callback_error:
#                     _logger.error("Alternative callback execution failed: %s", str(callback_error))
            
#             # If all else fails, return False
#             return False

#     def _get_payment_transaction_values(self, **kwargs):
#         """Override to add custom values for second opinion payments"""
#         values = super()._get_payment_transaction_values(**kwargs)
#         request_id = kwargs.get('request_id')
#         if request_id:
#             values.update({
#                 'reference_prefix': kwargs.get('reference', ''),
#                 'request_id': request_id,
#             })
#         return values

#     @http.route(['/payment/transaction/<int:tx_id>'], type='json', auth='public')
#     def payment_transaction(self, tx_id, **kwargs):
#         """Handle payment transaction"""
#         tx = request.env['payment.transaction'].sudo().browse(tx_id)
#         if not tx:
#             return False

#         # Get payment info from session
#         payment_info = request.session.get('second_opinion_payment')
#         if not payment_info:
#             return False

#         try:
#             # Process payment
#             tx.sudo().write({
#                 'state': 'done',
#                 'date': Datetime.now(),
#             })

#             # Update second opinion request
#             request_id = payment_info.get('request_id')
#             if request_id:
#                 second_opinion = request.env['second.opinion.request'].sudo().browse(request_id)
#                 if second_opinion.exists():
#                     second_opinion.write({
#                         'state': 'paid',
#                         'payment_status': 'paid'
#                     })

#                     # Update invoice
#                     invoice_id = payment_info.get('invoice_id')
#                     if invoice_id:
#                         invoice = request.env['account.move'].sudo().browse(invoice_id)
#                         if invoice and invoice.state == 'draft':
#                             invoice.sudo().action_post()
#                             invoice.sudo().action_invoice_paid()

#             # Clear session data
#             request.session.pop('second_opinion_payment', None)

#             return True

#         except Exception as e:
#             _logger.error(f"Error processing payment: {str(e)}")
#             return False

#     @http.route(['/payment/confirmation/<int:tx_id>'], type='http', auth='public')
#     def payment_confirmation(self, tx_id, **kwargs):
#         """Payment confirmation page"""
#         tx = request.env['payment.transaction'].sudo().browse(tx_id)
#         if not tx:
#             return request.redirect('/my/home')

#         payment_info = request.session.get('second_opinion_payment')
#         if not payment_info:
#             return request.redirect('/my/home')

#         request_id = payment_info.get('request_id')
#         if tx.state == 'done':
#             return request.redirect(f'/my/requests/{request_id}/payment/success')
#         else:
#             return request.redirect(f'/my/requests/{request_id}/payment/error')
