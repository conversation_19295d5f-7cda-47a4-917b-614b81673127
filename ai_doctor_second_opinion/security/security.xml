<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="group_medical_doctor" model="res.groups">
        <field name="name">Medical Doctor</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="group_medical_admin" model="res.groups">
        <field name="name">Medical Administrator</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <!-- Record Rules -->
    <record id="rule_doctor_see_own_requests" model="ir.rule">
        <field name="name">Doctors can only see their assigned requests</field>
        <field name="model_id" ref="ai_doctor_second_opinion.model_second_opinion_request"/>
        <field name="domain_force">[('doctor_id.user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('ai_doctor_second_opinion.group_medical_doctor'))]"/>
    </record>

    <record id="rule_doctor_profile" model="ir.rule">
        <field name="name">Doctors can only see their profile</field>
        <field name="model_id" ref="ai_doctor_second_opinion.model_medical_doctor"/>
        <field name="domain_force">[('user_id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('ai_doctor_second_opinion.group_medical_doctor'))]"/>
    </record>
</odoo>