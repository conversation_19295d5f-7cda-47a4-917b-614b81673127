<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- State Medical Council Form View -->
    <record id="view_state_medical_council_form" model="ir.ui.view">
        <field name="name">state.medical.council.form</field>
        <field name="model">state.medical.council</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name" placeholder="Council Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="active"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- State Medical Council Tree View -->
    <record id="view_state_medical_council_tree" model="ir.ui.view">
        <field name="name">state.medical.council.tree</field>
        <field name="model">state.medical.council</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="code"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- State Medical Council Search View -->
    <record id="view_state_medical_council_search" model="ir.ui.view">
        <field name="name">state.medical.council.search</field>
        <field name="model">state.medical.council</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <!-- State Medical Council Action -->
    <record id="action_state_medical_council" model="ir.actions.act_window">
            <field name="name">State Medical Councils</field>
            <field name="res_model">state.medical.council</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="view_state_medical_council_tree"/>
            <field name="search_view_id" ref="view_state_medical_council_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first State Medical Council!
                </p>
            </field>
        </record>

    <!-- Menu Item -->
    <menuitem id="menu_state_medical_council"
              name="State Medical Councils"
              parent="menu_medical_doctor_root"
              action="action_state_medical_council"
              sequence="30"
              groups="ai_doctor_second_opinion.group_medical_admin"/>
</odoo>
