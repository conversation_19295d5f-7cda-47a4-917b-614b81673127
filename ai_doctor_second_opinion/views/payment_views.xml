<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Add menu item for payment configuration -->
        <!-- <record id="action_payment_provider_config" model="ir.actions.act_window">
            <field name="name">Payment Providers</field>
            <field name="res_model">payment.provider</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_state': 'enabled'}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Configure your payment providers
                </p>
            </field>
        </record>

        <menuitem id="menu_payment_provider_config"
                  name="Payment Providers"
                  parent="menu_second_opinion_config"
                  action="action_payment_provider_config"
                  sequence="30"/> -->
    </data>
</odoo>
