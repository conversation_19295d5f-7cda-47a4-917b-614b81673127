<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Medical Doctor Form View -->
    <record id="view_medical_doctor_form" model="ir.ui.view">
        <field name="name">medical.doctor.form</field>
        <field name="model">medical.doctor</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_approve" string="Approve" type="object" class="oe_highlight" 
                            invisible="state not in ['pending']"/>
                    <button name="action_reject" string="Reject" type="object" class="btn btn-danger" 
                            invisible="state not in ['pending', 'approved']"/>
                    <field name="state" widget="statusbar" options="{'clickable': '1'}" 
                           statusbar_visible="draft,approved,rejected"/>
                </header>
                <sheet>
                    <field name="photo" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1><field name="name" placeholder="Doctor Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="email"/>
                            <field name="phone"/>
                            <field name="registration_number"/>
                            <field name="experience"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="new_case_consultation_fee" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="followup_case_consultation_fee" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <!-- <field name="consultation_fee" widget="monetary"/> -->
                        </group>
                        <group>
                            <field name="speciality_ids" widget="many2many_tags"/>
                            <field name="qualification"/>
                            <field name="address"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Documents">
                            <group>
                                <field name="photo" filename="photo_filename"/>
                                <field name="photo_filename" invisible="1"/>
                                <field name="resume" filename="resume_filename"/>
                                <field name="resume_filename" invisible="1"/>
                                <field name="license_doc" filename="license_filename"/>
                                <field name="license_filename" invisible="1"/>
                            </group>
                        </page>
                        <page string="Second Opinion Requests">
                            <field name="request_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="patient_id"/>
                                    <field name="issue_type"/>
                                    <field name="state"/>
                                    <field name="create_date"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Medical Doctor Tree View -->
    <record id="view_medical_doctor_tree" model="ir.ui.view">
        <field name="name">medical.doctor.tree</field>
        <field name="model">medical.doctor</field>
        <field name="arch" type="xml">
            <tree>
                <field name="photo" widget="image"/>
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="registration_number"/>
                <field name="experience"/>
                <field name="speciality_ids" widget="many2many_tags"/>
                <field name="new_case_consultation_fee"/>
                <field name="followup_case_consultation_fee"/>
                <!-- <field name="consultation_fee"/> -->
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Medical Doctor Search View -->
    <record id="view_medical_doctor_search" model="ir.ui.view">
        <field name="name">medical.doctor.search</field>
        <field name="model">medical.doctor</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="registration_number"/>
                <field name="speciality_ids"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Pending" name="pending" domain="[('state', '=', 'pending')]"/>
                <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                <group expand="0" string="Group By">
                    <filter string="State" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Experience" name="group_by_experience" context="{'group_by': 'experience'}"/>
                    <filter string="New Case Fee" name="group_by_new_fee" context="{'group_by': 'new_case_consultation_fee'}"/>
                    <filter string="Follow-up Fee" name="group_by_followup_fee" context="{'group_by': 'followup_case_consultation_fee'}"/>
                    <!-- <filter string="Consultation Fee" name="group_by_fee" context="{'group_by': 'consultation_fee'}"/> -->
                </group>
            </search>
        </field>
    </record>
</odoo>
