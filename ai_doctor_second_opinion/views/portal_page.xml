<odoo>
<template id="my_request_portal" inherit_id="portal.portal_my_home" name="Custom My Request Code">
    <xpath expr="//div[@class='o_portal_docs row g-2']" position="inside">
        <div class="o_portal_category row g-2 mt-3" id="portal_request_category">
            <div class="o_portal_index_card col-md-6">
                <a href="/my/requests" title="Second Opinion Requests"
                   class="d-flex justify-content-start gap-2 gap-md-3 align-items-center py-3 pe-2 px-md-3 h-100 rounded text-decoration-none text-reset text-bg-light">
                    <div class="o_portal_icon align-self-start">
                        <img src="/ai_doctor_second_opinion/static/src/img/opinion.png" loading="lazy" alt="My Request Icon" width="64" height="64" />
                    </div>
                    <div>
                        <h5 class="mt-0 mb-1">My Consultations</h5>
                        <p class="mb-0 text-muted">View My Consultations for Second Opinions</p>
                    </div>
                </a>
            </div>
        </div>
    </xpath>
</template>


 <template id="portal_layout" name="Portal Layout: My Request" inherit_id="portal.portal_breadcrumbs" priority="40">
    <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
        <!-- Breadcrumb for the second opinion listing page -->
        <li t-if="page_name == 'My Medical Requests'" class="breadcrumb-item active">
            My Consultations
        </li>
        <!-- Breadcrumb for the request detail page -->
        <li t-if="page_name == 'Recent Second Opinion Requests'" class="breadcrumb-item">
            <a t-attf-href="/my/requests">Consultations</a>
        </li>
        <li t-if="page_name == 'second_opinion_detail'" class="breadcrumb-item active text-truncate col-8 col-lg-10">
            <t t-esc="results.name"/>
        </li>
    </xpath>
</template>


</odoo>