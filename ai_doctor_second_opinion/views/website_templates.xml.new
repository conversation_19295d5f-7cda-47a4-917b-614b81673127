<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="portal_my_requests" name="My Second Opinion Requests">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Medical Second Opinion Requests</t>
                </t>
                <div class="container mt-4">
                    <h3>Second Opinion Requests</h3>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-12 col-md-6">
                            <div class="input-group">
                                <input type="text" id="requestSearch" class="form-control" placeholder="Search by reference, symptoms, or description..."/>
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="fa fa-times"/>
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-3">
                            <select id="statusFilter" class="form-select">
                                <option value="">All Status</option>
                                <option value="draft">Draft</option>
                                <option value="pending_payment">Pending Payment</option>
                                <option value="paid">Paid</option>
                                <option value="assigned">Doctor Assigned</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-3">
                            <select id="sortOption" class="form-select">
                                <option value="date_desc">Newest First</option>
                                <option value="date_asc">Oldest First</option>
                                <option value="ref_asc">Reference (A-Z)</option>
                                <option value="ref_desc">Reference (Z-A)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading Spinner -->
                    <div id="requestsLoading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <div id="requestsTableContainer">
                        <div class="table-responsive">
                            <table class="table table-hover o_portal_my_doc_table">
                                <thead>
                                    <tr class="active">
                                        <th>Reference</th>
                                        <th>Patient Name</th>
                                        <th>Request Date</th>
                                        <th>Doctor</th>
                                        <th>Status</th>
                                        <th class="text-end">Action</th>
                                    </tr>
                                </thead>
                                <tbody id="requestsTableBody">
                                    <t t-call="ai_doctor_second_opinion.portal_my_requests_table_content"/>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <nav aria-label="Request navigation" class="d-flex justify-content-center mt-4">
                            <ul id="requestsPagination" class="pagination">
                                <!-- Pagination will be populated by JavaScript -->
                            </ul>
                        </nav>
                    </div>

                    <script type="text/javascript">
                        $(document).ready(function() {
                            var currentPage = 1;
                            var itemsPerPage = 10;
                            var searchTimer;

                            function loadRequests(page) {
                                var searchQuery = $('#requestSearch').val();
                                var statusFilter = $('#statusFilter').val();
                                var sortOption = $('#sortOption').val();

                                $('#requestsLoading').removeClass('d-none');
                                $('#requestsTableContainer').addClass('d-none');

                                $.ajax({
                                    url: '/my/requests/search',
                                    type: 'POST',
                                    dataType: 'json',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        jsonrpc: "2.0",
                                        method: "call",
                                        params: {
                                            search: searchQuery,
                                            status: statusFilter,
                                            sort: sortOption,
                                            page: page,
                                            limit: itemsPerPage
                                        }
                                    }),
                                    success: function(response) {
                                        if (response.result) {
                                            $('#requestsTableBody').html(response.result.html);
                                            updatePagination(response.result.total, page);
                                        }
                                        $('#requestsLoading').addClass('d-none');
                                        $('#requestsTableContainer').removeClass('d-none');
                                    },
                                    error: function() {
                                        $('#requestsLoading').addClass('d-none');
                                        $('#requestsTableContainer').removeClass('d-none');
                                    }
                                });
                            }

                            function updatePagination(total, currentPage) {
                                var totalPages = Math.ceil(total / itemsPerPage);
                                var $pagination = $('#requestsPagination');
                                $pagination.empty();

                                if (totalPages &lt;= 1) return;

                                // Previous button
                                var prevDisabled = currentPage === 1 ? 'disabled' : '';
                                $pagination.append(
                                    '&lt;li class="page-item ' + prevDisabled + '">' +
                                    '&lt;a class="page-link" href="#" data-page="' + (currentPage - 1) + '">Previous&lt;/a>' +
                                    '&lt;/li>'
                                );

                                // Page numbers
                                for (var i = 1; i &lt;= totalPages; i++) {
                                    if (i === 1 || i === totalPages || (i >= currentPage - 2 &amp;&amp; i &lt;= currentPage + 2)) {
                                        var active = i === currentPage ? 'active' : '';
                                        $pagination.append(
                                            '&lt;li class="page-item ' + active + '">' +
                                            '&lt;a class="page-link" href="#" data-page="' + i + '">' + i + '&lt;/a>' +
                                            '&lt;/li>'
                                        );
                                    } else if (i === currentPage - 3 || i === currentPage + 3) {
                                        $pagination.append('&lt;li class="page-item disabled">&lt;span class="page-link">...&lt;/span>&lt;/li>');
                                    }
                                }

                                // Next button
                                var nextDisabled = currentPage === totalPages ? 'disabled' : '';
                                $pagination.append(
                                    '&lt;li class="page-item ' + nextDisabled + '">' +
                                    '&lt;a class="page-link" href="#" data-page="' + (currentPage + 1) + '">Next&lt;/a>' +
                                    '&lt;/li>'
                                );
                            }

                            // Event handlers
                            $('#requestSearch').on('input', function() {
                                clearTimeout(searchTimer);
                                searchTimer = setTimeout(function() {
                                    currentPage = 1;
                                    loadRequests(currentPage);
                                }, 300);
                            });

                            $('#clearSearch').on('click', function() {
                                $('#requestSearch').val('');
                                currentPage = 1;
                                loadRequests(currentPage);
                            });

                            $('#statusFilter, #sortOption').on('change', function() {
                                currentPage = 1;
                                loadRequests(currentPage);
                            });

                            $(document).on('click', '#requestsPagination .page-link', function(e) {
                                e.preventDefault();
                                var page = $(this).data('page');
                                if (page &amp;&amp; !$(this).parent().hasClass('disabled')) {
                                    currentPage = page;
                                    loadRequests(page);
                                }
                            });

                            // Initial load
                            loadRequests(currentPage);
                        });
                    </script>
                </div>
            </t>
        </template>

        <template id="portal_my_requests_table_content" name="Second Opinion Requests Table Content">
            <t t-foreach="requests" t-as="req">
                <tr>
                    <td>
                        <a t-attf-href="/my/requests/#{req.id}">
                            <span t-field="req.name"/>
                        </a>
                    </td>
                    <td>
                        <span t-field="req.patient_id.name"/>
                    </td>
                    <td>
                        <span t-field="req.create_date" t-options='{"widget": "date"}'/>
                    </td>
                    <td>
                        <t t-if="req.doctor_id">
                            <span t-field="req.doctor_id.name"/>
                        </t>
                        <t t-else="">
                            <span class="text-muted">Not Assigned</span>
                        </t>
                    </td>
                    <td>
                        <span t-field="req.state" class="badge" t-attf-class="badge bg-#{req.state_color}"/>
                    </td>
                    <td class="text-end">
                        <a t-attf-href="/my/requests/#{req.id}" class="btn btn-sm btn-primary">
                            View
                        </a>
                    </td>
                </tr>
            </t>
        </template>
    </data>
</odoo>
