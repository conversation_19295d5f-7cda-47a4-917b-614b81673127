<odoo>
    <data>
        <!-- Website Page Record -->
        <record id="second_opinion_request_page" model="website.page">
            <field name="name">Get Second Opinion</field>
            <field name="url">/second-opinion/request</field>
            <field name="view_id" ref="ai_doctor_second_opinion.create_request"/>
            <field name="website_published">True</field>
            <field name="is_published">True</field>
        </record>

        <record id="my_requests_page" model="website.page">
            <field name="name">My Consultations</field>
            <field name="url">/my/second-opinion</field>
            <field name="view_id" ref="ai_doctor_second_opinion.portal_my_requests"/>
            <field name="website_published">True</field>
            <field name="is_published">True</field>
        </record>

        <!-- Doctor Registration Page -->
        <record id="doctor_registration_page" model="website.page">
            <field name="name">Register as Doctor</field>
            <field name="url">/doctor/registration</field>
            <field name="view_id" ref="ai_doctor_second_opinion.doctor_registration_form"/>
            <field name="website_published">True</field>
            <field name="is_published">True</field>
        </record>
        
        <!-- Doctor Registration Menu -->
        <record id="menu_doctor_registration" model="website.menu">
            <field name="name">Doctor Registration</field>
            <field name="url">/doctor/registration</field>
            <field name="page_id" ref="doctor_registration_page"/>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence">50</field>
        </record>

    </data>
</odoo>
