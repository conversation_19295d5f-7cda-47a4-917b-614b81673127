<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="doctor_registration_form" name="Doctor Registration" page="True">
            <t t-call="website.layout">
                <div class="container mt32 mb32">
                    <h1 class="text-center mb32">Doctor Registration</h1>
                    <form action="/doctor/register" method="post" enctype="multipart/form-data" class="o_website_form">
                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="control-label" for="name">Full Name</label>
                                    <input type="text" class="form-control" name="name" required="required"/>
                                </div>
                                <div class="form-group">
                                    <label class="control-label" for="email">Email</label>
                                    <input type="email" class="form-control" name="email" required="required"/>
                                </div>
                                <div class="form-group">
                                    <label class="control-label" for="phone">Phone</label>
                                    <input type="tel" class="form-control" name="phone" required="required"/>
                                </div>
                                <div class="form-group">
                                    <label class="control-label" for="registration_number">Medical Registration Number</label>
                                    <input type="text" class="form-control" name="registration_number" required="required"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="control-label" for="experience">Years of Experience</label>
                                    <input type="number" class="form-control" name="experience" required="required"/>
                                </div>
                                <div class="form-group">
                                    <label class="control-label" for="qualification">Qualifications</label>
                                    <textarea class="form-control" name="qualification" required="required" rows="3"/>
                                </div>
                                <div class="form-group">
                                    <label class="control-label" for="address">Address</label>
                                    <textarea class="form-control" name="address" rows="3"/>
                                </div>
                            </div>
                        </div>
                        <div class="row mt16">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="control-label" for="resume">Resume/CV</label>
                                    <input type="file" class="form-control" name="resume" required="required"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label class="control-label" for="license_doc">Medical License</label>
                                    <input type="file" class="form-control" name="license_doc" required="required"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt16">
                            <label class="control-label" for="speciality_ids">Specialities</label>
                            <select class="form-control" name="speciality_ids" multiple="multiple" required="required">
                                <t t-foreach="specialities" t-as="speciality">
                                    <option t-att-value="speciality.id"><t t-esc="speciality.name"/></option>
                                </t>
                            </select>
                        </div>
                        <div class="clearfix"/>
                        <div class="form-group text-center mt32">
                            <button type="submit" class="btn btn-primary btn-lg">Submit Registration</button>
                        </div>
                    </form>
                </div>
            </t>
        </template>

        <!-- Success Template -->
        <template id="doctor_registration_success" name="Registration Success">
            <t t-call="website.layout">
                <div class="container mt32 mb32">
                    <div class="alert alert-success text-center">
                        <h2>Thank you for registering!</h2>
                        <p>Your registration has been submitted successfully. Our team will review your application and contact you soon.</p>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>
