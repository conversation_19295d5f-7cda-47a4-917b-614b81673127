<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Medical Qualification Form View -->
    <record id="view_medical_qualification_form" model="ir.ui.view">
        <field name="name">medical.qualification.form</field>
        <field name="model">medical.qualification</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1><field name="name" placeholder="Qualification Name"/></h1>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Medical Qualification Tree View -->
    <record id="view_medical_qualification_tree" model="ir.ui.view">
        <field name="name">medical.qualification.tree</field>
        <field name="model">medical.qualification</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <!-- Medical Qualification Search View -->
    <record id="view_medical_qualification_search" model="ir.ui.view">
        <field name="name">medical.qualification.search</field>
        <field name="model">medical.qualification</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <separator/>
            </search>
        </field>
    </record>

    <!-- Medical Qualification Action -->
    <record id="action_medical_qualification" model="ir.actions.act_window">
        <field name="name">Medical Qualifications</field>
        <field name="res_model">medical.qualification</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first medical qualification
            </p>
            <p>
                Get expert medical opinions from qualified doctors.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_medical_qualification"
              name="Medical Qualifications"
              parent="menu_medical_doctor_root"
              action="action_medical_qualification"
              sequence="35"
              groups="ai_doctor_second_opinion.group_medical_admin"
             />
</odoo>
