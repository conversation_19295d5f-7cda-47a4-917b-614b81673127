<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="create_request" name="Create Second Opinion Request">
            <t t-call="website.layout">
                    <!-- Styles -->
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"/>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"/>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"/>
                    <!-- Or for RTL support -->
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css"/>
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <!-- Scripts -->
                    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.0/dist/jquery.slim.min.js"/>
                    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"/>
                    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"/>
                    <style>
                    .form-box {
                        max-width: 1000px;
                        margin: 35px auto;
                        padding: 35px;
                        background: #fff; / White background for form container /
                        border-radius: 10px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                        border: 1px solid #ccc; / Border around form box /
                        --nf-input-focus-border-color: green !important;
                        --nf-input-focus-background-color: green !important;
                    }
                    
            </style>
            <section class="pt16 pb16" style="background-color: rgba(214, 239, 214, 0.41) !important; background-image: none;">
                <div class="form-box">
                    <form action="/second-opinion/submit" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                        <div class="row">
                        <div class="nice-form-group col-12">
                                    <label class="col-form-label col-sm-auto" style="width: 200px" for="patient_name">
                                        <span class="s_website_form_label_content">Patient Name</span>
                                        <span class="s_website_form_mark"> *</span>
                                    </label>
                                    <div class="col-sm">
                                        <input type="text" class="form-control" name="patient_name" required="1" 
                                            t-att-value="user_name if is_logged_in else ''"
                                            t-att-readonly="'readonly' if is_logged_in else None"/>
                                    </div>
                                </div>
                                 <div class="nice-form-group">
                                    <label for="is_followup" style="margin-top:5px;">Consultation For</label>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" id="new_case" name="is_followup" value="0" checked="checked"/>
                                        <label class="form-check-label" for="new_case">New Case</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" id="followup" name="is_followup" value="1"/>
                                        <label class="form-check-label" for="followup">Follow-up Case</label>
                                    </div>
                                </div>

                                <div class="nice-form-group" id="previous_request_div" style="display: none;">
                                    <label for="previous_request_id" style="margin-top:5px;">Previous Case</label>
                                    <select class="form-select" name="previous_request_id" style="margin-top:5px;">
                                        <option value="">Select Previous Case</option>
                                        <t t-foreach="previous_requests" t-as="prev_req">
                                            <option t-att-value="prev_req.id">
                                                <t t-esc="prev_req.name"/> - <t t-esc="prev_req.issue_type.name"/>
                                            </option>
                                        </t>
                                    </select>
                                </div>

                                <script type="text/javascript">
                                    document.addEventListener('DOMContentLoaded', function () {
                                        const newCaseRadio = document.getElementById('new_case');
                                        const followUpRadio = document.getElementById('followup');
                                        const previousRequestDiv = document.getElementById('previous_request_div');

                                        function togglePreviousRequestDiv() {
                                            if (followUpRadio.checked) {
                                                previousRequestDiv.style.display = 'block';
                                            } else {
                                                previousRequestDiv.style.display = 'none';
                                            }
                                        }

                                        // Add event listeners to radio buttons
                                        newCaseRadio.addEventListener('change', togglePreviousRequestDiv);
                                        followUpRadio.addEventListener('change', togglePreviousRequestDiv);
                                    });
                                </script>
                        <!-- <div class="nice-form-group col-md-6 col-12">
                            <label for="issue_type" style="margin-top:5px;">Issue Type</label>
                            <select class="form-select" name="issue_type" required="required" style="margin-top:5px;">
                                <option value="">Select Issue Type</option>
                                <t t-foreach="issue_types" t-as="issue">
                                    <option t-att-value="issue.id">
                                        <t t-esc="issue.name"/>
                                    </option>
                                </t>
                            </select>
                        </div> -->
                        <div class="nice-form-group col">
                            <label for="age" style="margin-top:5px;">Age</label>
                            <input type="number" name="age" class="form-control" required="required" style="margin-top:5px;"/>
                        </div>
                        </div>
                        <div class="nice-form-group">
                            <label for="gender" style="margin-top:5px;">Gender</label>
                            <br/>
                            <input type="radio" name="gender" value="male"/>
                            <label>Male</label>
                            <input type="radio" name="gender" value="female"/>
                            <label>Female</label>
                            <input type="radio" name="gender" value="other"/>
                            <label>Other</label>
                        </div>
                        <div class="nice-form-group">
                            <label style="margin-top:5px;">Symptoms</label>
                            <select class="form-select" id="multiple-select-field" data-placeholder="Choose anything" multiple="multiple" name="symptoms[]" style="margin-top:5px;">
                                <t t-foreach="symptoms" t-as="symptom">
                                    <option t-att-value="symptom.id">
                                        <t t-esc="symptom.name"/>
                                    </option>
                                </t>
                            </select>
                            <script>
                                $('#multiple-select-field').on('change', function() {
                                let selectedSymptoms = $('#multiple-select-field').val(); // Returns an array of selectedvalues
                                console.log(selectedSymptoms); // Check the values in the console
                                });
                                $( '#multiple-select-field' ).select2( {
                                theme: "bootstrap-5",
                                width: $( this ).data( 'width' ) ? $( this ).data( 'width' ) : $( this ).hasClass( 'w-100' )
                                ?
                                '100%' : 'style',
                                placeholder: $( this ).data( 'placeholder' ),
                                closeOnSelect: false,
                                } );
                            </script>
                        </div>
                        <div class="nice-form-group">
                            <label for="custom_symptoms" style="margin-top:5px;">Other Symptoms</label>
                            <textarea name="custom_symptoms" class="form-control" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="description" style="margin-top:5px;">Description</label>
                            <textarea name="description" class="form-control" required="required" style="margin-top:5px;"/>
                        </div>
                        



                        
                        <div class="nice-form-group">
                            <label for="lab_reports" style="margin-top:5px;">Lab Reports</label>
                            <input type="file" name="lab_report_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="medicine_photos" style="margin-top:5px;">Medicine Photos</label>
                            <input type="file" name="medicine_photo_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="prescriptions" style="margin-top:5px;">Prescriptions</label>
                            <input type="file" name="prescription_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="xray" style="margin-top:5px;">X-Ray Reports</label>
                            <input type="file" name="xray_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="ct_scan" style="margin-top:5px;">CT Scan Reports</label>
                            <input type="file" name="ct_scan_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="mri" style="margin-top:5px;">MRI Reports</label>
                            <input type="file" name="mri_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="doppler" style="margin-top:5px;">Vein Doppler Reports</label>
                            <input type="file" name="doppler_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <div class="nice-form-group">
                            <label for="sonography" style="margin-top:5px;">Sonography Reports</label>
                            <input type="file" name="sonography_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                        </div>
                        <button type="submit" class="btn btn-primary" style="display: block; margin: auto; margin-top:5px !important ">Submit Request</button>
                    </form>
                </div>
                </section>
            </t>
        </template>

        <template id="portal_my_requests" name="My Second Opinion Requests">
                    <t t-call="website.layout">
                        <!-- Remove any website-specific variables -->
                        <t t-set="no_breadcrumbs" t-value="True"/>
                        <t t-set="alternate_languages" t-value=""/>
                        <t t-set="is_frontend_multilang" t-value=""/>
                         <div class="container mt-4">
                    <h3>Second Opinion Requests</h3>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-12 col-md-6">
                            <div class="input-group">
                                <input type="text" id="requestSearch" class="form-control" placeholder="Search by reference, symptoms, or description..."/>
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="fa fa-search"/>
                                </button>
                            </div>
                        </div>
                        <div class="col-12 col-md-3">
                            <select id="statusFilter" class="form-select">
                                <option value="">All Status</option>
                                <option value="draft">Draft</option>
                                <option value="pending_payment">Pending Payment</option>
                                <option value="paid">Paid</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-12 col-md-3">
                            <select id="sortOption" class="form-select">
                                <option value="date_desc">Newest First</option>
                                <option value="date_asc">Oldest First</option>
                                <option value="ref_asc">Reference (A-Z)</option>
                                <option value="ref_desc">Reference (Z-A)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading Spinner -->
                    <div id="requestsLoading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <div id="requestsTableContainer">
                        <div class="table-responsive">
                            <table class="table table-hover o_portal_my_doc_table">
                                <thead>
                                    <tr class="active">
                                        <th>Reference</th>
                                        <th>Patient Name</th>
                                        <th>Request Date</th>
                                        <th>Doctor</th>
                                        <th>Status</th>
                                        <th>Amount</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody id="requestsTableBody">
                                    <t t-call="ai_doctor_second_opinion.portal_my_requests_table_content"/>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <nav aria-label="Request navigation" class="d-flex justify-content-center mt-4">
                            <ul id="requestsPagination" class="pagination">
                                <!-- Pagination will be populated by JavaScript -->
                            </ul>
                        </nav>
                    </div>

                    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

                    <script type="text/javascript">
                        $(document).ready(function() {
                            var currentPage = 1;
                            var itemsPerPage = 10;
                            var searchTimer;

                            function loadRequests(page) {
                                var searchQuery = $('#requestSearch').val();
                                var statusFilter = $('#statusFilter').val();
                                var sortOption = $('#sortOption').val();

                                $('#requestsLoading').removeClass('d-none');
                                $('#requestsTableContainer').addClass('d-none');

                                $.ajax({
                                    url: '/my/requests/search',
                                    type: 'POST',
                                    dataType: 'json',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        jsonrpc: "2.0",
                                        method: "call",
                                        params: {
                                            search: searchQuery,
                                            status: statusFilter,
                                            sort: sortOption,
                                            page: page,
                                            limit: itemsPerPage
                                        }
                                    }),
                                    success: function(response) {
                                        if (response.result) {
                                            $('#requestsTableBody').html(response.result.html);
                                            updatePagination(response.result.total, page);
                                        }
                                        $('#requestsLoading').addClass('d-none');
                                        $('#requestsTableContainer').removeClass('d-none');
                                    },
                                    error: function() {
                                        $('#requestsLoading').addClass('d-none');
                                        $('#requestsTableContainer').removeClass('d-none');
                                    }
                                });
                            }

                            function updatePagination(total, currentPage) {
                                var totalPages = Math.ceil(total / itemsPerPage);
                                var $pagination = $('#requestsPagination');
                                $pagination.empty();

                                if (totalPages &lt;= 1) return;

                                // Previous button
                                var prevDisabled = currentPage === 1 ? 'disabled' : '';
                                $pagination.append(
                                    '&lt;li class="page-item ' + prevDisabled + '"&gt;' +
                                    '&lt;a class="page-link" href="#" data-page="' + (currentPage - 1) + '"&gt;Previous&lt;/a&gt;' +
                                    '&lt;/li&gt;'
                                );

                                // Page numbers
                                for (var i = 1; i &lt;= totalPages; i++) {
                                    if (i === 1 || i === totalPages || (i &gt;= currentPage - 2 &amp;&amp; i &lt;= currentPage + 2)) {
                                        var active = i === currentPage ? 'active' : '';
                                        $pagination.append(
                                            '&lt;li class="page-item ' + active + '"&gt;' +
                                            '&lt;a class="page-link" href="#" data-page="' + i + '"&gt;' + i + '&lt;/a&gt;' +
                                            '&lt;/li&gt;'
                                        );
                                    } else if (i === currentPage - 3 || i === currentPage + 3) {
                                        $pagination.append('&lt;li class="page-item disabled"&gt;&lt;span class="page-link"&gt;...&lt;/span&gt;&lt;/li&gt;');
                                    }
                                }

                                // Next button
                                var nextDisabled = currentPage === totalPages ? 'disabled' : '';
                                $pagination.append(
                                    '&lt;li class="page-item ' + nextDisabled + '"&gt;' +
                                    '&lt;a class="page-link" href="#" data-page="' + (currentPage + 1) + '"&gt;Next&lt;/a&gt;' +
                                    '&lt;/li&gt;'
                                );
                            }

                            // Event handlers
                            $('#requestSearch').on('input', function() {
                                clearTimeout(searchTimer);
                                searchTimer = setTimeout(function() {
                                    currentPage = 1;
                                    loadRequests(currentPage);
                                }, 300);
                            });

                            $('#clearSearch').on('click', function() {
                                $('#requestSearch').val('');
                                currentPage = 1;
                                loadRequests(currentPage);
                            });

                            $('#statusFilter, #sortOption').on('change', function() {
                                currentPage = 1;
                                loadRequests(currentPage);
                            });

                            $(document).on('click', '#requestsPagination .page-link', function(e) {
                                e.preventDefault();
                                var page = $(this).data('page');
                                if (page &amp;&amp; !$(this).parent().hasClass('disabled')) {
                                    currentPage = page;
                                    loadRequests(page);
                                }
                            });

                            // Initial load
                            loadRequests(currentPage);
                        });
                    </script>
                </div>
                    </t>
        </template>


        <template id="portal_request_update_form" name="Update Second Opinion Request">
            <t t-call="portal.portal_layout">
                
                <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"/> -->
                <!-- Or for RTL support -->
                <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css"/> -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                <!-- Scripts -->
                <!-- <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.0/dist/jquery.slim.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"/> -->
                <style>
                .form-box {
                        max-width: 1000px;
                        margin: 35px auto;
                        padding: 35px;
                        background: #fff; / White background for form container /
                        border-radius: 10px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                        border: 1px solid #ccc; / Border around form box /
                        --nf-input-focus-border-color: green !important;
                        --nf-input-focus-background-color: green !important;
                    }
                    
                </style>    
                
                <section class="pt16 pb16"  style="background-color: rgba(214, 239, 214, 0.41) !important; background-image: none;">
                    <div class="form-box">
                        <div class="row">
                            <div class="col-md-12">
                                <h3>Update Request</h3>
                                <form action="/my/requests/update" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                    <input type="hidden" name="request_id" t-att-value="second_opinion_request.id"/>
                                    <div class="row">
                                    <div class="nice-form-group col-md-6 col-12">
                                        <label for="issue_type">Issue Type</label>
                                        <select class="form-select" name="issue_type" required="1">
                                            <option value="" hidden="">Select Issue Type</option>
                                            <t t-foreach="issue_type" t-as="issue">
                                                <option t-att-value="issue.id" t-att-selected="issue.id == second_opinion_request.issue_type.id">
                                                    <t t-esc="issue.name"/>
                                                </option>
                                            </t>
                                        </select>
                                    </div>  
                            <div class="nice-form-group col">
                                <label for="age">Age</label>
                                <input type="number" name="age" class="form-control" required="required" style="margin-top:5px;" t-att-value="second_opinion_request.age"/>
                            </div>
                            </div>
                            <!-- <div class="nice-form-group col">
                                <label for="gender" style="margin-top:5px;">Gender</label>
                                <br/>
                                        <input type="radio" id="gender_male" name="gender" value="male" required="required" t-att-checked="second_opinion_request.gender == 'male'"/>
                                        <label for="gender_male">Male</label>
                                        <input type="radio" id="gender_female" name="gender" value="female" required="required" t-att-checked="second_opinion_request.gender == 'female'"/>
                                        <label for="gender_female">Female</label>
                                        <input type="radio" id="gender_other" name="gender" value="other" required="required" t-att-checked="second_opinion_request.gender == 'other'"/>
                                        <label for="gender_other">Other</label>
                                    </div> -->
                            <div class="nice-form-group">
                                <label for="gender" style="margin-top:5px;">Gender</label>
                                <br/>
                                <input type="radio" name="gender" value="male" t-att-checked="second_opinion_request.gender == 'male'"/>
                                <label>Male</label>
                                <input type="radio" name="gender" value="female" t-att-checked="second_opinion_request.gender == 'female'"/>
                                <label>Female</label>
                                <input type="radio" name="gender" value="other" t-att-checked="second_opinion_request.gender == 'other'"/>
                                <label>Other</label>
                            </div>
                                
                                <div class="nice-form-group">
                                    <label style="margin-top:5px;">Symptoms</label>
                                    <select class="form-select" id="multiple-select-field" data-placeholder="Choose anything" multiple="multiple" name="symptoms[]" style="margin-top:5px;">
                                        <t t-foreach="symptoms" t-as="symptom">
                                            <option t-att-value="symptom.id" t-att-selected="symptom.id in second_opinion_request.symptom_ids.ids">
                                                <t t-esc="symptom.name"/>
                                            </option>
                                        </t>
                                    </select>
                                    <script>
                                        $('#multiple-select-field').on('change', function() {
                                            let selectedSymptoms = $('#multiple-select-field').val(); // Returns an array of selected values
                                            console.log(selectedSymptoms); // Check the values in the console
                                        });
                                        $('#multiple-select-field').select2({
                                            theme: "bootstrap-5",
                                            width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                                            placeholder: $(this).data('placeholder'),
                                            closeOnSelect: false,
                                        });
                                    </script>
                                </div>
                                    <div class="nice-form-group">
                                        <label for="description">Description</label>
                                        <textarea class="form-control" name="description" rows="4" required="1"><t t-esc="second_opinion_request.description"/></textarea>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="custom_symptoms">Custom Symptoms</label>
                                        <textarea class="form-control" name="custom_symptoms" rows="3"><t t-esc="second_opinion_request.custom_symptoms"/></textarea>
                                    </div>

                                    <div class="nice-form-group">
                                    <!-- <label>Lab Reports</label> -->
                                    <h6 class="text-muted mb-3">Lab Reports</h6>
                                    <div t-if="second_opinion_request.lab_report_ids" class="mb-4">
                                                    <!-- <h6 class="text-muted mb-3">Lab Reports</h6> -->
                                                    <div class="list-group">
                                                        <t t-foreach="second_opinion_request.lab_report_ids" t-as="doc">
                                                            <!-- <input type="checkbox" name="remove_prescription_ids" t-att-value="doc.id" class="mr-2"/> -->
                                                            <div class="row">
                                                                <div t-att-class="'col-auto ' + str(doc.id) ">
                                                                    <button type="button" class="btn remove-document" t-att-data-doc-id="doc.id"><i class="fa fa-close "/></button>
                                                                </div>
                                                                <div t-att-class="'col ' + str(doc.id) ">
                                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                        <i class="fa fa-file-pdf-o mr-2"/>
                                                                        <t t-esc="doc.name"/>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </t>
                                                    </div>
                                                </div> 
                                        
                                        <input type="file" name="lab_report_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>

                                    </div>

                                    <div class="nice-form-group">
                                    <h6 class="text-muted mb-3">Medicine Photos</h6>
                                    <div t-if="second_opinion_request.medicine_photo_ids" class="mb-4">
                                                    <!-- <div class="row">
                                                        <t t-foreach="second_opinion_request.medicine_photo_ids" t-as="photo">
                                                            <div class="col-md-3 col-6 mb-3">
                                                                <a t-attf-href="/web/image/#{photo.id}"
                                                                class="d-block">
                                                                    <img t-attf-src="/web/image/#{photo.id}"
                                                                        class="img-fluid rounded shadow-sm"
                                                                        t-att-alt="photo.name"/>
                                                                </a>
                                                            </div>
                                                        </t>
                                                    </div> -->
                                                    <div class="list-group">
                                                        <t t-foreach="second_opinion_request.medicine_photo_ids" t-as="doc">
                                                            <!-- <input type="checkbox" name="remove_prescription_ids" t-att-value="doc.id" class="mr-2"/> -->
                                                            <div class="row">
                                                                <div t-att-class="'col-auto ' + str(doc.id) ">
                                                                    <button type="button" class="btn remove-document" t-att-data-doc-id="doc.id"><i class="fa fa-close "/></button>
                                                                </div>
                                                                <div t-att-class="'col ' + str(doc.id) ">
                                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                        <i class="fa fa-file-pdf-o mr-2"/>
                                                                        <t t-esc="doc.name"/>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </t>
                                                    </div>
                                                </div>
                                        <!-- <label>Medicine Photos</label> -->
                                        <input type="file" name="medicine_photo_ids" class="form-control" multiple="multiple" style="margin-top:5px;" accept="image/*"/>

                                    </div>

                                    <div class="nice-form-group">
                                    <!-- Prescriptions -->
                                
                                    <h6 class="text-muted mb-3">Prescriptions</h6>
                                                <div t-if="second_opinion_request.prescription_ids" class="mb-4">
                                                    <div class="list-group">
                                                        
                                                        <t t-foreach="second_opinion_request.prescription_ids" t-as="doc">
                                                            <!-- <input type="checkbox" name="remove_prescription_ids" t-att-value="doc.id" class="mr-2"/> -->
                                                            <div class="row">
                                                                <div t-att-class="'col-auto ' + str(doc.id) ">
                                                                    <button type="button" class="btn remove-document" t-att-data-doc-id="doc.id"><i class="fa fa-close "/></button>
                                                                </div>
                                                                <div t-att-class="'col ' + str(doc.id) ">
                                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                        <i class="fa fa-file-pdf-o mr-2"/>
                                                                        <t t-esc="doc.name"/>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </t>
                                                    </div>
                                                </div>
                                        
                                        <input type="file" name="prescription_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>

                                    </div>

                                    <script type="text/javascript">
                                        $(document).ready(function() {
                                            $('.remove-document').on('click', function(e) {
                                                <!-- e.preventDefault(); // Prevent form submission -->
                                                var $btn = $(this);
                                                var docId = $btn.data('doc-id');

                                                $.ajax({
                                                    url: '/my/requests/remove_document',
                                                    type: 'POST',
                                                    data: {
                                                        'doc_id': docId,
                                                    },
                                                    success: function(response) {
                                                        response = JSON.parse(response);    
                                                        if (response.success) {
                                                            $('div.' + docId).remove();
                                                        } else {
                                                            alert('Failed to remove the document.');
                                                        }
                                                    },
                                                    error: function() {
                                                        alert('An error occurred while removing the document.');
                                                    }
                                                });
                                            });
                                        });
                                    </script>

                                <div class=" d-flex justify-content-center" style="gap: 10px; margin-top: 5px;">
                                    <button type="submit" class="btn btn-primary" >Update Request</button>
                                    <a href="/my/requests" class="btn btn-secondary" >Cancel</a>
                                    </div> 
                                </form>
                            </div>
                        </div>
                    </div>
                
                </section>
            </t>
        </template>

<template id="portal_request_detail" name="Second Opinion Request Detail">
    <t t-call="portal.portal_layout">
        <t t-set="o" t-value="results"/>

        <div class="container">
            <div class="row mt-4 mb-5">
                <div class="col-12">
                    <!-- Breadcrumb -->
                    <div class="o_portal_breadcrumbs mb-3">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/my/home">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="/my/requests">Second Opinion Requests</a>
                            </li>
                            <li class="breadcrumb-item active">
                                <t t-esc="o.name"/>
                            </li>
                        </ol>
                    </div>

                    <!-- Request Header with Payment Button -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2>Request:
                                <t t-esc="o.name"/>
                            </h2>
                            <div class="text-muted">
                                Submitted on
                                <span t-field="o.create_date" t-options='{"widget": "date"}'/>
                            </div>
                        </div>
                        <div t-if="show_pay_button" class="text-right">
                            <t t-if="o.doctor_id">
                                <div class="h4 mb-2">
                                    Price:
                                    <span t-esc="o.doctor_id.currency_id.symbol"/>
                                    <t t-if="o.is_followup">
                                        <span t-esc="'{:,.2f}'.format(o.doctor_id.followup_case_consultation_fee * 1.18)"/>
                                        <small class="text-muted">(Follow-up Case)</small>
                                    </t>
                                    <t t-else="">
                                        <span t-esc="'{:,.2f}'.format(o.doctor_id.new_case_consultation_fee * 1.18)"/>
                                        <small class="text-muted">(New Case)</small>
                                    </t>
                                </div>
                                <a t-attf-href="/my/requests/#{o.id}/pay"
                                class="btn btn-primary">
                                    <i class="fa fa-credit-card mr-2"/> Pay Now
                                </a>
                            </t>
                            <t t-else="">
                                <div class="alert alert-info mb-3">
                                    Please select a doctor before proceeding with payment.
                                </div>
                                <a t-attf-href="/select/doctor?request_id=#{o.id}&amp;is_followup=#{1 if o.is_followup else 0}"
                                class="btn btn-primary">
                                    <i class="fa fa-user-md mr-2"/>Select Doctor
                                </a>
                            </t>
                            <!-- <div class="h4 mb-2">
                                Price:
                                <span t-esc="o.doctor_id.currency_id.symbol"/>
                                <span t-esc="o.doctor_id.consultation_fee" t-options='{"widget": "float", "precision": 2}'/> 
                            </div>
                            <a t-attf-href="/my/requests/#{o.id}/pay"
                            class="btn btn-primary">
                                <i class="fa fa-credit-card mr-2"/> Pay Now
                            </a> -->
                        </div>
                    </div>

                    <!-- Status Banner -->
                    <t t-if="o.state == 'completed'">
                        <div t-attf-class="alert alert-success mb-4">
                            <strong>Status:</strong>
                            <span t-field="o.state" class="ml-2"/>
                            <t t-if="o.state == 'pending_payment'">
                                <div class="mt-2">
                                    <small>Your request is pending payment. Please complete the payment to proceed
                                        with the second opinion.
                                    </small>
                                </div>
                            </t>
                        </div>
                    </t>
                    <t t-else="">
                        <t t-if="o.state == 'draft'">
                            <div t-attf-class="alert alert-warning mb-4">
                                <strong>Status:</strong>
                                <span t-field="o.state" class="ml-2"/>
                                <t t-if="o.state == 'pending_payment'">
                                    <div class="mt-2">
                                        <small>Your request is pending payment. Please complete the payment to
                                            proceed with the second opinion.
                                        </small>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div t-attf-class="alert alert-info mb-4">
                                <strong>Status:</strong>
                                <span t-field="o.state" class="ml-2"/>
                                <t t-if="o.state == 'pending_payment'">
                                    <div class="mt-2">
                                        <small>Your request is pending payment. Please complete the payment to
                                            proceed with the second opinion.
                                        </small>
                                    </div>
                                </t>
                            </div>
                        </t>
                    </t>

                    <!-- Main Content Card -->
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <!-- Status and Type Section -->
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <strong>Issue Type:</strong>
                                        <span t-field="o.issue_type.name" class="ml-2"/>
                                    </div>
                                </div>
                            </div>

                            <!-- Patient Info Section -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5 class="mb-3">Patient Information</h5>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Patient Name:</strong>
                                            <span t-field="o.patient_id.name" class="ml-2"/>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Age:</strong>
                                            <span t-field="o.age" class="ml-2"/>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Gender:</strong>
                                            <span t-field="o.gender" class="ml-2"/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Symptoms Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5 class="mb-3">Symptoms</h5>
                                    <div t-field="o.symptom_ids"
                                        widget="many2many_tags"
                                        class="mb-2"/>
                                    <div t-if="o.custom_symptoms" class="mt-3">
                                        <strong>Additional Symptoms:</strong>
                                        <p t-field="o.custom_symptoms" class="text-muted mb-0"/>
                                    </div>
                                </div>
                            </div>

                            <!-- Medical History Section -->
                            <!--                            <div class="row mt-4">-->
                            <!--                                <div class="col-12">-->
                            <!--                                    <h5 class="mb-3">Medical History</h5>-->
                            <!--                                    <div class="row">-->
                            <!--                                        <div class="col-md-6">-->
                            <!--                                            <strong>Existing Conditions:</strong>-->
                            <!--                                            <div t-field="o.existing_condition_ids"-->
                            <!--                                                 widget="many2many_tags"-->
                            <!--                                                 class="mb-2"/>-->
                            <!--                                        </div>-->
                            <!--                                        <div class="col-md-6">-->
                            <!--                                            <strong>Current Medications:</strong>-->
                            <!--                                            <div t-field="o.current_medication_ids"-->
                            <!--                                                 widget="many2many_tags"-->
                            <!--                                                 class="mb-2"/>-->
                            <!--                                        </div>-->
                            <!--                                    </div>-->
                            <!--                                    <div t-if="o.medical_history" class="mt-3">-->
                            <!--                                        <strong>Additional Medical History:</strong>-->
                            <!--                                        <p t-field="o.medical_history" class="text-muted mb-0"/>-->
                            <!--                                    </div>-->
                            <!--                                </div>-->
                            <!--                            </div>-->

                            <!-- Description Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h5 class="mb-3">Description</h5>
                                    <p t-field="o.description" class="text-muted mb-0"/>
                                </div>
                            </div>

                                                   <!-- Doctor Information Section -->
                        <div class="row mt-4" t-if="o.doctor_id">
    <div class="col-12">
        <div class="card shadow-sm p-4 border-0 rounded">
            <h5 class="mb-3">Doctor Information</h5>
            <div class="row">
                <div class="col-md-3 col-12 text-center mb-3 mb-md-0">
                    <div class="doctor-image-container" 
                        style="width: 150px; height: 150px; overflow: hidden; border-radius: 10px; margin: auto;">
                        <t t-if="o.doctor_id.photo">
                            <img t-att-src="image_data_uri(o.doctor_id.photo)" 
                                 class="img-fluid" 
                                 style="width: 100%; height: 100%; object-fit: cover;" 
                                 alt="Doctor"/>
                        </t>
                        <t t-else="">
                            <img src="/web/static/img/placeholder.png" 
                                 class="img-fluid" 
                                 style="width: 100%; height: 100%; object-fit: cover;" 
                                 alt="No Image"/>
                        </t>
                    </div>
                </div>
                <div class="col-md-9 col-12">
                    <div class="row">
                        <div class="col-md-6 col-12 mb-3">
                            <strong>Name:</strong>
                            <span t-field="o.doctor_id.name" class="ml-2 text-primary"/>
                        </div>
                        <div class="col-md-6 col-12 mb-3">
                            <strong>Specialty:</strong>
                            <div t-field="o.doctor_id.speciality_ids" 
                                 class="ml-2 text-secondary" 
                                 widget="many2many_tags"/>
                        </div>
                        <div class="col-md-6 col-12 mb-3">
                            <strong>Experience:</strong>
                            <span t-field="o.doctor_id.experience" class="ml-2"/> Years
                        </div>
                        <div class="col-md-6 col-12 mb-3">
                            <strong>Consultation Fee:</strong>
                            <t t-if="o.is_followup">
                                <span 
                                      t-esc="'{:,.2f}'.format(o.doctor_id.followup_case_consultation_fee * 1.18)" 
                                      class="ml-2 text-success" 
                                      />
                                <span class="text-muted">(Incl. GST)</span>
                                <!-- Old Case Fee -->
                            </t>
                            <t t-else="">
                                <span
                                    t-esc="'{:,.2f}'.format(o.doctor_id.new_case_consultation_fee * 1.18)" 
                                    class="ml-2 text-success" 
                                    />
                                <span class="text-muted">(Incl. GST)</span>
                                <!-- New Case Fee -->

                            </t>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>Qualification:</strong>
                        <p t-field="o.doctor_id.qualification" class="mb-0 text-muted"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


                        <!-- Payment Section -->
                        <div t-if="o.state == 'pending_payment'" class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <p>Please proceed with the payment to confirm your consultation with 
                                    <strong t-field="o.doctor_id.name"/>.</p>
                                    <p class="mb-0">
                                        <strong>Total Amount: </strong>
                                        <t t-if="o.is_followup">
                                            <span 
                                                t-esc="'{:,.2f}'.format(o.doctor_id.followup_case_consultation_fee * 1.18)"
                                                class="ml-2" 
                                                />
                                            <span class="text-muted">(Incl. GST) </span>
                                            <!-- Old Case Fee -->
                                        </t>
                                        <t t-else="">
                                            <span
                                                t-esc="'{:,.2f}'.format(o.doctor_id.new_case_consultation_fee * 1.18)" 
                                                class="ml-2" 
                                                />
                                            <span class="text-muted">(Incl. GST) </span>
                                            <!-- New Case Fee -->
                                        </t>
                                         
                                    </p>
                                </div>
                                <div class="text-center">
                                    <a t-attf-href="/my/requests/#{o.id}/pay" 
                                    class="btn btn-primary">
                                        Proceed to Payment
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Documents Section -->
                        <div class="mt-4" t-if="o.lab_report_ids or o.medicine_photo_ids or o.prescription_ids or o.xray_ids or o.ct_scan_ids or o.mri_ids or o.doppler_ids or o.sonography_ids">
                            <h5 class="mb-3">Medical Documents</h5>

                            <!-- Lab Reports -->
                            <div t-if="o.lab_report_ids" class="mb-4">
                                <h6 class="text-muted">Lab Reports</h6>
                                <div class="list-group">
                                    <t t-foreach="o.lab_report_ids" t-as="doc">
                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                            <i class="fa fa-file-medical mr-2"></i>
                                            <t t-esc="doc.name"/>
                                        </a>
                                    </t>
                                </div>
                            </div>

                            <!-- Medicine Photos -->
                            <div t-if="o.medicine_photo_ids" class="mb-4">
                                <h6 class="text-muted">Medicine Photos</h6>
                                <div class="list-group">
                                    <t t-foreach="o.medicine_photo_ids" t-as="doc">
                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                            <i class="fa fa-file-medical mr-2"></i>
                                            <t t-esc="doc.name"/>
                                        </a>
                                    </t>
                                </div>
                            </div>

                            <!-- Prescriptions -->
                            <div t-if="o.prescription_ids" class="mb-4">
                                <h6 class="text-muted">Prescriptions</h6>
                                <div class="list-group">
                                    <t t-foreach="o.prescription_ids" t-as="doc">
                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                            <i class="fa fa-file-medical mr-2"></i>
                                            <t t-esc="doc.name"/>
                                        </a>
                                    </t>
                                </div>
                            </div>

                            <!-- X-Ray Reports -->
                            <div t-if="o.xray_ids" class="mb-4">
                                <h6 class="text-muted">X-Ray Reports</h6>
                                <div class="list-group">
                                    <t t-foreach="o.xray_ids" t-as="doc">
                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                            <i class="fa fa-file-medical mr-2"></i>
                                            <t t-esc="doc.name"/>
                                        </a>
                                    </t>
                                </div>
                            </div>

                            <!-- CT Scan Reports -->
                            <div t-if="o.ct_scan_ids" class="mb-4">
                                <h6 class="text-muted">CT Scan Reports</h6>
                                <div class="list-group">
                                    <t t-foreach="o.ct_scan_ids" t-as="doc">
                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                            <i class="fa fa-file-medical mr-2"></i>
                                            <t t-esc="doc.name"/>
                                        </a>
                                    </t>
                                </div>
                            </div>

                            <!-- MRI Reports -->
                            <div t-if="o.mri_ids" class="mb-4">
                                <h6 class="text-muted">MRI Reports</h6>
                                <div class="list-group">
                                    <t t-foreach="o.mri_ids" t-as="doc">
                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                            <i class="fa fa-file-medical mr-2"></i>
                                            <t t-esc="doc.name"/>
                                        </a>
                                    </t>
                                </div>
                            </div>


                                        <!-- Vein Doppler Reports -->
                                        <div t-if="o.doppler_ids" class="mb-4">
                                            <h6 class="text-muted">Vein Doppler Reports</h6>
                                            <div class="list-group">
                                                <t t-foreach="o.doppler_ids" t-as="doc">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </t>
                                            </div>
                                        </div>

                                        <!-- Sonography Reports -->
                                        <div t-if="o.sonography_ids" class="mb-4">
                                            <h6 class="text-muted">Sonography Reports</h6>
                                            <div class="list-group">
                                                <t t-foreach="o.sonography_ids" t-as="doc">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Doctor's Feedback Section -->
                                    <div t-if="o.state in ['in_progress', 'completed','assigned']" class="row mt-4">
                                        <hr/>
                                        <div class="col-12">
                                            <div t-attf-class="alert">
                                                <h5 class="mb-3">Doctor's Feedback</h5>
                                                <div class="mb-3">
                                                    <strong>Doctor:</strong>
                                                    <span t-field="o.doctor_id.name" class="ml-2"/>
                                                </div>
                                                <div class="mb-3">
                                                    <strong>Specialty:</strong>
                                                    <div t-field="o.doctor_id.speciality_ids" class="ml-2" widget="many2many_tags"/>
                                                </div>
                                                <div class="mb-3">
                                                    <strong>Feedback:</strong>
                                                    <p t-field="o.doctor_feedback" class="mt-2 mb-0"/>
                                                </div>

                                                <!-- Doctor's Reference Documents -->
                                                <div t-if="o.doctor_reference_ids" class="mt-4">
                                                    <h6>Reference Documents</h6>
                                                    <div class="list-group">
                                                        <t t-foreach="o.doctor_reference_ids" t-as="doc">
                                                            <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                <i class="fa fa-file mr-2"/>
                                                                <t t-esc="doc.name"/>
                                                            </a>
                                                        </t>
                                                    </div>
                                                </div>

                                                <!-- Recommendations -->
                                                <!--                                        <div t-if="o.recommendations" class="mt-4">-->
                                                <!--                                            <h6>Recommendations</h6>-->
                                                <!--                                            <p t-field="o.recommendations" class="mb-0"/>-->
                                                <!--                                        </div>-->
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Follow-up Section -->
                                    <!-- <div t-if="o.state == 'completed'" class="row mt-4">
                                            <div class="col-12">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h5 class="mb-3">Follow-up Information</h5>
                                                        <div class="mb-3">
                                                            <strong>Follow-up Date:</strong>
                                                            <span t-field="o.follow_up_date" class="ml-2"/>
                                                        </div>
                                                        <div t-if="o.follow_up_instructions">
                                                            <strong>Instructions:</strong>
                                                            <p t-field="o.follow_up_instructions" class="mt-2 mb-0"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div> -->

                                </div>
                            </div>

                            <!-- Timeline Section -->
                            <!-- <div class="mt-4">
                                <h5 class="mb-3">Request Timeline</h5>
                                <div class="timeline">
                                    <div t-foreach="o.message_ids" t-as="message" class="mb-3">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i t-attf-class="fa fa-circle text-#{{'success' if message.subtype_id.name == 'Stage Changed' else 'primary'}} mr-2"/>
                                            </div>
                                            <div>
                                                <div class="text-muted small">
                                                    <t t-esc="message.date" t-options='{"widget": "datetime"}'/>
                                                </div>
                                                <div t-field="message.body"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> -->
                        </div>
                </div>
            </div>
            </t>
        </template>

        <template id="doctor_selection" name="Select Doctor">
            <t t-call="website.layout">
                <div class="container py-4 doctor-selection-page">
                    <h2 class="text-center mb-4">Select Your Doctor</h2>
                    
                    <!-- Search and Sort Controls -->
                    <div class="row g-3 mb-4">
                        <div class="col-12 col-md-6 mb-3 mb-md-0">
                            <div class="input-group">
                                <input type="text" class="form-control" id="doctorSearch" 
                                    placeholder="Search by name, speciality or experience..."
                                    t-att-value="search or ''" />
                                <button class="btn btn-primary" type="button" id="searchButton">
                                    <i class="fa fa-search me-2"></i>Search
                                </button>
                            </div>
                            <small class="text-muted mt-1">Press Enter or click Search to search</small>
                        </div>
                        <div class="col-12 col-md-6">
                            <select id="doctorSort" class="form-select">
                                <option value="best_match">Best Match</option>
                                <option value="name_asc">Name (A-Z)</option>
                                <option value="name_desc">Name (Z-A)</option>
                                <option value="fee_asc">Consultation Fee (Low to High)</option>
                                <option value="fee_desc">Consultation Fee (High to Low)</option>
                                <option value="experience_desc">Most Experienced</option>
                                <option value="experience_asc">Newly Registered</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Doctor Cards Container -->
                    <div id="doctorCards" class="row doctor-cards-container g-4">
                        <t t-foreach="doctors" t-as="doctor">
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="card h-100 shadow-sm">
                                    <div class="card-body">
                                        <div class="text-center mb-3">
                                            <t t-if="doctor.photo">
                                                <img t-att-src="image_data_uri(doctor.photo)" class="rounded-circle img-fluid" style="width: 128px; height: 128px; object-fit: cover;" alt="Doctor"/>
                                            </t>
                                            <t t-else="">
                                                <img src="/web/static/img/placeholder.png" class="rounded-circle img-fluid" style="width: 128px; height: 128px; object-fit: cover;" alt="No Image"/>
                                            </t>
                                        </div>
                                        <h5 class="card-title text-center mb-3" t-field="doctor.name"/>
                                        <div class="specialities mb-3">
                                            <t t-foreach="doctor.speciality_ids" t-as="speciality">
                                                <span class="badge bg-info me-1 mb-1" t-field="speciality.name"/>
                                            </t>
                                        </div>
                                        <div class="card-text">
                                            <p class="mb-2">
                                                <i class="fa fa-user-md me-2"></i>
                                                <strong>Experience:</strong> <span t-field="doctor.experience"/> years
                                            </p>
                                            <!-- <p class="mb-3">
                                                <i class="fa fa-money me-2"></i>
                                                <strong>Consultation Fee:</strong> <span t-field="doctor.consultation_fee"/>
                                            </p> -->
                                            <div class="doctor-card-fees">
                                                <div class="fee-item">
                                                    <span class="fee-label">Consultation Fee:</span>
                                                    <t t-if="request.params.get('is_followup') == '1'">
                                                        <span class="fee-amount" t-esc="'{:,.2f}'.format(doctor.followup_case_consultation_fee * 1.18)"/>
                                                        <small>(Follow-up Case)</small>
                                                    </t>
                                                    <t t-else="">
                                                        <span class="fee-amount" t-esc="'{:,.2f}'.format(doctor.new_case_consultation_fee * 1.18)"/>
                                                        <small>(New Case)</small>
                                                    </t>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <a t-attf-href="/select/doctor/#{doctor.id}?request_id=#{request_id}" 
                                            class="btn btn-primary w-100">
                                            <i class="fa fa-check-circle me-2"></i>Select Doctor
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="pagination-container mt-4">
                        <t t-call="website.pager"/>
                    </div>
                </div>

                <style>
                    /* Loading State */
                    #doctorCards.loading {
                        opacity: 0.6;
                        pointer-events: none;
                        position: relative;
                        min-height: 200px;
                    }
                    #doctorCards.loading::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 40px;
                        height: 40px;
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #3498db;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }
                    @keyframes spin {
                        0% { transform: translate(-50%, -50%) rotate(0deg); }
                        100% { transform: translate(-50%, -50%) rotate(360deg); }
                    }

                    /* Responsive Styles */
                    @media (max-width: 767.98px) {
                        .input-group {
                            width: 100%;
                        }
                        #searchButton {
                            min-width: 100px;
                        }
                        .doctor-selection-page {
                            padding-left: 15px;
                            padding-right: 15px;
                        }
                    }

                    /* Card Hover Effect */
                    .card {
                        transition: transform 0.2s ease-in-out;
                    }
                    .card:hover {
                        transform: translateY(-5px);
                    }

                    /* Badge Styles */
                    .badge {
                        font-size: 0.85rem;
                        padding: 0.4em 0.8em;
                    }
                </style>
            </t>
        </template>

        <template id="doctor_registration_form" name="Doctor Registration">
            <t t-call="website.layout">
                                    <!-- Styles -->
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"/>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"/>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"/>
                    <!-- Or for RTL support -->
                    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css"/> -->
                    <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                    <!-- Scripts -->
                    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.0/dist/jquery.slim.min.js"/>
                    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"/>
                    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"/>
                    <style>
                    .form-box {
                        max-width: 1000px;
                        margin: 35px auto;
                        padding: 35px;
                        background: #fff; / White background for form container /
                        border-radius: 10px;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                        border: 1px solid #ccc; / Border around form box /
                        --nf-input-focus-border-color: green !important;
                        --nf-input-focus-background-color: green !important;
                    }
                    
                </style>
                <section class="pt16 pb16" style="background-color: rgba(214, 239, 214, 0.41) !important; background-image: none;">
                <div class="container">
                    <div class="form-box">
                        <h1 class="text-center mb32">Doctor Registration</h1>
                        
                        <!-- Error Alert -->
                        <div class="alert alert-danger d-none" id="error-message" role="alert">
                        </div>

                        <form id="doctor-registration-form" action="/doctor/register" method="post" enctype="multipart/form-data" class="o_website_form nice-form">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="nice-form-group">
                                        <label for="name">Full Name</label>
                                        <input type="text" class="form-control" name="name" required="required"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="registration_number">Registration Number:</label>
                                        <input type="text" class="form-control" id="registration_number" name="registration_number" />
                                        <!-- required="required"  -->
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="photo">Profile Photo</label>
                                        <input type="file" class="form-control" name="photo" accept="image/*" required="required"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="email">Email</label>
                                        <input type="email" class="form-control" name="email" required="required"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="phone">Phone</label>
                                        <input type="tel" class="form-control" name="phone" required="required"/>
                                    </div>
                                    <!-- <div class="nice-form-group">
                                        <label for="consultation_fee">Consultation Fee</label>
                                        <input type="number" class="form-control" name="consultation_fee" required="required" min="0" step="0.01"/>
                                    </div> -->
                                </div>
                                <div class="col-lg-6">
                                    <div class="nice-form-group">
                                        <label for="experience">Years of Experience</label>
                                        <input type="number" class="form-control" name="experience" required="required"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="qualification">Qualification</label>
                                        <textarea class="form-control" name="qualification" required="required" rows="3"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="address">Address</label>
                                        <textarea class="form-control" name="address" rows="3"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="new_case_consultation_fee">New Case Consultation Fee</label>
                                        <input type="number" class="form-control" name="new_case_consultation_fee" required="required" min="0" step="0.01"/>
                                    </div>
                                    <div class="nice-form-group">
                                        <label for="followup_case_consultation_fee">Old Case Consultation Fee</label>
                                        <input type="number" class="form-control" name="followup_case_consultation_fee" required="required" min="0" step="0.01"/>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="nice-form-group">
                                        <label for="multiple-select-field" style="margin-top:5px;">Specialities</label>
                                        <select class="form-select" id="multiple-select-field" data-placeholder="Choose specialities" multiple="multiple" name="speciality_ids" style="margin-top:5px;">
                                        <!-- required="required"  -->
                                            <t t-foreach="request.env['medical.speciality'].sudo().search([])" t-as="speciality">
                                                <option t-att-value="speciality.id"><t t-esc="speciality.name"/></option>
                                            </t>
                                        </select>

                                        <script>
                                            $('#multiple-select-field').on('change', function() {
                                            let selectedSymptoms = $('#multiple-select-field').val(); // Returns an array of selectedvalues
                                            console.log(selectedSymptoms); // Check the values in the console
                                            });
                                            $( '#multiple-select-field' ).select2( {
                                            theme: "bootstrap-5",
                                            width: $( this ).data( 'width' ) ? $( this ).data( 'width' ) : $( this ).hasClass( 'w-100' )
                                            ?
                                            '100%' : 'style',
                                            placeholder: $( this ).data( 'placeholder' ),
                                            closeOnSelect: false,
                                            } );
                                        </script>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-lg-6">
                                    <div class="nice-form-group">
                                        <label for="resume">Resume/CV</label>
                                        <input type="file" class="form-control" name="resume" required="required"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="nice-form-group">
                                        <label for="license_doc">Medical License</label>
                                        <input type="file" class="form-control" name="license_doc" required="required"/>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">Submit Registration</button>
                            </div>
                        </form>
                    </div>
                </div>
                </section>

                <script type="text/javascript">
                    $(document).ready(function() {
                        // Initialize Select2
                        <!-- $('.select2').select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                            placeholder: 'Select specialities',
                            allowClear: true,
                            closeOnSelect: false
                        }); -->

                        // Handle form submission
                        $('#doctor-registration-form').on('submit', function(e) {
                            e.preventDefault();
                            var form = $(this);
                            var formData = new FormData(form[0]);

                            $.ajax({
                                url: form.attr('action'),
                                type: 'POST',
                                data: formData,
                                processData: false,
                                contentType: false,
                                success: function(response) {
                                    try {
                                        var result = JSON.parse(response);
                                        if (result.error) {
                                            $('#error-message')
                                                .removeClass('d-none')
                                                .text(result.message);
                                        } else {
                                            window.location.href = '/doctor/registration/success';
                                        }
                                    } catch(e) {
                                        // If response is not JSON, it's probably the success page HTML
                                        document.write(response);
                                    }
                                },
                                error: function() {
                                    $('#error-message')
                                        .removeClass('d-none')
                                        .text('An error occurred. Please try again.');
                                }
                            });
                        });
                    });
                </script>
            </t>
        </template>

        <template id="doctor_registration_success" name="Registration Success">
            <t t-call="website.layout">
                <div class="container mt32 mb32">
                    <div class="alert alert-success text-center">
                        <h2>Thank you for registering!</h2>
                        <p>Your registration has been submitted successfully. Our team will review your application and contact you soon.</p>
                    </div>
                </div>
            </t>
        </template>

        <template id="second_opinion_payment" name="Second Opinion Payment">
            <t t-call="website.layout">
                <div class="container py-5">
                    <div class="row">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card">
                                <div class="card-body">
                                    <h3 class="mb-4 text-center">Second Opinion Payment</h3>
                                    
                                    <!-- Amount Display -->
                                    <div class="form-group">
                                        <label class="col-form-label" for="amount">Amount to Pay</label>
                                        <div class="input-group">
                                            <input type="number" name="amount" id="amount" 
                                                t-att-value="amount" class="form-control" 
                                                readonly="readonly"/>
                                            <div class="input-group-append">
                                                <span class="input-group-text" t-esc="currency.symbol"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Provider Selection -->
                                    <div class="mt-4">
                                        <t t-call="payment.payment_methods">
                                            <t t-set="reference" t-value="reference"/>
                                            <t t-set="amount" t-value="amount"/>
                                            <t t-set="currency" t-value="currency"/>
                                            <t t-set="partner_id" t-value="partner_id"/>
                                            <t t-set="access_token" t-value="access_token"/>
                                            <t t-set="transaction_route" t-value="'/payment/transaction'"/>
                                            <t t-set="landing_route" t-value="success_url"/>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="payment_success" name="Second Opinion Payment Success">
            <t t-call="website.layout">
                <div class="container py-5">
                    <div class="row">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fa fa-check-circle text-success fa-5x mb-3"></i>
                                    <h3>Thank You for Your Payment!</h3>
                                    <p>Your payment for the second opinion request has been processed successfully.</p>
                                    <p>You will be notified once a doctor is assigned to your case.</p>
                                    <div class="mt-3">
                                        <a href="/my/requests" class="btn btn-primary">View My Consultations</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

     

        <template id="payment_provider_selection" name="Payment Provider Selection">
            <t t-call="portal.portal_layout">
                <div class="container mt-4">
                    <div class="row">
                        <div class="col-12">
                            <h2>Select Payment Method</h2>
                            <div class="mt-4">
                                <p>Amount to Pay: <span t-field="invoice.amount_total" t-options='{"widget": "monetary", "display_currency": invoice.currency_id}'/></p>
                            </div>
                            <div class="row mt-4">
                                <t t-foreach="providers" t-as="provider">
                                    <div class="col-md-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">
                                                    <t t-if="provider.image_128">
                                                        <img t-att-src="image_data_uri(provider.image_128)" class="float-left mr-2" style="max-height: 25px;"/>
                                                    </t>
                                                    <span t-field="provider.name"/>
                                                </h5>
                                                <p class="card-text">
                                                    <t t-if="provider.state == 'test'">
                                                        <span class="badge badge-info">Test Mode</span>
                                                    </t>
                                                </p>
                                                <a t-att-href="'/my/requests/%s/pay/%s' % (second_opinion.id, provider.id)" 
                                                class="btn btn-primary">
                                                    Pay with <t t-esc="provider.name"/>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <template id="no_payment_provider" name="No Payment Provider">
            <t t-call="portal.portal_layout">
                <div class="container mt-4">
                    <div class="alert alert-warning">
                        <p><t t-esc="error_message"/></p>
                    </div>
                    <div class="mt-4">
                        <a href="/my/requests" class="btn btn-secondary">Back to Requests</a>
                    </div>
                </div>
            </t>
        </template>



        
        <template id="donation_form_inherited" inherit_id="website_payment.donation_information">
            <!-- Change the title -->
            <xpath expr="//h3[@class='o_page_header mt16 mb4'][1]" position="replace">
                <t t-if="request.params.get('is_second_opinion')">
                    <h3 class="o_page_header mt16 mb4">Second Opinion Payment</h3>
                </t>
                <t t-else="">
                    <h3 class="o_page_header mt16 mb4">Make Payment</h3>
                </t>            
            </xpath>
            
            <!-- Hide donation options and comment section -->
            <xpath expr="//div[@class='col-lg-12 px-0']" position="attributes">
                <attribute name="class">d-none</attribute>
            </xpath>    

            <!-- Set payment info from session -->
            <xpath expr="//div[hasclass('o_donation_payment_form')]" position="before">
                <t t-set="payment_info" t-value="request.session.get('second_opinion_payment', {})"/>
            </xpath>
        
            <!-- Customize the amount section -->
            <xpath expr="//div[@class='col-lg-6 px-0']" position="replace">
                <t t-if="request.session.get('second_opinion_payment', {}).get('amount')">
                    <div class="mb-3 col-lg-12">
                        <label class="col-form-label">Amount</label>
                        <div class="form-control-plaintext">
                            <span t-esc="request.env.company.currency_id.symbol"/>
                            <span t-esc="request.session.get('second_opinion_payment', {}).get('amount')" class="oe_currency_value"/>
                        </div>                    
                    </div>
                </t>
                <t t-else="">
                    <div class="mb-3 col-lg-12">
                        <label class="col-form-label" for="amount">Amount</label>
                        <div class="input-group">
                            <div class="input-group-append">
                                <span class="input-group-text">
                                    <span t-esc="request.env.company.currency_id.symbol"/>
                                </span>
                            </div>
                            <input type="number" name="amount" class="form-control" required="1"/>
                        </div>
                    </div>
                </t>
            </xpath>
        </template>

        
        <template id="payment_form_amount" inherit_id="payment.form">
            <xpath expr="//form[@id='o_payment_form']" position="attributes">
                <attribute name="t-att-data-amount">request.session.get('second_opinion_payment', {}).get('amount') or amount</attribute>
            </xpath>

            <!-- Set fixed button label -->
            <xpath expr="//t[@t-set='submit_button_label']" position="replace">
                <t t-set="submit_button_label">Pay Now</t>
            </xpath>
        </template>

        
        <template id="second_opinion_payment_page" inherit_id="website_payment.donation_pay">
            <!-- Set fixed page title -->
            <xpath expr="//t[@t-set='page_title']" position="replace">
                <t t-set="page_title">Second Opinion Payment</t>
            </xpath>
            
            <!-- Update additional title -->
            <xpath expr="//t[@t-set='additional_title']" position="replace">
                <t t-set="additional_title">Second Opinion Payment</t>
            </xpath>
        </template>

 
        <template id="portal_my_requests_table_content" name="Second Opinion Requests Table Content">
            <t t-foreach="requests" t-as="req">
                <tr>
                    <td>
                        <a t-attf-href="/my/requests/#{req.id}">
                            <span t-field="req.name"/>
                        </a>
                    </td>
                    <td>
                        <span t-field="req.patient_id.name"/>
                    </td>
                    <td>
                        <span t-field="req.create_date" t-options='{"widget": "date"}'/>
                    </td>
                    <td>
                        <t t-if="req.doctor_id">
                            <img t-if="req.doctor_id.photo" t-att-src="'/web/image/' + str(req.doctor_id.id) + '/photo'" class="img-rounded img-small" width="20" height="20" alt="Doctor Photo"/>
                             <span t-field="req.doctor_id.name"/>
                        </t>
                        <t t-else="">
                            <span class="text-muted">Not Assigned</span>
                        </t>
                    </td>
                    <td>
                        <span t-field="req.state" class="badge" t-attf-class="badge #{
                            req.state == 'draft' and 'bg-info' or
                            req.state == 'pending_payment' and 'bg-warning' or
                            req.state == 'paid' and 'bg-primary' or
                            req.state == 'assigned' and 'bg-success' or
                            req.state == 'completed' and 'bg-success' or
                            req.state == 'cancelled' and 'bg-danger'
                        }"/>
                    </td>
                    <td>                        
                        <t t-if="req.is_followup">
                                <span t-if="req.doctor_id.followup_case_consultation_fee"
                                      t-esc="'₹' + str(req.doctor_id.followup_case_consultation_fee * 1.18)"
                                      class="ml-2 text-success" 
                                      />
                                <span t-if="req.doctor_id.followup_case_consultation_fee" class="text-muted">(Incl. GST)</span>
                                <!-- Old Case Fee -->
                            </t>
                            <t t-else="">
                                <span t-if="req.doctor_id.new_case_consultation_fee"
                                    t-esc="'₹' + str(req.doctor_id.new_case_consultation_fee * 1.18)" 
                                    class="ml-2 text-success" 
                                    />
                                <span t-if="req.doctor_id.new_case_consultation_fee" class="text-muted">(Incl. GST)</span>
                                <!-- New Case Fee -->

                            </t>
                        <!-- <span t-field="req.amount" t-options='{"widget": "monetary", "display_currency": req.invoice_id.currency_id}'/> -->
                    </td>
                    <td class="text-end">
                        <t t-if="req.state == 'pending_payment'">
                            <a t-attf-href="/my/requests/#{req.id}/pay" class="btn btn-sm btn-primary">
                                <i class="fa fa-credit-card"/> Pay Now
                            </a>
                        </t>
                        <a t-attf-href="/my/requests/#{req.id}" class="btn btn-sm btn-secondary">
                            <i class="fa fa-eye"/> View
                        </a>
                    </td>
                </tr>
            </t>
        </template>
    </data>
</odoo>
