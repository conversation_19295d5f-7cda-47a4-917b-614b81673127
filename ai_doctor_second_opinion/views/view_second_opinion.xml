<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Second Opinion Request Form View -->
    <record id="view_second_opinion_request_form" model="ir.ui.view">
        <field name="name">second.opinion.request.form</field>
        <field name="model">second.opinion.request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_confirm" string="Confirm" type="object" class="oe_highlight" groups="ai_doctor_second_opinion.group_medical_admin"  invisible="state not in ['draft']"/>
                    <button name="action_submit_feedback" string="Submit Feedback" type="object" invisible="state not in ['assigned']"/>
                    <button name="action_cancel_refund" string="Cancel &amp; Refund" type="object" groups="ai_doctor_second_opinion.group_medical_admin" invisible="state in ['draft']"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="patient_id"/>
                            <field name="patient_id_name" string="Patient Name" readonly="1"/>
                            <field name="age" string="Age"/>
                            <field name="gender" string="Gender"/>
                            <field name="doctor_id"/>
                            <field name="issue_type"/>
                        </group>
                        <group>
                            <field name="amount"/>
                            <field name="invoice_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Symptoms">
                            <field name="symptom_ids" widget="many2many_tags"/>
                            <field name="custom_symptoms"/>
                            <field name="description"/>
                        </page>
                        <page string="Documents">
                            <group>
                                <field name="lab_report_ids" widget="many2many_binary"/>
                                <field name="medicine_photo_ids" widget="many2many_binary"/>
                                <field name="prescription_ids" widget="many2many_binary"/>
                                <field name="xray_ids" widget="many2many_binary"/>
                                <field name="ct_scan_ids" widget="many2many_binary"/>
                                <field name="mri_ids" widget="many2many_binary"/>
                                <field name="doppler_ids" widget="many2many_binary"/>
                                <field name="sonography_ids" widget="many2many_binary"/>
                            </group>
                        </page>
                        <page string="Doctor's Feedback" invisible ="state not in ['assigned', 'completed']">
                            <group>
                                <field name="doctor_feedback"/>
                                <field name="doctor_reference_ids" widget="many2many_binary"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_second_opinion_request_tree" model="ir.ui.view">
    <field name="name">second.opinion.request.tree</field>
    <field name="model">second.opinion.request</field>
    <field name="arch" type="xml">
        <tree decoration-info="state == 'draft'" decoration-warning="state == 'confirmed'" decoration-success="state == 'completed'">
            <field name="name"/>
            <field name="patient_id"/>
            <field name="doctor_id"/>
            <field name="issue_type"/>
            <field name="amount"/>
            <field name="state"/>
            <field name="create_date"/>
        </tree>
    </field>
</record>

<!-- Action -->
<record id="action_second_opinion_request" model="ir.actions.act_window">
    <field name="name">Second Opinion Requests</field>
    <field name="res_model">second.opinion.request</field>
    <field name="view_mode">tree,form</field>
    <field name="context">{}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first second opinion request
        </p>
        <p>
            Get expert medical opinions from qualified doctors.
        </p>
    </field>
</record>

<record id="action_second_opinion_request_draft" model="ir.actions.act_window">
    <field name="name">Second Opinion Request</field>
    <field name="res_model">second.opinion.request</field>
    <field name="view_mode">tree,form</field>
    <field name="domain">[('state', 'in', ['draft', 'pending_payment','cancelled'])]</field>
    <field name="context">{}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first second opinion request
        </p>
        <p>
            Get expert medical opinions from qualified doctors.
        </p>
    </field>
</record>

<record id="action_second_opinion_request_paid" model="ir.actions.act_window">
    <field name="name">Second Opinion Request</field>
    <field name="res_model">second.opinion.request</field>
    <field name="view_mode">tree,form</field>
    <field name="domain">[('state', 'in', ['paid'])]</field>
    <field name="context">{}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first second opinion request
        </p>
        <p>
            Get expert medical opinions from qualified doctors.
        </p>
    </field>
</record>

<record id="action_second_opinion_request_assigned" model="ir.actions.act_window">
    <field name="name">Second Opinion Request</field>
    <field name="res_model">second.opinion.request</field>
    <field name="view_mode">tree,form</field>
    <field name="domain">[('state', 'in', ['assigned'])]</field>
    <field name="context">{}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first second opinion request
        </p>
        <p>
            Get expert medical opinions from qualified doctors.
        </p>
    </field>

</record>

<record id="action_second_opinion_request_completed" model="ir.actions.act_window">
    <field name="name">Second Opinion Request</field>
    <field name="res_model">second.opinion.request</field>
    <field name="view_mode">tree,form</field>
    <field name="domain">[('state', 'in', ['completed'])]</field>
    <field name="context">{}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first second opinion request
        </p>
        <p>
            Get expert medical opinions from qualified doctors.
        </p>
    </field>
</record>

<record id="action_second_opinion_request_docter" model="ir.actions.act_window">
    <field name="name">Second Opinion Request</field>
    <field name="res_model">second.opinion.request</field>
    <field name="view_mode">tree,form</field>
<!--    <field name="domain">[('doctor_id.user_id.id', '=', user.id)]</field>-->
    <field name="context">{}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first second opinion request
        </p>
        <p>
            Get expert medical opinions from qualified doctors.
        </p>
    </field>
</record>

<!-- Menu Items -->
<menuitem id="menu_medical_second_opinion_root"
    name="Second Opinions"
    sequence="9"/>

<menuitem id="menu_medical_second_opinion"
    name="Second Opinions"
    parent="menu_medical_second_opinion_root"
    sequence="10"/>


<menuitem id="menu_medical_second_opinion_doctor_root"
    name="Second Opinions"
          groups="ai_doctor_second_opinion.group_medical_doctor"
    sequence="10"/>

<menuitem id="menu_second_opinion_request_docter"
    name="My Request"
    parent="menu_medical_second_opinion_doctor_root"
    action="action_second_opinion_request_docter"
    groups="ai_doctor_second_opinion.group_medical_doctor"
    sequence="1"/>

<menuitem id="menu_second_opinion_request"
    name="Requests"
    parent="menu_medical_second_opinion"
    action="action_second_opinion_request"
          groups="base.group_system"
    sequence="10"/>

<menuitem id="menu_second_opinion_request_draft"
    name="Draft &amp; Pending Payment &amp; Cancelled"
    parent="menu_second_opinion_request"
    action="action_second_opinion_request_draft"
          groups="base.group_system"
    sequence="10"/>

<menuitem id="menu_second_opinion_request_paid"
    name="Paid"
    parent="menu_second_opinion_request"
    action="action_second_opinion_request_paid"
          groups="base.group_system"
    sequence="20"/>

<menuitem id="menu_second_opinion_request_assigned"
    name="Assigned"
    parent="menu_second_opinion_request"
    action="action_second_opinion_request_assigned"
          groups="base.group_system"
    sequence="30"/>

    <menuitem id="menu_second_opinion_request_completed"
    name="Completed"
    parent="menu_second_opinion_request"
    action="action_second_opinion_request_completed"
              groups="base.group_system"
    sequence="40"/>

    <menuitem id="menu_second_opinion_request_docter"
    name="My Request"
    parent="menu_second_opinion_request"
    action="action_second_opinion_request_docter"
          groups="ai_doctor_second_opinion.group_medical_doctor"
    sequence="30"/>

    <!-- Medical Doctor Tree View -->
    <record id="view_medical_doctor_tree" model="ir.ui.view">
        <field name="name">medical.doctor.tree</field>
        <field name="model">medical.doctor</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="registration_number"/>
                <field name="experience"/>
                <field name="speciality_ids" widget="many2many_tags"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Medical Doctor Form View -->
    <record id="view_medical_doctor_form" model="ir.ui.view">
        <field name="name">medical.doctor.form</field>
        <field name="model">medical.doctor</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_submit" string="Submit for Approval" type="object" class="oe_highlight" invisible="state not in ['draft', 'rejected']"/>
                    <button name="action_approve" string="Approve" type="object" class="oe_highlight" groups="ai_doctor_second_opinion.group_medical_admin" invisible="state not in ['pending']"/>
                    <button name="action_reject" string="Reject" type="object"  groups="ai_doctor_second_opinion.group_medical_admin" invisible="state not in ['pending', 'approved']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,pending,approved"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Doctor Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="partner_id"/>
                            <field name="user_id"/>
                            <field name="registration_number"/>
                        </group>
                        <group>
                            <field name="email"/>
                            <field name="experience"/>
                            <field name="speciality_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Qualifications">
                            <field name="qualification"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Medical Doctor Search View -->
    <record id="view_medical_doctor_search" model="ir.ui.view">
        <field name="name">medical.doctor.search</field>
        <field name="model">medical.doctor</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="registration_number"/>
                <field name="speciality_ids"/>
                <filter string="Draft" name="draft" domain="[('state','=','draft')]"/>
                <filter string="Pending" name="pending" domain="[('state','=','pending')]"/>
                <filter string="Approved" name="approved" domain="[('state','=','approved')]"/>
                <group expand="0" string="Group By">
                    <filter string="State" name="state" context="{'group_by':'state'}"/>
                    <filter string="Experience" name="experience" context="{'group_by':'experience'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action window -->
    <record id="action_medical_doctors" model="ir.actions.act_window">
        <field name="name">Doctors</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">medical.doctor</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first doctor!
            </p>
        </field>
    </record>

    <!-- Menu items -->
    <menuitem id="menu_medical_doctor_root"
              name="Medical Second Opinion"
              sequence="10"/>

    <menuitem id="menu_medical_doctor"
              name="Doctors"
              parent="menu_medical_doctor_root"
              action="action_medical_doctors"

              sequence="10"/>

<!-- views/medical_symptom_views.xml -->

    <!-- Medical Symptom Tree View -->
    <record id="view_medical_symptom_tree" model="ir.ui.view">
        <field name="name">medical.symptom.tree</field>
        <field name="model">medical.symptom</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Medical Symptom Form View -->
    <record id="view_medical_symptom_form" model="ir.ui.view">
        <field name="name">medical.symptom.form</field>
        <field name="model">medical.symptom</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button"/>
                        </button>
                    </div>
                    <group>
                        <field name="name"/>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Medical Symptom Search View -->
    <record id="view_medical_symptom_search" model="ir.ui.view">
        <field name="name">medical.symptom.search</field>
        <field name="model">medical.symptom</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
            </search>
        </field>
    </record>

    <!-- Action window -->
    <record id="action_medical_symptoms" model="ir.actions.act_window">
        <field name="name">Symptoms</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">medical.symptom</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first symptom!
            </p>
        </field>
    </record>

    <!-- Menu item -->
    <menuitem id="menu_medical_symptom"
              name="Symptoms"
              parent="menu_medical_doctor_root"
              action="action_medical_symptoms"
                groups="ai_doctor_second_opinion.group_medical_admin"
              sequence="20"/>
    
<!-- views/medical_speciality_views.xml -->

    <!-- Medical Speciality Tree View -->
    <record id="view_medical_speciality_tree" model="ir.ui.view">
        <field name="name">medical.speciality.tree</field>
        <field name="model">medical.speciality</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="symptom_ids" widget="many2many_tags"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Medical Speciality Form View -->
    <record id="view_medical_speciality_form" model="ir.ui.view">
        <field name="name">medical.speciality.form</field>
        <field name="model">medical.speciality</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button"/>
                        </button>
                    </div>
                    <group>
                        <field name="name"/>
                        <field name="symptom_ids" widget="many2many_tags"/>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Medical Speciality Search View -->
    <record id="view_medical_speciality_search" model="ir.ui.view">
        <field name="name">medical.speciality.search</field>
        <field name="model">medical.speciality</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
            </search>
        </field>
    </record>

    <!-- Action window -->
    <record id="action_medical_specialities" model="ir.actions.act_window">
        <field name="name">Specialities</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">medical.speciality</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first speciality!
            </p>
        </field>
    </record>

    <!-- Menu item -->
    <menuitem id="menu_medical_speciality"
              name="Specialities"
              parent="menu_medical_doctor_root"
              action="action_medical_specialities"
              groups="ai_doctor_second_opinion.group_medical_admin"
              sequence="30"/>




    <!-- Editable Tree View for Doctor Assignments -->
    <record id="view_medical_doctor_assignment_tree" model="ir.ui.view">
        <field name="name">medical.doctor.assignment.tree</field>
        <field name="model">medical.doctor.assignment</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="speciality_id"/>
                <field name="last_assigned_doctor_id"/>
            </tree>
        </field>
    </record>

<!--    <menuitem id="menu_medical_doctor_management_root" name="Doctor Assignment" parent="menu_medical_second_opinion_root"/>-->

    <!-- Action for Tree View -->
    <record id="action_medical_doctor_assignment" model="ir.actions.act_window">
        <field name="name">Doctor Assignments</field>
        <field name="res_model">medical.doctor.assignment</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No Doctor Assignments found.
            </p>
        </field>
    </record>
        <menuitem id="menu_medical_doctor_assignment" name="Doctor Assignment" parent="menu_medical_second_opinion_root" action="action_medical_doctor_assignment" groups="ai_doctor_second_opinion.group_medical_admin"/>

</odoo>