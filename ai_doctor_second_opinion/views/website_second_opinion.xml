<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <template id="create_request" name="Create Second Opinion Request">
         <t t-call="website.layout">
                <!-- Styles -->
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"/>
                <!-- Or for RTL support -->
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css"/>
                 <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                <!-- Scripts -->
                <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.0/dist/jquery.slim.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"/>
                <style>
                  .form-box {
                    max-width: 1000px;
                    margin: 35px auto;
                    padding: 35px;
                    background: #fff; / White background for form container /
                    border-radius: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    border: 1px solid #ccc; / Border around form box /
                    --nf-input-focus-border-color: green !important;
                    --nf-input-focus-background-color: green !important;
                }
                
          </style>
          <section class="pt16 pb16" style="background-color: rgba(214, 239, 214, 0.41) !important; background-image: none;">
            <div class="form-box">
                <form action="/second-opinion/submit" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                    <div class="row">
                    <div class="nice-form-group col-md-6 col-12">
                        <label for="issue_type" style="margin-top:5px;">Issue Type</label>
                        <select class="form-select" name="issue_type" required="required" style="margin-top:5px;">
                            <option value="">Select Issue Type</option>
                            <t t-foreach="issue_types" t-as="issue">
                                <option t-att-value="issue.id">
                                    <t t-esc="issue.name"/>
                                </option>
                            </t>
                        </select>
                    </div>
                    <div class="nice-form-group col">
                        <label for="age" style="margin-top:5px;">Age</label>
                        <input type="number" name="age" class="form-control" required="required" style="margin-top:5px;"/>
                    </div>
                    </div>
                    <div class="nice-form-group">
                        <label for="gender" style="margin-top:5px;">Gender</label>
                        <br/>
                        <input type="radio" name="gender" value="male"/>
                        <label>Male</label>
                        <input type="radio" name="gender" value="female"/>
                        <label>Female</label>
                        <input type="radio" name="gender" value="other"/>
                        <label>Other</label>
                    </div>
                    <div class="nice-form-group">
                        <label style="margin-top:5px;">Symptoms</label>
                        <select class="form-select" id="multiple-select-field" data-placeholder="Choose anything" multiple="multiple" name="symptom_ids" style="margin-top:5px;">
                            <t t-foreach="symptoms" t-as="symptom">
                                <option t-att-value="symptom.id">
                                    <t t-esc="symptom.name"/>
                                </option>
                            </t>
                        </select>
                        <script>
                            $('#multiple-select-field').on('change', function() {
                            let selectedSymptoms = $('#multiple-select-field').val(); // Returns an array of selectedvalues
                            console.log(selectedSymptoms); // Check the values in the console
                            });
                            $( '#multiple-select-field' ).select2( {
                            theme: "bootstrap-5",
                            width: $( this ).data( 'width' ) ? $( this ).data( 'width' ) : $( this ).hasClass( 'w-100' )
                            ?
                            '100%' : 'style',
                            placeholder: $( this ).data( 'placeholder' ),
                            closeOnSelect: false,
                            } );
                        </script>
                    </div>
                    <div class="nice-form-group">
                        <label for="custom_symptoms" style="margin-top:5px;">Other Symptoms</label>
                        <textarea name="custom_symptoms" class="form-control" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="description" style="margin-top:5px;">Description</label>
                        <textarea name="description" class="form-control" required="required" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="lab_reports" style="margin-top:5px;">Lab Reports</label>
                        <input type="file" name="lab_report_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="medicine_photos" style="margin-top:5px;">Medicine Photos</label>
                        <input type="file" name="medicine_photo_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="prescriptions" style="margin-top:5px;">Prescriptions</label>
                        <input type="file" name="prescription_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="xray" style="margin-top:5px;">X-Ray Reports</label>
                        <input type="file" name="xray_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="ct_scan" style="margin-top:5px;">CT Scan Reports</label>
                        <input type="file" name="ct_scan_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="mri" style="margin-top:5px;">MRI Reports</label>
                        <input type="file" name="mri_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="doppler" style="margin-top:5px;">Vein Doppler Reports</label>
                        <input type="file" name="doppler_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <div class="nice-form-group">
                        <label for="sonography" style="margin-top:5px;">Sonography Reports</label>
                        <input type="file" name="sonography_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>
                    </div>
                    <button type="submit" class="btn btn-primary" style="display: block; margin: auto; margin-top:5px !important ">Submit Request</button>
                </form>
            </div>
              </section>
        </t>
    </template>

    <!-- List/My Requests Template -->

    <!-- Update Form Template -->
    <template id="portal_request_update_form" name="Update Second Opinion Request">
          <t t-call="portal.portal_layout">
            
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css"/>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"/>
                <!-- Or for RTL support -->
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.rtl.min.css"/>
                 <link rel="stylesheet" href="https://unpkg.com/nice-forms.css@0.1.7/dist/nice-forms.css"/>
                <!-- Scripts -->
                <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.0/dist/jquery.slim.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"/>
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"/>
            <style>
              .form-box {
                    max-width: 1000px;
                    margin: 35px auto;
                    padding: 35px;
                    background: #fff; / White background for form container /
                    border-radius: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    border: 1px solid #ccc; / Border around form box /
                    --nf-input-focus-border-color: green !important;
                    --nf-input-focus-background-color: green !important;
                }
                
          </style>    
            
            <section class="pt16 pb16"  style="background-color: rgba(214, 239, 214, 0.41) !important; background-image: none;">
            <div class="form-box">
                <div class="row">
                    <div class="col-md-12">
                        <h3>Update Request</h3>
                        <form action="/my/requests/update" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <input type="hidden" name="request_id" t-att-value="second_opinion_request.id"/>
                            <div class="row">
                            <div class="nice-form-group col-md-6 col-12">
                                <label for="issue_type">Issue Type</label>
                                <select class="form-select" name="issue_type" required="1">
                                    <option value="" hidden="">Select Issue Type</option>
                                    <t t-foreach="issue_type" t-as="issue">
                                        <option t-att-value="issue.id" t-att-selected="issue.id == second_opinion_request.issue_type.id">
                                            <t t-esc="issue.name"/>
                                        </option>
                                    </t>
                                </select>
                            </div>  
                    <div class="nice-form-group col">
                        <label for="age">Age</label>
                        <input type="number" name="age" class="form-control" required="required" style="margin-top:5px;" t-att-value="second_opinion_request.age"/>
                    </div>
                    </div>
                    <!-- <div class="nice-form-group col">
                        <label for="gender" style="margin-top:5px;">Gender</label>
                        <br/>
                                <input type="radio" id="gender_male" name="gender" value="male" required="required" t-att-checked="second_opinion_request.gender == 'male'"/>
                                <label for="gender_male">Male</label>
                                <input type="radio" id="gender_female" name="gender" value="female" required="required" t-att-checked="second_opinion_request.gender == 'female'"/>
                                <label for="gender_female">Female</label>
                                <input type="radio" id="gender_other" name="gender" value="other" required="required" t-att-checked="second_opinion_request.gender == 'other'"/>
                                <label for="gender_other">Other</label>
                            </div> -->
                    <div class="nice-form-group">
                        <label for="gender" style="margin-top:5px;">Gender</label>
                        <br/>
                        <input type="radio" name="gender" value="male" t-att-checked="second_opinion_request.gender == 'male'"/>
                        <label>Male</label>
                        <input type="radio" name="gender" value="female" t-att-checked="second_opinion_request.gender == 'female'"/>
                        <label>Female</label>
                        <input type="radio" name="gender" value="other" t-att-checked="second_opinion_request.gender == 'other'"/>
                        <label>Other</label>
                    </div>
                        
                        <div class="nice-form-group">
                            <label style="margin-top:5px;">Symptoms</label>
                            <select class="form-select" id="multiple-select-field" data-placeholder="Choose anything" multiple="multiple" name="symptoms[]" style="margin-top:5px;">
                                <t t-foreach="symptoms" t-as="symptom">
                                    <option t-att-value="symptom.id" t-att-selected="symptom.id in second_opinion_request.symptom_ids.ids">
                                        <t t-esc="symptom.name"/>
                                    </option>
                                </t>
                            </select>
                            <script>
                                $('#multiple-select-field').on('change', function() {
                                    let selectedSymptoms = $('#multiple-select-field').val(); // Returns an array of selected values
                                    console.log(selectedSymptoms); // Check the values in the console
                                });
                                $('#multiple-select-field').select2({
                                    theme: "bootstrap-5",
                                    width: $(this).data('width') ? $(this).data('width') : $(this).hasClass('w-100') ? '100%' : 'style',
                                    placeholder: $(this).data('placeholder'),
                                    closeOnSelect: false,
                                });
                            </script>
                        </div>
                            <div class="nice-form-group">
                                <label for="description">Description</label>
                                <textarea class="form-control" name="description" rows="4" required="1"><t t-esc="second_opinion_request.description"/></textarea>
                            </div>
                            <div class="nice-form-group">
                                <label for="custom_symptoms">Custom Symptoms</label>
                                <textarea class="form-control" name="custom_symptoms" rows="3"><t t-esc="second_opinion_request.custom_symptoms"/></textarea>
                            </div>

                            <div class="nice-form-group">
                            <!-- <label>Lab Reports</label> -->
                            <h6 class="text-muted mb-3">Lab Reports</h6>
                            <div t-if="second_opinion_request.lab_report_ids" class="mb-4">
                                            <!-- <h6 class="text-muted mb-3">Lab Reports</h6> -->
                                            <div class="list-group">
                                                <t t-foreach="second_opinion_request.lab_report_ids" t-as="doc">
                                                    <!-- <input type="checkbox" name="remove_prescription_ids" t-att-value="doc.id" class="mr-2"/> -->
                                                    <div class="row">
                                                        <div t-att-class="'col-auto ' + str(doc.id) ">
                                                            <button type="button" class="btn remove-document" t-att-data-doc-id="doc.id"><i class="fa fa-close "/></button>
                                                        </div>
                                                        <div t-att-class="'col ' + str(doc.id) ">
                                                            <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                <i class="fa fa-file-pdf-o mr-2"/>
                                                                <t t-esc="doc.name"/>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </t>
                                            </div>
                                        </div> 
                                
                                <input type="file" name="lab_report_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>

                            </div>

                            <div class="nice-form-group">
                            <h6 class="text-muted mb-3">Medicine Photos</h6>
                            <div t-if="second_opinion_request.medicine_photo_ids" class="mb-4">
                                            <!-- <div class="row">
                                                <t t-foreach="second_opinion_request.medicine_photo_ids" t-as="photo">
                                                    <div class="col-md-3 col-6 mb-3">
                                                        <a t-attf-href="/web/content/#{photo.id}?download=true"
                                                           class="d-block">
                                                            <img t-attf-src="/web/image/#{photo.id}"
                                                                 class="img-fluid rounded shadow-sm"
                                                                 t-att-alt="photo.name"/>
                                                        </a>
                                                    </div>
                                                </t>
                                            </div> -->
                                            <div class="list-group">
                                                <t t-foreach="second_opinion_request.medicine_photo_ids" t-as="doc">
                                                    <!-- <input type="checkbox" name="remove_prescription_ids" t-att-value="doc.id" class="mr-2"/> -->
                                                    <div class="row">
                                                        <div t-att-class="'col-auto ' + str(doc.id) ">
                                                            <button type="button" class="btn remove-document" t-att-data-doc-id="doc.id"><i class="fa fa-close "/></button>
                                                        </div>
                                                        <div t-att-class="'col ' + str(doc.id) ">
                                                            <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                <i class="fa fa-file-pdf-o mr-2"/>
                                                                <t t-esc="doc.name"/>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </t>
                                            </div>
                                        </div>
                                <!-- <label>Medicine Photos</label> -->
                                <input type="file" name="medicine_photo_ids" class="form-control" multiple="multiple" style="margin-top:5px;" accept="image/*"/>

                            </div>

                            <div class="nice-form-group">
                            <!-- Prescriptions -->
                           
                            <h6 class="text-muted mb-3">Prescriptions</h6>
                                        <div t-if="second_opinion_request.prescription_ids" class="mb-4">
                                            <div class="list-group">
                                                
                                                <t t-foreach="second_opinion_request.prescription_ids" t-as="doc">
                                                    <!-- <input type="checkbox" name="remove_prescription_ids" t-att-value="doc.id" class="mr-2"/> -->
                                                    <div class="row">
                                                        <div t-att-class="'col-auto ' + str(doc.id) ">
                                                            <button type="button" class="btn remove-document" t-att-data-doc-id="doc.id"><i class="fa fa-close "/></button>
                                                        </div>
                                                        <div t-att-class="'col ' + str(doc.id) ">
                                                            <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                                <i class="fa fa-file-pdf-o mr-2"/>
                                                                <t t-esc="doc.name"/>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </t>
                                            </div>
                                        </div>
                                
                                <input type="file" name="prescription_ids" class="form-control" multiple="multiple" style="margin-top:5px;"/>

                            </div>

                            <script type="text/javascript">
    $(document).ready(function() {
        $('.remove-document').on('click', function(e) {
            <!-- e.preventDefault(); // Prevent form submission -->
            var $btn = $(this);
            var docId = $btn.data('doc-id');

            $.ajax({
                url: '/my/requests/remove_document',
                type: 'POST',
                data: {
                    'doc_id': docId,
                },
                success: function(response) {
                    response = JSON.parse(response);    
                    if (response.success) {
                        $('div.' + docId).remove();
                    } else {
                        alert('Failed to remove the document.');
                    }
                },
                error: function() {
                    alert('An error occurred while removing the document.');
                }
            });
        });
    });
</script>

                          <div class=" d-flex justify-content-center" style="gap: 10px; margin-top: 5px;">
                            <button type="submit" class="btn btn-primary" >Update Request</button>
                             <a href="/my/requests" class="btn btn-secondary" >Cancel</a>
                             </div> 
                        </form>
                    </div>
                </div>
            </div>
               </section>
        </t>
    </template>

    <template id="portal_request_detail" name="Second Opinion Request Detail">
        <t t-call="portal.portal_layout">
            <t t-set="o" t-value="results"/>

            <div class="container">
                <div class="row mt-4 mb-5">
                    <div class="col-12">
                        <!-- Breadcrumb -->
                        <div class="o_portal_breadcrumbs mb-3">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="/my/home">Home</a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="/my/requests">Second Opinion Requests</a>
                                </li>
                                <li class="breadcrumb-item active">
                                    <t t-esc="o.name"/>
                                </li>
                            </ol>
                        </div>

                        <!-- Request Header with Payment Button -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div>
                                <h2>Request:
                                    <t t-esc="o.name"/>
                                </h2>
                                <div class="text-muted">
                                    Submitted on
                                    <span t-field="o.create_date" t-options='{"widget": "date"}'/>
                                </div>
                            </div>
                            <div t-if="show_pay_button" class="text-right">
                                <div class="h4 mb-2">
                                    Price:
                                    <span t-field="product.list_price" t-options='{"widget": "monetary"}'/>
                                </div>
                                <a t-attf-href="/my/requests/#{o.id}/pay"
                                   class="btn btn-primary">
                                    <i class="fa fa-credit-card mr-2"/>Pay Now
                                </a>
                            </div>
                        </div>

                        <!-- Status Banner -->
                        <t t-if="o.state == 'completed'">
                            <div t-attf-class="alert alert-success mb-4">
                                <strong>Status:</strong>
                                <span t-field="o.state" class="ml-2"/>
                                <t t-if="o.state == 'pending_payment'">
                                    <div class="mt-2">
                                        <small>Your request is pending payment. Please complete the payment to proceed
                                            with the second opinion.
                                        </small>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <t t-if="o.state == 'draft'">
                                <div t-attf-class="alert alert-warning mb-4">
                                    <strong>Status:</strong>
                                    <span t-field="o.state" class="ml-2"/>
                                    <t t-if="o.state == 'pending_payment'">
                                        <div class="mt-2">
                                            <small>Your request is pending payment. Please complete the payment to
                                                proceed with the second opinion.
                                            </small>
                                        </div>
                                    </t>
                                </div>
                            </t>
                            <t t-else="">
                                <div t-attf-class="alert alert-info mb-4">
                                    <strong>Status:</strong>
                                    <span t-field="o.state" class="ml-2"/>
                                    <t t-if="o.state == 'pending_payment'">
                                        <div class="mt-2">
                                            <small>Your request is pending payment. Please complete the payment to
                                                proceed with the second opinion.
                                            </small>
                                        </div>
                                    </t>
                                </div>
                            </t>
                        </t>
                        <!--                    <div t-attf-class="alert alert-#{{'warning' if o.state == 'in_progress' else 'info'}} mb-4">-->

                        <!--                    <div t-attf-class="alert alert-#{{'success' if o.state == 'completed' else 'warning' if o.state == 'in_progress' else 'info'}} mb-4">-->


                        <!-- Main Content Card -->
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <!-- Status and Type Section -->
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <strong>Issue Type:</strong>
                                            <span t-field="o.issue_type.name" class="ml-2"/>
                                        </div>
                                    </div>
                                </div>

                                <!-- Patient Info Section -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <h5 class="mb-3">Patient Information</h5>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <strong>Patient Name:</strong>
                                                <span t-field="o.patient_id.name" class="ml-2"/>
                                            </div>
                                            <div class="col-md-4">
                                                <strong>Age:</strong>
                                                <span t-field="o.age" class="ml-2"/>
                                            </div>
                                            <div class="col-md-4">
                                                <strong>Gender:</strong>
                                                <span t-field="o.gender" class="ml-2"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Symptoms Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5 class="mb-3">Symptoms</h5>
                                        <div t-field="o.symptom_ids"
                                             widget="many2many_tags"
                                             class="mb-2"/>
                                        <div t-if="o.custom_symptoms" class="mt-3">
                                            <strong>Additional Symptoms:</strong>
                                            <p t-field="o.custom_symptoms" class="text-muted mb-0"/>
                                        </div>
                                    </div>
                                </div>

                                <!-- Medical History Section -->
                                <!--                            <div class="row mt-4">-->
                                <!--                                <div class="col-12">-->
                                <!--                                    <h5 class="mb-3">Medical History</h5>-->
                                <!--                                    <div class="row">-->
                                <!--                                        <div class="col-md-6">-->
                                <!--                                            <strong>Existing Conditions:</strong>-->
                                <!--                                            <div t-field="o.existing_condition_ids"-->
                                <!--                                                 widget="many2many_tags"-->
                                <!--                                                 class="mb-2"/>-->
                                <!--                                        </div>-->
                                <!--                                        <div class="col-md-6">-->
                                <!--                                            <strong>Current Medications:</strong>-->
                                <!--                                            <div t-field="o.current_medication_ids"-->
                                <!--                                                 widget="many2many_tags"-->
                                <!--                                                 class="mb-2"/>-->
                                <!--                                        </div>-->
                                <!--                                    </div>-->
                                <!--                                    <div t-if="o.medical_history" class="mt-3">-->
                                <!--                                        <strong>Additional Medical History:</strong>-->
                                <!--                                        <p t-field="o.medical_history" class="text-muted mb-0"/>-->
                                <!--                                    </div>-->
                                <!--                                </div>-->
                                <!--                            </div>-->

                                <!-- Description Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h5 class="mb-3">Description</h5>
                                        <p t-field="o.description" class="text-muted"/>
                                    </div>
                                </div>

                                <!-- Documents Section -->
                                <div class="mt-4">
                                    <h5 class="mb-3">Medical Documents</h5>

                                    <!-- Lab Reports -->
                                    <div t-if="o.lab_report_ids" class="mb-4">
                                        <h6 class="text-muted">Lab Reports</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.lab_report_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Medicine Photos -->
                                    <div t-if="o.medicine_photo_ids" class="mb-4">
                                        <h6 class="text-muted">Medicine Photos</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.medicine_photo_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Prescriptions -->
                                    <div t-if="o.prescription_ids" class="mb-4">
                                        <h6 class="text-muted">Prescriptions</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.prescription_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- X-Ray Reports -->
                                    <div t-if="o.xray_ids" class="mb-4">
                                        <h6 class="text-muted">X-Ray Reports</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.xray_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- CT Scan Reports -->
                                    <div t-if="o.ct_scan_ids" class="mb-4">
                                        <h6 class="text-muted">CT Scan Reports</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.ct_scan_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- MRI Reports -->
                                    <div t-if="o.mri_ids" class="mb-4">
                                        <h6 class="text-muted">MRI Reports</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.mri_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Vein Doppler Reports -->
                                    <div t-if="o.doppler_ids" class="mb-4">
                                        <h6 class="text-muted">Vein Doppler Reports</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.doppler_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Sonography Reports -->
                                    <div t-if="o.sonography_ids" class="mb-4">
                                        <h6 class="text-muted">Sonography Reports</h6>
                                        <div class="list-group">
                                            <t t-foreach="o.sonography_ids" t-as="doc">
                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                    <a t-attf-href="/web/content/#{doc.id}?download=true" class="text-primary">
                                                        <i class="fa fa-file-medical mr-2"></i>
                                                        <t t-esc="doc.name"/>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>
                                </div>
                                <!-- Doctor's Feedback Section -->
                                <div t-if="o.state in ['in_progress', 'completed','assigned']" class="row mt-4">
                                    <div class="col-12">
                                        <div t-attf-class="alert">
                                            <h5 class="mb-3">Doctor's Feedback</h5>
                                            <div class="mb-3">
                                                <strong>Doctor:</strong>
                                                <span t-field="o.doctor_id.name" class="ml-2"/>
                                            </div>
                                            <div class="mb-3">
                                                <strong>Specialty:</strong>
                                                <span t-field="o.doctor_id.speciality_ids" class="ml-2"/>
                                            </div>
                                            <div class="mb-3">
                                                <strong>Feedback:</strong>
                                                <p t-field="o.doctor_feedback" class="mt-2 mb-0"/>
                                            </div>

                                            <!-- Doctor's Reference Documents -->
                                            <div t-if="o.doctor_reference_ids" class="mt-4">
                                                <h6>Reference Documents</h6>
                                                <div class="list-group">
                                                    <t t-foreach="o.doctor_reference_ids" t-as="doc">
                                                        <a t-attf-href="/web/content/#{doc.id}?download=true" class="list-group-item list-group-item-action">
                                                            <i class="fa fa-file mr-2"/>
                                                            <t t-esc="doc.name"/>
                                                        </a>
                                                    </t>
                                                </div>
                                            </div>

                                            <!-- Recommendations -->
                                            <!--                                        <div t-if="o.recommendations" class="mt-4">-->
                                            <!--                                            <h6>Recommendations</h6>-->
                                            <!--                                            <p t-field="o.recommendations" class="mb-0"/>-->
                                            <!--                                        </div>-->
                                        </div>
                                    </div>
                                </div>

                                <!-- Follow-up Section -->
                                <!-- <div t-if="o.state == 'completed'" class="row mt-4">
                                    <div class="col-12">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h5 class="mb-3">Follow-up Information</h5>
                                                <div t-if="o.follow_up_date" class="mb-3">
                                                    <strong>Follow-up Date:</strong>
                                                    <span t-field="o.follow_up_date" class="ml-2"/>
                                                </div>
                                                <div t-if="o.follow_up_instructions">
                                                    <strong>Instructions:</strong>
                                                    <p t-field="o.follow_up_instructions" class="mt-2 mb-0"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> -->
                            </div>
                        </div>

                        <!-- Timeline Section -->
<!--                        <div class="mt-4">-->
<!--                            <h5 class="mb-3">Request Timeline</h5>-->
<!--                            <div class="timeline">-->
<!--                                <div t-foreach="o.message_ids" t-as="message" class="mb-3">-->
<!--                                    <div class="d-flex">-->
<!--                                        <div class="flex-shrink-0">-->
<!--                                            &lt;!&ndash;                                        <i t-attf-class="fa fa-circle text-#{{'success' if message.subtype_id.name == 'Stage Changed' else 'primary'}} mr-2"/>&ndash;&gt;-->
<!--                                        </div>-->
<!--                                        <div>-->
<!--                                            <div class="text-muted small">-->
<!--                                                <t t-esc="message.date" t-options='{"widget": "datetime"}'/>-->
<!--                                            </div>-->
<!--                                            <div t-field="message.body"/>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>
