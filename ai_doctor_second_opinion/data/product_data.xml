<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="product_second_opinion" model="product.template">
            <field name="name">Medical Second Opinion</field>
            <field name="type">service</field>
            <field name="list_price">100.00</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="False"/>
            <field name="website_published" eval="True"/>
            <field name="is_consultation_fee" eval="False"/>
        </record>

        <record id="consultation_fee_product" model="product.template">
            <field name="name">Doctor Consultation Fee</field>
            <field name="type">service</field>
            <field name="list_price">0.00</field>
            <field name="sale_ok" eval="True"/>
            <field name="purchase_ok" eval="False"/>
            <field name="website_published" eval="True"/>
            <field name="description">Consultation fee for doctor second opinion service</field>
            <field name="detailed_type">service</field>
            <field name="invoice_policy">order</field>
            <field name="is_consultation_fee" eval="True"/>
        </record>
    </data>
</odoo>