<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Create a test payment provider -->
        <!-- <record id="payment_provider_test" model="payment.provider">
            <field name="name">Wire Transfer</field>
            <field name="code">custom</field>
            <field name="state">enabled</field>
            <field name="company_id" ref="base.main_company"/>
            <field name="custom_mode">wire_transfer</field>
            <field name="is_published" eval="True"/>
        </record> -->

        <!-- Create a payment method for the provider -->
        <!-- <record id="payment_method_wire_transfer" model="payment.method">
            <field name="name">Wire Transfer</field>
            <field name="code">wire_transfer</field>
            <field name="provider_id" ref="payment_provider_test"/>
            <field name="active" eval="True"/>
        </record> -->
    </data>
</odoo>
