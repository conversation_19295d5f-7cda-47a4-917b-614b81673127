<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="doctor_registration_notification_template" model="mail.template">
            <field name="name">New Doctor Registration Notification</field>
            <field name="model_id" ref="ai_doctor_second_opinion.model_medical_doctor"/>
            <field name="subject">New Doctor Registration: {{ object.name }}</field>
            <field name="email_from">{{ object.company_id.email or user.email_formatted }}</field>
            <field name="email_to">{{ user.company_id.email }}</field>
            <field name="body_html"><![CDATA[
<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6;">
    <div style="background-color: #f4f4f4; padding: 20px; border-radius: 10px;">
        <h2 style="color: #333; border-bottom: 2px solid #3498db; padding-bottom: 10px;">New Doctor Registration</h2>
        
        <p>Dear Administrator,</p>
        
        <p>A new doctor has registered on the portal:</p>
        
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Name:</strong></td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ object.name }}</td>
            </tr>
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Email:</strong></td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ object.email }}</td>
            </tr>
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Phone:</strong></td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ object.phone }}</td>
            </tr>
            <tr>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Registration Number:</strong></td>
                <td style="padding: 8px; border-bottom: 1px solid #ddd;">{{ object.registration_number or 'Not provided' }}</td>
            </tr>
            <tr>
                <td style="padding: 8px;"><strong>Experience:</strong></td>
                <td style="padding: 8px;">{{ object.experience }} years</td>
            </tr>
        </table>
        
        <p>Please review and approve/reject the registration.</p>
        
        <div style="background-color: #ecf0f1; padding: 10px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; color: #7f8c8d; font-size: 0.9em;">
                This is an automated notification from {{ user.company_id.name }}
            </p>
        </div>
    </div>
</div>
            ]]></field>
        </record>
        <record id="doctor_credentials_email_template" model="mail.template">
            <field name="name">Doctor Registration - Login Credentials</field>
            <field name="model_id" ref="ai_doctor_second_opinion.model_medical_doctor"/>
            <field name="subject">Welcome to Second Opinion Portal - Your Login Credentials</field>
            <field name="email_from">{{ object.company_id.email or user.email_formatted }}</field>
            <field name="email_to">{{ object.email }}</field>
            <field name="body_html"><![CDATA[
<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6;">
    <div style="background-color: #f4f4f4; padding: 20px; border-radius: 10px;">
        <h2 style="color: #333; border-bottom: 2px solid #3498db; padding-bottom: 10px;">Doctor Registration Confirmation</h2>
        
        <p>Dear Dr. {{ object.name }},</p>
        
        <p>Your registration as a medical doctor has been approved. Here are your login credentials:</p>
        
        <div style="background-color: #ffffff; border: 1px solid #e0e0e0; padding: 15px; margin: 15px 0; border-radius: 5px;">
            <p style="margin: 5px 0;"><strong>Username:</strong> {{ object.email }}</p>
            <p style="margin: 5px 0;"><strong>Password:</strong> {{ ctx.get('password', 'TEMPORARY_PASSWORD') }}</p>
        </div>
        
        <p>You can login at: <a href="/web/login" style="color: #3498db; text-decoration: none;">
            {{ user.company_id.website or 'https://example.com' }}/web/login
        </a></p>
        
        <p style="color: #e74c3c; font-weight: bold;">Please change your password upon first login.</p>
        
        <div style="background-color: #ecf0f1; padding: 10px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; color: #7f8c8d; font-size: 0.9em;">
                If you did not request this registration, please contact our support team.
            </p>
        </div>
        
        <p>Best regards,<br/>{{ user.company_id.name or 'Second Opinion Portal' }} Team</p>
    </div>
</div>
            ]]></field>
        </record>
    </data>
</odoo>
