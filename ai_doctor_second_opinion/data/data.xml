<odoo>
    <data>
        <record id="ai_doctor_second_opinion.model_payment_transaction" model="ir.model">
            <field name="website_form_key">metal_id</field>
            <field name="website_form_access">True</field>
            <field name="website_form_label">metal id</field>
        </record>
        <function model="ir.model.fields" name="formbuilder_whitelist">
            <value>payment.transaction</value>
            <value eval="[
                'metal_id'
            ]"/>
        </function>
    </data>
</odoo>