from odoo import models, fields, api, _
from odoo.addons.website.models import ir_http
from odoo.exceptions import UserError
import random

import logging

_logger = logging.getLogger(__name__)


class MedicalSymptom(models.Model):
    _name = 'medical.symptom'
    _description = 'Medical Symptoms'

    name = fields.Char('Symptom Name', required=True)
    description = fields.Text('Description')
    active = fields.Boolean(default=True)


class MedicalSpeciality(models.Model):
    _name = 'medical.speciality'
    _description = 'Medical Specialities'

    name = fields.Char('Speciality Name', required=True)
    symptom_ids = fields.Many2many('medical.symptom', string='Symptoms')
    description = fields.Text('Description')
    active = fields.Boolean(default=True)


class MedicalDoctor(models.Model):
    _name = 'medical.doctor'
    _description = 'Medical Doctors'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Doctor Name', required=True)
    partner_id = fields.Many2one('res.partner', 'Related Partner')
    user_id = fields.Many2one('res.users', 'Related User', readonly=True)
    speciality_ids = fields.Many2many('medical.speciality', string='Specialities')
    qualification = fields.Text('Qualifications', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', 
        default=lambda self: self.env.company.currency_id.id)
    new_case_consultation_fee = fields.Monetary(string='New Case Fee', currency_field='currency_id')
    followup_case_consultation_fee = fields.Monetary(string='Follow-up Fee', currency_field='currency_id')
    email = fields.Char('Email', required=True)
    phone = fields.Char('Phone', required=True)
    experience = fields.Integer('Years of Experience', required=True)
    registration_number = fields.Char('Registration Number')
    photo = fields.Binary('Photo', attachment=True)
    photo_filename = fields.Char('Photo Filename')
    currency_id = fields.Many2one('res.currency', string='Currency', 
        default=lambda self: self.env.company.currency_id)
    consultation_fee = fields.Monetary('Consultation Fee', required=True, default=0.0,
        currency_field='currency_id')
    request_ids = fields.One2many('second.opinion.request', 'doctor_id', string='Second Opinion Requests')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('pending', 'Pending Approval'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected')
    ], default='draft', tracking=True)
    active = fields.Boolean(default=True)
    address = fields.Text('Address')
    resume = fields.Binary('Resume', attachment=True)
    resume_filename = fields.Char('Resume Filename')
    license_doc = fields.Binary('Medical License', attachment=True)
    license_filename = fields.Char('License Filename')

    _sql_constraints = [
        ('email_uniq', 'unique(email)', 'Email must be unique!')
        # ('registration_number_uniq', 'unique(registration_number)', 'Registration number must be unique!')
    ]

    @api.model
    def create(self, vals):
        # Create partner if not exists
        if not vals.get('partner_id'):
            partner = self.env['res.partner'].create({
                'name': vals.get('name'),
                'email': vals.get('email'),
                'phone': vals.get('phone'),
                'company_type': 'person',
            })
            vals['partner_id'] = partner.id
        return super(MedicalDoctor, self).create(vals)

    def write(self, vals):
        res = super().write(vals)
        if vals.get('state') == 'approved' and not self.user_id:
            self._create_user_and_send_credentials()
        return res

    def _create_user_and_send_credentials(self):
        # Generate a random password
        password = ''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=10))
        
        # Create user
        user = self.env['res.users'].create({
            'name': self.name,
            'login': self.email,
            'partner_id': self.partner_id.id,
            'password': password,
            'groups_id': [(6, 0, [
                self.env.ref('ai_doctor_second_opinion.group_medical_doctor').id,
                self.env.ref('base.group_user').id
            ])]
        })
        self.write({'user_id': user.id})

        # Send credentials via email
        template = self.env.ref('ai_doctor_second_opinion.doctor_credentials_email_template')
        template.with_context(password=password).send_mail(self.id, force_send=True)

    def action_approve(self):
        self.write({'state': 'approved'})

    def action_reject(self):
        self.write({'state': 'rejected'})
        if self.user_id:
            self.user_id.active = False

    def action_reset_draft(self):
        self.write({'state': 'draft'})

    def action_submit(self):
        # if not self.email or not self.phone or not self.registration_number:
        if not self.email or not self.phone:
            raise UserError(_('Please fill in all required fields before submitting.'))
        self.write({'state': 'pending'})


class MedicalDoctorAssignment(models.Model):
    _name = 'medical.doctor.assignment'
    _description = 'Doctor Assignment Tracker'

    speciality_id = fields.Many2one('medical.speciality', string='Speciality', required=True)
    last_assigned_doctor_id = fields.Many2one('medical.doctor', string='Last Assigned Doctor')


class SecondOpinionRequest(models.Model):
    _name = 'second.opinion.request'
    _description = 'Second Opinion Requests'
    _inherit = ['portal.mixin', 'mail.thread', 'mail.activity.mixin', 'website.published.mixin']

    name = fields.Char('Request Reference', readonly=True, copy=False)
    is_followup = fields.Boolean('Is Follow-up', default=False)
    age = fields.Integer('Patient Age')
    gender = fields.Selection([('male', 'Male'), ('female', 'Female'), ('other', 'Other')])
    patient_id = fields.Many2one('res.partner', 'Patient')
    patient_id_name = fields.Char(related='patient_id.name', string='Patient Name')
    doctor_id = fields.Many2one('medical.doctor', 'Assigned Doctor')
    issue_type = fields.Many2one('medical.speciality', string='Issue Type')
    symptom_ids = fields.Many2many('medical.symptom', string='Symptoms')
    custom_symptoms = fields.Text('Other Symptoms')
    description = fields.Text('Issue Description')
    lab_report_ids = fields.Many2many('ir.attachment', 'lab_report_rel', string='Lab Reports')
    medicine_photo_ids = fields.Many2many('ir.attachment', 'medicine_photo_rel', string='Medicine Photos')
    prescription_ids = fields.Many2many('ir.attachment', 'prescription_rel', string='Prescriptions')
    xray_ids = fields.Many2many('ir.attachment', 'xray_rel', string='X-Ray Reports')
    ct_scan_ids = fields.Many2many('ir.attachment', 'ct_scan_rel', string='CT Scan Reports')
    mri_ids = fields.Many2many('ir.attachment', 'mri_rel', string='MRI Reports')
    doppler_ids = fields.Many2many('ir.attachment', 'doppler_rel', string='Vein Doppler Reports')
    sonography_ids = fields.Many2many('ir.attachment', 'sonography_rel', string='Sonography Reports')
    doctor_feedback = fields.Text('Doctor\'s Feedback')
    doctor_reference_ids = fields.Many2many('ir.attachment', 'doctor_reference_rel', string='Reference Documents')
    invoice_id = fields.Many2one('account.move', 'Related Invoice')
    amount = fields.Float('Amount', compute='_compute_amount', store=True)
    sale_order_id = fields.Many2one('sale.order', string='Sale Order', readonly=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('pending_payment', 'Pending Payment'),
        ('paid', 'Paid'),
        ('assigned', 'Doctor Assigned'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', required=True, tracking=True)
    payment_transaction_id = fields.Many2one('payment.transaction', string='Payment Transaction', readonly=True)
    payment_status = fields.Selection([
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled')
    ], string='Payment Status', default='draft', tracking=True)

    @api.onchange('is_followup', 'previous_request_id')
    def _onchange_followup(self):
        if self.is_followup and self.previous_request_id:
            # Auto-assign same doctor for follow-up
            self.doctor_id = self.previous_request_id.doctor_id
            # Copy relevant information
            self.issue_type = self.previous_request_id.issue_type
            self.symptom_ids = self.previous_request_id.symptom_ids

    @api.onchange('doctor_id')
    def _onchange_doctor_id(self):
        if self.doctor_id:
            if self.doctor_id.state == 'approved':
                if self.state not in ['assigned']:
                    self.state = 'assigned'
        else:
            self.state = 'paid'

    @api.depends('doctor_id')
    def _compute_doctor_name(self):
        for rec in self:
            if not rec.doctor_id:
                rec.state = 'paid'
            

    # @api.depends('invoice_id', 'invoice_id.amount_total')
    # def _compute_amount(self):
    #     for rec in self:
    #         if rec.invoice_id.amount_total:
    #             rec.amount = rec.invoice_id.amount_total
    @api.depends('doctor_id', 'is_followup')
    def _compute_amount(self):
        for rec in self:
            if rec.doctor_id:
                if rec.is_followup:
                    rec.amount = rec.doctor_id.followup_case_consultation_fee
                else:
                    rec.amount = rec.doctor_id.new_case_consultation_fee
            elif rec.invoice_id and rec.invoice_id.amount_total:
                rec.amount = rec.invoice_id.amount_total
            else:
                rec.amount = 0.0

    @api.model
    def create(self, vals):
        if vals.get('name', '/') == '/':
            vals['name'] = self.env['ir.sequence'].next_by_code('second.opinion.request') or '/'
        
        request = super(SecondOpinionRequest, self).create(vals)
        
        # if request.state == 'paid':
        #     request.auto_assign_doctor()
        
        return request

    def write(self, vals):
        res = super(SecondOpinionRequest, self).write(vals)
        
        # if vals.get('state') == 'paid':
        #     for record in self:
        #         record.auto_assign_doctor()
        
        return res

    # Keep the method for future reference but comment out its content
    def auto_assign_doctor(self):
        # """
        # This method is temporarily disabled as doctor selection is now manual.
        # The logic below is kept for reference:
        
        # # Find doctors based on speciality
        # domain = [
        #     ('state', '=', 'approved'),
        #     ('speciality_ids', 'in', [self.issue_type.id])
        # ]
        # available_doctors = self.env['medical.doctor'].search(domain)
        
        # if available_doctors:
        #     # Get the doctor with the least number of active requests
        #     doctor = min(available_doctors, key=lambda d: len(d.request_ids.filtered(
        #         lambda r: r.state in ['assigned']
        #     )))
        #     self.write({
        #         'doctor_id': doctor.id,
        #         'state': 'assigned'
        #     })
        # """
        pass

    # def action_confirm(self):
    #     self.write({'state': 'assigned'})
    def action_confirm(self):
        for request in self:
            if request.doctor_id:
                if request.is_followup:
                    amount = request.doctor_id.followup_case_consultation_fee
                else:
                    amount = request.doctor_id.new_case_consultation_fee
                
                # Create invoice
                invoice_vals = {
                    'partner_id': request.patient_id.id,
                    'move_type': 'out_invoice',
                    'invoice_line_ids': [(0, 0, {
                        'name': f'Medical Consultation - {"Follow-up" if request.is_followup else "New Case"}',
                        'quantity': 1,
                        'price_unit': amount,
                    })],
                    'second_opinion_request_id': request.id,
                }
                invoice = self.env['account.move'].create(invoice_vals)
                request.write({
                    'invoice_id': invoice.id,
                    'state': 'pending_payment'
                })
            else:
                raise UserError(_('Please assign a doctor before confirming the request.'))

    def action_submit_feedback(self):
        if not self.doctor_feedback:
            raise UserError(_("Please provide feedback before submitting."))
        self.write({'state': 'completed'})
        # doctor_assignment = self.env['medical.doctor.assignment'].search([('doctor_id', '=', self.doctor_id.id)], limit=1)
        # if doctor_assignment:
        #     doctor_assignment.unlink()
        #     _logger.info('Doctor Assignment Deleted: %s', doctor_assignment)
        #
        # else:
        #     _logger.info('Doctor Assignment Not Found:' )

    def action_cancel_refund(self):
        if self.invoice_id and self.invoice_id.state == 'posted':
            refund = self.invoice_id._reverse_moves()
            self.write({'state': 'refunded'})

    def payment_callback(self, tx):
        """Handle the payment transaction callback"""
        self.ensure_one()
        if tx.state == 'done':
            # Payment successful
            self.write({
                'state': 'paid',
            })
            # Post a message in the chatter
            self.message_post(
                body=_('Payment received: %s') % tx.reference,
                subtype_xmlid='mail.mt_note'
            )
            # Confirm the invoice
            if self.invoice_id and self.invoice_id.state == 'draft':
                self.invoice_id.action_post()
        elif tx.state == 'cancel':
            # Payment was cancelled
            self.message_post(
                body=_('Payment cancelled: %s') % tx.reference,
                subtype_xmlid='mail.mt_note'
            )
        elif tx.state == 'error':
            # Payment failed
            self.message_post(
                body=_('Payment failed: %s') % tx.reference,
                subtype_xmlid='mail.mt_note'
            )

    def _handle_payment_success(self, transaction):
        """Handle successful payment transaction"""
        self.ensure_one()
        _logger.info("Transaction ID in Model Method: %s", transaction.id)
        _logger.info("Transaction State in Model Method: %s", transaction.state)
        _logger.info("Current Request State: %s", self.state)

        try:
            # Check if transaction is in an acceptable state and request is pending payment
            if transaction.state in ['authorized', 'done'] and self.state == 'pending_payment':
                # Prepare update values
                update_vals = {
                    'state': 'paid',
                    'payment_status': 'paid',
                    'payment_transaction_id': transaction.id
                }

                # Update request status
                self.write(update_vals)
                _logger.info("Request status updated: %s", update_vals)

                # Handle invoice if exists
                if self.invoice_id:
                    try:
                        # Ensure invoice is in draft state before posting
                        if self.invoice_id.state == 'draft':
                            self.invoice_id.sudo().action_post()

                        # Create payment
                        payment_method = self.env['account.payment.method'].search([
                            ('code', '=', 'manual'), 
                            ('payment_type', '=', 'inbound')
                        ], limit=1)

                        # Prepare payment values
                        payment_vals = {
                            'payment_type': 'inbound',
                            'partner_id': self.invoice_id.partner_id.id,
                            'amount': self.invoice_id.amount_total,
                            'journal_id': self.env['account.journal'].search([('type', '=', 'bank')], limit=1).id,
                            'payment_method_id': payment_method.id,
                            'ref': f'Payment for {self.name}',
                        }

                        # Create payment
                        payment = self.env['account.payment'].sudo().create(payment_vals)
                        
                        # Manually link invoice to payment
                        payment.sudo().write({
                            'reconciled_invoice_ids': [(4, self.invoice_id.id)]
                        })

                        # Post the payment
                        payment.sudo().action_post()

                        # Manually reconcile payment with invoice
                        payment_move = payment.move_id
                        invoice_move = self.invoice_id

                        # Find receivable/payable lines
                        payment_lines = payment_move.line_ids.filtered(
                            lambda l: l.account_id.account_type in ('asset_receivable', 'liability_payable')
                        )
                        invoice_lines = invoice_move.line_ids.filtered(
                            lambda l: l.account_id.account_type in ('asset_receivable', 'liability_payable')
                        )

                        # Detailed logging for debugging
                        _logger.info("Payment Lines: %s", payment_lines)
                        _logger.info("Invoice Lines: %s", invoice_lines)

                        # Perform manual reconciliation
                        for pay_line in payment_lines:
                            for inv_line in invoice_lines:
                                if pay_line.account_id == inv_line.account_id:
                                    (pay_line + inv_line).reconcile()
                                    _logger.info("Reconciled lines: %s and %s", pay_line.id, inv_line.id)

                        # Verify invoice payment status
                        self.invoice_id.sudo()._compute_payment_state()
                        
                        _logger.info("Invoice Payment State: %s", self.invoice_id.payment_state)
                        _logger.info("Invoice Payment Status: %s", self.invoice_id.invoice_payment_state)

                        _logger.info("Invoice and payment processed successfully")
                    except Exception as invoice_error:
                        _logger.error("Error processing invoice: %s", str(invoice_error))

                # Ensure transaction is marked as done
                transaction.sudo().write({
                    'state': 'done',
                })

                return True

            _logger.warning("Payment conditions not met. Transaction state: %s, Request state: %s", 
                            transaction.state, self.state)
            return False

        except Exception as e:
            _logger.error("Unexpected error in payment success handling: %s", str(e))
            return False

class AccountMove(models.Model):
    _inherit = 'account.move'
    second_opinion_request_id = fields.Many2one('second.opinion.request', string='Second Opinion Request')

    @api.model
    def create(self, vals):
        res = super().create(vals)
        if res.second_opinion_request_id:
            res.second_opinion_request_id.write({'invoice_id': res.id})
        return res


class Saleorder(models.Model):
    _inherit = 'sale.order'
    second_opinion_request_id = fields.Many2one('second.opinion.request', string='Second Opinion Request')

    # Inherit to pass second_opinion_request_id to invoice
    def _prepare_invoice(self):
        invoice_vals = super()._prepare_invoice()
        if self.second_opinion_request_id:
            invoice_vals['second_opinion_request_id'] = self.second_opinion_request_id.id
        return invoice_vals


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    is_consultation_fee = fields.Boolean('Is Consultation Fee', default=False,
        help='Check this if this product is used for doctor consultation fees')

class PaymentTransaction(models.Model):
    _inherit = 'payment.transaction'

    second_opinion_request_id = fields.Many2one('second.opinion.request', string='Second Opinion Request', readonly=True)

    def _reconcile_after_done(self):
        """Handle post-processing after transaction is done"""
        res = super()._reconcile_after_done()
        
        for tx in self:
            if tx.state == 'done' and tx.second_opinion_request_id:
                # Handle payment success for second opinion request
                _logger.info("Second Opinion Request ID _reconcile_after_done: %s", tx.second_opinion_request_id.id)
                tx.second_opinion_request_id._handle_payment_success(tx)
        
        return res

    def _process_payment_notification(self, notification_data):
        """Handle payment notification from provider"""
        res = super()._process_payment_notification(notification_data)
        
        for tx in self:
            if tx.state == 'done' and tx.second_opinion_request_id:
                # Handle payment success for second opinion request
                _logger.info("Second Opinion Request ID _process_payment_notification: %s", tx.second_opinion_request_id.id)
                tx.second_opinion_request_id._handle_payment_success(tx)
        
        return res
