from . import models

import logging
_logger = logging.getLogger(__name__)

def log_payment_transaction_operations():
    """
    Log valid payment transaction operations when the module is loaded
    """
    try:
        from odoo import api, SUPERUSER_ID
        
        def _log_operations(env):
            transaction_model = env['payment.transaction']
            valid_operations = transaction_model._fields['operation'].selection
            _logger.info("Payment Transaction Valid Operations:")
            for op in valid_operations:
                _logger.info(f"  - {op[0]}: {op[1]}")
        
        # Use a new cursor to avoid interfering with other operations
        with api.Environment.manage():
            with api.Environment.reset():
                registry = api.Registry('secondopinion.arihantai.com')
                with registry.cursor() as cr:
                    env = api.Environment(cr, SUPERUSER_ID, {})
                    _log_operations(env)
    except Exception as e:
        _logger.error(f"Error logging payment transaction operations: {e}")

# Call the logging function when the module is loaded
log_payment_transaction_operations()
