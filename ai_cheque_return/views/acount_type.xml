<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_account_type" model="ir.actions.act_window">
            <field name="name">Account Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">account.type</field>
            <field name="view_mode">tree,form,search</field>
        </record>

         <record id="view_account_type_tree" model="ir.ui.view">
            <field name="name">account_type.tree</field>
            <field name="model">account.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_account_type_form" model="ir.ui.view">
            <field name="name">account_type.form</field>
            <field name="model">account.type</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <field name="x_state" widget="statusbar" nolabel="1" />
                    </header>
                    <field name="name"/>
                    <field name="x_html" placeholder="HTML file"/>
                </form>
            </field>
        </record>

           <record id="view_account_type_search" model="ir.ui.view">
            <field name="name">account_type.search</field>
            <field name="model">account.type</field>
            <field name="arch" type="xml">
                <search>
                    <!-- <field name="name"/> -->
                </search>
            </field>
        </record>



    
        <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="15"/>
        <menuitem id="menu_account_type"  parent="menu_selection" name="Account Type" action="ai_cheque_return.action_account_type" sequence="10"/>


    </data>
</odoo>