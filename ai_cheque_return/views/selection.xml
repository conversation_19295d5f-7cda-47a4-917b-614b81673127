<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

         <record id="action_transact_type" model="ir.actions.act_window">
            <field name="name">Transact Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">transact.type</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_transact_type_tree" model="ir.ui.view">
            <field name="name">transact_type.tree</field>
            <field name="model">transact.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_transact_type_form" model="ir.ui.view">
            <field name="name">transact_type.form</field>
            <field name="model">transact.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         
        
        

        
         

          <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="15"/>
          <menuitem id="menu_transaction_type"  parent="menu_selection" name="Transact Type" action="ai_cheque_return.action_transact_type" sequence="1"/>
          <!-- <menuitem id="menu_purchase_items"  parent="menu_selection" name="Purchase Items" action="cheque_return.action_purchase_items" sequence="2"/> -->
          <!-- <menuitem id="menu_cheque_filler"  parent="menu_selection" name="Cheque Filler" action="cheque_return.action_cheque_filler" sequence="3"/> -->
          <!-- <menuitem id="menu_account_type"  parent="menu_selection" name="Account Type" action="ai_cheque_return.action_account_type" sequence="8"/> -->
    
   
    </data>
    
</odoo>
