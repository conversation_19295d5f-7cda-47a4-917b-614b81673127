<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="action_cheque_details" model="ir.actions.act_window">
            <field name="name">Cheque Details</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">cheque.details</field>
            <field name="view_mode">tree,form</field>
        </record>

        <record id="view_cheque_details_tree" model="ir.ui.view">
            <field name="name">cheque_details.tree</field>
            <field name="model">cheque.details</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name='x_notifier_type' string="Notifier Type" />
                    <field name='x_notifier_first_name' string="Notifier First Name"/>
                    <field name='x_notifier_last_name' string="Notifier Last Name"/>
                    <field name='x_recipient_type' string="Recipient Type" />
                    <field name='x_recipient_first_name' string="Recipient First Name"/>
                    <field name='x_recipient_last_name' string="Recipient Last Name"/>
                    <field name='x_partnership_json'/>
                    <field name='x_recipient_json'/>
                    <field name='x_purchase_json'/>
                    <field name='x_recipient_bank_json'/>
                    <field name='x_notifier_bank_json'/>
                    <field name='x_token'/>
                </tree>
            </field>
        </record>

        <record id="cheque_details_form" model="ir.ui.view">
            <field name="name">cheque_details.form</field>
            <field name="model">cheque.details</field>
            <field name="arch" type="xml">
                <form string="Cheque Details Form">
                    <sheet>
                        <notebook>
                            <page string="Notifier Details">
                                <group>
                                    <group string="Personal Information">
                                        <group>
                                            <field name='x_notifier_type' string="Notifier Type" />
                                            <field name='x_notifier_first_name' string="First Name"/>
                                            <field name='x_notifier_middle_name' string="Middle Name"/>
                                            <field name='x_notifier_last_name' string="Last Name"/>
                                        </group>
                                        <group>
                                            <field name='x_notifier_email' string="Your Email" />
                                            <field name='x_notifier_mobile' string="Your Mobile No" />
                                            <field name='x_notifier_upi_id' string="Your UPI ID" /> 
                                            <field name='x_notifier_telephone_no' string="Your Telephone No" />
                                        </group>
                                    </group>
                                    <group>
                                        <group>
                                            <field name='x_notifier_relation_types' string="Relationship Type" invisible="x_notifier_type != 25" />
                                        </group>
                                        <group>
                                            <field name='x_notifier_relation' string="Relation to Notice Recipient" invisible="x_notifier_relation_types != 1"/>
                                        </group>
                                    </group>
                                </group>
                            </page>
                            <page string="Business Details" invisible="x_notifier_relation_types == 1">
                                <group>
                                    <group>
                                        <field name='x_notifier_firm_name' string="Business Name" />
                                        <field name='x_notifier_business_type' string="Your Business Type" />
                                        <field name='x_notifier_merchant_type' invisible="x_notifier_business_type != 1"/>
                                        <field name='x_notifier_which_business' string="What is your Business?" invisible="x_notifier_business_type != 1"/>
                                        <field name='x_notifier_license_registration_no' string="License/Registration Number" invisible="x_notifier_business_type != 5"/>
                                        <field name='x_notifier_registrar_office_name' string="Licence issuing Registrar Office" invisible="x_notifier_business_type != 5"/>
                                        <field name='x_notifier_which_manufacturer' string="What do you Manufacture?" invisible="x_notifier_business_type != 2"/>
                                        <field name='x_notifier_which_service_provider' string="What Service do you Provide?" invisible="x_notifier_business_type != 4"/>
                                        <field name='x_notifier_which_supplier' string="Which supplier you are?" invisible="x_notifier_business_type != 3"/>
                                    </group>
                                    <group>
                                        <field name='x_notifier_partner_regi_no' string="Partnership Firm Registration Number" invisible="x_notifier_type != 26"/>
                                        <field name='x_notifier_partnership_regi_registrar_office' string="Firm Registration Registrar Office" invisible="x_notifier_type != 26"/>
                                        <field name='x_notifier_dpin_no' string="DPIN No" invisible="x_notifier_type != 31"/>
                                        <field name='x_notifier_llpin_no' string="LLPIN No" invisible="x_notifier_type != 31"/>
                                        <field name='x_notifier_bank_name' string="Bank Name" invisible="x_notifier_relation_types != 1"/>
                                        <field name='x_notifier_bank_branch_name' string="Bank Branch Name" invisible="x_notifier_relation_types != 1"/>
                                        <field name='x_notifier_ifsc' string="IFSC Code" invisible="x_notifier_relation_types != 1"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Address Details">
                                <group>
                                    <group string="Residential Address">
                                        <field name='x_notifier_residential_building_no' string="Building No./Name" />
                                        <field name='x_notifier_residential_street' string="Street" />
                                        <field name='x_notifier_residential_landmark' string="Landmark" />
                                        <field name='x_notifier_residential_village' string="Village" />
                                        <field name='x_notifier_residential_taluka' string="Taluka" />
                                        <field name='x_notifier_residential_district' string="District" />
                                        <field name='x_notifier_residential_pincode' string="Pincode" /> 
                                        <field name='x_notifier_residential_state' string="State" />
                                    </group>
                                    <group string="Commercial Address" invisible="x_notifier_relation_types == 1">
                                        <field name='x_notifier_commercial_building_no' string="Building No./Name" />
                                        <field name='x_notifier_commercial_street' string="Street" />
                                        <field name='x_notifier_commercial_landmark' string="Landmark" />
                                        <field name='x_notifier_commercial_village' string="Village" />
                                        <field name='x_notifier_commercial_taluka' string="Taluka" />
                                        <field name='x_notifier_commercial_district' string="District" />
                                        <field name='x_notifier_commercial_state' string="State" />
                                        <field name='x_notifier_commercial_pincode' string="Pincode" />
                                    </group>
                                </group>
                            </page>
                            <page string="Partners Details" invisible="x_notifier_type != 26 and x_notifier_type != 31">
                                <group>
                                    <field name='x_notifier_partnership' string="Partners Details" />
                                </group>
                            </page>
                        </notebook>
                        <notebook>
                            <page string="Notice Recipient Details">
                                <group string="Personal Information">
                                    <group>
                                        <field name='x_recipient_type' string="Recipient Type" />
                                        <field name='x_recipient_first_name' string="First Name"/>
                                        <field name='x_recipient_middle_name' string="Middle Name"/>
                                        <field name='x_recipient_last_name' string="Last Name"/>
                                    </group>
                                </group>
                                <group>
                                    <field name='x_recipient_mobile_no' string="Your Mobile No" />
                                    <field name='x_recipient_telephone_no' string="Your Telephone No" />
                                    <field name='x_recipient_email' string="Your Email" />
                                    <field name='x_recipient_upi_id' string="Your UPI ID" />
                                </group>
                            </page>
                            <page string="Transaction Details">
                                <group>
                                    <field name='x_transact_type'/>
                                    <field name='x_recipient_needs'/>
                                    <field name='x_transaction_mode'/>
                                    <field name='x_borrowed_money' widget="many2one" track_visibility='onchange' invisible="x_transact_type != 25 and x_transact_type != 26"/>
                                    <field name="x_how_cheque_sent" invisible="x_transact_type != 25"/>
                                    <field name='x_interest_types' invisible="x_transact_type != 25"/>
                                    <field name='x_cheque_details'/>
                                    <field name='x_purchase'/>
                                    <field name='x_cheque_issued'/>
                                    <field name='x_cheque_filler'/>
                                    <field name='x_person_name' invisible="x_cheque_filler != 2"/>
                                    <field name='x_partial_payment'/> 
                                    <field name='x_insufficient_payment'/>
            <field name='x_return_cheque_date'/>
            <field name='x_return_date_notification'/>
            <field name='x_notice_intimation'/>
          </group>
        </page>

        
        
        
      </notebook>
    </sheet>
  </form>



  
  </field>
</record>
      
      <!-- <menuitem id="menu_cheque_details" name="Cheque Details" sequence="10"/> -->
      <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="25"/>
      <menuitem id="menu_realted_models" parent="menu_cheque_return_details" name="Related Models" sequence="15"/>
      <menuitem id="menu_money_at_intrest" parent="menu_cheque_return_details" name="Money At Intrest" sequence="11"/>
      <menuitem id="menu_configuration" parent="menu_cheque_return_details" name="Configuration" sequence="7"/>
      <!-- <menuitem id="menu_cheque_detail" parent="menu_ai_cheque_return_details" name="Cheque Details" sequence="1"/> -->
      <menuitem id="menu_cheque_detail"  parent="menu_cheque_return_details" name="Cheque Details" action="ai_cheque_return.action_cheque_details" sequence="1"/>
     
    
    </data>
    
    </odoo>
