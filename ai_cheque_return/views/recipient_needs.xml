<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_recipient_needs" model="ir.actions.act_window">
            <field name="name">Recipient Needs</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">recipient.needs</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_recipient_needs_tree" model="ir.ui.view">
            <field name="name">recipient_needs.tree</field>
            <field name="model">recipient.needs</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_recipient_needs_form" model="ir.ui.view">
            <field name="name">recipient_needs.form</field>
            <field name="model">recipient.needs</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>



    
    
        <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="15"/>
        <menuitem id="menu_recipient_needs"  parent="menu_selection" name="Recipient Needs" action="ai_cheque_return.action_recipient_needs" sequence="60"/>


    </data>
</odoo>