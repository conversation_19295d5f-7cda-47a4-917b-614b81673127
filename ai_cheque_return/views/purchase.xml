<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="action_purchase" model="ir.actions.act_window">
            <field name="name">Purchase</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase</field>
            <field name="view_mode">tree,form,search</field>
        </record>

        <record id="view_purchase_tree" model="ir.ui.view">
            <field name="name">purchase.tree</field>
            <field name="model">purchase</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="x_purchase_items"/>
                    <field name="x_other_item" invisible="x_purchase_items != 7"/>
                    <field name="x_invoice_no"/>
                    <field name="x_purchase_date"/>
                </tree>
            </field>
        </record>

        <record id="view_purchase_search" model="ir.ui.view">
            <field name="name">purchase.search</field>
            <field name="model">purchase</field>
            <field name="arch" type="xml">
                <search>
                    <field name="x_purchase_items"/>
                    <field name="x_invoice_no"/>
                    <field name="x_purchase_date"/>
                    <filter name="filter_1" string="Purchase Item" domain="[('x_purchase_items', '==', 4)]"/>
                    <group expand="1" string="Group By">
                        <filter name="group_by_type" string="Invoice No" domain="[]" context="{'group_by':'x_invoice_no'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_purchase_form" model="ir.ui.view">
            <field name="name">purchase.form</field>
            <field name="model">purchase</field>
            <field name="arch" type="xml">
                <form string="Purchase">
                    <header>
                        <field name="x_states" widget="statusbar" nolabel="1"/>
                    </header>
                    <sheet>
                        <group>
                            <field name="x_purchase_items"/>
                            <field name="x_other_item" invisible="x_purchase_items != 7"/>
                            <field name="x_invoice_no"/>
                            <field name="x_purchase_date"/>
                            <field name="x_other_item"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <menuitem id="menu_related_models" parent="menu_cheque_return_details" name="Related Models" sequence="15"/>
        <menuitem id="menu_purchase" parent="menu_related_models" name="Purchase" action="action_purchase" sequence="15"/>
   
    </data>
</odoo>
