<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_cheque_issued" model="ir.actions.act_window">
            <field name="name">Cheque Issued</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">cheque.issued</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_cheque_issued_tree" model="ir.ui.view">
            <field name="name">cheque_issued.tree</field>
            <field name="model">cheque.issued</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_cheque_issued_form" model="ir.ui.view">
            <field name="name">cheque_issued.form</field>
            <field name="model">cheque.issued</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>



    
        <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="15"/>
        <menuitem id="menu_cheque_issued"  parent="menu_selection" name="Cheque Issued" action="ai_cheque_return.action_cheque_issued" sequence="50"/>


    </data>
</odoo>