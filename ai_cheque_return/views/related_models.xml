<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

         <record id="action_cheque" model="ir.actions.act_window">
            <field name="name">Cheque Detail </field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">cheque</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_chequetree" model="ir.ui.view">
            <field name="name">cheque.tree</field>
            <field name="model">cheque</field>
            <field name="arch" type="xml">
                 <tree>
                        <field name='x_bank_name'/>
                        <field name='x_account_type'/>
                        <field name='x_cheque_no'/>
                        <field name='x_date'/>
                        <field name='x_amount'/>
                </tree>
            </field>
        </record>

         <record id="view_cheque_form" model="ir.ui.view">
            <field name="name">cheque.form</field>
            <field name="model">cheque</field>
            <field name="arch" type="xml">
                <form string="Cheque Details">
                    <sheet>
                    <group>
                        <!--<field name='x_name'/>-->
                        <field name='x_bank_name'/>
                        <field name='x_bank_branch'/>
                        <field name='x_account_type'/>
                        <field name='x_cheque_no'/>
                        <field name='x_ifsc_code'/>
                        <field name='x_date'/>
                        <field name='x_amount'/>
                        <field name='x_amount_words'/>
                    </group>
                    </sheet>
                </form>
            </field>
        </record>

         
        
        

        
         

          <menuitem id="menu_related_models" parent="menu_cheque_return_details" name="Related Models" sequence="15"/>
          <menuitem id="menu_cheque"  parent="menu_related_models" name="Cheque Detail" action="ai_cheque_return.action_cheque" sequence="20"/>
          <!-- <menuitem id="menu_purchase_items"  parent="menu_selection" name="Purchase Items" action="cheque_return.action_purchase_items" sequence="2"/> -->
          <!-- <menuitem id="menu_cheque_filler"  parent="menu_selection" name="Cheque Filler" action="cheque_return.action_cheque_filler" sequence="3"/> -->
          <!-- <menuitem id="menu_account_type"  parent="menu_selection" name="Account Type" action="cheque_return.action_account_type" sequence="8"/> -->
    
   
    </data>
    
</odoo>
