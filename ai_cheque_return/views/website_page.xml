<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
  <template id="ai_cheque_return.website_cheque_details" name="Cheque Details">
      <t t-call="website.layout">
        <t t-set="user" t-value="env['res.users'].sudo().search([('id','=',uid)])"/>
        <style>
          .container {
            max-width: 1200px;
            margin: 0 auto;
          }

          /* Form Styles */
          .oe_form {
            margin-top: 20px;
          }

          /* Input Styles */
          .oe_form input[type="text"],
          .oe_form select {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
          }

          .oe_form input[type="checkbox"] {
            margin-right: 5px;
          }

          /* Button Styles */
          .oe_form button {
            background-color: #007bff;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          }
          .oe_form textarea {
            width: 100%; /* Set the width to 100% */
            height: auto; /* Allow the height to adjust automatically */
            min-height: 4em; /* Set the minimum height to 4 rows */
            max-height: 10em; /* Set the maximum height to 10 rows */
            resize: vertical; /* Allow vertical resizing */
            overflow-y: auto; /* Add vertical scrollbar if content exceeds the height */
          }
          /* Responsive Styles */
          @media (max-width: 768px) {
            .container {
              width: 100%;
            }
          }
        </style>
        <!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"/> -->
        
        <xpath expr="//div[@class='container']" position="inside">
         <section class="s_tabs pt48 pb48 o_colored_level" data-vcss="001" data-snippet="s_tabs" data-name="Tabs" style="background-image: none;">
            <div class="container">
                <div class="s_tabs_main card bg-o-color-2">
                    <div class="s_tabs_content tab-content card-body s_tabs_slide_right">
                        <form class="oe_form o_mark_required" name="cheque_details_notifier" method="post" action="/submit_cheque_details" enctype="multipart/form-data" data-mark="*" data-pre-fill="true">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <div class="row">
                                <div class="col-md-6">
                                    <h3>Personal Information</h3>
                                    <div class="form-group">
                                        <input type="text" name="x_notifier_first_name" id="x_notifier_first_name" class="form-control" placeholder="First Name" />
                                    </div>
                                    <div class="form-group">
                                        <input type="text" name="x_notifier_middle_name" id="x_notifier_middle_name" class="form-control" placeholder="Middle Name"/>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" name="x_notifier_last_name" id="x_notifier_last_name" class="form-control" placeholder="Last Name"/>
                                    </div>
                                    <!-- <div class="form-group">
                                        <input type="text" name="x_surname" id="x_surname" class="form-control" placeholder="Last Name" required="1"/>
                                    </div>
                                    <div class="form-group">
                                        <input type="email" name="x_email" id="x_email" class="form-control" placeholder="Email"/>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" name="x_mobile_number" id="x_mobile_no" class="form-control" placeholder="Mobile Number"/>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" name="x_whatsapp_number" id="x_whatsapp_no" class="form-control" placeholder="WhatsApp Number"/>
                                    </div> -->
                                </div>
                                <!-- <div class="col-md-6"> -->
                                    <h3>Address Details</h3>
                                    <div class="form-group">
                                        <input type="text" name="x_flat_no" id="x_streetline_1" class="form-control" placeholder="Street line 1"/>
                                    </div>
                                    <div class="form-group">
                                        <input type="text" name="x_street" id="x_streetline_2" class="form-control" placeholder="Street line 2"/>
                                    </div>
                                    <div class="form-group">
                                        <select class="form-control" id="stateSelect" autocomplete="off" name="x_state" required="1">
                                            <option value="">Select State</option>
                                            <!-- Add options for states -->
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <select class="form-control" name="x_district" required="1">
                                            <option value="0">Select A District</option>
                                            <option value="1">Create New District</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <select name="x_taluka" id="talukaSelect" class="form-control" required="1">
                                            <option value="0">Select Taluka/City</option>
                                            <option value="1">Create New Taluka/City</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <select name="x_village" id="villageSelect" class="form-control" required="1">
                                            <option value="0">Select Village/Area</option>
                                            <option value="1">Create New Village/Area</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <input type="number" name="x_pincode" id="x_pincode" class="form-control" placeholder="Pincode" required="1"/>
                                    </div>
                                <!-- </div> -->
                            </div>
                        
                            <!-- <div class="row"> -->
                                <div class="col-md-6">
                                    <h3>Select Office:</h3>
                                    <select id="officeSelect">
                                        <option value="">Select an office</option>
                                    </select>
                                </div>
                            <!-- </div>      -->
                        
                            <!-- <div class="row"> -->
                                <div class="col-md-6">
                                    <h3>Select Department:</h3>
                                    <select id="departmentSelect">
                                        <option value="">Select a department</option>
                                    </select>
                                </div>
                            <!-- </div>     -->
                        
                            <!-- <div class="row"> -->
                                <div class="col-md-12">
                                    <textarea name="x_rti_details" id="x_rti_details" class="form-control" placeholder="RTI Details" required="1" rows="4"/>
                                </div>
                            <!-- </div>         -->
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
            </section>

   
        </xpath>
      
      </t>
    </template>
  </data>
</odoo>