<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

         <record id="action_partnership" model="ir.actions.act_window">
            <field name="name">Partnership</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">partnership</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_partnership_tree" model="ir.ui.view">
            <field name="name">partnership.tree</field>
            <field name="model">partnership</field>
            <field name="arch" type="xml">
               <tree>
                    <field name='x_first_name'/>
                    <field name='x_last_name'/>
                    <field name='x_middle_name'/>
                    </tree>
            </field>
        </record>

         <record id="view_partnership_form" model="ir.ui.view">
            <field name="name">partnership.form</field>
            <field name="model">partnership</field>
            <field name="arch" type="xml">
                <form string="Partnership">
                    <sheet>
                    <group>
                        <field name='x_first_name'/>
                        <field name='x_last_name'/>
                        <field name='x_middle_name'/>
                        <field name='x_dpin_no'/>
                    </group>
                    </sheet>
                </form>
            </field>
        </record>

         
        
        

        
         

          <menuitem id="menu_configuration"  parent="menu_cheque_return_details"  name="Configuration" sequence="8"/>
          <menuitem id="menu_partnership"  parent="menu_configuration" name="Partnership" action="ai_cheque_return.action_partnership" sequence="15"/>
          <!-- <menuitem id="menu_purchase_items"  parent="menu_selection" name="Purchase Items" action="cheque_return.action_purchase_items" sequence="2"/> -->
          <!-- <menuitem id="menu_cheque_filler"  parent="menu_selection" name="Cheque Filler" action="cheque_return.action_cheque_filler" sequence="3"/> -->
          <!-- <menuitem id="menu_account_type"  parent="menu_selection" name="Account Type" action="cheque_return.action_account_type" sequence="8"/> -->
    
   
    </data>
    
</odoo>
