<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_recipient_business_type" model="ir.actions.act_window">
            <field name="name">recipient business Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">recipient.business.type</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_recipient_business_type_tree" model="ir.ui.view">
            <field name="name">recipient_business_type.tree</field>
            <field name="model">recipient.business.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_recipient_business_type_form" model="ir.ui.view">
            <field name="name">recipient_business_type.form</field>
            <field name="model">recipient.business.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>



    
        <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="15"/>
        <menuitem id="menu_recipient_business_type"  parent="menu_selection" name="Recipient Business Type" action="ai_cheque_return.action_recipient_business_type" sequence="35"/>


    </data>
</odoo>