<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_notifier_merchant_type" model="ir.actions.act_window">
            <field name="name">Notifier merchant Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">notifier.merchant.type</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_notifier_merchant_type_tree" model="ir.ui.view">
            <field name="name">notifier_merchant_type.tree</field>
            <field name="model">notifier.merchant.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_notifier_merchant_type_form" model="ir.ui.view">
            <field name="name">notifier_merchant_type.form</field>
            <field name="model">notifier.merchant.type</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>



    
        <menuitem id="menu_selection"  parent="menu_cheque_return_details"  name="Selection" sequence="15"/>
        <menuitem id="menu_notifier_merchant_type"  parent="menu_selection" name="Notifier Merchant Type" action="ai_cheque_return.action_notifier_merchant_type" sequence="25"/>


    </data>
</odoo>