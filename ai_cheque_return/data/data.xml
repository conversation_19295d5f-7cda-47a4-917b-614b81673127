<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
<record id="ai_cheque_return.model_cheque_details" model="ir.model">
                    <field name="website_form_key">create_Cheque_return</field>
                    <field name="website_form_access">True</field>
                    <field name="website_form_label">Create a Cheque Return Registration </field>
        </record>
        <!-- <function model="ir.model.fields" name="formbuilder_whitelist">
            <value>x_blog_complains</value>
            <value eval="[
              'name', 'x_phone_no', 'x_email', 'x_blog_url', 'x_reason'
              ]"/>
        </function> -->
          </data>
</odoo>
