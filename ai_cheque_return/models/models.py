from odoo import models, fields, api

class ChequeDetails(models.Model):
        _name = "cheque.details"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Cheque details module" 
        name = fields.Char(string="Name")
        x_notifier_which_manufacturer = fields.Char(string="Notifier Which Manufacturer")
        x_recipient_ifsc = fields.Char(string="Recipient ifsc code")
        x_notifier_ifsc = fields.Char(string="Notifier IFSC Code")
        x_notifier_first_name = fields.Char(string="Notifier First Name")
        x_notifier_which_service_provider = fields.Char(string="Notifier Which Service Provider")
        x_notifier_which_supplier = fields.Char(string="Notifier Which Supplier")
        x_recipient_which_manufacturer = fields.Char(string="Recipient which manufacturer")
        x_recipient_which_service_provider = fields.Char(string="Recipient Which Service provider")
        x_notifier_mobile = fields.Char(string="Notifier Mobile")
        x_notifier_commercial_pincode = fields.Char(string="Notifier Commercial Pincode")
        x_notifier_email = fields.Char(string="Notifier Email")
        x_notifier_last_name = fields.Char(string="Notifier Last Name")
        x_notifier_middle_name = fields.Char(string="Notifier Middle Name")
        x_notifier_commercial_street = fields.Char(string="Notifier Commercial Street")
        x_notifier_commercial_building_no = fields.Char(string="Notifier Commercial Building No - Name")
        x_notifier_license_registration_no = fields.Char(string="Notifier License Number / Registration Number")
        x_notifier_which_business = fields.Char(string="Notifier What is your Business")
        x_notifier_registrar_office_name = fields.Char(string="Notifier Registrar Office Name (Money Dhirdhar)")
        x_recipient_mobile = fields.Char(string="Recipient Mobile")
        x_notifier_residential_street = fields.Char(string="Notifier Residential Street")
        x_notifier_residential_pincode = fields.Char(string="Notifier Residential Pincode")
        x_notifier_residential_landmark = fields.Char(string="Notifier Residential Landmark")
        x_notifier_residential_building_no = fields.Char(string="Notifier Residential Building No - Name")
        x_recipient_middle_name = fields.Char(string="Recipient Middle Name")
        x_recipient_last_name = fields.Char(string="Recipient Last Name")
        x_recipient_residential_landmark = fields.Char(string="Recipient Residential Landmark")
        x_notifier_upi_id = fields.Char(string="Notifier UPI Id")
        x_recipient_upi_id = fields.Char(string="Recipient UPI Id")
        x_notifier_mobile_no = fields.Char(string="notifier Mobile No")
        x_recipient_mobile_no = fields.Char(string="Recipient Mobile No")
        x_recipient_commercial_building_no = fields.Char(string="Recipient Commercial Building No - Name")
        x_recipient_commercial_street = fields.Char(string="Recipient Commercial Street")
        x_notifier_commercial_landmark = fields.Char(string="Notifier Commercial LandMark")
        x_recipient_commercial_landmark = fields.Char(string="Recipient Commercial LandMark")
        x_recipient_email = fields.Char(string="Recipient Email")
        x_recipient_commercial_pincode = fields.Char(string="Recipient Commercial Pincode")
        # x_notifier_residential_district = fields.Char(string="Notifier Residential District")
        x_recipient_first_name = fields.Char(string="Recipient First Name")
        # x_notifier_residential_village = fields.Char(string="Notifier Residential Village")
        x_recipient_license_registration_no = fields.Char(string="Recipient License Number / Registration Number")
        # x_recipient_residential_taluka = fields.Char(string="Recipient Residential Taluka")
        # x_recipient_residential_village = fields.Char(string="Recipient Residential Village")
        x_recipient_which_business = fields.Char(string="Recipient What is your Business?")
        # x_recipient_commercial_village = fields.Char(string="Recipient Commercial Village")
        x_cheque_return_reason = fields.Char(string="Cheque Return Reason")
        x_recipient_residential_pincode = fields.Char(string="Recipient Residential Pincode")
        # x_notifier_residential_taluka = fields.Char(string="Notifier Residential Taluka")
        # x_notifier_commercial_district = fields.Char(string="Notifier Commercial District")
        x_recipient_registrar_office_name = fields.Char(string="Recipient Registrar Office Name (Money Dhirdhar)")
        x_recipient_residential_building_no = fields.Char(string="Recipient Residential Building No - Name")
        x_recipient_residential_street = fields.Char(string="Recipient Residential Street")
        # x_recipient_commercial_taluka = fields.Char(string="Recipient Commercial Taluka")
        # x_recipient_commercial_district = fields.Char(string="Recipient Commercial District")
        x_token = fields.Char(string="Token")
        x_notifier_dpin_no = fields.Char(string="Notifier DPIN Number")
        x_notifier_partnership_regi_registrar_office = fields.Char(string="Notifier Registrar Office Name")
        x_recipient_partnership_regi_registrar_office = fields.Char(string="Recipient's Company Registrar Office Name")
        x_recipient_dpin_no = fields.Char(string="Recipient DPIN Number")
        x_recipient_firm_name = fields.Char(string="Recipient Business Name")
        x_recipient_llpin_no = fields.Char(string="Recipient LLPIN No")
        x_notifier_firm_name = fields.Char(string="Notifier Business Name")
        x_notifier_partner_regi_no = fields.Char(string="Notifier Company Registration No")
        x_notifier_llpin_no = fields.Char(string="Notifier LLPIN No")
        x_notifier_telephone_no = fields.Char(string="Notifier Telephone No")
        x_notifier_bank_branch_name = fields.Char(string="Notifier Bank Name")
        x_recipient_telephone_no = fields.Char(string="Recipient Telephone No")
        x_person_name = fields.Char(string="Particular Person's Name")
        x_notifier_bank_name = fields.Char(string="Notifier Bank Name")
        x_partnership_json = fields.Text(string="Partnership JSON")
        x_recipient_json = fields.Text(string="Recipient JSON")
        x_purchase_json = fields.Text(string="Purchase JSON")
        x_notifier_bank_json = fields.Text(string="Notifier Bank JSON")
        x_recipient_bank_json = fields.Text(string="Recipient Bank JSON")
        x_purchase = fields.One2many('purchase', 'x_check_return_notice', string='Purchase')
        x_cheque_details = fields.One2many('cheque', 'x_details', string='Cheque Details')
        x_notifier_partnership = fields.One2many('partnership', 'x_check_return_notice', string='Notifier Partnership')
        # x_notifier_payment_details = fields.One2many('x_lenders_payment_details', 'x_check_return_notice', string='Notifier Payment Details')
        x_recipient_partnership = fields.One2many('partnership', 'x_drawee_partnership', string='Recipients Company Partners')
        x_notifier_merchant_type = fields.Many2one('notifier.merchant.type', string='Notifier Merchant Type')
        x_recipient_merchant_type = fields.Many2one('recipient.merchant.type', string='Recipient Merchant Type')
        x_notifier_business_type = fields.Many2one('notifier.business.type', string='Notifier Business Type')
        x_notifier_commercial_village = fields.Many2one('location.village', string='Notifier Commercial Village')
        x_notifier_commercial_taluka = fields.Many2one('location.taluka', string='Notifier Commercial Taluka')
        x_notifier_commercial_district = fields.Many2one('location.district', string='Notifier Commercial District')
        x_notifier_residential_village = fields.Many2one('location.village', string='Notifier Residential Village')
        x_notifier_residential_taluka= fields.Many2one('location.taluka', string='Notifier Residential Taluka')
        x_notifier_residential_district = fields.Many2one('location.district', string='Notifier Residential District')
        x_recipient_commercial_village = fields.Many2one('location.village', string='Recipient Commercial Village')
        x_recipient_commercial_taluka = fields.Many2one('location.taluka', string='Recipient Commercial Taluka')
        x_recipient_commercial_district = fields.Many2one('location.district', string='Recipient Commercial District')
        x_recipient_residential_village = fields.Many2one('location.village', string='Recipient Residential Village')
        x_recipient_residential_taluka = fields.Many2one('location.taluka', string='Recipient Residential Taluka')
        x_recipient_residential_district = fields.Many2one('location.district', string='Recipient Residential District')
        # fields.Char(string="Notifier Commercial Village")

        x_recipient_business_type = fields.Many2one('recipient.business.type', string='Recipient Business Type')
        x_recipient_relation_type = fields.Many2one('recipient.relation.type', string='Recipient Relation Type')
        x_notifier_relation_types = fields.Many2one('notifier.relation.type', string='Notifier Relation Type')
        x_notice_intimation = fields.Many2one('notice.intimation', string='Notice Intimation')
        x_notifier_residential_state = fields.Many2one('res.country.state', string='Notifier Residential State')
        x_cheque_issued = fields.Many2one('cheque.issued', string='Cheque Issued')
        x_how_cheque_sent = fields.Many2one('how.cheque.sent', string='How Cheque Sent')
        x_notifier_commercial_state = fields.Many2one('res.country.state', string='Notifier Commercial State')
        x_recipient_relation = fields.Many2one('relationship', string='Recipients Relation')
        x_recipient_commercial_state = fields.Many2one('recipient.commercial.state', string='Recipient Commercial State')
        x_recipient_needs = fields.Many2one('recipient.needs', string='Recipient Needs')
        x_recipient_residential_state = fields.Many2one('res.country.state', string='Recipient Residential State')
        x_recipient_type = fields.Many2one('party', string='Recipient Type')
        x_notifier_type = fields.Many2one('party', string='Notifier Type')
        x_transaction_mode = fields.Many2one('transaction.mode', string='Transaction Mode')
        x_notifier_relation = fields.Many2one('relationship', string='Notifiers Relation')
        x_transact_type = fields.Many2one('transact.type', string='Transact Type')
        # x_interest_type = fields.Many2one('interest.type', string='Intrest Type')
        x_interest_types = fields.Many2one('intrest.type', string='Intrest Type')
        x_borrowed_money = fields.Many2one('borrowed.money', string='Borrowed Money')
        x_purchase_items = fields.Many2one('purchase.items', string='Purchase Items')
        x_goods_delivery = fields.Many2one('goods.delivery', string='Goods Delivery')
        x_recipient_need = fields.Many2one('recipient.needs', string='Recipient needs')
        x_cheque_filler = fields.Many2one('cheque.filler', string='Cheque Filler')
        x_partial_payment = fields.Boolean(string="Partial Payment", default=True)
        x_insufficient_payment =  fields.Float(string='Insufficient Payment')
        #  fields.Float(string='Some Float Field')
        x_return_cheque_date = fields.Date(  string='Return Cheque Date')
        x_return_date_notification = fields.Date(  string='Return Notification Date')
        x_notifier_money_material_give_date = fields.Date(  string='Notifier Amount/Material Transfer Date')
        x_check_recipient_to_notifier = fields.Date( string='Date of check given Recipient to Notifier')



# class Website(models.Model):
#         _name = "cheque.filler"
#         description = "Cheque Filler Manytoone module" 
#         name = fields.Char(string="Name")
        
        

class ChequeFiller(models.Model):
        _name = "cheque.filler"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Cheque Filler Manytoone module" 
        name = fields.Char(string="Name")


class MoneyIntrest(models.Model):
        _name = "money.intrest"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Money Intrest" 
        name = fields.Char(string="Name")


class Relationship(models.Model):
        _name = "relationship"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Relationship module" 
        name = fields.Char(string="Name")


class ChequeIssued(models.Model):
        _name = "cheque.issued"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Cheque Issued Manytoone module" 
        name = fields.Char(string="Name")

class Party(models.Model):
        _name = "party"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Party" 
        name = fields.Char(string="Name")

class TransactType(models.Model):
        _name = "transact.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Transact Type" 
        name = fields.Char(string="Name")

class PurchaseItems(models.Model):
        _name = "purchase.items"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Purchase Items" 
        name = fields.Char(string="Name")

class NotifierBusinessType(models.Model):
        _name = "notifier.business.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Notifier businesstype " 
        name = fields.Char(string="Name")


class NotifierMerchantType(models.Model):
        _name = "notifier.merchant.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Notifier Merchant type" 
        name = fields.Char(string="Name")

class NotifierRelationType(models.Model):
        _name = "notifier.relation.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Notifier Relation type" 
        name = fields.Char(string="Name")
        
class RecipientBusinessType(models.Model):
        _name = "recipient.business.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Recipient business type" 
        name = fields.Char(string="Name")

class RecipientMerchantType(models.Model):
        _name = "recipient.merchant.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Recipient Merchant type" 
        name = fields.Char(string="Name")

class RecipientRelationType(models.Model):
        _name = "recipient.relation.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Recipient relation type" 
        name = fields.Char(string="Name")

class RecipientNeeds(models.Model):
        _name = "recipient.needs"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Recipient needs" 
        name = fields.Char(string="Name")

class AccountType(models.Model):
        _name = "account.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Account type" 
        name = fields.Char(string="Name")
        x_html = fields.Html(string="Html")
        x_state = fields.Selection([
        ('draft', 'Draft'),
        ('done', 'Done'),
    ], string='State', default='draft')

class TransactionMode(models.Model):
        _name = "transaction.mode"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Transaction Mode" 
        name = fields.Char(string="Name")

class HowChequeSent(models.Model):
        _name = "how.cheque.sent"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = " How Cheque Sent " 
        name = fields.Char(string="Name")

class NoticeIntimation(models.Model):
        _name = "notice.intimation"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = " Notice Intimation " 
        name = fields.Char(string="Name")

class InterestType(models.Model):
        _name = "intrest.type"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Interest Type" 
        name = fields.Char(string="Name")    

class GoodsDelivery(models.Model):
        _name = "goods.delivery"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Goods Delivery" 
        name = fields.Char(string="Name")     



class GoodsDelivery(models.Model):
        _name = "goods.delivery"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Goods Delivery" 
        name = fields.Char(string="Name") 


class NotifierDisbursedMoney(models.Model):
        _name = "notifier.disbursed.money"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Notifier Disbursed Money" 
        name = fields.Char(string="Name") 
        x_amount_words = fields.Char(string="Amount Words")
        x_bank_name = fields.Char(string="Bank Name")
        x_cheque_no = fields.Char(string="Cheque No")
        x_bank_branch = fields.Char(string="Bank Branch")
        x_exact_date = fields.Date(string="Exact Date")
        x_transaction_mode = fields.Many2one('transaction.mode', string='Transaction Mode')
        x_borrowed_money = fields.Many2one('borrowed.money', string='Borrowed Money')
        x_account_type = fields.Many2one('account.type', string='Account Type')


class chequeDetail(models.Model):
        _name = "cheque"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Cheque Detail" 
        name = fields.Char(string="Name") 
        x_amount = fields.Char(string="Amount")
        x_amount_words = fields.Char(string="Amount Words")
        x_bank_name = fields.Char(string="Bank Name")
        x_cheque_no = fields.Char(string="Cheque No")
        x_ifsc_code = fields.Char(string="IFSC Code")
        x_bank_branch = fields.Char(string="Bank Branch")
        x_date = fields.Date(string="Date")
        # x_transaction_mode = fields.Many2one('transaction.mode', string='Transaction Mode')
        x_details = fields.Many2one('cheque.details', string='Cheque Details')
        x_account_type = fields.Many2one('account.type', string='Account Type')


class Purchase(models.Model):
        _name = "purchase"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Purchase" 
        name = fields.Char(string="Name") 
        x_invoice_no = fields.Char(string="Invoice No")
        x_other_item = fields.Char(string="Other Item")
        x_purchase_date = fields.Date(string="Purchase Date")
        x_purchase_items = fields.Many2one('purchase.items', string='Purchase Item')
        x_check_return_notice = fields.Many2one('cheque.details', string='Cheque Details')
        x_states = fields.Selection([
        ('draft', 'Draft'),
        ('done', 'Done'),
    ], string='State', default='draft')
class BorrowedMoney(models.Model):
        _name = "borrowed.money"
        description = "Borrowed Money" 
        name = fields.Char(string="Name") 
        x_other_recipient_need = fields.Char(string="Other recipient need")
        x_check_return_notifier = fields.Char(string="Check return Notifier")
        x_returning_amount_date = fields.Date(string="Returning Amount Date")
        x_after_month = fields.Integer(string="After Month")
        x_after_day = fields.Integer(string="After Day")
        x_after_year = fields.Integer(string="After Year")
        x_notifier_disbursed_money_json = fields.Text(string="Notifier Disbursed Money Json")
        x_one_record = fields.Boolean(string="One Record", default=True)
        x_check_return_notice = fields.Many2one('cheque.details', string='Check Return Notice')
        x_recipient_needs = fields.Many2one('recipient.needs', string='Recipient Needs')
        x_notifier_disbursed_money = fields.One2many('notifier.disbursed.money', 'x_borrowed_money', string='Notifier Disbursed Money')




class Demand(models.Model):
        _name = "demand"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Demand" 
        name = fields.Char(string="Name") 
        x_to_contact = fields.Char(string="Recipient Contact")
        x_from_contact = fields.Char(string="Notifier Contact")
        x_demand_date = fields.Date(string="Demand Date")
        
class Partnership(models.Model):
        _name = "partnership"
        _inherit = ['mail.thread', 'mail.activity.mixin'] 
        description = "Partnership" 
        name = fields.Char(string="Name") 
        x_first_name = fields.Char(string="First Name")
        x_middle_name = fields.Char(string="Middle Name")
        x_last_name = fields.Char(string="Last Name")
        x_dpin_no = fields.Char(string="DPIN No")
        x_drawee_partnership = fields.Many2one('cheque.details', string='Drawee Partnership')
        x_check_return_notice = fields.Many2one('cheque.details', string='Check Return Notice')







        


       

        



     
