<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data noupdate="1">
        
        <!--PRODUCT CATEGORY DEMO DATA -->
        <record id="party" model="party">
            <!-- <field name="parent_id" ref="product.product_category_all"/> -->
            <field name="name">Party 1 </field>
        
        </record>

        <record id="1" model="transact.type">
                    <field name="name">Borrowed Money </field>
        </record>
        <record id="2" model="transact.type">
                    <field name="name">Money At Intrest </field>
        </record>
        <record id="3" model="transact.type">
                    <field name="name">Loan </field>
        </record>
        <record id="4" model="transact.type">
                    <field name="name">Purchase </field>
        </record>
         <record id="5" model="transact.type">
                    <field name="name">Brokerage </field>
        </record>
         <record id="6" model="transact.type">
                    <field name="name">Service </field>
        </record>
         <record id="7" model="transact.type">
                    <field name="name">Rent </field>
        </record> 
        <record id="8" model="transact.type">
                    <field name="name">Salary </field>
        </record>
         <record id="9" model="transact.type">
                    <field name="name">Expenses </field>
        </record> 
        <record id="10" model="transact.type">
                    <field name="name">Bank </field>
        </record> 
        <record id="11" model="transact.type">
                    <field name="name">Co-Operative Society </field>
        </record>
         <record id="12" model="transact.type">
                    <field name="name">Non Banking Finance Company </field>
        </record> 

        <record id="p_1" model="purchase.items">
                    <field name="name">FMCG Product </field>
        </record>
        <record id="p_2" model="purchase.items">
                    <field name="name">Grocery </field>
        </record>
        <record id="p_3" model="purchase.items">
                    <field name="name"> Metal </field>
        </record>
        <record id="p_4" model="purchase.items">
                    <field name="name"> Building Materials </field>
        </record>
         <record id="p_5" model="purchase.items">
                    <field name="name"> Stationery </field>
        </record>
         <record id="p_6" model="purchase.items">
                    <field name="name"> Fabric </field>
        </record>
         <record id="p_7" model="purchase.items">
                    <field name="name"> Other </field>
        </record>

        <record id="cf_1" model="cheque.filler">
                    <field name="name">Particular Person </field>
        </record>
        <record id="cf_2" model="cheque.filler">
                    <field name="name">Who's Cheque has been returned </field>
        </record>


        <record id="t_1" model="transaction.mode">
                    <field name="name">Cash </field>
        </record>
        <record id="t_2" model="transaction.mode">
                    <field name="name">Cheque/DD </field>
        </record>
        <record id="t_3" model="transaction.mode">
                    <field name="name">Online-IMPS/RTGS </field>
        </record>
        <record id="t_4" model="transaction.mode">
                    <field name="name">Google Pay/Paytm </field>
        </record>


        
        <record id="at_1" model="account.type">
                    <field name="name"> Saving </field>
        </record>
        <record id="at_2" model="account.type">
                    <field name="name"> Current  </field>
        </record>
        <record id="at_3" model="account.type">
                    <field name="name"> CC  </field>
        </record>



         <record id="rn_1" model="recipient.needs">
                    <field name="name"> Social Event </field>
        </record>
        <record id="rn_2" model="recipient.needs">
                    <field name="name"> Childern's studies  </field>
        </record>
        <record id="rn_3" model="recipient.needs">
                    <field name="name"> Buying House  </field>
        </record>
        <record id="rn_4" model="recipient.needs">
                    <field name="name"> Business Necessity  </field>
        </record>
        <record id="rn_5" model="recipient.needs">
                    <field name="name"> Treatment of Diseas of  </field>
        </record>
        <record id="rn_6" model="recipient.needs">
                    <field name="name"> Personal  Necessity </field>
        </record>
        <record id="rn_7" model="recipient.needs">
                    <field name="name"> Other  </field>
        </record>

        <record id="nbt_1" model="notifier.business.type">
                    <field name="name"> Merchant </field>
        </record>
        <record id="nbt_2" model="notifier.business.type">
                    <field name="name"> Manufacture </field>
        </record>
         <record id="nbt_3" model="notifier.business.type">
                    <field name="name"> Supplier </field>
        </record>
        <record id="nbt_4" model="notifier.business.type">
                    <field name="name"> Service Provider </field>
        </record>
        <record id="nbt_5" model="notifier.business.type">
                    <field name="name"> Money Lenders </field>
        </record>


        <record id="rbt_1" model="recipient.business.type">
                    <field name="name"> Merchant </field>
        </record>
        <record id="rbt_2" model="recipient.business.type">
                    <field name="name"> Manufacture </field>
        </record>
         <record id="rbt_3" model="recipient.business.type">
                    <field name="name"> Supplier </field>
        </record>
        <record id="rbt_4" model="recipient.business.type">
                    <field name="name"> Service Provider </field>
        </record>
        <record id="rbt_5" model="recipient.business.type">
                    <field name="name"> Money Lenders </field>
        </record>




        <record id="nmt_1" model="notifier.merchant.type">
                    <field name="name"> Wholesale  </field>
        </record>
        <record id="nmt_2" model="notifier.merchant.type">
                    <field name="name"> Retail</field>
        </record>
        <record id="nmt_3" model="notifier.merchant.type">
                    <field name="name"> Wholesale and Retail</field>
        </record>

         <record id="nrt_1" model="notifier.relation.type">
                    <field name="name"> Personal  </field>
        </record>
        
         <record id="nrt_2" model="notifier.relation.type">
                    <field name="name"> Business  </field>
        </record>


        <record id="rt_1" model="recipient.relation.type">
                    <field name="name"> Personal  </field>
        </record>
        
         <record id="rt_2" model="recipient.relation.type">
                    <field name="name"> Business  </field>
        </record>
       

       <record id="rmt_1" model="recipient.merchant.type">
                    <field name="name"> Wholesale  </field>
        </record>
        <record id="rmt_2" model="recipient.merchant.type">
                    <field name="name"> Retail</field>
        </record>
        <record id="rmt_3" model="recipient.merchant.type">
                    <field name="name"> Wholesale and Retail</field>
        </record>


       <record id="ci_1" model="cheque.issued">
                    <field name="name"> At a time</field>
        </record>
        <record id="ci_2" model="cheque.issued">
                    <field name="name"> After some time</field>
        </record>
        <record id="ci_3" model="cheque.issued">
                    <field name="name"> When asked for money</field>
        </record>

       <record id="h_1" model="how.cheque.sent">
                    <field name="name"> Face to Face</field>
        </record>
        <record id="h_2" model="how.cheque.sent">
                    <field name="name"> Post</field>
        </record>
        <record id="h_3" model="how.cheque.sent">
                    <field name="name"> Courier</field>
        </record>
        <record id="h_4" model="how.cheque.sent">
                    <field name="name"> Unknown Person</field>
        </record>


        <record id="ni_1" model="notice.intimation">
                    <field name="name"> Face to Face</field>
        </record>
        <record id="ni_" model="notice.intimation">
                    <field name="name"> Email</field>
        </record>
        <record id="ni_3" model="notice.intimation">
                    <field name="name"> Message</field>
        </record>
        <record id="ni_4" model="notice.intimation">
                    <field name="name"> Letter</field>
        </record>

        <record id="ni_5" model="notice.intimation">
                    <field name="name"> Whatsapp</field>
        </record>
        <record id="ni_6" model="notice.intimation">
                    <field name="name"> Otherwise </field>
        </record>

         <record id="i_1" model="intrest.type">
                    <field name="name"> Simple Intrest</field>
        </record>        
        <record id="i_2" model="intrest.type">
                    <field name="name"> Compound Intrest</field>
        </record>

        <record id="gd_1" model="goods.delivery">
                    <field name="name"> In person</field>
        </record>
        <record id="gd_2" model="goods.delivery">
                    <field name="name"> By transport </field>
        </record>
        <record id="gd_3" model="goods.delivery">
                    <field name="name"> By seller of goods</field>
        </record>

        <record id="p_18" model="party">
                    <field name="name"> Individual</field>
        </record>
        
        <record id="p_19" model="party">
                    <field name="name"> Partnership Firm</field>
        </record>
        <record id="p_20" model="party">
                    <field name="name"> Public Ltd Company</field>
        </record>
        <record id="p_21" model="party">
                    <field name="name"> Proprietary firm</field>
        </record>
        <record id="p_22" model="party">
                    <field name="name"> Section 8 company</field>
        </record>
        <record id="p_23" model="party">
                    <field name="name"> Private Ltd Company</field>
        </record>
        <record id="p_24" model="party">
                    <field name="name"> Limited Liablity Partnership </field>
        </record>
        <record id="p_8" model="party">
                    <field name="name"> One Person Company</field>
        </record>
        <record id="p_9" model="party">
                    <field name="name"> Foriegn Company</field>
        </record>
        <record id="p_10" model="party">
                    <field name="name"> Non-banking Finance Company</field>
        </record>
        <record id="p_11" model="party">
                    <field name="name"> Co-operative Bank</field>
        </record>
        <record id="p_12" model="party">
                    <field name="name"> Co-operative Society</field>
        </record>
        <record id="p_13" model="party">
                    <field name="name"> Trust</field>
        </record>


        <record id="r_1" model="relationship">
                    <field name="name"> Friend</field>
        </record>
        <record id="r_2" model="relationship">
                    <field name="name"> Wife</field>
        </record>

<record id="r_3" model="relationship">
                    <field name="name"> Husband</field>
        </record>

<record id="r_4" model="relationship">
                    <field name="name"> Brother</field>
        </record>
<record id="r_5" model="relationship">
                    <field name="name"> Sister</field>
        </record>
<record id="r_6" model="relationship">
                    <field name="name">Mother</field>
        </record>
<record id="r_7" model="relationship">
                    <field name="name"> Father</field>
        </record>
<record id="r_8" model="relationship">
                    <field name="name"> Uncle</field>
        </record>
<record id="r_9" model="relationship">
                    <field name="name"> Aunt</field>
        </record>
<record id="r_10" model="relationship">
                    <field name="name"> Son</field>
        </record>
<record id="r_11" model="relationship">
                    <field name="name"> Son In law</field>
        </record>
<record id="r_12" model="relationship">
                    <field name="name"> Daughter</field>
        </record>
<record id="r_13" model="relationship">
                    <field name="name"> Daughter in law</field>
        </record>
<record id="r_14" model="relationship">
                    <field name="name"> Father In law</field>
        </record>
<record id="r_15" model="relationship">
                    <field name="name"> Mother In law</field>
        </record>


    </data>
</odoo>