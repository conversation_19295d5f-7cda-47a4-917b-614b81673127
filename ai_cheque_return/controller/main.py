
from odoo import http
from odoo.http import request



# class Test(http.Controller):
#     @http.route('/test', auth='public')
#     def index(self, **kw):
#         return "Hello, world"
    
class ChequeReturn(http.Controller):
   @http.route(['/cheque-details'], type='http', auth="public", website=True)
   def ChequeReturn(self):
       partners = request.env['res.partner'].sudo().search([])
       values = {}
       values.update({
           'partners': partners
       })
       return request.render("ai_cheque_return.details_form", values)
   

#         try:
#             # Define default template
#             template_name = 'cheque_return.details_form'
#             # Render the selected template
#             return request.render(template_name, {})
        
#         except Exception as e:
#             self.logger.exception("An error occurred while rendering RTI form page: %s", str(e))
#             return http.request.make_response("An error occurred while rendering RTI form page.")




# class WebsiteSort(Home):
#     @http.route(auth='public')
#     def index(self, **kw):

#         products = request.env['product.template'].sudo().search([])
        # for each in products:
        #     each.qty_sold = 0
        #     each.views = 0
        #     each.top_selling = False
           
        # date = fields.Datetime.now()
        # date_before = date - datetime.timedelta(days=7)

        # orders = request.env['sale.order'].sudo().search([('date_order', '<=', date),
        #                                                   ('date_order', '>=', date_before), ('website_id', '!=', False),
        #                 ('state', 'in',('sale', 'done'))])
        # for order in orders:
        #     order_line = order.order_line
        #     for product in order_line:
        #         print(product.product_id.qty_sold)
        #         product.product_id.qty_sold = product.product_id.qty_sold + 1

        # products = request.env['website.track'].sudo().search(
        #     [('visit_datetime', '<=', date),
        #      ('visit_datetime', '>=', date_before),
        #      ('product_id', '!=', False)])
        # for pro in products:
        #     pro.product_id.views = pro.product_id.views + 1
        # super(WebsiteSort, self).index()
        # website_product_ids = request.env['product.template'].sudo().search(
        #     [('is_published', '=', True),
        #      ('qty_sold', '!=', 0)],
        #     order='qty_sold desc', limit=4)
        # website_product_ids.top_selling = True
        # return request.render('website_homepage', {})