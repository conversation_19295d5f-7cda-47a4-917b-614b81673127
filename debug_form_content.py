#!/usr/bin/env python3
"""
Debug script to check form content and selection options
"""

import requests
import re

def check_form_content():
    """Check the actual form content"""
    print("🔍 DEBUGGING FORM CONTENT")
    print("=" * 60)
    
    try:
        response = requests.get('https://oneclickvakil.com/customer-feedback-main', timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Find the overall_rating select element
            rating_pattern = r'<select[^>]*name="overall_rating"[^>]*>(.*?)</select>'
            rating_match = re.search(rating_pattern, content, re.DOTALL)
            
            if rating_match:
                select_content = rating_match.group(1)
                print("✅ Found overall_rating select element:")
                print("=" * 40)
                print(f"<select name=\"overall_rating\">{select_content}</select>")
                print("=" * 40)
                
                # Check for options
                option_pattern = r'<option[^>]*value="([^"]*)"[^>]*>([^<]*)</option>'
                options = re.findall(option_pattern, select_content)
                
                print(f"\n📊 Found {len(options)} options:")
                for i, (value, text) in enumerate(options, 1):
                    print(f"  {i}. Value: '{value}' | Text: '{text}'")
                
                # Check if we have actual rating options
                rating_options = [opt for opt in options if opt[0] and opt[0] != ""]
                if rating_options:
                    print(f"\n✅ Found {len(rating_options)} valid rating options")
                    return True
                else:
                    print("\n❌ No valid rating options found (only empty placeholder)")
                    return False
            else:
                print("❌ overall_rating select element not found")
                # Search for any select elements
                select_pattern = r'<select[^>]*>(.*?)</select>'
                selects = re.findall(select_pattern, content, re.DOTALL)
                print(f"Found {len(selects)} select elements total")
                return False
        else:
            print(f"❌ Form page failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing form page: {e}")
        return False

def test_form_submission_debug():
    """Test form submission with debug info"""
    print("\n🧪 TESTING FORM SUBMISSION WITH DEBUG")
    print("=" * 60)
    
    form_data = {
        'name': 'Debug Test User',
        'customer_name': 'John Debug',
        'customer_email': '<EMAIL>',
        'overall_rating': '5',  # Explicitly set rating
        'feedback_comments': 'Debug test submission'
    }
    
    print("📤 Submitting form data:")
    for key, value in form_data.items():
        print(f"  {key}: '{value}'")
    
    try:
        response = requests.post(
            'https://oneclickvakil.com/customer-feedback-main/submit',
            data=form_data,
            timeout=15
        )
        
        print(f"\n📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            if 'Thank You' in response.text:
                print("✅ SUCCESS: Form submission worked!")
                return True
            elif 'Error' in response.text:
                print("❌ ERROR: Form returned error page")
                # Extract error details
                if 'null value in column' in response.text:
                    print("❌ Still getting null constraint violation")
                    print("❌ This means the selection value is not being processed")
                print(f"Error preview: {response.text[:500]}...")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main debug function"""
    print("🎯 FORM CONTENT AND SUBMISSION DEBUG")
    print()
    
    # Check form content
    form_ok = check_form_content()
    
    # Test submission
    submission_ok = test_form_submission_debug()
    
    print("\n" + "=" * 60)
    print("📋 DEBUG SUMMARY:")
    print(f"Form Content: {'✅ OK' if form_ok else '❌ ISSUE'}")
    print(f"Form Submission: {'✅ OK' if submission_ok else '❌ ISSUE'}")
    
    if not form_ok:
        print("\n🔧 ISSUE: Selection options not properly rendered in form")
    if not submission_ok:
        print("\n🔧 ISSUE: Selection value not being processed by controller")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
