<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Legal Document Security Groups -->
        <record id="group_legal_document_user" model="res.groups">
            <field name="name">Legal Document User</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <record id="group_legal_document_manager" model="res.groups">
            <field name="name">Legal Document Manager</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('group_legal_document_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
        
        <record id="group_legal_document_expert" model="res.groups">
            <field name="name">Legal Document Expert</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('group_legal_document_user'))]"/>
        </record>
    </data>
    
    <data noupdate="1">
        <!-- Multi-company rules -->
        <record id="legal_document_service_comp_rule" model="ir.rule">
            <field name="name">Legal Document Service: multi-company</field>
            <field name="model_id" ref="model_legal_document_service"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="legal_document_request_comp_rule" model="ir.rule">
            <field name="name">Legal Document Request: multi-company</field>
            <field name="model_id" ref="model_legal_document_request"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <!-- Portal access rules -->
        <record id="legal_document_request_portal_rule" model="ir.rule">
            <field name="name">Portal: Legal Document Request</field>
            <field name="model_id" ref="model_legal_document_request"/>
            <field name="domain_force">[('partner_id', '=', user.partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
        </record>
    </data>
</odoo>
