<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default Service Types -->
        <record id="service_type_rti" model="legal.document.service.type">
            <field name="name">Right to Information (RTI)</field>
            <field name="description">Services related to filing and managing Right to Information requests</field>
            <field name="sequence">10</field>
        </record>
        
        <record id="service_type_cheque_return" model="legal.document.service.type">
            <field name="name">Cheque Return</field>
            <field name="description">Services related to handling cheque return cases</field>
            <field name="sequence">20</field>
        </record>
        
        <record id="service_type_legal_notice" model="legal.document.service.type">
            <field name="name">Legal Notice</field>
            <field name="description">Services for drafting and sending legal notices</field>
            <field name="sequence">30</field>
        </record>
        
        <record id="service_type_agreement" model="legal.document.service.type">
            <field name="name">Agreements</field>
            <field name="description">Services for drafting various types of legal agreements</field>
            <field name="sequence">40</field>
        </record>
        
        <record id="service_type_property" model="legal.document.service.type">
            <field name="name">Property Documents</field>
            <field name="description">Services related to property documentation</field>
            <field name="sequence">50</field>
        </record>
    </data>
</odoo>
