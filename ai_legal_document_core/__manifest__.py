{
    'name': 'AI Legal Document Core',
    'version': '1.0',
    'category': 'Document Management',
    'summary': 'Core module for AI-powered legal document drafting',
    'description': """
        AI Legal Document Core Module
        ============================

        This module provides the core functionality for AI-powered legal document drafting services.

        Features:
        - Legal document service management
        - Multilingual support
        - Integration with legal_case_management
        - Sales Order and Invoice generation
        - Base models for AI conversation and document generation
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'mail',
        'sale',
        'portal',
        'web',
        'website',
        'payment',
        'website_payment',
        'sale_management',
        'account',
        'legal_case_management',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/legal_document_sequence.xml',
        'data/service_types_data.xml',
        'views/legal_document_service_views.xml',
        'views/legal_document_request_views.xml',
        'views/sale_order_views.xml',
        'views/portal_templates.xml',
        'views/website_templates.xml',
        'views/menu_views.xml',
    ],
    'assets': {
        'website.assets_frontend': [
            'ai_legal_document_core/static/src/css/legal_document_styles.css',
            'ai_legal_document_core/static/src/js/legal_document_scripts.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
