from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class LegalDocumentService(models.Model):
    _name = 'legal.document.service'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Legal Document Service'

    name = fields.Char(string='Service Name', required=True, tracking=True)
    code = fields.Char(string='Service Code', required=True, tracking=True)
    description = fields.Text(string='Description', tracking=True)
    service_type_id = fields.Many2one('legal.document.service.type', string='Service Type', required=True)

    # Product for sales order
    product_id = fields.Many2one('product.product', string='Service Product', required=True,
                                domain=[('type', '=', 'service')],
                                help="Product used for creating sales orders")

    active = fields.Boolean(string='Active', default=True, tracking=True)

    language_ids = fields.Many2many('res.lang', string='Available Languages')

    # Service content
    instructions = fields.Text(string='Instructions', help="Instructions for the AI to follow when handling this service")
    initial_prompt = fields.Text(string='Initial Prompt', help="Initial prompt to start the conversation with the user")

    # Service features for "What's Included" section
    feature_ids = fields.One2many('legal.document.service.feature', 'service_id', string='Service Features')

    # Service metadata
    icon = fields.Binary(string='Icon')
    banner_image = fields.Binary(string='Banner Image')
    sequence = fields.Integer(string='Sequence', default=10)

    # Related fields
    request_ids = fields.One2many('legal.document.request', 'service_id', string='Requests')
    request_count = fields.Integer(string='Request Count', compute='_compute_request_count')

    # Case management integration (optional)
    case_category_id = fields.Many2one('case.category', string='Case Category',
                                      help="Related case category in legal case management",
                                      ondelete='set null')

    # AI Chatbot Configuration
    chatbot_config_id = fields.Many2one('ai.chatbot.config', string='AI Chatbot Configuration',
                                       help="Specific AI configuration for this service",
                                       ondelete='set null')

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        index=True
    )
    @api.depends('request_ids')
    def _compute_request_count(self):
        for record in self:
            record.request_count = len(record.request_ids)

    @api.constrains('code')
    def _check_code_unique(self):
        for record in self:
            if self.search_count([('code', '=', record.code), ('id', '!=', record.id)]) > 0:
                raise ValidationError(_("Service code must be unique!"))

    def action_view_requests(self):
        self.ensure_one()
        return {
            'name': _('Requests'),
            'type': 'ir.actions.act_window',
            'res_model': 'legal.document.request',
            'view_mode': 'tree,form',
            'domain': [('service_id', '=', self.id)],
            'context': {'default_service_id': self.id},
        }


class LegalDocumentServiceFeature(models.Model):
    _name = 'legal.document.service.feature'
    _description = 'Legal Document Service Feature'
    _order = 'sequence, id'

    name = fields.Char(string='Feature Name', required=True)
    description = fields.Text(string='Feature Description')
    icon = fields.Char(string='Icon Class', default='fa-check', help="Font Awesome icon class (e.g., fa-check, fa-star)")
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(string='Active', default=True)

    service_id = fields.Many2one('legal.document.service', string='Service', required=True, ondelete='cascade')


class LegalDocumentServiceType(models.Model):
    _name = 'legal.document.service.type'
    _description = 'Legal Document Service Type'

    name = fields.Char(string='Type Name', required=True)
    description = fields.Text(string='Description')
    sequence = fields.Integer(string='Sequence', default=10)

    service_ids = fields.One2many('legal.document.service', 'service_type_id', string='Services')
    service_count = fields.Integer(string='Service Count', compute='_compute_service_count')

    @api.depends('service_ids')
    def _compute_service_count(self):
        for record in self:
            record.service_count = len(record.service_ids)
