from odoo import api, fields, models, _


class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    legal_document_request_id = fields.Many2one('legal.document.request', string='Legal Document Request', 
                                               readonly=True, copy=False)
    
    def _prepare_invoice(self):
        """Add legal document request reference to invoice"""
        invoice_vals = super()._prepare_invoice()
        if self.legal_document_request_id:
            invoice_vals['legal_document_request_id'] = self.legal_document_request_id.id
            invoice_vals['narration'] = f"Legal Document Request: {self.legal_document_request_id.name}"
        return invoice_vals
    
    def action_confirm(self):
        """Override to handle legal document request state changes"""
        res = super().action_confirm()
        for order in self:
            if order.legal_document_request_id and order.legal_document_request_id.state == 'pending_payment':
                # Update the request's invoices
                invoices = order.invoice_ids.filtered(lambda inv: inv.state == 'posted')
                if invoices:
                    order.legal_document_request_id.write({
                        'invoice_ids': [(6, 0, invoices.ids)]
                    })
        return res
    
    def _get_payment_type(self):
        """Add legal document payment type"""
        res = super()._get_payment_type()
        if self.legal_document_request_id:
            return 'legal_document'
        return res


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    
    legal_document_request_id = fields.Many2one(related='order_id.legal_document_request_id', 
                                               string='Legal Document Request', readonly=True)


class AccountMove(models.Model):
    _inherit = 'account.move'
    
    legal_document_request_id = fields.Many2one('legal.document.request', string='Legal Document Request', 
                                               readonly=True, copy=False)
    
    def action_post(self):
        """Override to handle legal document request state changes"""
        res = super().action_post()
        for move in self:
            if move.legal_document_request_id and move.payment_state == 'paid':
                move.legal_document_request_id.action_mark_paid()
        return res


class PaymentTransaction(models.Model):
    _inherit = 'payment.transaction'
    
    legal_document_request_id = fields.Many2one('legal.document.request', string='Legal Document Request', 
                                               readonly=True, copy=False)
    
    def _reconcile_after_done(self):
        """Handle post-processing after transaction is done"""
        res = super()._reconcile_after_done()
        for tx in self:
            sale_orders = tx.sale_order_ids
            for order in sale_orders:
                if order.legal_document_request_id and tx.state == 'done':
                    # Update the legal document request
                    order.legal_document_request_id.write({
                        'payment_status': 'paid',
                        'payment_date': fields.Datetime.now(),
                    })
                    # If all invoices are paid, mark the request as paid
                    if all(inv.payment_state == 'paid' for inv in order.invoice_ids):
                        order.legal_document_request_id.action_mark_paid()
        return res
