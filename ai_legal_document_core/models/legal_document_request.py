from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class LegalDocumentRequest(models.Model):
    _name = 'legal.document.request'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']
    _description = 'Legal Document Request'
    _order = 'create_date desc'
    
    name = fields.Char(string='Reference', readonly=True, copy=False, default='New')
    service_id = fields.Many2one('legal.document.service', string='Service', required=True, tracking=True)
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user, readonly=True)
    partner_id = fields.Many2one('res.partner', string='Customer', default=lambda self: self.env.user.partner_id, required=True)
    
    language_id = fields.Many2one('res.lang', string='Language', required=True, tracking=True)
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('pending_payment', 'Pending Payment'),
        ('paid', 'Paid'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)
    
    # Sales and payment
    sale_order_id = fields.Many2one('sale.order', string='Sales Order', copy=False, tracking=True)
    invoice_ids = fields.Many2many('account.move', string='Invoices', copy=False, tracking=True)
    invoice_count = fields.Integer(string='Invoice Count', compute='_compute_invoice_count')


    payment_status = fields.Selection([
        ('not_paid', 'Not Paid'),
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed')
    ], string='Payment Status', default='not_paid', tracking=True)
    
    # Document content
    document_html = fields.Html(string='Document Content', sanitize=False)
    document_text = fields.Text(string='Document Text')
    
    # Dates
    create_date = fields.Datetime(string='Created On', readonly=True)
    payment_date = fields.Datetime(string='Payment Date', readonly=True)
    completion_date = fields.Datetime(string='Completion Date', readonly=True)
    
    # Expert assignment
    expert_user_id = fields.Many2one('res.users', string='Assigned Expert', tracking=True)
    expert_notes = fields.Text(string='Expert Notes')
    
    # Case management integration
    case_id = fields.Many2one('case.registration', string='Related Case', tracking=True)
    
    # Related fields
    conversation_ids = fields.One2many('legal.document.conversation', 'request_id', string='Conversations')
    form_response_ids = fields.One2many('legal.document.form.response', 'request_id', string='Form Responses')
    
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        index=True
    )

    def action_view_case(self):
        self.ensure_one()
        if not self.case_id:
            return
        return {
            'name': 'Case Details',
            'type': 'ir.actions.act_window',
            'res_model': 'case.registration',
            'view_mode': 'form',
            'res_id': self.case_id.id,
            'target': 'current',
        }
    
    @api.depends('invoice_ids')
    def _compute_invoice_count(self):
        for record in self:
            record.invoice_count = len(record.invoice_ids)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('legal.document.request') or 'New'
        return super().create(vals_list)
    
    def action_start(self):
        self.ensure_one()
        self.write({'state': 'in_progress'})
    
    def action_request_payment(self):
        """Create sales order and request payment"""
        self.ensure_one()
        
        if self.sale_order_id:
            return self._redirect_to_payment()
        
        # Create sales order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.partner_id.id,
            'legal_document_request_id': self.id,
            'client_order_ref': self.name,
        })
        
        # Add service product line
        self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': self.service_id.product_id.id,
            'name': f"{self.service_id.name} - {self.name}",
            'product_uom_qty': 1.0,
            'price_unit': self.service_id.product_id.list_price,
        })
        
        # Confirm the sales order
        sale_order.action_confirm()
        
        # Link the sales order to the request
        self.write({
            'sale_order_id': sale_order.id,
            'state': 'pending_payment'
        })
        
        return self._redirect_to_payment()
    
    def _redirect_to_payment(self):
        """Redirect to payment page"""
        return {
            'type': 'ir.actions.act_url',
            'url': f'/my/orders/{self.sale_order_id.id}?access_token={self.sale_order_id.access_token}',
            'target': 'self',
        }
    
    def action_view_sale_order(self):
        self.ensure_one()
        action = self.env.ref('sale.action_orders').read()[0]
        action['views'] = [(self.env.ref('sale.view_order_form').id, 'form')]
        action['res_id'] = self.sale_order_id.id
        return action

    def action_view_invoices(self):
        self.ensure_one()
        action = self.env.ref('account.action_move_out_invoice_type').read()[0]
        if len(self.invoice_ids) == 1:
            action['views'] = [(self.env.ref('account.view_move_form').id, 'form')]
            action['res_id'] = self.invoice_ids.id
        else:
            action['domain'] = [('id', 'in', self.invoice_ids.ids)]
        return action


    def action_mark_paid(self):
        self.ensure_one()
        self.write({
            'state': 'paid',
            'payment_status': 'paid',
            'payment_date': fields.Datetime.now()
        })
        
        # Create case in legal case management if configured
        if self.service_id.case_category_id and not self.case_id:
            self._create_legal_case()
    
    def _create_legal_case(self):
        """Create a case in legal case management"""
        case_vals = {
            'name': self.name,
            'client_id': self.partner_id.id,
            'case_category_id': self.service_id.case_category_id.id,
            'description': f"<p>AI Legal Document Request: {self.name}</p><p>{self.service_id.name}</p>",
            'register_date': fields.Date.today(),
            'payment_method': 'case',
            'state': 'in_progress',
        }
        
        case = self.env['case.registration'].create(case_vals)
        self.write({'case_id': case.id})
        
        return case
    
    def action_process(self):
        self.ensure_one()
        self.write({'state': 'processing'})
    
    def action_complete(self):
        self.ensure_one()
        self.write({
            'state': 'completed',
            'completion_date': fields.Datetime.now()
        })
    
    def action_cancel(self):
        self.ensure_one()
        self.write({'state': 'cancelled'})
    
    def _compute_access_url(self):
        super()._compute_access_url()
        for record in self:
            record.access_url = f'/my/legal/requests/{record.id}'


class LegalDocumentConversation(models.Model):
    _name = 'legal.document.conversation'
    _description = 'Legal Document Conversation'
    _order = 'create_date asc'
    
    request_id = fields.Many2one('legal.document.request', string='Request', required=True, ondelete='cascade')
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user)
    is_ai = fields.Boolean(string='Is AI Message', default=False)
    message = fields.Text(string='Message', required=True)
    create_date = fields.Datetime(string='Created On', readonly=True)
    
    # For form elements
    has_form = fields.Boolean(string='Has Form', default=False)
    form_data = fields.Text(string='Form Data', help="JSON representation of the form elements")


class LegalDocumentFormResponse(models.Model):
    _name = 'legal.document.form.response'
    _description = 'Legal Document Form Response'
    _order = 'create_date desc'
    
    request_id = fields.Many2one('legal.document.request', string='Request', required=True, ondelete='cascade')
    conversation_id = fields.Many2one('legal.document.conversation', string='Conversation')
    form_data = fields.Text(string='Form Data', help="JSON representation of the form elements")
    response_data = fields.Text(string='Response Data', help="JSON representation of the user's responses")
    create_date = fields.Datetime(string='Created On', readonly=True)
