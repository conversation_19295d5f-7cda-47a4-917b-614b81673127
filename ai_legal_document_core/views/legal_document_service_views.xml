<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Legal Document Service Type Views -->
    <record id="view_legal_document_service_type_form" model="ir.ui.view">
        <field name="name">legal.document.service.type.form</field>
        <field name="model">legal.document.service.type</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Service Type Name"/>
                        </h1>
                    </div>
                    <group>
                        <field name="description"/>
                        <field name="sequence"/>
                    </group>
                    <notebook>
                        <page string="Services" name="services">
                            <field name="service_ids" readonly="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="code"/>
                                    <field name="product_id"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_legal_document_service_type_tree" model="ir.ui.view">
        <field name="name">legal.document.service.type.tree</field>
        <field name="model">legal.document.service.type</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="service_count"/>
            </tree>
        </field>
    </record>

    <!-- Legal Document Service Views -->
    <record id="view_legal_document_service_form" model="ir.ui.view">
        <field name="name">legal.document.service.form</field>
        <field name="model">legal.document.service</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_requests" type="object" class="oe_stat_button" icon="fa-file-text-o">
                            <field name="request_count" widget="statinfo" string="Requests"/>
                        </button>
                    </div>
                    <field name="icon" widget="image" class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Service Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="service_type_id"/>
                            <field name="product_id"/>
                            <field name="case_category_id"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="sequence"/>
                            <field name="language_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Describe the service..."/>
                        </page>
                        <page string="AI Configuration" name="ai_configuration">
                            <group>
                                <field name="chatbot_config_id" placeholder="Select AI configuration for this service"/>
                                <field name="initial_prompt" placeholder="Initial prompt to start the conversation..."/>
                                <field name="instructions" placeholder="Instructions for the AI to follow..."/>
                            </group>
                        </page>
                        <page string="Service Features" name="features">
                            <field name="feature_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="icon"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Media" name="media">
                            <group>
                                <field name="banner_image" widget="image"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_legal_document_service_tree" model="ir.ui.view">
        <field name="name">legal.document.service.tree</field>
        <field name="model">legal.document.service</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="service_type_id"/>
                <field name="product_id"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_legal_document_service_search" model="ir.ui.view">
        <field name="name">legal.document.service.search</field>
        <field name="model">legal.document.service</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <field name="service_type_id"/>
                <field name="product_id"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Service Type" name="groupby_type" context="{'group_by': 'service_type_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_legal_document_service_type" model="ir.actions.act_window">
        <field name="name">Service Types</field>
        <field name="res_model">legal.document.service.type</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="action_legal_document_service" model="ir.actions.act_window">
        <field name="name">Services</field>
        <field name="res_model">legal.document.service</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
    </record>
</odoo>
