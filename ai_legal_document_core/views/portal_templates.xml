<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Portal Home Inheritance -->
    <template id="portal_my_home_legal_documents" name="Portal My Home : Legal Documents" inherit_id="portal.portal_my_home" priority="40">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
            <t t-if="legal_document_count" t-call="portal.portal_docs_entry">
                <t t-set="title">Legal Documents</t>
                <t t-set="url" t-value="'/my/legal/requests'"/>
                <t t-set="count" t-value="legal_document_count"/>
            </t>
        </xpath>
    </template>
    
    <!-- Portal Legal Documents List -->
    <template id="portal_my_legal_documents" name="My Legal Documents">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>
            
            <t t-call="portal.portal_searchbar">
                <t t-set="title">Legal Documents</t>
            </t>
            
            <t t-if="not legal_documents">
                <div class="alert alert-info text-center" role="alert">
                    There are currently no legal document requests.
                </div>
            </t>
            
            <t t-if="legal_documents">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr class="active">
                                <th>Reference</th>
                                <th>Service</th>
                                <th>Language</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Payment</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="legal_documents" t-as="document">
                                <tr>
                                    <td>
                                        <a t-att-href="'/my/legal/requests/%s' % document.id">
                                            <t t-esc="document.name"/>
                                        </a>
                                    </td>
                                    <td><t t-esc="document.service_id.name"/></td>
                                    <td><t t-esc="document.language_id.name"/></td>
                                    <td><t t-esc="document.create_date" t-options="{'widget': 'date'}"/></td>
                                    <td>
                                        <span t-attf-class="badge bg-#{document.state == 'completed' and 'success' or document.state == 'cancelled' and 'danger' or document.state == 'pending_payment' and 'warning' or 'primary'}">
                                            <t t-esc="document.state"/>
                                        </span>
                                    </td>
                                    <td>
                                        <span t-attf-class="badge bg-#{document.payment_status == 'paid' and 'success' or document.payment_status == 'failed' and 'danger' or document.payment_status == 'pending' and 'warning' or 'secondary'}">
                                            <t t-esc="document.payment_status"/>
                                        </span>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>
                
                <div class="o_portal_pager">
                    <t t-call="portal.pager"/>
                </div>
            </t>
        </t>
    </template>
    
    <!-- Portal Legal Document Detail -->
    <template id="portal_my_legal_document" name="My Legal Document">
        <t t-call="portal.portal_layout">
            <t t-set="o_portal_fullwidth_alert" groups="sales_team.group_sale_salesman">
                <t t-call="portal.portal_back_in_edit_mode">
                    <t t-set="backend_url" t-value="'/web#model=legal.document.request&amp;id=%s&amp;view_type=form' % document_request.id"/>
                </t>
            </t>
            
            <t t-call="portal.portal_record_layout">
                <t t-set="card_header">
                    <h5 class="mb-0">
                        <small class="text-muted">Document Request - </small><span t-field="document_request.name"/>
                    </h5>
                </t>
                
                <t t-set="card_body">
                    <div class="row">
                        <div class="col-12 col-md-6">
                            <strong>Service:</strong> <span t-field="document_request.service_id.name"/><br/>
                            <strong>Language:</strong> <span t-field="document_request.language_id.name"/><br/>
                            <strong>Created on:</strong> <span t-field="document_request.create_date" t-options='{"widget": "datetime"}'/><br/>
                            <t t-if="document_request.payment_date">
                                <strong>Payment Date:</strong> <span t-field="document_request.payment_date" t-options='{"widget": "datetime"}'/><br/>
                            </t>
                            <t t-if="document_request.completion_date">
                                <strong>Completion Date:</strong> <span t-field="document_request.completion_date" t-options='{"widget": "datetime"}'/><br/>
                            </t>
                        </div>
                        <div class="col-12 col-md-6">
                            <strong>Status:</strong> 
                            <span t-attf-class="badge bg-#{document_request.state == 'completed' and 'success' or document_request.state == 'cancelled' and 'danger' or document_request.state == 'pending_payment' and 'warning' or 'primary'}">
                                <t t-esc="document_request.state"/>
                            </span><br/>
                            <strong>Payment Status:</strong> 
                            <span t-attf-class="badge bg-#{document_request.payment_status == 'paid' and 'success' or document_request.payment_status == 'failed' and 'danger' or document_request.payment_status == 'pending' and 'warning' or 'secondary'}">
                                <t t-esc="document_request.payment_status"/>
                            </span><br/>
                            <t t-if="document_request.sale_order_id">
                                <strong>Sales Order:</strong> 
                                <a t-att-href="'/my/orders/%s' % document_request.sale_order_id.id">
                                    <span t-field="document_request.sale_order_id.name"/>
                                </a><br/>
                            </t>
                            <t t-if="document_request.invoice_ids">
                                <strong>Invoices:</strong> 
                                <t t-foreach="document_request.invoice_ids" t-as="invoice">
                                    <a t-att-href="'/my/invoices/%s' % invoice.id">
                                        <span t-field="invoice.name"/>
                                    </a>
                                    <t t-if="not invoice_last">, </t>
                                </t><br/>
                            </t>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>Actions</h4>
                        <div class="d-flex flex-wrap">
                            <a t-att-href="'/legal-documents/conversation/%s' % document_request.id" class="btn btn-primary me-2 mb-2">
                                Continue Conversation
                            </a>
                            
                            <t t-if="document_request.state == 'pending_payment'">
                                <a t-att-href="'/my/orders/%s' % document_request.sale_order_id.id" class="btn btn-success me-2 mb-2">
                                    Make Payment
                                </a>
                            </t>
                            
                            <t t-if="document_request.state == 'completed' and document_request.document_html">
                                <a t-att-href="'/my/legal/requests/%s/document' % document_request.id" class="btn btn-info me-2 mb-2">
                                    View Document
                                </a>
                                <a t-att-href="'/my/legal/requests/%s/document/pdf' % document_request.id" class="btn btn-secondary me-2 mb-2">
                                    Download PDF
                                </a>
                            </t>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4>Conversation History</h4>
                        <div class="conversation-history mt-3">
                            <t t-foreach="document_request.conversation_ids" t-as="conversation">
                                <div t-attf-class="message mb-3 #{conversation.is_ai and 'ai-message' or 'user-message'}">
                                    <div t-attf-class="message-bubble p-3 rounded #{conversation.is_ai and 'bg-light' or 'bg-primary text-white'}">
                                        <div class="message-header mb-2">
                                            <strong><t t-esc="conversation.is_ai and 'AI Assistant' or conversation.user_id.name"/></strong>
                                            <small class="text-muted ms-2">
                                                <t t-esc="conversation.create_date" t-options="{'widget': 'datetime'}"/>
                                            </small>
                                        </div>
                                        <div class="message-content">
                                            <t t-esc="conversation.message"/>
                                        </div>
                                        <t t-if="conversation.has_form">
                                            <div class="message-form mt-3 p-3 bg-white border rounded">
                                                <p class="text-muted">Form data available</p>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
