<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Legal Documents Home Page -->
    <template id="legal_documents_home" name="Legal Documents Home">
        <t t-call="website.layout">
            <div class="container mt-4 mb-5">
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h1 class="display-4">Legal Document Services</h1>
                        <p class="lead">AI-powered legal document drafting services</p>
                    </div>
                </div>
                
                <div class="row">
                    <t t-foreach="service_types" t-as="service_type">
                        <div class="col-12 mb-4">
                            <h2 class="border-bottom pb-2"><t t-esc="service_type.name"/></h2>
                            
                            <div class="row">
                                <t t-foreach="services.filtered(lambda s: s.service_type_id.id == service_type.id)" t-as="service">
                                    <div class="col-md-4 mb-4">
                                        <div class="card h-100 shadow-sm">
                                            <t t-if="service.banner_image">
                                                <img class="card-img-top" t-att-src="'data:image/png;base64,%s' % to_text(service.banner_image)" alt="Service Banner"/>
                                            </t>
                                            <t t-else="">
                                                <div class="card-img-top bg-light text-center py-5">
                                                    <i class="fa fa-file-text-o fa-3x text-muted"></i>
                                                </div>
                                            </t>
                                            <div class="card-body d-flex flex-column">
                                                <h5 class="card-title"><t t-esc="service.name"/></h5>
                                                <p class="card-text flex-grow-1"><t t-esc="service.description"/></p>
                                                <a t-att-href="'/legal-documents/service/%s' % service.id" class="btn btn-primary mt-auto">
                                                    View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </t>
                </div>
            </div>
        </t>
    </template>
    
    <!-- Legal Document Service Details Page -->
    <template id="legal_document_service" name="Legal Document Service">
        <t t-call="website.layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-md-8">
                        <h1><t t-esc="service.name"/></h1>
                        <div class="mb-4">
                            <t t-esc="service.description"/>
                        </div>
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <h3 class="mb-0">Start a New Request</h3>
                            </div>
                            <div class="card-body">
                                <form action="/legal-documents/create" method="post">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                    <input type="hidden" name="service_id" t-att-value="service.id"/>
                                    
                                    <div class="form-group mb-3">
                                        <label for="language_id">Select Language</label>
                                        <select class="form-select" id="language_id" name="language_id" required="required">
                                            <option value="">Select a language</option>
                                            <t t-foreach="available_languages" t-as="language">
                                                <option t-att-value="language.id">
                                                    <t t-esc="language.name"/>
                                                </option>
                                            </t>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            Start Document Request
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h3 class="mb-0">Service Details</h3>
                            </div>
                            <div class="card-body">
                                <p><strong>Service Type:</strong> <t t-esc="service.service_type_id.name"/></p>
                                <p><strong>Price:</strong> <t t-esc="service.product_id.list_price" t-options="{'widget': 'monetary', 'display_currency': request.env.company.currency_id}"/></p>
                                <p><strong>Available Languages:</strong></p>
                                <ul>
                                    <t t-foreach="available_languages" t-as="language">
                                        <li><t t-esc="language.name"/></li>
                                    </t>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
    
    <!-- Legal Document Conversation Page -->
    <template id="legal_document_conversation" name="Legal Document Conversation">
        <t t-call="website.layout">
            <div class="container mt-4 mb-5">
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/legal-documents">Legal Documents</a></li>
                                <li class="breadcrumb-item"><a t-att-href="'/legal-documents/service/%s' % document_request.service_id.id"><t t-esc="document_request.service_id.name"/></a></li>
                                <li class="breadcrumb-item active" aria-current="page">Conversation</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h1>Document Request: <t t-esc="document_request.name"/></h1>
                    </div>
                    <div class="col-md-4 text-end">
                        <t t-if="document_request.state == 'in_progress'">
                            <a t-att-href="'/my/legal/requests/%s/payment' % document_request.id" class="btn btn-primary">
                                Proceed to Payment
                            </a>
                        </t>
                        <t t-elif="document_request.state == 'pending_payment'">
                            <a t-att-href="'/my/legal/requests/%s/payment' % document_request.id" class="btn btn-primary">
                                Make Payment
                            </a>
                        </t>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <!-- Conversation Area -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3 class="mb-0">Conversation</h3>
                                <span class="badge bg-primary"><t t-esc="document_request.state"/></span>
                            </div>
                            <div class="card-body">
                                <div id="conversation-messages" class="mb-4" style="max-height: 400px; overflow-y: auto;">
                                    <t t-foreach="conversations" t-as="conversation">
                                        <div t-attf-class="message mb-3 #{conversation.is_ai and 'ai-message' or 'user-message'}">
                                            <div t-attf-class="message-bubble p-3 rounded #{conversation.is_ai and 'bg-light' or 'bg-primary text-white'}">
                                                <div class="message-header mb-2">
                                                    <strong><t t-esc="conversation.is_ai and 'AI Assistant' or conversation.user_id.name"/></strong>
                                                    <small class="text-muted ms-2">
                                                        <t t-esc="conversation.create_date" t-options="{'widget': 'datetime'}"/>
                                                    </small>
                                                </div>
                                                <div class="message-content">
                                                    <t t-esc="conversation.message"/>
                                                </div>
                                                <t t-if="conversation.has_form">
                                                    <div class="message-form mt-3 p-3 bg-white border rounded">
                                                        <p class="text-muted">Form data available</p>
                                                    </div>
                                                </t>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                                
                                <t t-if="document_request.state in ['draft', 'in_progress']">
                                    <div id="message-input">
                                        <form id="conversation-form" class="d-flex">
                                            <input type="hidden" name="request_id" t-att-value="document_request.id"/>
                                            <input type="text" name="message" class="form-control me-2" placeholder="Type your message..." required="required"/>
                                            <button type="submit" class="btn btn-primary">Send</button>
                                        </form>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- Service Details -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h3 class="mb-0">Service Details</h3>
                            </div>
                            <div class="card-body">
                                <p><strong>Service:</strong> <t t-esc="document_request.service_id.name"/></p>
                                <p><strong>Language:</strong> <t t-esc="document_request.language_id.name"/></p>
                                <p><strong>Status:</strong> <span t-attf-class="badge bg-#{document_request.state == 'completed' and 'success' or document_request.state == 'cancelled' and 'danger' or 'primary'}"><t t-esc="document_request.state"/></span></p>
                                <p><strong>Created:</strong> <t t-esc="document_request.create_date" t-options="{'widget': 'datetime'}"/></p>
                                <t t-if="document_request.payment_date">
                                    <p><strong>Payment Date:</strong> <t t-esc="document_request.payment_date" t-options="{'widget': 'datetime'}"/></p>
                                </t>
                                <t t-if="document_request.completion_date">
                                    <p><strong>Completion Date:</strong> <t t-esc="document_request.completion_date" t-options="{'widget': 'datetime'}"/></p>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script type="text/javascript">
                $(document).ready(function() {
                    // Scroll to bottom of conversation
                    var conversationDiv = document.getElementById('conversation-messages');
                    if (conversationDiv) {
                        conversationDiv.scrollTop = conversationDiv.scrollHeight;
                    }
                    
                    // Handle form submission
                    $('#conversation-form').submit(function(e) {
                        e.preventDefault();
                        
                        var requestId = $('input[name="request_id"]').val();
                        var message = $('input[name="message"]').val();
                        
                        if (!message) return;
                        
                        // Clear input
                        $('input[name="message"]').val('');
                        
                        // Add message to conversation (optimistic UI update)
                        var userMessageHtml = `
                            <div class="message mb-3 user-message">
                                <div class="message-bubble p-3 rounded bg-primary text-white">
                                    <div class="message-header mb-2">
                                        <strong>${$('input[name="message"]').data('user-name') || 'You'}</strong>
                                        <small class="text-muted ms-2">Just now</small>
                                    </div>
                                    <div class="message-content">
                                        ${message}
                                    </div>
                                </div>
                            </div>
                        `;
                        $('#conversation-messages').append(userMessageHtml);
                        
                        // Scroll to bottom
                        conversationDiv.scrollTop = conversationDiv.scrollHeight;
                        
                        // Send message to server
                        $.ajax({
                            url: '/legal-documents/conversation/send',
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                jsonrpc: '2.0',
                                method: 'call',
                                params: {
                                    request_id: requestId,
                                    message: message
                                }
                            }),
                            success: function(data) {
                                if (data.result &amp;&amp; data.result.success) {
                                    // In the next module, we'll handle the AI response here
                                    console.log('Message sent successfully');
                                } else {
                                    console.error('Error sending message:', data.result ? data.result.error : 'Unknown error');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('AJAX error:', error);
                            }
                        });
                    });
                });
            </script>
        </t>
    </template>
</odoo>
