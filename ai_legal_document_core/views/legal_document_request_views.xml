<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Legal Document Request Views -->
    <record id="view_legal_document_request_form" model="ir.ui.view">
        <field name="name">legal.document.request.form</field>
        <field name="model">legal.document.request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_start" string="Start" type="object" class="oe_highlight"
                            invisible="state != 'draft'"/>
                    <button name="action_request_payment" string="Request Payment" type="object" class="oe_highlight"
                            invisible="state != 'in_progress'"/>
                    <button name="action_mark_paid" string="Mark as Paid" type="object"
                            invisible="state != 'pending_payment'"/>
                    <button name="action_process" string="Process" type="object" class="oe_highlight"
                            invisible="state != 'paid'"/>
                    <button name="action_complete" string="Complete" type="object" class="oe_highlight"
                            invisible="state != 'processing'"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            invisible="state in ['completed', 'cancelled']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,pending_payment,paid,processing,completed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_sale_order" type="object" class="oe_stat_button" icon="fa-dollar"
                                invisible="sale_order_id == False">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Sales Order</span>
                            </div>
                        </button>
                        <button name="action_view_invoices" type="object" class="oe_stat_button" icon="fa-file-text-o"
                                invisible="invoice_ids == []">
                            <field name="invoice_count" widget="statinfo" string="Invoices"/>
                        </button>
                        <button name="action_view_case" type="object" class="oe_stat_button" icon="fa-gavel"
                                invisible="case_id == False">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Legal Case</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="service_id" readonly="state != 'draft'"/>
                            <field name="partner_id" readonly="state != 'draft'"/>
                            <field name="language_id" readonly="state != 'draft'"/>
                            <field name="user_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="create_date" readonly="1"/>
                            <field name="payment_date" readonly="1"/>
                            <field name="completion_date" readonly="1"/>
                            <field name="payment_status" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Conversation" name="conversation">
                            <field name="conversation_ids">
                                <tree>
                                    <field name="create_date"/>
                                    <field name="user_id"/>
                                    <field name="is_ai"/>
                                    <field name="message"/>
                                    <field name="has_form"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Form Responses" name="form_responses">
                            <field name="form_response_ids">
                                <tree>
                                    <field name="create_date"/>
                                    <field name="conversation_id"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Document" name="document" invisible="document_html == False">
                            <field name="document_html"/>
                        </page>
                        <page string="Expert Assignment" name="expert_assignment"
                              groups="ai_legal_document_core.group_legal_document_manager,ai_legal_document_core.group_legal_document_expert">
                            <group>
                                <field name="expert_user_id"/>
                                <field name="expert_notes"/>
                            </group>
                        </page>
                        <page string="Sales &amp; Invoicing" name="sales_invoicing">
                            <group>
                                <field name="sale_order_id" readonly="1"/>
                                <field name="invoice_ids" readonly="1" widget="many2many_tags"/>
                                <field name="case_id" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_legal_document_request_tree" model="ir.ui.view">
        <field name="name">legal.document.request.tree</field>
        <field name="model">legal.document.request</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'" decoration-muted="state == 'cancelled'"
                  decoration-success="state == 'completed'" decoration-warning="state == 'pending_payment'">
                <field name="name"/>
                <field name="service_id"/>
                <field name="partner_id"/>
                <field name="language_id"/>
                <field name="create_date"/>
                <field name="state"/>
                <field name="payment_status"/>
            </tree>
        </field>
    </record>

    <record id="view_legal_document_request_search" model="ir.ui.view">
        <field name="name">legal.document.request.search</field>
        <field name="model">legal.document.request</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="service_id"/>
                <field name="partner_id"/>
                <field name="language_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                <filter string="Pending Payment" name="pending_payment" domain="[('state', '=', 'pending_payment')]"/>
                <filter string="Paid" name="paid" domain="[('state', '=', 'paid')]"/>
                <filter string="Processing" name="processing" domain="[('state', '=', 'processing')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <separator/>
                <filter string="Not Paid" name="not_paid" domain="[('payment_status', '=', 'not_paid')]"/>
                <filter string="Payment Pending" name="payment_pending" domain="[('payment_status', '=', 'pending')]"/>
                <filter string="Payment Paid" name="payment_paid" domain="[('payment_status', '=', 'paid')]"/>
                <filter string="Payment Failed" name="payment_failed" domain="[('payment_status', '=', 'failed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Service" name="groupby_service" context="{'group_by': 'service_id'}"/>
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Payment Status" name="groupby_payment_status" context="{'group_by': 'payment_status'}"/>
                    <filter string="Language" name="groupby_language" context="{'group_by': 'language_id'}"/>
                    <filter string="Customer" name="groupby_partner" context="{'group_by': 'partner_id'}"/>
                    <filter string="Expert" name="groupby_expert" context="{'group_by': 'expert_user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Conversation Views -->
    <record id="view_legal_document_conversation_form" model="ir.ui.view">
        <field name="name">legal.document.conversation.form</field>
        <field name="model">legal.document.conversation</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="request_id"/>
                        <field name="user_id"/>
                        <field name="is_ai"/>
                        <field name="create_date" readonly="1"/>
                    </group>
                    <group>
                        <field name="message"/>
                        <field name="has_form"/>
                        <field name="form_data" invisible="has_form == False"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Form Response Views -->
    <record id="view_legal_document_form_response_form" model="ir.ui.view">
        <field name="name">legal.document.form.response.form</field>
        <field name="model">legal.document.form.response</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="request_id"/>
                        <field name="conversation_id"/>
                        <field name="create_date" readonly="1"/>
                    </group>
                    <group>
                        <field name="form_data"/>
                        <field name="response_data"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_legal_document_request" model="ir.actions.act_window">
        <field name="name">Document Requests</field>
        <field name="res_model">legal.document.request</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>
