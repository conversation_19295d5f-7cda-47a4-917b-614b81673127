from odoo import http, _
from odoo.http import request
import logging
import json
from werkzeug.exceptions import NotFound

_logger = logging.getLogger(__name__)


class LegalDocumentController(http.Controller):
    
    @http.route(['/legal-documents'], type='http', auth='public', website=True)
    def legal_documents_home(self, **kw):
        """Display the legal documents home page with available services"""
        services = request.env['legal.document.service'].sudo().search([('active', '=', True)])
        service_types = request.env['legal.document.service.type'].sudo().search([])
        
        values = {
            'services': services,
            'service_types': service_types,
            'page_name': 'legal_documents_home',
        }
        
        return request.render('ai_legal_document_core.legal_documents_home', values)
    
    @http.route(['/legal-documents/service/<int:service_id>'], type='http', auth='public', website=True)
    def legal_document_service(self, service_id, **kw):
        """Display the service details page"""
        service = request.env['legal.document.service'].sudo().browse(service_id)
        if not service.exists() or not service.active:
            return NotFound()
        
        # Get available languages
        available_languages = service.language_ids or request.env['res.lang'].sudo().search([('active', '=', True)])
        
        values = {
            'service': service,
            'available_languages': available_languages,
            'page_name': 'legal_document_service',
        }
        
        return request.render('ai_legal_document_core.legal_document_service', values)
    
    @http.route(['/legal-documents/create'], type='http', auth='user', website=True, methods=['POST'])
    def create_legal_document_request(self, **post):
        """Create a new legal document request"""
        service_id = int(post.get('service_id', 0))
        language_id = int(post.get('language_id', 0))
        
        if not service_id or not language_id:
            return request.redirect('/legal-documents')
        
        service = request.env['legal.document.service'].sudo().browse(service_id)
        language = request.env['res.lang'].sudo().browse(language_id)
        
        if not service.exists() or not language.exists():
            return request.redirect('/legal-documents')
        
        # Create the document request
        vals = {
            'service_id': service_id,
            'language_id': language_id,
            'partner_id': request.env.user.partner_id.id,
            'state': 'draft',
        }
        
        request_id = request.env['legal.document.request'].sudo().create(vals)
        
        # Start the request
        request_id.action_start()
        
        # Redirect to the conversation page
        return request.redirect(f'/legal-documents/conversation/{request_id.id}')
    
    @http.route(['/legal-documents/conversation/<int:request_id>'], type='http', auth='user', website=True)
    def legal_document_conversation(self, request_id, **kw):
        """Display the conversation page for a document request"""
        document_request = request.env['legal.document.request'].sudo().browse(request_id)
        
        # Check if the request exists and belongs to the current user
        if not document_request.exists() or document_request.partner_id.id != request.env.user.partner_id.id:
            return request.redirect('/my/legal/requests')
        
        values = {
            'document_request': document_request,
            'conversations': document_request.conversation_ids,
            'page_name': 'legal_document_conversation',
        }
        
        return request.render('ai_legal_document_core.legal_document_conversation', values)
    
    @http.route(['/legal-documents/conversation/send'], type='json', auth='user', website=True)
    def send_message(self, **post):
        """Send a message in the conversation"""
        request_id = int(post.get('request_id', 0))
        message = post.get('message', '')
        
        if not request_id or not message:
            return {'error': 'Missing parameters'}
        
        document_request = request.env['legal.document.request'].sudo().browse(request_id)
        
        # Check if the request exists and belongs to the current user
        if not document_request.exists() or document_request.partner_id.id != request.env.user.partner_id.id:
            return {'error': 'Invalid request'}
        
        # Create the user message
        conversation = request.env['legal.document.conversation'].sudo().create({
            'request_id': request_id,
            'user_id': request.env.user.id,
            'is_ai': False,
            'message': message,
        })
        
        # TODO: In the next module, we'll add the AI response logic here
        
        return {
            'success': True,
            'conversation_id': conversation.id,
        }
