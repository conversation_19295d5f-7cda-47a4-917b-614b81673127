from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
from odoo.exceptions import AccessError, MissingError
from odoo.osv.expression import OR
import logging

_logger = logging.getLogger(__name__)


class LegalDocumentPortal(CustomerPortal):
    
    def _prepare_home_portal_values(self, counters):
        """Add legal document requests count to portal home page"""
        values = super()._prepare_home_portal_values(counters)
        
        if 'legal_document_count' in counters:
            legal_document_count = request.env['legal.document.request'].search_count([
                ('partner_id', '=', request.env.user.partner_id.id)
            ])
            values['legal_document_count'] = legal_document_count
            
        return values
    
    def _legal_document_get_page_view_values(self, document_request, access_token, **kwargs):
        """Prepare values for rendering portal pages"""
        values = {
            'page_name': 'legal_document',
            'document_request': document_request,
        }
        return self._get_page_view_values(document_request, access_token, values, 'my_legal_documents_history', False, **kwargs)
    
    @http.route(['/my/legal/requests', '/my/legal/requests/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_legal_documents(self, page=1, date_begin=None, date_end=None, sortby=None, filterby=None, search=None, search_in='content', **kw):
        """Display the list of legal document requests in the portal"""
        values = self._prepare_portal_layout_values()
        LegalDocumentRequest = request.env['legal.document.request']
        
        domain = [('partner_id', '=', request.env.user.partner_id.id)]
        
        # Archive groups - Default Group By 'create_date'
        archive_groups = self._get_archive_groups('legal.document.request', domain)
        
        # Search
        searchbar_sortings = {
            'date': {'label': _('Newest'), 'order': 'create_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'state': {'label': _('Status'), 'order': 'state'},
        }
        
        searchbar_filters = {
            'all': {'label': _('All'), 'domain': []},
            'draft': {'label': _('Draft'), 'domain': [('state', '=', 'draft')]},
            'in_progress': {'label': _('In Progress'), 'domain': [('state', '=', 'in_progress')]},
            'pending_payment': {'label': _('Pending Payment'), 'domain': [('state', '=', 'pending_payment')]},
            'paid': {'label': _('Paid'), 'domain': [('state', '=', 'paid')]},
            'processing': {'label': _('Processing'), 'domain': [('state', '=', 'processing')]},
            'completed': {'label': _('Completed'), 'domain': [('state', '=', 'completed')]},
            'cancelled': {'label': _('Cancelled'), 'domain': [('state', '=', 'cancelled')]},
        }
        
        searchbar_inputs = {
            'content': {'input': 'content', 'label': _('Search in Content')},
            'name': {'input': 'name', 'label': _('Search in Reference')},
            'service': {'input': 'service', 'label': _('Search in Service')},
            'all': {'input': 'all', 'label': _('Search in All')},
        }
        
        # Default sort by date
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']
        
        # Default filter by value
        if not filterby:
            filterby = 'all'
        domain += searchbar_filters[filterby]['domain']
        
        # Search
        if search and search_in:
            search_domain = []
            if search_in in ('content', 'all'):
                search_domain = OR([search_domain, [('document_text', 'ilike', search)]])
            if search_in in ('name', 'all'):
                search_domain = OR([search_domain, [('name', 'ilike', search)]])
            if search_in in ('service', 'all'):
                search_domain = OR([search_domain, [('service_id.name', 'ilike', search)]])
            domain += search_domain
        
        # Count for pager
        legal_document_count = LegalDocumentRequest.search_count(domain)
        
        # Pager
        pager = portal_pager(
            url="/my/legal/requests",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'filterby': filterby, 'search_in': search_in, 'search': search},
            total=legal_document_count,
            page=page,
            step=self._items_per_page
        )
        
        # Content according to pager and archive selected
        legal_documents = LegalDocumentRequest.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
        request.session['my_legal_documents_history'] = legal_documents.ids[:100]
        
        values.update({
            'date': date_begin,
            'legal_documents': legal_documents,
            'page_name': 'legal_document',
            'pager': pager,
            'archive_groups': archive_groups,
            'default_url': '/my/legal/requests',
            'searchbar_sortings': searchbar_sortings,
            'searchbar_filters': searchbar_filters,
            'searchbar_inputs': searchbar_inputs,
            'sortby': sortby,
            'filterby': filterby,
            'search_in': search_in,
            'search': search,
        })
        
        return request.render("ai_legal_document_core.portal_my_legal_documents", values)
    
    @http.route(['/my/legal/requests/<int:request_id>'], type='http', auth="user", website=True)
    def portal_my_legal_document(self, request_id=None, access_token=None, **kw):
        """Display a specific legal document request in the portal"""
        try:
            document_request = self._document_check_access('legal.document.request', request_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')
        
        values = self._legal_document_get_page_view_values(document_request, access_token, **kw)
        
        return request.render("ai_legal_document_core.portal_my_legal_document", values)
