/**
 * Legal Document Core Scripts
 * Simple implementation for Odoo 17 Community Edition
 */
$(document).ready(function() {
    'use strict';

    var LegalDocumentConversation = {
        /**
         * Initialize the conversation widget
         */
        init: function() {
            this.bindEvents();
            this.scrollToBottom();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;
            $(document).on('submit', '#conversation-form', function(e) {
                return self.onSubmitMessage(e);
            });
        },

        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------

        /**
         * Scrolls the conversation to the bottom
         */
        scrollToBottom: function () {
            var conversationDiv = document.querySelector('#conversation-messages');
            if (conversationDiv) {
                conversationDiv.scrollTop = conversationDiv.scrollHeight;
            }
        },

        /**
         * Adds a message to the conversation
         * @param {String} message - The message text
         * @param {Boolean} isAi - Whether the message is from the AI
         * @param {String} userName - The name of the user
         */
        addMessage: function (message, isAi, userName) {
            var conversationDiv = document.querySelector('#conversation-messages');
            if (!conversationDiv) return;

            var messageDiv = document.createElement('div');
            messageDiv.className = 'message mb-3 ' + (isAi ? 'ai-message' : 'user-message');

            var bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble p-3 rounded ' +
                (isAi ? 'bg-light' : 'bg-primary text-white');

            var headerDiv = document.createElement('div');
            headerDiv.className = 'message-header mb-2';

            var nameSpan = document.createElement('strong');
            nameSpan.textContent = isAi ? 'AI Assistant' : (userName || 'You');

            var timeSpan = document.createElement('small');
            timeSpan.className = 'text-muted ms-2';
            timeSpan.textContent = 'Just now';

            var contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = message;

            headerDiv.appendChild(nameSpan);
            headerDiv.appendChild(timeSpan);

            bubbleDiv.appendChild(headerDiv);
            bubbleDiv.appendChild(contentDiv);

            messageDiv.appendChild(bubbleDiv);

            conversationDiv.appendChild(messageDiv);
            this.scrollToBottom();
        },

        /**
         * Sends a message to the server
         * @param {String} message - The message text
         * @param {Number} requestId - The ID of the document request
         * @returns {Promise} - A promise that resolves when the message is sent
         */
        sendMessage: function (message, requestId) {
            return $.ajax({
                url: '/legal-documents/conversation/send',
                method: 'POST',
                data: {
                    request_id: requestId,
                    message: message,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json'
            });
        },

        //--------------------------------------------------------------------------
        // Handlers
        //--------------------------------------------------------------------------

        /**
         * Handles the submission of a new message
         * @param {Event} ev - The submit event
         */
        onSubmitMessage: function (ev) {
            ev.preventDefault();
            var self = this;

            var form = ev.currentTarget;
            var requestId = parseInt(form.querySelector('input[name="request_id"]').value);
            var message = form.querySelector('input[name="message"]').value;
            var userName = form.querySelector('input[name="message"]').dataset.userName;

            if (!message || !requestId) return;

            // Clear input
            form.querySelector('input[name="message"]').value = '';

            // Add message to conversation
            this.addMessage(message, false, userName);

            // Send message to server
            this.sendMessage(message, requestId)
                .then(function (result) {
                    if (result && result.success) {
                        console.log('Message sent successfully');
                        // Handle AI response here if needed
                    } else {
                        console.error('Error sending message:', result ? result.error : 'Unknown error');
                    }
                })
                .catch(function (error) {
                    console.error('AJAX error:', error);
                });
        }
    };

    // Initialize if conversation elements exist
    if ($('.legal-document-conversation').length) {
        LegalDocumentConversation.init();
    }

    // Export for global access
    window.LegalDocumentConversation = LegalDocumentConversation;
});
