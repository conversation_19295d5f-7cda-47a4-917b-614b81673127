/* Legal Document Styles */

/* Conversation Styles */
.message {
    display: flex;
    margin-bottom: 1rem;
}

.user-message {
    justify-content: flex-end;
}

.ai-message {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 80%;
    border-radius: 1rem !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
    border-bottom-right-radius: 0.25rem !important;
}

.ai-message .message-bubble {
    border-bottom-left-radius: 0.25rem !important;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.message-content {
    white-space: pre-wrap;
    word-break: break-word;
}

/* Form Styles */
.dynamic-form {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dynamic-form .form-group {
    margin-bottom: 1.5rem;
}

.dynamic-form label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.dynamic-form .form-text {
    font-size: 0.875rem;
}

/* Service Card Styles */
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Status Badge Styles */
.badge-draft {
    background-color: #6c757d;
}

.badge-in_progress {
    background-color: #17a2b8;
}

.badge-pending_payment {
    background-color: #ffc107;
    color: #212529;
}

.badge-paid {
    background-color: #28a745;
}

.badge-processing {
    background-color: #6610f2;
}

.badge-completed {
    background-color: #28a745;
}

.badge-cancelled {
    background-color: #dc3545;
}

/* Conversation Page Styles */
#conversation-messages {
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

#message-input {
    display: flex;
}

#message-input input {
    flex-grow: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#message-input button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
