{
    'name': 'Dynamic Cart Expiry',
    'version': '********.0',
    'summary': 'Manage cart expiration and price updates for abandoned carts',
    'description': """
        This module adds dynamic cart expiration functionality:
        - Set configurable expiration periods for shopping carts
        - Automatically refresh prices for abandoned carts
        - Notify customers about price changes when they return to abandoned carts
        - Implement tiered handling based on cart age and price change magnitude
        - Provide clear explanations for price changes
    """,
    'category': 'Website/eCommerce',
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'sale',
        'website_sale',
        'jewelry_management',
        'website_sale_stock',
        'web_editor',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/cron.xml',
        'data/system_parameters.xml',
        'data/config_parameter.xml',
        'views/sale_order_views.xml',
        'views/product_views.xml',  # Keep for backward compatibility
        'views/product_product_views.xml',
        'views/product_category_views.xml',
        'views/product_template_views.xml',
        'views/website_templates.xml',
        'views/testing_mode_views.xml',
    ],
    'assets': {
        'web.assets_frontend': [
            'ai_dynamic_cart_expiry/static/src/css/price_change_notice.css',
        ],
    },
    'demo': [],
    'installable': True,  # Re-enabled after fixing cart issues
    'application': False,
    'auto_install': False,
}
