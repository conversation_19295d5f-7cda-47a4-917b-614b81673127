from odoo import http, _
from odoo.http import request
import json
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)

class DynamicCartExpiry(http.Controller):
    
    @http.route('/shop/cart/check_validity', type='json', auth='public', website=True)
    def check_cart_validity(self):
        """Check if the current cart is valid or expired"""
        order = request.website.sale_get_order()
        if not order:
            return {'valid': True}
            
        result = order.check_cart_validity()
        
        # If cart is valid, check if prices need refresh
        if result.get('valid', False):
            significant_changes = order.check_cart_freshness()
            if significant_changes:
                result.update({
                    'price_changes': significant_changes,
                    'requires_confirmation': any(abs(change['percent_change']) > 5 for change in significant_changes)
                })
                
        return result
    
    @http.route('/shop/cart/refresh_prices', type='json', auth='public', website=True)
    def refresh_cart_prices(self):
        """Manually refresh prices for the current cart"""
        order = request.website.sale_get_order()
        if not order:
            return {'success': False, 'message': _("No active cart found")}
            
        significant_changes = order._update_jewelry_prices()
        
        return {
            'success': True,
            'price_changes': significant_changes,
            'requires_confirmation': any(abs(change['percent_change']) > 5 for change in significant_changes)
        }
    
    @http.route('/shop/cart/confirm_price_changes', type='json', auth='public', website=True)
    def confirm_price_changes(self):
        """Confirm price changes and proceed with checkout"""
        order = request.website.sale_get_order()
        if not order:
            return {'success': False, 'message': _("No active cart found")}
            
        # Mark the order as having confirmed price changes
        order.write({
            'last_price_update': datetime.now(),
        })
        
        return {'success': True}
    
    @http.route('/shop/cart/recover', type='http', auth='public', website=True)
    def recover_cart(self, **kw):
        """Handle cart recovery from abandoned cart email"""
        order_id = kw.get('order_id')
        access_token = kw.get('access_token')
        
        if not order_id or not access_token:
            return request.redirect('/shop/cart')
            
        try:
            order_id = int(order_id)
            order = request.env['sale.order'].sudo().browse(order_id)
            
            # Verify access token
            if order.access_token != access_token:
                return request.redirect('/shop/cart')
                
            # Set the recovered order as the current cart
            request.session['sale_order_id'] = order.id
            
            # Check validity and refresh prices
            order.check_cart_validity()
            significant_changes = order.check_cart_freshness()
            
            # Redirect to cart page
            return request.redirect('/shop/cart')
            
        except Exception as e:
            _logger.error("Error recovering cart: %s", str(e))
            return request.redirect('/shop/cart')
