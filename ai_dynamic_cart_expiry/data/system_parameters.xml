<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default system parameters for cart expiry -->
        <!-- Default cart expiry time in minutes (1440 = 24 hours) -->
        <record id="param_default_expiry_minutes" model="ir.config_parameter">
            <field name="key">ai_dynamic_cart_expiry.default_expiry_minutes</field>
            <field name="value">1440</field>
        </record>

        <!-- Default time in minutes before cart prices are refreshed (1440 = 24 hours) -->
        <record id="param_refresh_minutes" model="ir.config_parameter">
            <field name="key">ai_dynamic_cart_expiry.refresh_minutes</field>
            <field name="value">1440</field>
        </record>

        <!-- Default number of days before cart expires for non-jewelry products -->
        <record id="param_expiry_days" model="ir.config_parameter">
            <field name="key">ai_dynamic_cart_expiry.expiry_days</field>
            <field name="value">30</field>
        </record>

        <!-- Threshold percentage for significant price changes that require user confirmation -->
        <record id="param_significant_change_threshold" model="ir.config_parameter">
            <field name="key">ai_dynamic_cart_expiry.significant_change_threshold</field>
            <field name="value">5.0</field>
        </record>
    </data>
</odoo>
