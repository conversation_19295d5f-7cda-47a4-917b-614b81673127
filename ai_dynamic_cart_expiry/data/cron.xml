<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Cron job to expire abandoned carts -->
        <record id="ir_cron_expire_abandoned_carts" model="ir.cron">
            <field name="name">Expire Abandoned Carts</field>
            <field name="model_id" ref="model_sale_order"/>
            <field name="state">code</field>
            <field name="code">model._cron_expire_abandoned_carts()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
        </record>

        <!-- Cron job to refresh cart prices -->
        <record id="ir_cron_refresh_cart_prices" model="ir.cron">
            <field name="name">Refresh Cart Prices</field>
            <field name="model_id" ref="model_sale_order"/>
            <field name="state">code</field>
            <field name="code">model._cron_refresh_cart_prices()</field>
            <field name="interval_number">30</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>
