<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add cart expiry fields to product form -->
    <record id="view_product_form_inherit_cart_expiry" model="ir.ui.view">
        <field name="name">product.product.form.inherit.cart.expiry</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='general_information']" position="after">
                <page string="Variant Cart Expiry" name="variant_cart_expiry">
                    <group>
                        <field name="requires_realtime_price"/>
                        <field name="enable_cart_expiry"/>
                        <field name="cart_expiry_hours" invisible="enable_cart_expiry == False" string="Cart Expiry Minutes"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
