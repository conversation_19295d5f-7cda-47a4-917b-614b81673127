<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add cart expiry fields to sale order form -->
    <record id="view_order_form_inherit_cart_expiry" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.cart.expiry</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_term_id']" position="after">
                <field name="cart_expiry" invisible="state != 'draft'"/>
                <field name="is_expired" invisible="state != 'draft'"/>
                <field name="last_price_update" invisible="state != 'draft'"/>
            </xpath>
            <notebook position="inside">
                <page string="Price Change Logs" invisible="not price_change_log_ids">
                    <field name="price_change_log_ids" readonly="1">
                        <tree>
                            <field name="change_date"/>
                            <field name="product_id"/>
                            <field name="old_price"/>
                            <field name="new_price"/>
                            <field name="percent_change"/>
                            <field name="change_type"/>
                        </tree>
                    </field>
                </page>
            </notebook>
        </field>
    </record>

    <!-- Price Change Log views -->
    <record id="view_price_change_log_tree" model="ir.ui.view">
        <field name="name">sale.order.price.change.log.tree</field>
        <field name="model">sale.order.price.change.log</field>
        <field name="arch" type="xml">
            <tree string="Price Change Logs">
                <field name="order_id"/>
                <field name="product_id"/>
                <field name="old_price"/>
                <field name="new_price"/>
                <field name="percent_change"/>
                <field name="change_date"/>
                <field name="change_type"/>
            </tree>
        </field>
    </record>

    <record id="view_price_change_log_form" model="ir.ui.view">
        <field name="name">sale.order.price.change.log.form</field>
        <field name="model">sale.order.price.change.log</field>
        <field name="arch" type="xml">
            <form string="Price Change Log">
                <sheet>
                    <group>
                        <group>
                            <field name="order_id"/>
                            <field name="order_line_id"/>
                            <field name="product_id"/>
                        </group>
                        <group>
                            <field name="old_price"/>
                            <field name="new_price"/>
                            <field name="percent_change"/>
                            <field name="change_date"/>
                            <field name="change_type"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_price_change_logs" model="ir.actions.act_window">
        <field name="name">Price Change Logs</field>
        <field name="res_model">sale.order.price.change.log</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_price_change_logs"
              name="Price Change Logs"
              parent="sale.menu_sale_report"
              action="action_price_change_logs"
              sequence="20"/>
</odoo>
