<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Testing Mode Configuration Form -->
    <record id="view_cart_expiry_testing_mode_form" model="ir.ui.view">
        <field name="name">cart.expiry.testing.mode.form</field>
        <field name="model">ir.config_parameter</field>
        <field name="arch" type="xml">
            <form string="Cart Expiry Testing Mode">
                <sheet>
                    <group>
                        <field name="key" invisible="1"/>
                        <field name="value" widget="boolean" string="Enable Testing Mode" invisible="key != 'ai_dynamic_cart_expiry.testing_mode_enabled'"/>
                        <field name="value" string="Testing Mode Expiry (seconds)" invisible="key != 'ai_dynamic_cart_expiry.testing_mode_expiry_seconds'"/>
                    </group>
                    <div class="alert alert-info" role="alert">
                        <p>When testing mode is enabled, cart expiry will be set to the specified number of seconds instead of using the normal configuration. This allows for quick testing of the cart expiry functionality.</p>
                        <p>To enable testing mode, set the "Enable Testing Mode" field to True and specify the number of seconds in the "Testing Mode Expiry (seconds)" field.</p>
                        <p>Remember to disable testing mode when you're done testing!</p>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Testing Mode Configuration Action -->
    <record id="action_cart_expiry_testing_mode" model="ir.actions.act_window">
        <field name="name">Cart Expiry Testing Mode</field>
        <field name="res_model">ir.config_parameter</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_cart_expiry_testing_mode_form"/>
        <field name="domain">[('key', 'in', ['ai_dynamic_cart_expiry.testing_mode_enabled', 'ai_dynamic_cart_expiry.testing_mode_expiry_seconds'])]</field>
        <field name="context">{'create': False, 'delete': False}</field>
        <field name="target">new</field>
    </record>

    <!-- Menu Item for Testing Mode -->
    <menuitem id="menu_cart_expiry_testing_mode"
              name="Testing Mode"
              parent="website_sale.menu_ecommerce_settings"
              action="action_cart_expiry_testing_mode"
              sequence="50"/>
</odoo>
