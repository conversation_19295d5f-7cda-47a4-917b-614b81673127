<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add cart expiry fields to product category form -->
    <record id="view_product_category_form_inherit_cart_expiry" model="ir.ui.view">
        <field name="name">product.category.form.inherit.cart.expiry</field>
        <field name="model">product.category</field>
        <field name="inherit_id" ref="product.product_category_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='first']" position="after">
                <group string="Cart Expiry">
                    <field name="requires_realtime_price"/>
                    <field name="enable_cart_expiry"/>
                    <field name="cart_expiry_hours" invisible="enable_cart_expiry == False" string="Cart Expiry Minutes"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Add cart expiry fields to public category form -->
    <record id="view_product_public_category_form_inherit_cart_expiry" model="ir.ui.view">
        <field name="name">product.public.category.form.inherit.cart.expiry</field>
        <field name="model">product.public.category</field>
        <field name="inherit_id" ref="website_sale.product_public_category_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='parent_id']" position="after">
                <field name="requires_realtime_price"/>
                <field name="enable_cart_expiry"/>
                <field name="cart_expiry_hours" invisible="enable_cart_expiry == False" string="Cart Expiry Minutes"/>
            </xpath>
        </field>
    </record>
</odoo>
