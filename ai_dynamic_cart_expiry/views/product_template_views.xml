<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add cart expiry fields to product template form -->
    <record id="view_product_template_form_inherit_cart_expiry" model="ir.ui.view">
        <field name="name">product.template.form.inherit.cart.expiry</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='general_information']" position="after">
                <page string="Cart Expiry" name="cart_expiry" invisible="product_variant_count == 0">
                    <group>
                        <field name="requires_realtime_price"/>
                        <field name="enable_cart_expiry"/>
                        <field name="cart_expiry_hours" invisible="enable_cart_expiry == False" string="Cart Expiry Minutes"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
