<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add price change notification to cart page -->
    <template id="cart_lines_price_change" inherit_id="website_sale.cart_lines">
        <xpath expr="//div[@id='cart_products']" position="before">
            <t t-if="request.session.get('price_updated', False)">
                <div class="alert alert-warning" role="alert">
                    <strong>Price Update:</strong> Some prices have been updated to reflect current market rates.
                </div>
            </t>
            <t t-if="website_sale_order and website_sale_order.is_expired">
                <div class="alert alert-warning" role="alert">
                    <strong>Cart Expired:</strong> Your cart was inactive for an extended period. Prices have been updated to reflect current market rates.
                </div>
            </t>
        </xpath>
    </template>


</odoo>
