from odoo import models, fields, api

class ProductCategory(models.Model):
    _inherit = 'product.category'

    enable_cart_expiry = fields.Boolean(
        string="Enable Cart Expiry",
        default=False,
        help="Enable cart expiry for products in this category"
    )
    cart_expiry_hours = fields.Float(
        string="Cart Expiry Minutes",
        default=1440.0,  # 24 hours = 1440 minutes
        help="Number of minutes before cart expires for products in this category"
    )
    requires_realtime_price = fields.Boolean(
        string="Requires Realtime Price Computation",
        default=False,
        help="Products in this category require realtime price computation"
    )
