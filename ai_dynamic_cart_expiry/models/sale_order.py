from odoo import models, fields, api, _
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    cart_expiry = fields.Datetime(string="Cart Expiry Date",
                                 help="Date when the cart will expire if not confirmed")
    is_expired = fields.Boolean(string="Is Expired", default=False,
                               help="Indicates if the cart has expired")
    last_price_update = fields.Datetime(string="Last Price Update",
                                       help="Date when prices were last updated")
    price_change_log_ids = fields.One2many('sale.order.price.change.log', 'order_id',
                                          string="Price Change Logs")

    @api.model
    def create(self, vals):
        """Set cart expiration when creating order"""
        res = super(SaleOrder, self).create(vals)
        res.last_price_update = fields.Datetime.now()
        # Cart expiry will be calculated when products are added
        return res

    def calculate_cart_expiry(self):
        """Calculate cart expiry based on products in the cart"""
        self.ensure_one()

        # Check if testing mode is enabled
        testing_mode = self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.testing_mode_enabled', 'False').lower() == 'true'

        if testing_mode:
            # Use testing mode expiry time in seconds
            testing_seconds = int(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.testing_mode_expiry_seconds', '60'))
            _logger.info("Testing mode enabled. Setting cart expiry to %s seconds from now.", testing_seconds)
            self.cart_expiry = fields.Datetime.now() + timedelta(seconds=testing_seconds)
            return

        if not self.order_line:
            # If cart is empty, use default expiry
            default_minutes = float(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.default_expiry_minutes', '1440.0'))
            self.cart_expiry = fields.Datetime.now() + timedelta(minutes=default_minutes)
            return

        # Check if any product requires cart expiry
        requires_expiry = False
        min_expiry_minutes = float(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.default_expiry_minutes', '1440.0'))

        for line in self.order_line:
            product = line.product_id
            product_tmpl = line.product_id.product_tmpl_id

            # Check if either product or product template requires cart expiry
            if (hasattr(product, 'requires_cart_expiry') and product.requires_cart_expiry()) or \
               (hasattr(product_tmpl, 'requires_cart_expiry') and product_tmpl.requires_cart_expiry()):
                requires_expiry = True

                # Get expiry minutes from product or product template
                if hasattr(product, 'get_cart_expiry_hours'):
                    # The method name is still get_cart_expiry_hours but it returns hours, so we convert to minutes
                    product_expiry_minutes = product.get_cart_expiry_hours() * 60.0
                elif hasattr(product_tmpl, 'get_cart_expiry_hours'):
                    # The method name is still get_cart_expiry_hours but it returns hours, so we convert to minutes
                    product_expiry_minutes = product_tmpl.get_cart_expiry_hours() * 60.0
                else:
                    product_expiry_minutes = min_expiry_minutes

                min_expiry_minutes = min(min_expiry_minutes, product_expiry_minutes)

        if requires_expiry:
            self.cart_expiry = fields.Datetime.now() + timedelta(minutes=min_expiry_minutes)
        else:
            # If no product requires expiry, use default expiry
            default_days = int(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.expiry_days', '30'))
            self.cart_expiry = fields.Datetime.now() + timedelta(days=default_days)

    def check_cart_validity(self):
        """Check if cart is still valid or expired"""
        self.ensure_one()

        if self.state != 'draft':
            # Only draft orders (carts) can expire
            return {'valid': True}

        if not self.cart_expiry:
            # Calculate expiry if not set
            self.calculate_cart_expiry()

        if fields.Datetime.now() > self.cart_expiry:
            self.is_expired = True
            return {
                'valid': False,
                'message': _("Your cart has expired due to significant time passing. Please review your items and current prices.")
            }

        return {'valid': True}

    def _cart_update(self, product_id=None, line_id=None, add_qty=0, set_qty=0, **kwargs):
        """Override cart update to recalculate cart expiry when products are added or removed"""
        result = super(SaleOrder, self)._cart_update(product_id=product_id, line_id=line_id, add_qty=add_qty, set_qty=set_qty, **kwargs)

        # Recalculate cart expiry
        self.calculate_cart_expiry()

        return result

    def _update_jewelry_prices(self):
        """Update prices for all jewelry items in cart and log changes"""
        self.ensure_one()
        significant_changes = []

        for line in self.order_line:
            if line.product_id.is_jewelry:
                product = line.product_id
                old_price = line.price_unit

                # Calculate current price based on current metal rates
                current_price = product._calculate_jewelry_price()

                # Calculate percent change
                percent_change = 0
                if old_price:
                    percent_change = ((current_price - old_price) / old_price) * 100

                # Check if price change is significant (e.g., >5%)
                if abs(percent_change) > 5:
                    significant_changes.append({
                        'product': product.name,
                        'old_price': old_price,
                        'new_price': current_price,
                        'percent_change': percent_change
                    })

                # Update price
                line.price_unit = current_price

                # Log the price change
                self.env['sale.order.price.change.log'].create({
                    'order_id': self.id,
                    'order_line_id': line.id,
                    'product_id': product.id,
                    'old_price': old_price,
                    'new_price': current_price,
                    'change_date': fields.Datetime.now(),
                })

        # Update last price update timestamp
        self.last_price_update = fields.Datetime.now()

        return significant_changes

    def check_cart_freshness(self):
        """Check if cart prices need mandatory refresh"""
        self.ensure_one()

        # If cart is older than 24 hours (1440 minutes), force price refresh
        refresh_minutes = int(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.refresh_minutes', '1440'))

        if not self.last_price_update or (fields.Datetime.now() - self.last_price_update).total_seconds() > refresh_minutes * 60:
            significant_changes = self._update_jewelry_prices()
            return significant_changes

        return []

    @api.model
    def _cron_expire_abandoned_carts(self):
        """Cron job to expire abandoned carts"""
        expired_carts = self.search([
            ('state', '=', 'draft'),
            ('cart_expiry', '<', fields.Datetime.now()),
            ('is_expired', '=', False)
        ])

        for cart in expired_carts:
            cart.write({'is_expired': True})
            _logger.info("Cart %s has been marked as expired", cart.name)

        return True

    @api.model
    def _cron_refresh_cart_prices(self):
        """Cron job to refresh prices for active carts"""
        refresh_minutes = int(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.refresh_minutes', '1440'))

        carts_to_refresh = self.search([
            ('state', '=', 'draft'),
            ('is_expired', '=', False),
            '|',
            ('last_price_update', '=', False),
            ('last_price_update', '<', fields.Datetime.now() - timedelta(minutes=refresh_minutes))
        ])

        for cart in carts_to_refresh:
            cart._update_jewelry_prices()
            _logger.info("Prices updated for cart %s", cart.name)

        return True
