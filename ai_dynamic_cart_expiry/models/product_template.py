from odoo import models, fields, api

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Define fields directly instead of using related fields
    enable_cart_expiry = fields.Boolean(
        string="Enable Cart Expiry",
        default=False,
        help="Enable cart expiry for this product"
    )
    cart_expiry_hours = fields.Float(
        string="Cart Expiry Minutes",
        default=1440.0,  # 24 hours = 1440 minutes
        help="Number of minutes before cart expires for this product"
    )
    requires_realtime_price = fields.Boolean(
        string="Requires Realtime Price Computation",
        default=False,
        help="This product requires realtime price computation"
    )

    # Sync values between template and variants
    @api.model
    def create(self, vals):
        template = super(ProductTemplate, self).create(vals)
        # Copy values to variants
        if template.product_variant_ids:
            for variant in template.product_variant_ids:
                variant.write({
                    'enable_cart_expiry': template.enable_cart_expiry,
                    'cart_expiry_hours': template.cart_expiry_hours,
                    'requires_realtime_price': template.requires_realtime_price,
                })
        return template

    def write(self, vals):
        result = super(ProductTemplate, self).write(vals)
        # Copy values to variants if they were changed
        if any(field in vals for field in ['enable_cart_expiry', 'cart_expiry_hours', 'requires_realtime_price']):
            for template in self:
                for variant in template.product_variant_ids:
                    variant_vals = {}
                    if 'enable_cart_expiry' in vals:
                        variant_vals['enable_cart_expiry'] = template.enable_cart_expiry
                    if 'cart_expiry_hours' in vals:
                        variant_vals['cart_expiry_hours'] = template.cart_expiry_hours
                    if 'requires_realtime_price' in vals:
                        variant_vals['requires_realtime_price'] = template.requires_realtime_price
                    if variant_vals:
                        variant.write(variant_vals)
        return result

    def get_cart_expiry_hours(self):
        """Get cart expiry hours for this product, considering category settings if not set at product level"""
        self.ensure_one()

        # If product has specific expiry settings, use those
        if self.enable_cart_expiry:
            return self.cart_expiry_hours / 60.0  # Convert minutes to hours

        # Check product categories
        category = self.categ_id
        while category:
            if category.enable_cart_expiry:
                return category.cart_expiry_hours / 60.0  # Convert minutes to hours
            category = category.parent_id

        # Check public categories
        for public_category in self.public_categ_ids:
            if public_category.enable_cart_expiry:
                return public_category.cart_expiry_hours / 60.0  # Convert minutes to hours

        # Use default from system parameters (in minutes, convert to hours)
        return float(self.env['ir.config_parameter'].sudo().get_param('ai_dynamic_cart_expiry.default_expiry_minutes', '1440.0')) / 60.0

    def requires_cart_expiry(self):
        """Check if this product requires cart expiry"""
        self.ensure_one()

        # If product is jewelry, it requires cart expiry
        if hasattr(self, 'is_jewelry') and self.is_jewelry:
            return True

        # If product has specific setting, use it
        if self.enable_cart_expiry or self.requires_realtime_price:
            return True

        # Check product categories
        category = self.categ_id
        while category:
            if category.enable_cart_expiry or category.requires_realtime_price:
                return True
            category = category.parent_id

        # Check public categories
        for public_category in self.public_categ_ids:
            if public_category.enable_cart_expiry or public_category.requires_realtime_price:
                return True

        return False
