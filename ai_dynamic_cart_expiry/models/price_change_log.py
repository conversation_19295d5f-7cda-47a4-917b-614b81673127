from odoo import models, fields, api

class SaleOrderPriceChangeLog(models.Model):
    _name = 'sale.order.price.change.log'
    _description = 'Price Change Log for Sale Orders'
    _order = 'change_date desc'
    
    order_id = fields.Many2one('sale.order', string="Sale Order", required=True, ondelete='cascade')
    order_line_id = fields.Many2one('sale.order.line', string="Order Line", ondelete='cascade')
    product_id = fields.Many2one('product.product', string="Product", required=True)
    old_price = fields.Float(string="Old Price", required=True)
    new_price = fields.Float(string="New Price", required=True)
    change_date = fields.Datetime(string="Change Date", required=True, default=fields.Datetime.now)
    percent_change = fields.Float(string="Percent Change", compute='_compute_percent_change', store=True)
    change_type = fields.Selection([
        ('silent', 'Silent Update'),
        ('notify', 'Notification'),
        ('confirm', 'Confirmation Required')
    ], string="Change Type", compute='_compute_change_type', store=True)
    
    @api.depends('old_price', 'new_price')
    def _compute_percent_change(self):
        for record in self:
            if record.old_price:
                record.percent_change = ((record.new_price - record.old_price) / record.old_price) * 100
            else:
                record.percent_change = 0
    
    @api.depends('percent_change')
    def _compute_change_type(self):
        for record in self:
            if abs(record.percent_change) <= 1:
                record.change_type = 'silent'
            elif abs(record.percent_change) <= 5:
                record.change_type = 'notify'
            else:
                record.change_type = 'confirm'
