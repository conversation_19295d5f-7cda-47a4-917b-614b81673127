from odoo import http, fields
from odoo.http import request
import json
import logging
import uuid


logger = logging.getLogger(__name__)


class ProductController(http.Controller):

    @http.route('/api/v3/get_products', type='http', auth='public', website=True)
    def get_products(self, **kwargs):
        logger.info('Received request to fetch all products.')

        # Fetch all products from the product.template model
        domain = []

        # Check for 'name' in query parameters and add to domain for search
        if 'name' in kwargs:
            domain.append(('name', 'ilike', kwargs.get('name')))

        if 'min_value' in kwargs and kwargs.get('min_value') != '':
            domain.append(('list_price', '>=', float(kwargs.get('min_value'))))
        if 'max_value' in kwargs and kwargs.get('max_value') != '':
            domain.append(('list_price', '<=', float(kwargs.get('max_value'))))

            # Determine sorting
        sort_order = kwargs.get('sort', 'asc')  # Default to 'asc' if not specified
        if sort_order == 'desc':
            order = 'list_price desc'
        else:
            order = 'list_price asc'

        # Fetch products based on the search domain
        products = request.env['product.template'].sudo().search(domain, order=order)

        if products:
            # Prepare product data
            product_data = [{'id': product.id, 'name': product.name, 'list_price': product.list_price} for product in
                            products]
            # Return JSON response
            return request.make_response(json.dumps(product_data), headers={'Content-Type': 'application/json'})
        else:
            return request.make_response(json.dumps({'error': 'No products found'}),
                                         headers={'Content-Type': 'application/json'}, status=404)

    @http.route('/api/v3/create_sale_order', type='json', auth='public', methods=['POST'])
    def create_sale_order(self, **kwargs):
        logger.info('Received request to create sale order with data: %s', kwargs)

        user_id = kwargs.get('user_id')
        products = kwargs.get('products')

        if not user_id or not products:
            return {
                'status': 'error',
                'message': 'User ID and products are required.'
            }

        user = request.env['res.users'].sudo().search([('id', '=', user_id)], limit=1)
        if not user:
            return {
                'status': 'error',
                'message': 'Invalid user ID.'
            }

        customer = user.partner_id
        if not customer:
            return {
                'status': 'error',
                'message': 'No partner associated with this user.'
            }

        sale_order_vals = {
            'partner_id': customer.id,
            'order_line': []
        }

        for product_data in products:
            product_id = product_data.get('product_id')
            quantity = product_data.get('quantity', 1)

            product = request.env['product.product'].sudo().search([('id', '=', product_id)], limit=1)
            if not product:
                return {
                    'status': 'error',
                    'message': f'Invalid product ID: {product_id}'
                }

            sale_order_vals['order_line'].append((0, 0, {
                'product_id': product.id,
                'product_uom_qty': quantity,
                'price_unit': product.list_price,
            }))

        sale_order = request.env['sale.order'].sudo().create(sale_order_vals)

        return {
            'status': 'success',
            'sale_order_id': sale_order.id,
            'sale_order_name': sale_order.name,
            'sale_order_access_token': sale_order.access_token,
        }

    @http.route('/api/v3/update_sale_order', type='json', auth='public', methods=['PUT'])
    def update_sale_order(self, **kwargs):
        logger.info('Received request to update sale order with data: %s', kwargs)

        sale_order_id = kwargs.get('sale_order_id')
        products = kwargs.get('products')

        if not sale_order_id or not products:
            return {
                'status': 'error',
                'message': 'Sale Order ID and products are required.'
            }

        sale_order = request.env['sale.order'].sudo().search([('id', '=', sale_order_id)], limit=1)
        if not sale_order:
            return {
                'status': 'error',
                'message': 'Invalid Sale Order ID.'
            }

        # Clear existing order lines
        sale_order.order_line.unlink()

        # Add new or updated order lines
        for product_data in products:
            product_id = product_data.get('product_id')
            quantity = product_data.get('quantity', 1)

            product = request.env['product.product'].sudo().search([('id', '=', product_id)], limit=1)
            if not product:
                return {
                    'status': 'error',
                    'message': f'Invalid product ID: {product_id}'
                }

            sale_order.write({
                'order_line': [(0, 0, {
                    'product_id': product.id,
                    'product_uom_qty': quantity,
                    'price_unit': product.list_price,
                })]
            })

        return {
            'status': 'success',
            'sale_order_id': sale_order.id,
            'sale_order_name': sale_order.name,
        }

    @http.route('/api/v3/delete_sale_order', type='json', auth='public', methods=['DELETE'])
    def delete_sale_order(self, **kwargs):
        logger.info('Received request to delete sale order with data: %s', kwargs)

        sale_order_id = kwargs.get('sale_order_id')

        if not sale_order_id:
            return {
                'status': 'error',
                'message': type(sale_order_id)
            }

        sale_order = request.env['sale.order'].sudo().search([('id', '=', sale_order_id)], limit=1)
        if not sale_order:
            return {
                'status': 'error',
                'message': 'Invalid Sale Order ID.'
            }

        sale_order.unlink()

        return {
            'status': 'success',
            'message': f'Sale Order {sale_order_id} has been deleted.'
        }

    @http.route('/api/v3/payment', type='json', auth='public', methods=['POST'])
    def payment_and_invoice(self, **kwargs):
        sale_order_id = kwargs.get('sale_order_id')
        provider_id = kwargs.get('provider_id')  # Payment provider
        amount = kwargs.get('amount')

        if not sale_order_id or not provider_id or not amount:
            return {"error": "Missing required parameters"}

        # Fetch Sale Order
        sale_order = request.env['sale.order'].browse(sale_order_id)
        if not sale_order.exists():
            return {"error": "Sale order not found"}

        # Step 1: Create Payment Transaction
        payment_transaction = request.env['payment.transaction'].create({
            'amount': amount,
            'provider_id': provider_id,  # Correct field to link provider
            'currency_id': sale_order.currency_id.id,
            'partner_id': sale_order.partner_id.id,
            'sale_order_ids': [(6, 0, [sale_order.id])],
            'reference': sale_order.name,
        })

        # Step 2: Confirm Payment Transaction
        payment_transaction.sudo()._set_transaction_done()

        if payment_transaction.state != 'done':
            return {"error": "Payment transaction failed"}

        # Step 3: Create Invoice from Sale Order
        invoice = sale_order._create_invoices()

        # Step 4: Register Payment on Invoice
        payment = request.env['account.payment'].create({
            'amount': invoice.amount_total,
            'payment_type': 'inbound',
            'partner_type': 'customer',
            'partner_id': invoice.partner_id.id,
            'partner_bank_id': sale_order.partner_id.bank_ids[:1].id if sale_order.partner_id.bank_ids else False,
            'journal_id': payment_transaction.provider_id.journal_id.id,
            'payment_method_id': request.env.ref('account.account_payment_method_manual_in').id,
        })
        payment.action_post()

        # Step 5: Reconcile Payment with Invoice
        invoice.js_assign_outstanding_line(payment.line_ids.id)

        return {
            "success": True,
            "payment_transaction_id": payment_transaction.id,
            "invoice_id": invoice.id,
            "payment_id": payment.id,
            "transaction_state": payment_transaction.state,
            "invoice_state": invoice.state,
            "payment_state": payment.state,
        }