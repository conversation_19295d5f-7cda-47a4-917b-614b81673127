# -*- coding: utf-8 -*-
{
    'name': 'Document Drafting',
    'version': '1.0',
    'category': 'Administration',
    'summary': 'Document Drafting module.',
    'description': 'Document Drafting modeule for gathering data of various doucment drafting services like RTI, Cheque return, etc . Please note you need to install magic libary before installing this module which can be done using  pip install python-magic',
    'author': '<PERSON><PERSON><PERSON>',
    'website': 'https://arihantai.com/',
    'depends': [
        'base',
        'website',
        'product',
    ],
    'data': [
        
        'security/ir.model.access.csv',
        'views/views.xml',
        'data/data.xml',
        'views/service_details.xml',
        'views/faqs.xml',
        'views/tips.xml',
        'views/about.xml',
        'views/topics_views.xml',
        'views/tutorials_views.xml',
        'views/howitsworks.xml',
        'views/web_template.xml',
        'data/website_menu.xml',
        'views/page_record.xml',
        'views/portal_page.xml',
        
    ],
    
    'installable': True,
    'auto_install': False,
    'application': True,
    
'license': 'LGPL-3',

}