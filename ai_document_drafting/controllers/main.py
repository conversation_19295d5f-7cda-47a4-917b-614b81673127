from odoo import http
from odoo.http import request
import json
import base64
import datetime


class Document_Drafting_Webpages(http.Controller):

    @http.route(['/document-drafting'], auth="public", website=True)
    def document_drafting_webpage(self, **kw):
        return request.render('ai_document_drafting.document_drafting_webpage')

    @http.route(['/howitsworks'], auth="public", website=True)
    def howitsworks(self, **kw):
        return request.render('ai_document_drafting.howitsworks')
    
    @http.route(['/sub-document-drafting'], auth="public", website=True)
    def howitsworks(self, **kw):
        return request.render('ai_document_drafting.document_drafting_sub_description')

    @http.route(['/document-drafting-description'], auth="public", website=True)
    def document_drafting_description(self, **kw):
        service_id = kw.get('service_id')
        if service_id:
            return http.request.render('ai_document_drafting.document_drafting_description', {
                'service_id': service_id
            })
        
    
    @http.route('/sub-document-drafting', auth='public', website=True)
    def sub_document_drafting(self, **kw):
        service_id = kw.get('service_id')
        state_id = kw.get('state_id')
        
        # You can handle logic here based on service_id and state_id
        if service_id and state_id:
            return request.render('ai_document_drafting.sub_document_drafting_template', {
                'service_id': service_id,
                'state_id': state_id
            })

class DocumentDraftingPortal(http.Controller):

    @http.route(['/my/services'], type='http', auth="user", website=True)
    def services_listing(self, **kwargs):
        # Fetch services without a parent category
        services = request.env['document_drafting'].sudo().search([('x_service_id', '=', False)])
        return request.render('ai_document_drafting.portal_my_services', {'services': services})

    @http.route(['/my/services/<int:service_id>'], type='http', auth="user", website=True)
    def service_detail(self, service_id, **kwargs):
        # Fetch service details based on ID
        service = request.env['document_drafting'].sudo().browse(service_id)
        return request.render('ai_document_drafting.portal_my_service', {'service': service})

 
