from odoo import models, fields, api
import base64
import os
# import magic
import logging
import mimetypes
import re
_logger = logging.getLogger(__name__)

class document_drafting(models.Model):
    _name = 'document_drafting'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Drafting services'
    name = fields.Char(string='Name')
    x_description = fields.Char(string='Service short Description')
    x_state_id = fields.Many2one('res.country.state', string='State', domain=[('country_id', '=', 104)])
    x_language_id = fields.Many2many('res.lang', 'document_drafting_lang_rel', 'document_drafting_id', 'lang_id', string='Languages')
    x_service_id = fields.Many2one('document_drafting', string='Service Category')
    x_product_ids = fields.Many2one('product.template', string='Product')
    x_custom_url = fields.Char(string='Custom URL')
    x_desc = fields.Text(string='Description about Service')
    x_image = fields.Binary(string='Image')
    x_tips = fields.One2many('tips', 'x_tips_id', string='Tips')
    x_about = fields.One2many('about', 'x_about_id', string='About the service')
    x_howitsworks = fields.One2many('howitsworks', 'x_howitsworks_id', string='How its works')
    x_faqs = fields.One2many('faqs', 'x_faq_id', string='FAQS')
    x_cost = fields.Char(string='Cost')
    x_dynamic_fields = fields.One2many('dynamic.field', 'x_service_id', string='Dynamic Fields')
    x_topics = fields.One2many('topics', 'x_service_id', string='Topics')
    x_status = fields.Selection([('draft', 'Draft'), ('paid', 'Paid')], string='Status', default='draft')


    
    @api.model
    def create(self, vals):
        record = super(document_drafting, self).create(vals)
        record._create_dynamic_fields()
        return record

    def write(self, vals):
        result = super(document_drafting, self).write(vals)
        self._create_dynamic_fields()
        return result

    def _create_dynamic_fields(self):
        for field in self.x_dynamic_fields:
            field_name = field.name
            field_type = field.field_type
            if field_type == 'char':
                self._add_dynamic_field(field_name, fields.Char)
            elif field_type == 'text':
                self._add_dynamic_field(field_name, fields.Text)
            elif field_type == 'integer':
                self._add_dynamic_field(field_name, fields.Integer)
            elif field_type == 'boolean':
                self._add_dynamic_field(field_name, fields.Boolean)
            # elif field_type == 'many2one':
            #     relation_model = field.relation_model
            #     self._add_dynamic_field(field_name, fields.Many2one, relation_model)

    def _add_dynamic_field(self, field_name, field_type, relation_model=None):
        if not hasattr(self, field_name):
            if relation_model:
                field = field_type(comodel_name=relation_model, string=field_name.replace('_', ' ').title())
            else:
                field = field_type(string=field_name.replace('_', ' ').title())
            self._add_field(field_name, field)
            self._fields[field_name] = field



class DynamicField(models.Model):
    _name = 'dynamic.field'
    _description = 'Dynamic Field'
    name = fields.Char(string='Field Name', required=True)
    label = fields.Char(string='Field Label', required=True)
    field_type = fields.Selection([
        ('char', 'Char'),
        ('text', 'Text'),
        ('integer', 'Integer'),
        ('boolean', 'Boolean'),
        # ('many2one', 'Many2one'),
    ], string='Field Type', required=True)
    relation_model = fields.Char(string='Relation Model')
    x_service_id = fields.Many2one('document_drafting', string='Service', required=True)

class FAQs(models.Model):
    _name = 'faqs'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description =  'FAQs'
    name = fields.Char(string='Name', compute='_compute_full_name')
    x_question =  fields.Char(string='Questions')  
    x_answers =  fields.Text(string='Answers')
    x_faq_id = fields.Many2one('document_drafting',string='Services') 
    x_is_in_cart = fields.Boolean(string='Is in cart ?', default=False)
    x_in_sub_service = fields.Boolean(string='Is in sub-service ?', default=False)
    x_sequence = fields.Integer(string='Sequence')

    @api.depends('x_question', 'x_faq_id')
    def _compute_full_name(self):
        for record in self:
            full_name = ' '.join(filter(None, [record.x_question, record.x_faq_id.name]))
            record.name = full_name

class Tips(models.Model):
    _name = 'tips'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Tips'
    name = fields.Char(string='Name')
    x_tips_id = fields.Many2one('document_drafting', string='Service Category')
    x_inclusion = fields.Boolean(string='Inclusion', default=True)
    x_sequence = fields.Integer(string='Sequence')

class Howitsworks(models.Model):
    _name = 'howitsworks'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'How its works'
    name = fields.Char(string='Name')
    x_howitsworks_id = fields.Many2one('document_drafting', string='Services Category')
    x_image = fields.Binary(string='Image')
    x_description = fields.Text(string='Description')
    x_sequence = fields.Integer(string='Sequence')

class About(models.Model):
    _name = 'about'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'About'
    name = fields.Char(string='Name')
    x_about_id = fields.Many2one('document_drafting', string='Services')
    x_inclusion = fields.Boolean(string='Inclusion',default=True)
    x_sequence = fields.Integer(string='Sequence')

class Topics(models.Model):
    _name = 'topics'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Topics'
    name = fields.Char(string='Name')
    x_background_image = fields.Binary(string='Background Image')
    x_service_id = fields.Many2one('document_drafting', string='Service')
    x_tutorial_ids = fields.One2many('tutorials', 'x_topic_id', string='Tutorials')



class Tutorials(models.Model):
    _name = 'tutorials'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Tutorials'
    name = fields.Char(string='Name')
    x_description = fields.Text(string='Story Description')
    x_background_colour = fields.Char(string='Background Colour')
    x_sequence = fields.Integer(string='Sequence')
    x_topic_id = fields.Many2one('topics', string='Topic')
    x_file = fields.Binary(string='File')
    x_file_link = fields.Char(string='File Link', compute='_compute_file_link', store=True, readonly=False)
    x_file_type = fields.Char(string='File Type')

    @api.model
    def create(self, vals):
        record = super(Tutorials, self).create(vals)
        if record.x_file:
            record.transfer_to_static_folder()
        return record

    def transfer_to_static_folder(self):
        for record in self:
            # Ensure the directory exists
            directory = '/usr/lib/python3/dist-packages/odoo/addons/website/static/assets/legaldocs/' + str(record.id) + '/'
            if not os.path.exists(directory):
                os.makedirs(directory)
            

            # Get the data of the file
            if record.x_file:
                file_data = base64.b64decode(record.x_file)

                # Determine the file type using magic
                mime = magic.Magic(mime=True)
                file_type = mime.from_buffer(file_data)
                file_ext = mimetypes.guess_extension(file_type) or '.unknown'

                # Specify the path where the file will be stored
                file_path = os.path.join(directory, 'file' + file_ext)

                # Write the file data to the specified path
                with open(file_path, 'wb') as file:
                    file.write(file_data)
                

                # Update the computed file link
                base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                record.x_file_link = f"/website/static/assets/legaldocs/{record.id}/file{file_ext}"
                # base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                # record.x_file_link = f"{base_url}/website/static/assets/legaldocs/{record.id}/file{file_ext}"
                record.x_file = None

    @api.depends('x_file_type')
    def _compute_file_link(self):
        for record in self:
            
            record.x_file_link = f"/website/static/assets/legaldocs/{record.id}/file.{record.x_file_type}"
            # base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            # record.x_file_link = f"{base_url}/website/static/assets/legaldocs/{record.id}/file.{record.x_file_type}"


class ResLang(models.Model):
    _inherit = 'res.lang'

    x_language_symbol = fields.Char(string='Language Symbol')