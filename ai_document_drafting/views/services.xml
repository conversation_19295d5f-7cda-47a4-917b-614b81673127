<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_services" model="ir.actions.act_window">
            <field name="name">Services</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">services.category</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_services_tree" model="ir.ui.view">
            <field name="name">services.tree</field>
            <field name="model">services.category</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_services_form" model="ir.ui.view">
            <field name="name">services.form</field>
            <field name="model">services.category</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                    <group>
                    
                        <field name="name"/>
                      
                     </group>
                     </sheet>
                     <div class="oe_chatter">
                               <field name="message_follower_ids"/>
                               <field name="activity_ids"/>
                               <field name="message_ids"
                                      options="{'post_refresh': 'recipients'}"/>
                            </div>
                 </form>
            </field>
        </record>

          


    
        <!-- <menuitem id="menu_settings"  parent="menu_diary_management"  name="Settings" sequence="15"/> -->
        <!-- <menuitem id="menu_services" name="Services"  sequence="10"/> -->
         <!-- <menuitem id="menu_services" name="Services"  sequence="10"/> -->
        <menuitem id="menu_document_drafting_cat"  parent="menu_document_drafting" name="Service Categories" action="ai_document_drafting.action_document_drafting" sequence="8"/>


    </data>
</odoo>