<odoo>
    <data>
        <record id="view_topics_tree" model="ir.ui.view">
            <field name="name">topics.tree</field>
            <field name="model">topics</field>
            <field name="arch" type="xml">
                <tree string="Topics">
                    <field name="name"/>
                    <field name="x_service_id"/>
                    <field name="x_background_image"/>

                </tree>
            </field>
        </record>

        <record id="view_topics_form" model="ir.ui.view">
            <field name="name">topics.form</field>
            <field name="model">topics</field>
            <field name="arch" type="xml">
                <form string="Topics">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="x_service_id"/>
                        <field name="x_background_image"/>
                    </group>
                    <notebook>
                        <page string="Tutorials">
                            <field name="x_tutorial_ids"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"
                           options="{'post_refresh': 'recipients'}"/>
                </div>
            </form>
        </field>
    </record>

    <record id="action_topics" model="ir.actions.act_window">
        <field name="name">Topics</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">topics</field>
        <field name="view_mode">tree,form</field>
    </record>
    <!-- <menuitem id="menu_services" name="Services" sequence="10"/> -->
    <menuitem id="topics" name="Topics" parent="menu_document_drafting" action="ai_document_drafting.action_topics" sequence="55"/>
    </data>
</odoo>

