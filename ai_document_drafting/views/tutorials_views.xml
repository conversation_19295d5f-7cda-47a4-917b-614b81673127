<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <!-- Tree View -->
        <record id="view_tutorials_tree" model="ir.ui.view">
            <field name="name">tutorials.tree</field>
            <field name="model">tutorials</field>
            <field name="arch" type="xml">
                <tree string="Tutorials">
                    <field name="name"/>
                    <field name="x_description"/>
                    <field name="x_background_colour"/>
                    <field name="x_sequence"/>
                    <field name="x_topic_id"/>
                    <!-- <field name="x_file_type"/> -->
                    <field name="x_file_link"/>
                </tree>
            </field>
        </record>

        <!-- Form View -->
        <record id="view_tutorials_form" model="ir.ui.view">
            <field name="name">tutorials.form</field>
            <field name="model">tutorials</field>
            <field name="arch" type="xml">
                <form string="Tutorials">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="x_description"/>
                            <field name="x_background_colour"/>
                            <field name="x_sequence"/>
                            <field name="x_topic_id"/>
                        </group>
                        <group>
                            <field name="x_file" widget="binary"/>
                            <!-- <field name="x_file_type"/> -->
                            <field name="x_file_link"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                               <field name="message_follower_ids"/>
                               <field name="activity_ids"/>
                               <field name="message_ids"
                                      options="{'post_refresh': 'recipients'}"/>
                            </div>
                </form>
            </field>
        </record>

      



    <record id="action_tutorials" model="ir.actions.act_window">
        <field name="name">Tutorials</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">tutorials</field>
        <field name="view_mode">tree,form</field>   
    </record>
    <!-- <menuitem id="topics" name="Topics" parent="menu_document_drafting" action="ai_document_drafting.action_topics" sequence="55"/> -->
    <menuitem id="tutorials" name="Tutorials" parent="menu_document_drafting" action="ai_document_drafting.action_tutorials" />
</data>
</odoo>
