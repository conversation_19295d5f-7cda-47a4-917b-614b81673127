<odoo>
	<template id="document_drafting_webpage" name="document_drafting">
		<t t-call="website.layout">
			<div id="wrap" class="oe_structure oe_empty">
				<t t-set="services" t-value="request.env['document_drafting'].search([])"/>
				<section class="s_text_block pt40 pb40 o_colored_level" data-snippet="s_text_block" data-name="Text" style="background-image: none;">
					<div class="s_allow_columns container">
						<h3 style="text-align: center;">Oneclickvalkil Document Drafting Services</h3>
						<form>
							<div role="search" class="input-group ">
								<input type="text" id="serviceSearch" onkeyup="filterServices()" name="search" class="form-control" placeholder="Search for Document Drafting Services..." data-search-type="all" data-limit="5" data-display-image="true" data-display-description="true" data-display-extra-link="true" data-display-detail="true" data-order-by="name asc"/>
							</div>
						</form>
					</div>
				</section>
				<style>
                    .card1{

                    height:300px;
                    width:250px;
                    }
                </style>
				<style>
                    .card-container {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    justify-content: left;
                    grid-template-columns: repeat(3, 0fr);
                    grid-template-rows: repeat(3, 0fr);
                    }

                    .card1 h3 {
                    color: #262626;
                    font-size: 17px;
                    line-height: 24px;
                    font-weight: 700;
                    margin-bottom: 4px;
                    }

                    p {
                    font-size: 17px;
                    font-weight: 400;
                    line-height: 20px;
                    color: #666666;


                    }

                    .go-corner {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    width: 32px;
                    height: 32px;
                    overflow: hidden;
                    top: 0;
                    right: 0;
                    
					<!--background-color: #00838d;-->
                    background-color: #FFE0b1;
                    border-radius: 0 4px 0 32px;
                    }

                    .go-arrow {
                    margin-top: -4px;
                    margin-right: -4px;
                    color: black;
                    font-family: courier, sans;
                    }

                    .card1 {
                    display: block;
                    position: relative;
                    max-width: 262px;
                    background-color: #f2f8f9;
                    border-radius: 4px;
                    padding: 32px 24px;
                    margin: 12px;
                    text-decoration: none;
                    z-index: 0;
                    overflow: hidden;
                    transition: all 0.3s ease-out;
                    }

                    .card1:before {
                    content: "";
                    color: black !important;
                    position: absolute;
                    z-index: -1;
                    top: -16px;
                    right: -16px;
                    
					<!--background: #00838d;-->
                    background: #FFE0b1;
                    height: 40px;
                    width: 40px;
                    border-radius: 32px;
                    transform: scale(1);
                    transform-origin: 50% 50%;
                    transition: transform 0.25s ease-out;
                    }

                    .card1:hover {
                    transition: all 0.3s ease-out;
                    text-decoration: none;

                    p {
                    transition: all 0.3s ease-out;
                    color: #000;
                    }
                    h3 {
                    transition: all 0.3s ease-out;
                    color: #000;
                    }
                    img{
                    transition: all 0.3s ease-out;
                    
					<!--color: #ffffff;-->
                    }
                    .helpimgs1{
                    transition: all 0.3s ease-out;

                    }
                    button{
                    transition: all 0.3s ease-out;
                    color: #ffffff;

                    }
                    }

                    .card1:hover:before {
                    transform: scale(21);
                    }

                
				</style>
				<div class="card-container container">
					<t t-foreach="services" t-as="service" t-if="not service.x_service_id">
						<a class="card1" t-attf-href="/document-drafting-description?service_id={{ service.id }}">
							<img class="helpimgs1" t-attf-src="data:image/jpeg;base64,{{ service.x_image }}" style="mix-blend-mode: multiply; height: 120px; border-radius: 10px;" loading="lazy"/>
							<h3>
								<t t-esc="service.name"/>
							</h3>
							<p class="small">
								<t t-esc="service.x_description"/>
							</p>
							<div class="go-corner">
								<div class="go-arrow">
                                    →
                                </div>
							</div>
							<!--<button type="button" t-attf-href="/card-designs-multiple-clone?service_id={{ service.id }}" class="btn btn-primary">View More</button>-->
						</a>
					</t>
					<!--    <script>-->
					<!--    function filterServices() {-->
					<!--        const searchInput = document.getElementById('serviceSearch').value.toLowerCase();-->
					<!--        const services = document.querySelectorAll('.card1');-->
					<!--        services.forEach(service => {-->
					<!--            const serviceName = service.querySelector('h3').innerText.toLowerCase();-->
					<!--            if (serviceName.includes(searchInput)) {-->
					<!--                service.style.display = '';-->
					<!--            } else {-->
					<!--                service.style.display = 'none';-->
					<!--            }-->
					<!--        });-->
					<!--   }-->
					<!--</script>-->
					<script>
                        function filterServices() {
                        const searchInput = document.getElementById('serviceSearch').value.toLowerCase();
                        const services = document.querySelectorAll('.card1');
                        const cardContainer = document.querySelector('.card-container');
                        let hasVisibleService = false;

                        services.forEach(service =&gt; {
                        const serviceName = service.querySelector('h3').innerText.toLowerCase();
                        if (serviceName.includes(searchInput)) {
                        service.style.display = '';
                        hasVisibleService = true;
                        } else {
                        service.style.display = 'none';
                        }
                        });

                        // Handle message display based on service visibility
                        let message = cardContainer.querySelector('h6');
                        if (hasVisibleService) {
                        // Remove message if it exists
                        if (message) {
                        message.remove();
                        }
                        } else {
                        // Create and display the message if not present
                        if (!message) {
                        message = document.createElement('h6');
                        message.style.textAlign = 'center';
                        message.style.width = '100%';
                        message.textContent = 'No service available';
                        cardContainer.appendChild(message);
                        } else {
                        // Update existing message
                        message.textContent = 'No service available';
                        }
                        }
                        }

                        // Ensure the function is available globally and runs after DOM content is loaded
                        document.addEventListener('DOMContentLoaded', () =&gt; {
                        // Initialize the service visibility check
                        filterServices();

                        // Add event listener to the search input field
                        const searchInput = document.getElementById('serviceSearch');
                        if (searchInput) {
                        searchInput.addEventListener('input', filterServices);
                        }
                        });
                    </script>
				</div>
			</div>
		</t>
	</template>
	<template id="document_drafting_description" name="document-drafting-description">
    <t t-call="website.layout">
          <t t-set="service_id" t-value="request.params.get('service_id')"/>
            <t t-set="des" t-value="request.env['document_drafting'].search([['id','=',int(service_id)]])"/>
            <t t-set="topics" t-value="request.env['topics'].search([['x_service_id','=',int(service_id)]])"/>
            <t t-set="faqs" t-value="request.env['faqs'].search([('x_in_sub_service','=','True'),('x_faq_id','=',int(service_id))])"/>
             <t t-set="states" t-value="request.env['res.country.state'].search([('country_id','=',104)])"/>
             <t t-set="state_id" t-value="request.params.get('state_id')"/>
             
            <div id="wrap" class="oe_structure oe_empty">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"/>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"/>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"/>
                <!--<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"/>-->
                <!--<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js" integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p" crossorigin="anonymous"/>-->
                <!--<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"/>-->
                <style>
                    .story-card {
                      cursor: pointer;
                      transition: transform 0.3s;
                      width: 100%;
                      height: 370px;
                      position: relative;
                      border-radius: 10px;
                      background-color: #f8f9fa;
                      color: #000;
                      display: flex;
                      flex-direction: column;
                      justify-content: flex-end;
                      align-items: center;
                      overflow: hidden;
                    }
                    .story-card:hover {
                      transform: scale(1.05);
                    }
                    .story-card img {
                      position: relative;
                      top: 0;
                      left: 0;
                      width: 100%;
                      height: 100%;
                      object-fit: cover;
                    }
                    .story-card .text {
                      position: relative;
                      bottom: 0;
                      width: 100%;
                      padding: 10px;
                      background: rgba(218, 165, 32, 0.7);
                      text-align: center;
                    }
                    .story-view {
                      position: fixed;
                      top: 0;
                      left: 100%;
                      width: 100%;
                      height: 100%;
                      background-color: rgba(0, 0, 0, 0.8);
                      transition: left 0.3s;
                      z-index: 1000;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    }
                    .story-container {
                      width: 100%;
                      max-width: 360px;
                      height: 720px;
                      position: relative;
                      background-color: #000;
                      display: flex;
                      flex-direction: column;
                      justify-content: space-between;
                      padding: 20px;
                      color: white;
                      text-align: center;
                      overflow: hidden;
                      border-radius: 10px;
                    }
                    .close-btn {
                      position: absolute;
                      top: 20px;
                      right: 20px;
                      background: none;
                      border: none;
                      color: white;
                      font-size: 30px;
                      cursor: pointer;
                      z-index: 1001;
                    }
                    .progress-container {
                      display: flex;
                      justify-content: space-between;
                      position: absolute;
                      top: 10px;
                      left: 10px;
                      right: 10px;
                    }
                    .progress-bar {
                      height: 4px;
                      background-color: rgba(255, 255, 255, 0.5);
                      flex-grow: 1;
                      margin: 0 2px;
                      position: relative;
                    }
                    .progress-bar-active::before {
                      content: "";
                      position: absolute;
                      top: 0;
                      left: 0;
                      height: 100%;
                      background-color: white;
                      animation: progress 5s linear forwards;
                    }
                    .story-content {
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                    }
                    .story-content h2 {
                      font-size: 24px;
                      margin-bottom: 10px;
                    }
                    .story-content p {
                      font-size: 18px;
                      margin-bottom: 20px;
                    }
                    .story-content video,
                    .story-content iframe {
                      width: 100%;
                      height: auto;
                      display: none;
                    }
                    .know-more-button {
                      background-color: #ffc107;
                      color: #000;
                      border: none;
                      border-radius: 5px;
                      padding: 10px 20px;
                      font-size: 18px;
                      cursor: pointer;
                    }
                    .know-more-button:hover {
                      background-color: #e0a800;
                    }
                    @keyframes progress {
                      from {
                        width: 0%;
                      }
                      to {
                        width: 100%;
                      }
                    }
                </style>
                <style>
                    .info-header {
                      color: black;
                      text-align: left;
                      margin: 2rem auto;
                    }
                    
                    .info-header h1 {
                      margin: 0;
                      font-size: 3rem;
                    }
                    
                    .info-main {
                      max-width: auto;
                      margin: 0 auto;
                    }
                    
                    .info-section {
                      display: flex;
                      align-items: center;
                      box-shadow: none;
                      max-width: 90%;
                      margin-bottom: 1rem;
                      margin: 0 auto;
                      gap: 10rem;
                    }
                    
                    .info-section img {
                      max-width: 500px;
                      height: auto;
                      max-height: 250px;
                      object-fit: cover;
                      border-radius: 10px;
                    }
                    
                    .info-text {
                      flex-grow: 1;
                      font-size: 0.9rem;
                      color: #333;
                    }
                    
                    .info-text p {
                      margin: 0 0 1rem;
                    }
                    
                    .apply-btn {
                      display: block;auto;
                      padding: 0.75rem 2rem;
                      background-color: #28a745;
                      color: white;
                      border: none;
                      border-radius: 4px;
                      cursor: pointer;
                      font-size: 0.9rem;
                      font-weight: bold;
                      white-space: nowrap;
                      text-decoration: none;
                    }
                    .apply-btn:hover {
                      background-color: #28a745;
                    }
                    
                    .other-forms {
                      text-align: center;
                      margin: 0 auto;
                      margin-top: 2rem;
                      padding: 45px;
                    }
                    
                    .other-forms h2 {
                      margin: 0 0 1rem;
                      font-size: 2rem;
                      font-weight: normal;
                    }
                    
                    .other-forms ul {
                      list-style-type: disc; /* This ensures the list has bullet points */
                      display: inline-block;
                      margin: 0rem; /* Adjust margin as needed */
                      margin-bottom: 2rem;
                      text-align: left; /* Align text to the left */
                    }
                    
                    .other-forms ul li {
                      margin-bottom: 0.3rem; /* Space between list items */
                      font-size: 1rem; /* Adjust font size as needed */
                    }
                    
                    .accordion-item {
                      width: 70%;
                      margin: 0 auto;
                      background-color: #f8f9fa;
                      border: 1px solid #e7e7e7;
                    }
                    
                    .accordion-button {
                      font-size: 1rem;
                      font-weight: 500;
                      color: #333;
                      background-color: #f8f9fa;
                      padding: 1rem 1.25rem;
                    }
                    
                    .accordion-button:not(.collapsed) {
                      color: #fff;
                      background-color: #9179b3;
                    }
                    
                    .accordion-body {
                      color: black;
                      font-size: 0.9rem;
                      padding: 1rem 1.5rem;
                    }
                    
                    /* Media Queries for Common Resolutions */
                    
                    /* 4K UHD (3840×2160) */
                    @media (min-width: 3840px) {
                      .info-header,
                      .info-section {
                        max-width: 3200px;
                      }
                      .info-header h1 {
                        font-size: 4rem;
                      }
                      .info-section {
                        gap: 15rem;
                      }
                      .info-section img {
                        max-width: 800px;
                        max-height: 300px;
                      }
                      .info-text {
                        font-size: 1.2rem;
                      }
                      .apply-btn {
                        padding: 1rem 3rem;
                        font-size: 1.5rem;
                        margin: 1rem 0 1rem auto;
                      }
                      .accordion-item {
                        width: 50%;
                        max-width: 2400px;
                      }
                    }
                    
                    /* QHD (2560×1440) */
                    @media (min-width: 2560px) and (max-width: 3839px) {
                      .info-header,
                      .info-section {
                        max-width: 2200px;
                      }
                      .info-header h1 {
                        font-size: 3.5rem;
                      }
                      .info-section {
                        gap: 12rem;
                      }
                      .info-section img {
                        max-width: 600px;
                        max-height: 250px;
                      }
                      .info-text {
                        font-size: 1.1rem;
                      }
                      .apply-btn {
                        margin: 1rem 0 1rem auto;
                      }
                      .accordion-item {
                        width: 60%;
                        max-width: 1800px;
                      }
                    }
                    
                    /* Full HD (1920×1080) */
                    @media (min-width: 1920px) and (max-width: 2559px) {
                      .info-header,
                      .info-section {
                        max-width: 1700px;
                      }
                      .info-section {
                        gap: 10rem;
                      }
                      .accordion-item {
                        width: 65%;
                        max-width: 1400px;
                      }
                    }
                    
                    /* Common High Resolutions */
                    @media (min-width: 1680px) and (max-width: 1919px) {
                      /* 1680×1050 */
                      .info-header,
                      .info-section {
                        max-width: 1500px;
                      }
                      .info-section {
                        gap: 8rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 70%;
                        max-width: 1300px;
                      }
                    }
                    
                    @media (min-width: 1600px) and (max-width: 1679px) {
                      /* 1600×900 */
                      .info-header,
                      .info-section {
                        max-width: 1400px;
                      }
                      .info-section {
                        gap: 7rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 70%;
                        max-width: 1200px;
                      }
                    }
                    
                    @media (min-width: 1440px) and (max-width: 1599px) {
                      /* 1440×900 */
                      .info-header,
                      .info-section {
                        max-width: 1300px;
                      }
                      .info-header h1 {
                        font-size: 2.75rem;
                      }
                      .info-section {
                        gap: 3rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 75%;
                        max-width: 1100px;
                      }
                    }
                    
                    /* Common Laptop and Monitor Resolutions */
                    @media (min-width: 1366px) and (max-width: 1439px) {
                      /* 1366×768 */
                      .info-header,
                      .info-section {
                        max-width: 1200px;
                      }
                      .info-header h1 {
                        font-size: 2.5rem;
                      }
                      .info-section {
                        gap: 5rem;
                      }
                      .info-section img {
                        max-width: 450px;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 80%;
                        max-width: 1000px;
                      }
                    }
                    
                    @media (min-width: 1280px) and (max-width: 1365px) {
                      /* 1280×1024 and 1280×800 */
                      .info-header,
                      .info-section {
                        max-width: 1100px;
                      }
                      info-header h1 {
                        font-size: 2.25rem;
                      }
                      .info-section {
                        gap: 4rem;
                      }
                      .info-section img {
                        max-width: 400px;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 80%;
                        max-width: 900px;
                      }
                    }
                    
                    /* Smaller Screens */
                    @media (min-width: 1024px) and (max-width: 1279px) {
                      /* 1024×768 */
                      .info-header,
                      .info-section {
                        max-width: 900px;
                      }
                      .info-header h1 {
                        font-size: 2rem;
                      }
                      .info-section {
                        gap: 3rem;
                      }
                      .info-section img {
                        max-width: 350px;
                      }
                      .info-text {
                        font-size: 0.85rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 85%;
                        max-width: 800px;
                      }
                    }
                    
                    @media (max-width: 1023px) {
                      /* Smaller than 1024×768 */
                      .info-header,
                      .info-section {
                        max-width: 90%;
                      }
                      .info-header h1 {
                        font-size: 1.75rem;
                      }
                      .info-section {
                        flex-direction: column;
                        align-items: center;
                        gap: 1.5rem;
                      }
                      .info-section img {
                        width: 100%;
                        max-width: 500px;
                      }
                      .info-text {
                        text-align: center;
                        font-size: 0.8rem;
                      }
                      .apply-btn {
                        padding: 0.75rem 2rem;
                        font-size: 1rem;
                      }
                      .other-forms h2 {
                        font-size: 1.5rem;
                      }
                      .other-forms {
                        padding: 3rem 1rem;
                      }
                      .accordion-item {
                        width: 90%;
                      }
                    }
                    
                    /* Tablets and Large Phones */
                    @media (min-width: 768px) and (max-width: 1023px) {
                      .accordion-item {
                        width: 80%;
                      }
                    }
                    
                    /* Mobile and Small Tablet */
                    @media (max-width: 768px) {
                      .info-header {
                        margin: 1rem auto;
                        margin-bottom: 1.5rem;
                      }
                      info-header h1 {
                        font-size: 1.5rem;
                      }
                      .apply-btn {
                        margin: 1rem auto;
                        padding: 0.6rem 1.5rem;
                        font-size: 0.9rem;
                      }
                      .form-buttons {
                        gap: 0.5rem;
                      }
                      .form-buttons button {
                        font-size: 0.75rem;
                        padding: 0.5rem 0.75rem;
                        min-width: 100px;
                      }
                      .accordion-item {
                        width: 95%;
                      }
                    }
                    
                    /* Small Mobile */
                    @media (max-width: 480px) {
                      .apply-btn {
                        margin: 1rem auto;
                      }
                      info-header h1 {
                        font-size: 1.25rem;
                      }
                      .info-section img {
                        max-height: 150px;
                      }
                      .info-text {
                        font-size: 0.7rem;
                      }
                      .other-forms h2 {
                        font-size: 1.25rem;
                      }
                      .other-forms {
                        padding: 2rem 0.5rem;
                      }
                      .form-buttons button {
                        min-width: 80px;
                      }
                      .accordion-item {
                        width: 100%;
                      }
                      .other-forms {
                        padding: 0;
                      }
                    }
                    
                    /* Extra Small Mobile */
                    @media (max-width: 360px) {
                      info-header h1 {
                        font-size: 1.1rem;
                      }
                      .other-forms h2 {
                        font-size: 1.1rem;
                      }
                      .info-section img {
                        max-height: 120px;
                      }
                      .apply-btn {
                        padding: 0.5rem 1.25rem;
                        font-size: 0.8rem;
                      }
                      .form-buttons button {
                        font-size: 0.7rem;
                        padding: 0.4rem 0.6rem;
                        min-width: 70px;
                      }
                      .apply-btn {
                        margin: 1rem auto;
                      }
                    }
                </style>
             <div class="info-main" t-if="des">
                <section class="info-section pt88 pb88 d-flex">
                    <!-- Info Section (Left) -->
                    <div class="info-text col-6">
                        <t t-if="des.x_image">
                            <img t-attf-src="data:image/jpeg;base64,{{des.x_image}}" style="mix-blend-mode: multiply; width: 100%;"/>
                        </t>
                        <div class="info-header">
                            <h1><b><t t-esc="des.name"/></b></h1>
                        </div>
                        <p><t t-esc="des.x_desc"/></p>
                    </div>
            
                    <!-- State Selection Section (Right) -->
                    <div class="state-selection-container col-6 pt40 pb40 o_colored_level">
                        <style>
                            .state-selection-container .option {
                                position: relative;
                            }
                            .state-selection-container .option input {
                                opacity: 0;
                                position: absolute;
                            }
                            .state-selection-container .option input:checked+span {
                                background-color: #ffc107 !important;
                            }
                            .state-selection-container .btn-option {
                                margin: 0 10px 10px 0;
                                width: 100%;
                                background-color: transparent;
                                border: 1px solid #ffc107;
                            }
                            .state-selection-container .btn-option:hover {
                                background-color: #ffc107;
                            }
                            .state-selection-container .search-state {
                                margin-bottom: 15px;
                                padding: 8px;
                                <!--width: 100%;-->
                                border: 1px solid #ccc;
                                border-radius: 4px;
                            }
                            
                            .selectstate{
                                font-size: calc(1.2rem + 0.3vw);
                                    margin-left: -20px;
                            }                  
                            
                             @media (min-width: 320px) and  (max-width: 480px) {
                                .btn-group{
                                display: grid;
                                }
                            }
                            
                             
                        </style>
            
                        <div class="container">
                            <h4 class="selectstate ">Select State</h4>
                            <input type="text" class="search-state " id="search-state" placeholder="Search State..." style="    margin-left: -20px;"/>
            
                            <div class="btn-group col-xs-12 row " style="margin-left: -35px;">
                                <label class="option col-6 selected-state" t-foreach="states" t-as="state">
                                    <input type="radio" class="state-selection" name="optradio" t-att-value="state.id"/>
                                    <span class="btn btn-warning btn-option d-flex align-items-center justify-content-between">
                                        <p class="m-0" style="white-space: nowrap; overflow: hidden;">
                                            <t t-esc="state.name"/>
                                        </p>
                                        <i class="fa fa-chevron-right" aria-hidden="true"/>
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>
            
                <script>
                    document.addEventListener('DOMContentLoaded', function () {
                        const stateInputs = document.querySelectorAll('input[name="optradio"]');
                        const searchInput = document.getElementById('search-state');
                        const serviceId = "<t t-esc="service_id"/>"; // Safely injected in JS
            
                        // Function to filter states based on search input
                        searchInput.addEventListener('input', function () {
                            const filter = searchInput.value.toLowerCase();
                            const options = document.querySelectorAll('.selected-state');
            
                            options.forEach(option =&gt; {
                                const stateName = option.querySelector('p').textContent.toLowerCase();
                                option.style.display = stateName.includes(filter) ? '' : 'none';
                            });
                        });
            
                        // Handle state selection
                        stateInputs.forEach(input =&gt; {
                            input.addEventListener('change', function () {
                                const stateId = this.value;
                                if (stateId) {
                                    const newUrl = 'sub-document-drafting?service_id=' + serviceId + '&amp;state_id=' + stateId;
                                    window.location.href = newUrl;  // Direct navigation when a state is selected
                                }
                            });
                        });
                    });
                </script>


                    <section class="w-100">
                        <div>
                            <div class="container mt-4">
                                <div class="owl-carousel owl-theme">
                                    <t t-set="count" t-value="0"/>
                                    <div class="item" t-foreach="topics" t-as="topic">
                                        <div class="story-card" t-attf-onclick="openStory({{ count }})">
                                            <img t-attf-src="data:image/jpeg;base64,{{topic.x_background_image}}"/>
                                            <div class="text">
                                                <h5>
                                                    <t t-esc="topic.name"/>
                                                </h5>
                                            </div>
                                        </div>
                                        <t t-set="count" t-value="count + 1"/>
                                    </div>
                                </div>
                            </div>
                            <div id="storyView" class="story-view">
                                <div class="story-container" id="storyContainer">
                                    <button class="close-btn" onclick="closeStory()">×</button>
                                    <div class="progress-container" id="progressContainer"/>
                                    <div id="storyContent" class="story-content">
                                        <h2 id="storyTitle"/>
                                        <p id="storyText"/>
                                        <video id="storyVideo" style="position: relative; right: 0; bottom: 0; min-width: 40%;  min-height: 100%;" autoplay="1"/>
                                        <iframe id="storyIframe" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""/>
                                        <button id="knowMoreButton" class="know-more-button">Know More</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section class="other-forms w-100" t-if="des.x_tips or faqs">
                        <div class="row w-100 pt-4" style="background: aliceblue; border-radius: 15px;">
                            <div class="col-lg-6" t-if="des.x_tips">
                                <h2>
                                    <strong>Tips</strong>
                                </h2>
                                <ul class="w-100">
                                    <t t-if="des.x_tips" t-foreach="des.x_tips" t-as="tips">
                                        <li style="list-style-type: none;">
                                            <t t-if="tips.x_inclusion">
                                                <i class="fa-solid fa-circle-check" style="color: #28d72a;"/>
                                            </t>
                                            <t t-else="">
                                                <i class="fa-solid fa-circle-xmark" style="color: red;"/>
                                            </t>
                                            <t t-esc="tips.name"/>
                                        </li>
                                    </t>
                                </ul>
                            </div>
                            <div t-if="faqs" class="s_faq_collapse pt10 pb32 o_colored_level s_faq_collapse_light col col-lg-6" data-snippet="s_faq_collapse" data-name="Accordion" style="background-image: none;">
                                <h2 class="pb20" style="text-align: center;">
                                    <strong>FAQs</strong>
                                </h2>
                                <t t-if="faqs" t-foreach="faqs" t-as="faq">
                                    <!--<div class="container collapse-container" style="transition: max-height 0.3s ease-out;">-->
                                    <!--    <a type="button" class="btn collapse_heading" data-toggle="collapse" style="transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;" t-att-data-bs-target="'#body_'+str(faq.id)" aria-expanded="true">-->
                                    <!--        <t t-esc="faq.x_question"/>-->
                                    <!--        <p class="dropdown-toggle" style="float: right;"/>-->
                                    <!--    </a>-->
                                    <!--    <div t-att-id="'body_'+str(faq.id)" class="collapse_content collapse" style="padding: 20px;">-->
                                    <!--        <p style="text-align: left;">-->
                                    <!--            <span style="font-size: 18px;">-->
                                    <!--                <t t-esc="faq.x_answers"/>-->
                                    <!--            </span>-->
                                    <!--        </p>-->
                                    <!--    </div>-->
                                    <!--</div>-->
                                    <div id="myCollapse" class="accordion container text-start" role="tablist">
                                        <div class="card" data-name="Item" role="presentation" style="background: transparent; border: none;">
                                            <a href="#" role="tab" data-bs-toggle="collapse" aria-expanded="true" class="card-header text-black" t-att-data-bs-target="'#body_'+str(faq.id)" style="border: none; padding-left: 5px; transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;">
                                                <t t-esc="faq.x_question"/>
                                                <p class="fa-solid fa-chevron-down" style="float: right;"/>
                                            </a>
                                            <div class="collapse" data-bs-parent="#myCollapse" role="tabpanel" t-att-id="'body_'+str(faq.id)" style="border: none;background-color: transparent;">
                                                <div class="card-body" style="background-color: transparent !important; padding-left: 15px;">
                                                    <p class="card-text o_default_snippet_text">
                                                        <t t-esc="faq.x_answers"/>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                        <div id="myCollapse" class="accordion container text-start" role="tablist">
                                            <div class="card" data-name="Item" role="presentation" style="background: transparent; border: none;">
                                                <a href="#" role="tab" data-bs-toggle="collapse" aria-expanded="true" class="card-header text-black" data-bs-target="#myCollapseTab105725_1" style="border: none; padding-left: 5px; transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;">
                                                    Terms of service
                                                    <p class="fa-solid fa-chevron-down" style="float: right;"/>
                                                </a>
                                                <div class="collapse" data-bs-parent="#myCollapse" role="tabpanel" id="myCollapseTab105725_1" style="border: none;background-color: transparent;">
                                                    <div class="card-body" style="background-color: transparent !important; padding-left: 15px;">
                                                        <p class="card-text o_default_snippet_text">These terms of service ("Terms", "Agreement") are an agreement between the website ("Website operator", "us", "we" or "our") and you ("User", "you" or "your"). This Agreement sets forth the general terms and conditions of your use of this website and any of its products or services (collectively, "Website" or "Services").</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                            </div>
                        </div>
                    </section>
                </div>
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"/>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"/>
                <!--<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"/>-->
                <!--<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"/>-->
                <script>
                    $(document).ready(function(){
                        $('.owl-carousel').owlCarousel({
                            loop: false,
                            margin: 10,
                            nav: true,
                            responsive: {
                                0: { items: 1 },
                                600: { items: 3 },
                                1000: { items: 4 }
                            }
                        });
                    });

                    let currentStoryIndex = 0;
                    let currentSubStoryIndex = 0;
                    let storyTimer;
                    let isPaused = false;
                    const storyDuration = 5000;
                    const stories = [
                    <t t-foreach="topics" t-as="topic" t-foreach-index="topic_index">
                        <!--<t t-set="tutorials" t-value="request.env['tutorials'].search([])"/>-->
                        <t t-set="tutorials" t-value="request.env['tutorials'].search([['x_topic_id','=',topic.id]])"/>
                        {
                        <t t-if="tutorials">
                            subStories: [
                            <t t-foreach="tutorials" t-as="tutorial" t-foreach-index="tutorial_index">
                                {
                                <t t-if="tutorial.name">
                                    "title": "<t t-esc="tutorial.name"/>",
                                </t>
                                <t t-else="">
                                    "title": "",
                                </t>
                                <t t-if="tutorial.x_description">
                                    "description": "<t t-esc="tutorial.x_description"/>",
                                </t>
                                <t t-else="">
                                    "description": "",
                                </t>
                                <t t-if="tutorial.x_background_colour and not tutorial.x_file_link">
                                    "backgroundColor": "<t t-esc="tutorial.x_background_colour"/>",
                                    "contentType": "text"
                                </t>
                                <t t-if="not tutorial.x_file_link and not tutorial.x_background_colour">
                                    "backgroundColor": "black",
                                    "contentType": "text"
                                </t>
                                <t t-else=""/>
                                <t t-if="tutorial.x_file_link">
                                    <t t-if=" '.mp4' in tutorial.x_file_link">
                                        "videoSrc": "<t t-esc="tutorial.x_file_link"/>",
                                        "contentType": "video"
                                    </t>
                                    <t t-elif="'.png' in tutorial.x_file_link or '.jpg' in tutorial.x_file_link or '.jpeg' in tutorial.x_file_link">
                                        "backgroundImage": "<t t-esc="tutorial.x_file_link"/>",
                                        "contentType": "text"
                                    </t>
                                    <t t-else=""/>
                                </t>
                                }
                                <t t-if="tutorial_index + 1 &lt; len(tutorials)">,</t>
                            </t>
                            ]
                        </t>
                        }
                        <t t-if="topic_index + 1 &lt; len(topics)">,</t>
                    </t>
                    ];

                    function openStory(index) {
                        if (!stories[index] || !stories[index].subStories || stories[index].subStories.length === 0) {
                            console.error("Story or subStories are not properly defined for index", index);
                            return;
                        }
                        currentStoryIndex = index;
                        currentSubStoryIndex = 0;
                        document.getElementById('storyView').style.left = '0';
                        startStory();
    
                        const navbar = document.querySelector('nav.navbar');
                        const navbar2 = document.getElementById("o_main_navbar");
                        if (navbar){ navbar.style.display = 'none';}
                        if (navbar2){ navbar2.style.display = 'none';}
                    }

                    function closeStory() {
                        document.getElementById('storyView').style.left = '100%';
                        clearInterval(storyTimer);
                        isPaused = false;
                        const navbar = document.querySelector('nav.navbar');
                        const navbar2 = document.getElementById("o_main_navbar");
                        if (navbar){ navbar.style.display = 'block';}
                        if (navbar2){ navbar2.style.display = 'block';}
                    }

                    function startStory() {
                        clearInterval(storyTimer);
                        setTimeout(() =&gt; {
                            if (!stories[currentStoryIndex] || !stories[currentStoryIndex].subStories || stories[currentStoryIndex].subStories.length === 0) {
                                console.error("Story or subStories are not properly defined for currentStoryIndex",
                                currentStoryIndex);
                                return;
                            }
                            const story = stories[currentStoryIndex];
                            const subStory = story.subStories[currentSubStoryIndex];
                            document.getElementById('storyTitle').innerText = subStory.title;
                            document.getElementById('storyText').innerText = subStory.description;
                            document.getElementById('knowMoreButton').style.display = subStory.contentType === 'text' ? 'block'
                            : 'none';
                            document.getElementById('storyVideo').style.display = subStory.contentType === 'video' ? 'block' :
                            'none';
                            document.getElementById('storyIframe').style.display = subStory.contentType === 'iframe' ? 'block' :
                            'none';
        
                            if (subStory.backgroundColor) {
                                document.getElementById('storyContainer').style.backgroundColor = subStory.backgroundColor;
                                document.getElementById('storyContainer').style.backgroundImage = '';
                            }
                            
                            else if (subStory.backgroundImage) {
                                document.getElementById('storyContainer').style.backgroundImage =
                                `url(${subStory.backgroundImage})`;
                            }
                            
                            else if (subStory.backgroundGif) {
                                document.getElementById('storyContainer').style.backgroundImage = `url(${subStory.backgroundGif})`;
                            }
        
                            if (subStory.videoSrc) {
                                document.getElementById('storyVideo').src = subStory.videoSrc;
                            }
                            
                            updateProgressBar();
                            
                            if (!isPaused) {
                                storyTimer = setInterval(nextSubStory, storyDuration);
                            }
                        }, 100);
                    }

                    function nextSubStory() {
                        const story = stories[currentStoryIndex];
                        const subStory = story.subStories[currentSubStoryIndex];
                        if (!stories[currentStoryIndex] || !stories[currentStoryIndex].subStories || stories[currentStoryIndex].subStories.length === 0) {
                            console.error("Story or subStories are not properly defined for currentStoryIndex",
                            currentStoryIndex);
                            return;
                        }
                        currentSubStoryIndex++;
                        if (currentSubStoryIndex &gt;= stories[currentStoryIndex].subStories.length) {
                            closeStory();
                            return;
                        }
                        startStory();
                        if (subStory.videoSrc) {
                            var videoElement = document.getElementById('storyVideo');
                            videoElement.pause();
                        }
                    }

                    function prevSubStory() {
                        if (currentSubStoryIndex &gt; 0) {
                            currentSubStoryIndex--;
                            startStory();
                        }
                    }

                    function updateProgressBar() {
                        if (!stories[currentStoryIndex] || !stories[currentStoryIndex].subStories || stories[currentStoryIndex].subStories.length === 0) {
                            console.error("Story or subStories are not properly defined for currentStoryIndex",
                            currentStoryIndex);
                            return;
                        }
                        const progressContainer = document.getElementById('progressContainer');
                        progressContainer.innerHTML = '';
                        for (let i = 0; i &lt; stories[currentStoryIndex].subStories.length; i++) {
                            const progressBar = document.createElement('div');
                            progressBar.className = 'progress-bar';
                            if (i === currentSubStoryIndex) {
                                progressBar.classList.add('progress-bar-active');
                            } 
                            else if (i &lt; currentSubStoryIndex) {
                                progressBar.style.backgroundColor = 'white';
                            }
                            progressContainer.appendChild(progressBar);
                        }
                    }

                    function pauseStory() {
                        clearInterval(storyTimer);
                        isPaused = true;
                    }

                    function resumeStory() {
                        if (isPaused) {
                            storyTimer = setInterval(nextSubStory, storyDuration);
                            isPaused = false;
                        }
                    }

                    document.getElementById('storyContainer').addEventListener('mousedown', pauseStory);
                    document.getElementById('storyContainer').addEventListener('mouseup', resumeStory);
                    document.getElementById('storyContainer').addEventListener('mouseleave', resumeStory);
                    document.getElementById('storyContainer').addEventListener('click', function(event) {
                        const storyContainer = document.getElementById('storyContainer');
                        const x = event.clientX - storyContainer.getBoundingClientRect().left;
                        if (x &lt; storyContainer.clientWidth / 3) {
                            prevSubStory();
                        } 
                        else if (x &gt; (storyContainer.clientWidth * 2 / 3)) {
                            nextSubStory();
                        }
                    });

                    document.getElementById('storyContainer').addEventListener('touchstart', pauseStory);
                    document.getElementById('storyContainer').addEventListener('touchend', function(event) {
                        resumeStory();
                        if (event.changedTouches.length &gt; 0) {
                            const touch = event.changedTouches[0];
                            const storyContainer = document.getElementById('storyContainer');
                            const x = touch.clientX - storyContainer.getBoundingClientRect().left;
                            if (x &lt; storyContainer.clientWidth / 3) {
                                prevSubStory();
                            }
                            else if (x &gt; (storyContainer.clientWidth * 2 / 3)) {
                                nextSubStory();
                            }
                        }
                    });
                </script>

            </div>
        </t>
	</template>
	<template id="sub_document_drafting" name="sub-document-drafting">
		<t t-call="website.layout">
        <div id="wrap" class="oe_structure oe_empty">
            <t t-set="service_id" t-value="request.params.get('service_id')"/>
            <t t-set="state_id" t-value="int(request.params.get('state_id'))"/>
<t t-set="service_languages" t-value="env['res.lang'].sudo().search([('active', '!=', False)]) or []"/>
            <t t-set="services" t-value="request.env['document_drafting'].search([['x_service_id','=',int(service_id)], ['x_state_id', '=', int(state_id)]])"/>
            <!--<t t-esc="service_languages.mapped('id')"/>-->
            <link href="https://fonts.googleapis.com/css?family=Open+Sans" rel="stylesheet"/>

            <!-- Language Selection Section -->

                <style>
                   .sub-service-card {
                    border: none;
                    border-radius: 15px;
                    background-color: #fff;
                    padding: 20px;
                    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
                    transition: transform 0.3s ease, box-shadow 0.3s ease;
                    text-align: center;
                }
                
                .sub-service-card:hover {
                       text-decoration: none;
                }
                
                .sub-service-card .circle {
                    margin: 0 auto;
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    overflow: hidden;
                    background-color: #f5f5f5;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .sub-service-card .circle img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 50%;
                }
                
                .service-name {
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    margin-top: 15px;
                }
                 @media (min-width: 320px) and  (max-width: 480px){
                    .services-container{
                        gap:0.5rem;
                    }
                 
                 }
                
                
                </style>

               <section class="language-selection-container pt40 pb40 o_colored_level">
    <style>
        .languages {
            height: 200px;
            width: 200px;
        }
    </style>
    <div class="container mt-3">
        <h4 class="mb-4">Select Language</h4>
        <div class="row d-flex justify-content-center" style="height: 100%; gap: 20px; align-items: center;">
           <t t-foreach="service_languages" t-as="language">
              <t t-if="state_id == 588">
             
                  <button class="card languages img-fluid" style="padding: 25px; background-color: rgb(229, 248, 254); color: rgb(0, 81, 162);" t-att-id="language.id">
                      <h6 class="card-title"><t t-esc="language.name"/></h6>
                      <h2 style="font-size: 100px; text-align: center; text-shadow: 1px 1px #fff; width: 100%;">
                          <t t-esc="language.x_language_symbol"/>
                      </h2>
                  </button>
              </t>
              <t t-if="state_id != 588 and language.id != 35">
                
                  <button class="card languages img-fluid" style="padding: 25px; background-color: rgb(229, 248, 254); color: rgb(0, 81, 162);" t-att-id="language.id">
                      <h6 class="card-title"><t t-esc="language.name"/></h6>
                      <h2 style="font-size: 100px; text-align: center; text-shadow: 1px 1px #fff; width: 100%;">
                          <t t-esc="language.x_language_symbol"/>
                      </h2>
                  </button>
              </t>
          </t>
        </div>
    </div>
</section>

<!-- Sub-services Section -->
<section class="sub-services pt40 pb40 o_colored_level">
    <section class="s_text_block pt40 pb40 o_colored_level" data-snippet="s_text_block" data-name="Text" style="background-image: none;">
        <div class="s_allow_columns container">
            <h3 style="text-align: center;">Sub Document Drafting Services</h3>
            <form>
                <div role="search" class="input-group">
                    <input type="text" id="serviceSearch" name="search" class="form-control" placeholder="Search for Sub Document Drafting Services..."/>
                </div>
            </form>
        </div>
    </section>

    <div class="container">
        <div id="services-container" class="row services-container">
            <!-- Dynamic content will be inserted here -->
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        let selectedLanguageId = null;
        let serviceId = '<t t-esc="service_id"/>';
        let stateId = '<t t-esc="state_id"/>';

        // Show both sections on page load
        document.querySelector('.sub-services').style.display = 'block';

        // Handle language selection
        document.querySelectorAll('.languages').forEach(function(element) {
            element.addEventListener('click', function() {
                selectedLanguageId = this.id;
                console.log('Selected Language ID:', selectedLanguageId);

                // Fetch the services based on the selected language
                fetchServices();
            });
        });

        // Function to fetch services
        function fetchServices() {
            const domainFilter = stateId &amp;&amp; selectedLanguageId
                ? `[(\"x_service_id\",\"=\",${serviceId}), (\"x_state_id\",\"=\",${stateId}), (\"x_language_id\",\"=\",${selectedLanguageId})]`
                : `[(\"x_service_id\",\"=\",${serviceId})]`;

            const payload = {
                data: {
                    domain_filter: domainFilter,
                    "include_binary": ["x_image"],
                    args: { order: "id ASC" }
                },
                batch_size: 10,
                page: 1
            };

            console.log('Fetching services with payload:', payload);

            fetch('/api/oneclickvakil_domain/document_drafting', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            })
            .then(response =&gt; response.json())
            .then(data =&gt; {
                console.log('Service response data:', data);

                const servicesContainer = document.getElementById('services-container');
                if (!servicesContainer) return;

                servicesContainer.innerHTML = ''; // Clear existing content
                const services = data.result &amp;&amp; Array.isArray(data.result.records) ? data.result.records : [];

                if (services.length &gt; 0) {
                    const additionalClasses = ['education', 'credentialing', 'wallet', 'human-resources'];

                    services.forEach((service, index) =&gt; {
                        const serviceElement = document.createElement('div');
                        serviceElement.className = 'col-lg-5 col-md-5 col-xl-6 card_items sub-service ' + (service.x_service_id[0]?.name || 'unknown');

                        const decodedImage = atob(service.x_image);
                        const additionalClass = additionalClasses[index % additionalClasses.length];

                        serviceElement.innerHTML = `
                            <a href="/document-drafting-sub-description?service_id=${service.id}" class="card sub-service-card ${additionalClass}">
                                
                                <div class="circle">
                                    <img src="data:image/jpeg;base64,${decodedImage}" alt="${service.name}"/>
                                </div>
                                <div>
                                    <p class="service-name">${service.name}</p>
                                </div>
                            </a>
                        `;
                        servicesContainer.appendChild(serviceElement);
                    });
                } else {
                    servicesContainer.innerHTML = '<h6>No services found</h6>';
                }
            })
            .catch(error =&gt; {
                console.error('Error fetching services:', error);
                const servicesContainer = document.getElementById('services-container');
                servicesContainer.innerHTML = '<h6>Error fetching services. Please try again later.</h6>';
            });
        }

        // Fetch services immediately on page load
        fetchServices();
    });
</script>
              </div>
            </t>
	</template>
	<template id="document_drafting_sub_description" name="document-drafting-sub-description">
	  <t t-call="website.layout">
          <t t-set="service_id" t-value="request.params.get('service_id')"/>
            <t t-set="des" t-value="request.env['document_drafting'].search([['id','=',int(service_id)]])"/>
            <t t-set="topics" t-value="request.env['topics'].search([['x_service_id','=',int(service_id)]])"/>
            <t t-set="faqs" t-value="request.env['faqs'].search([('x_in_sub_service','=','True'),('x_faq_id','=',int(service_id))])"/>
             <t t-set="states" t-value="request.env['res.country.state'].search([('country_id','=',104)])"/>
             <t t-set="state_id" t-value="request.params.get('state_id')"/>
             
            <div id="wrap" class="oe_structure oe_empty">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"/>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"/>
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"/>
                <!--<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"/>-->
                <!--<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js" integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p" crossorigin="anonymous"/>-->
                <!--<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"/>-->
                <style>
                    .story-card {
                      cursor: pointer;
                      transition: transform 0.3s;
                      width: 100%;
                      height: 370px;
                      position: relative;
                      border-radius: 10px;
                      background-color: #f8f9fa;
                      color: #000;
                      display: flex;
                      flex-direction: column;
                      justify-content: flex-end;
                      align-items: center;
                      overflow: hidden;
                    }
                    .story-card:hover {
                      transform: scale(1.05);
                    }
                    .story-card img {
                      position: relative;
                      top: 0;
                      left: 0;
                      width: 100%;
                      height: 100%;
                      object-fit: cover;
                    }
                    .story-card .text {
                      position: relative;
                      bottom: 0;
                      width: 100%;
                      padding: 10px;
                      background: rgba(218, 165, 32, 0.7);
                      text-align: center;
                    }
                    .story-view {
                      position: fixed;
                      top: 0;
                      left: 100%;
                      width: 100%;
                      height: 100%;
                      background-color: rgba(0, 0, 0, 0.8);
                      transition: left 0.3s;
                      z-index: 1000;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    }
                    .story-container {
                      width: 100%;
                      max-width: 360px;
                      height: 720px;
                      position: relative;
                      background-color: #000;
                      display: flex;
                      flex-direction: column;
                      justify-content: space-between;
                      padding: 20px;
                      color: white;
                      text-align: center;
                      overflow: hidden;
                      border-radius: 10px;
                    }
                    .close-btn {
                      position: absolute;
                      top: 20px;
                      right: 20px;
                      background: none;
                      border: none;
                      color: white;
                      font-size: 30px;
                      cursor: pointer;
                      z-index: 1001;
                    }
                    .progress-container {
                      display: flex;
                      justify-content: space-between;
                      position: absolute;
                      top: 10px;
                      left: 10px;
                      right: 10px;
                    }
                    .progress-bar {
                      height: 4px;
                      background-color: rgba(255, 255, 255, 0.5);
                      flex-grow: 1;
                      margin: 0 2px;
                      position: relative;
                    }
                    .progress-bar-active::before {
                      content: "";
                      position: absolute;
                      top: 0;
                      left: 0;
                      height: 100%;
                      background-color: white;
                      animation: progress 5s linear forwards;
                    }
                    .story-content {
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                    }
                    .story-content h2 {
                      font-size: 24px;
                      margin-bottom: 10px;
                    }
                    .story-content p {
                      font-size: 18px;
                      margin-bottom: 20px;
                    }
                    .story-content video,
                    .story-content iframe {
                      width: 100%;
                      height: auto;
                      display: none;
                    }
                    .know-more-button {
                      background-color: #ffc107;
                      color: #000;
                      border: none;
                      border-radius: 5px;
                      padding: 10px 20px;
                      font-size: 18px;
                      cursor: pointer;
                    }
                    .know-more-button:hover {
                      background-color: #e0a800;
                    }
                    @keyframes progress {
                      from {
                        width: 0%;
                      }
                      to {
                        width: 100%;
                      }
                    }
                    
                    
                    .owl-carousel .owl-stage-outer{
                        display: flex;
                        justify-content: center;
                    }
                </style>
                <style>
                    .info-header {
                      color: black;
                      text-align: left;
                      margin: 2rem auto;
                    }
                    
                    .info-header h1 {
                      margin: 0;
                      font-size: 3rem;
                    }
                    
                    .info-main {
                      max-width: auto;
                      margin: 0 auto;
                    }
                    
                    .info-section {
                      display: flex;
                      align-items: center;
                      box-shadow: none;
                      max-width: 90%;
                      margin-bottom: 1rem;
                      margin: 0 auto;
                      gap: 10rem;
                    }
                    
                    .info-section img {
                      max-width: 500px;
                      height: auto;
                      max-height: 250px;
                      object-fit: cover;
                      border-radius: 10px;
                    }
                    
                    .info-text {
                      flex-grow: 1;
                      font-size: 0.9rem;
                      color: #333;
                    }
                    
                    .info-text p {
                      margin: 0 0 1rem;
                    }
                    
                    .apply-btn {
                      display: block;auto;
                      padding: 0.75rem 2rem;
                      background-color: #28a745;
                      color: white;
                      border: none;
                      border-radius: 4px;
                      cursor: pointer;
                      font-size: 0.9rem;
                      font-weight: bold;
                      white-space: nowrap;
                      text-decoration: none;
                    }
                    .apply-btn:hover {
                      background-color: #28a745;
                    }
                    
                    .other-forms {
                      text-align: center;
                      margin: 0 auto;
                      margin-top: 2rem;
                      padding: 45px;
                    }
                    
                    .other-forms h2 {
                      margin: 0 0 1rem;
                      font-size: 2rem;
                      font-weight: normal;
                    }
                    
                    .other-forms ul {
                      list-style-type: disc; /* This ensures the list has bullet points */
                      display: inline-block;
                      margin: 0rem; /* Adjust margin as needed */
                      margin-bottom: 2rem;
                      text-align: left; /* Align text to the left */
                    }
                    
                    .other-forms ul li {
                      margin-bottom: 0.3rem; /* Space between list items */
                      font-size: 1rem; /* Adjust font size as needed */
                    }
                    
                    .accordion-item {
                      width: 70%;
                      margin: 0 auto;
                      background-color: #f8f9fa;
                      border: 1px solid #e7e7e7;
                    }
                    
                    .accordion-button {
                      font-size: 1rem;
                      font-weight: 500;
                      color: #333;
                      background-color: #f8f9fa;
                      padding: 1rem 1.25rem;
                    }
                    
                    .accordion-button:not(.collapsed) {
                      color: #fff;
                      background-color: #9179b3;
                    }
                    
                    .accordion-body {
                      color: black;
                      font-size: 0.9rem;
                      padding: 1rem 1.5rem;
                    }
                    
                    /* Media Queries for Common Resolutions */
                    
                    /* 4K UHD (3840×2160) */
                    @media (min-width: 3840px) {
                      .info-header,
                      .info-section {
                        max-width: 3200px;
                      }
                      .info-header h1 {
                        font-size: 4rem;
                      }
                      .info-section {
                        gap: 15rem;
                      }
                      .info-section img {
                        max-width: 800px;
                        max-height: 300px;
                      }
                      .info-text {
                        font-size: 1.2rem;
                      }
                      .apply-btn {
                        padding: 1rem 3rem;
                        font-size: 1.5rem;
                        margin: 1rem 0 1rem auto;
                      }
                      .accordion-item {
                        width: 50%;
                        max-width: 2400px;
                      }
                    }
                    
                    /* QHD (2560×1440) */
                    @media (min-width: 2560px) and (max-width: 3839px) {
                      .info-header,
                      .info-section {
                        max-width: 2200px;
                      }
                      .info-header h1 {
                        font-size: 3.5rem;
                      }
                      .info-section {
                        gap: 12rem;
                      }
                      .info-section img {
                        max-width: 600px;
                        max-height: 250px;
                      }
                      .info-text {
                        font-size: 1.1rem;
                      }
                      .apply-btn {
                        margin: 1rem 0 1rem auto;
                      }
                      .accordion-item {
                        width: 60%;
                        max-width: 1800px;
                      }
                    }
                    
                    /* Full HD (1920×1080) */
                    @media (min-width: 1920px) and (max-width: 2559px) {
                      .info-header,
                      .info-section {
                        max-width: 1700px;
                      }
                      .info-section {
                        gap: 10rem;
                      }
                      .accordion-item {
                        width: 65%;
                        max-width: 1400px;
                      }
                    }
                    
                    /* Common High Resolutions */
                    @media (min-width: 1680px) and (max-width: 1919px) {
                      /* 1680×1050 */
                      .info-header,
                      .info-section {
                        max-width: 1500px;
                      }
                      .info-section {
                        gap: 8rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 70%;
                        max-width: 1300px;
                      }
                    }
                    
                    @media (min-width: 1600px) and (max-width: 1679px) {
                      /* 1600×900 */
                      .info-header,
                      .info-section {
                        max-width: 1400px;
                      }
                      .info-section {
                        gap: 7rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 70%;
                        max-width: 1200px;
                      }
                    }
                    
                    @media (min-width: 1440px) and (max-width: 1599px) {
                      /* 1440×900 */
                      .info-header,
                      .info-section {
                        max-width: 1300px;
                      }
                      .info-header h1 {
                        font-size: 2.75rem;
                      }
                      .info-section {
                        gap: 6rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 75%;
                        max-width: 1100px;
                      }
                    }
                    
                    /* Common Laptop and Monitor Resolutions */
                    @media (min-width: 1366px) and (max-width: 1439px) {
                      /* 1366×768 */
                      .info-header,
                      .info-section {
                        max-width: 1200px;
                      }
                      .info-header h1 {
                        font-size: 2.5rem;
                      }
                      .info-section {
                        gap: 5rem;
                      }
                      .info-section img {
                        max-width: 450px;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 80%;
                        max-width: 1000px;
                      }
                    }
                    
                    @media (min-width: 1280px) and (max-width: 1365px) {
                      /* 1280×1024 and 1280×800 */
                      .info-header,
                      .info-section {
                        max-width: 1100px;
                      }
                      info-header h1 {
                        font-size: 2.25rem;
                      }
                      .info-section {
                        gap: 4rem;
                      }
                      .info-section img {
                        max-width: 400px;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 80%;
                        max-width: 900px;
                      }
                    }
                    
                    /* Smaller Screens */
                    @media (min-width: 1024px) and (max-width: 1279px) {
                      /* 1024×768 */
                      .info-header,
                      .info-section {
                        max-width: 900px;
                      }
                      .info-header h1 {
                        font-size: 2rem;
                      }
                      .info-section {
                        gap: 3rem;
                      }
                      .info-section img {
                        max-width: 350px;
                      }
                      .info-text {
                        font-size: 0.85rem;
                      }
                      .apply-btn {
                      }
                      .accordion-item {
                        width: 85%;
                        max-width: 800px;
                      }
                    }
                    
                    @media (max-width: 1023px) {
                      /* Smaller than 1024×768 */
                      .info-header,
                      .info-section {
                        max-width: 90%;
                      }
                      .info-header h1 {
                        font-size: 1.75rem;
                      }
                      .info-section {
                        flex-direction: column;
                        align-items: center;
                        gap: 1.5rem;
                      }
                      .info-section img {
                        width: 100%;
                        max-width: 310px;
                      }
                      .info-text {
                        text-align: center;
                        font-size: 0.8rem;
                      }
                      .apply-btn {
                        padding: 0.75rem 2rem;
                        font-size: 1rem;
                      }
                      .other-forms h2 {
                        font-size: 1.5rem;
                      }
                      .other-forms {
                        padding: 3rem 1rem;
                      }
                      .accordion-item {
                        width: 90%;
                      }
                    }
                    
                    /* Tablets and Large Phones */
                    @media (min-width: 768px) and (max-width: 1023px) {
                      .accordion-item {
                        width: 80%;
                      }
                    }
                    
                    /* Mobile and Small Tablet */
                    @media (max-width: 768px) {
                      .info-header {
                        margin: 1rem auto;
                        margin-bottom: 1.5rem;
                      }
                      info-header h1 {
                        font-size: 1.5rem;
                      }
                      .apply-btn {
                        margin: 1rem auto;
                        padding: 0.6rem 1.5rem;
                        font-size: 0.9rem;
                      }
                      .form-buttons {
                        gap: 0.5rem;
                      }
                      .form-buttons button {
                        font-size: 0.75rem;
                        padding: 0.5rem 0.75rem;
                        min-width: 100px;
                      }
                      .accordion-item {
                        width: 95%;
                      }
                    }
                    
                    /* Small Mobile */
                    @media (max-width: 480px) {
                      .apply-btn {
                        margin: 1rem auto;
                      }
                      info-header h1 {
                        font-size: 1.25rem;
                      }
                      .info-section img {
                        max-height: 310px;
                      }
                      .info-text {
                        font-size: 0.7rem;
                      }
                      .other-forms h2 {
                        font-size: 1.25rem;
                      }
                      .other-forms {
                        padding: 2rem 0.5rem;
                      }
                      .form-buttons button {
                        min-width: 80px;
                      }
                      .accordion-item {
                        width: 100%;
                      }
                      .other-forms {
                        padding: 0;
                      }
                    }
                    
                    /* Extra Small Mobile */
                    @media (max-width: 360px) {
                      info-header h1 {
                        font-size: 1.1rem;
                      }
                      .other-forms h2 {
                        font-size: 1.1rem;
                      }
                      .info-section img {
                        max-height: 245px;
                      }
                      .apply-btn {
                        padding: 0.5rem 1.25rem;
                        font-size: 0.8rem;
                      }
                      .form-buttons button {
                        font-size: 0.7rem;
                        padding: 0.4rem 0.6rem;
                        min-width: 70px;
                      }
                      .apply-btn {
                        margin: 1rem auto;
                      }
                    }
                </style>
                <div class="info-main" t-if="des">
                    <section class="info-section pt88 pb88">
                        <t t-if="des.x_image">
                            <img t-attf-src="data:image/jpeg;base64,{{des.x_image}}" style="mix-blend-mode: multiply;"/>
                        </t>
                        <div class="info-text">
                            <div class="info-header">
                                <h1>
                                    <b>
                                        <t t-esc="des.name"/>
                                    </b>
                                </h1>
                            </div>
                            <p>
                                <t t-esc="des.x_desc"/>
                            </p>
                            <button id="more-info-btn" class="btn btn-info" t-att-href="'/rti-form'">
                                More Info
                            </button>
                            <script>
                                document.getElementById('more-info-btn').addEventListener('click', function () {
                                  window.location.href = '/rti-form';
                              });
                            </script>
                        </div>
                    </section>
                  
                   
                    <section class="w-100">
                        <div>
                            <div class="container mt-4">
                                <div class="owl-carousel owl-theme">
                                    <t t-set="count" t-value="0"/>
                                    <div class="item" t-foreach="topics" t-as="topic">
                                        <div class="story-card" t-attf-onclick="openStory({{ count }})">
                                            <img t-attf-src="data:image/jpeg;base64,{{topic.x_background_image}}"/>
                                            <div class="text">
                                                <h5>
                                                    <t t-esc="topic.name"/>
                                                </h5>
                                            </div>
                                        </div>
                                        <t t-set="count" t-value="count + 1"/>
                                    </div>
                                </div>
                            </div>
                            <div id="storyView" class="story-view">
                                <div class="story-container" id="storyContainer">
                                    <button class="close-btn" onclick="closeStory()">×</button>
                                    <div class="progress-container" id="progressContainer"/>
                                    <div id="storyContent" class="story-content">
                                        <h2 id="storyTitle"/>
                                        <p id="storyText"/>
                                        <video id="storyVideo" style="position: relative; right: 0; bottom: 0; min-width: 40%;  min-height: 100%;" autoplay="1"/>
                                        <iframe id="storyIframe" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""/>
                                        <button id="knowMoreButton" class="know-more-button">Know More</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section class="other-forms w-100" t-if="des.x_tips or faqs">
                        <div class="row w-100 pt-4" style="background: aliceblue; border-radius: 15px;">
                            <div class="col-lg-6" t-if="des.x_tips">
                                <h2>
                                    <strong>Tips</strong>
                                </h2>
                                <ul class="w-100">
                                    <t t-if="des.x_tips" t-foreach="des.x_tips" t-as="tips">
                                        <li style="list-style-type: none;">
                                            <t t-if="tips.x_inclusion">
                                                <i class="fa fa-check" style="color: #28d72a;"/>
                                            </t>
                                            <t t-else="">
                                                <i class="fa fa-close" style="color: red;"/>
                                            </t>
                                            <t t-esc="tips.name"/>
                                        </li>
                                    </t>
                                </ul>
                            </div>
                            <div t-if="faqs" class="s_faq_collapse pt10 pb32 o_colored_level s_faq_collapse_light col col-lg-6" data-snippet="s_faq_collapse" data-name="Accordion" style="background-image: none;">
                                <h2 class="pb20" style="text-align: center;">
                                    <strong>FAQs</strong>
                                </h2>
                                <t t-if="faqs" t-foreach="faqs" t-as="faq">
                                    <!--<div class="container collapse-container" style="transition: max-height 0.3s ease-out;">-->
                                    <!--    <a type="button" class="btn collapse_heading" data-toggle="collapse" style="transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;" t-att-data-bs-target="'#body_'+str(faq.id)" aria-expanded="true">-->
                                    <!--        <t t-esc="faq.x_question"/>-->
                                    <!--        <p class="dropdown-toggle" style="float: right;"/>-->
                                    <!--    </a>-->
                                    <!--    <div t-att-id="'body_'+str(faq.id)" class="collapse_content collapse" style="padding: 20px;">-->
                                    <!--        <p style="text-align: left;">-->
                                    <!--            <span style="font-size: 18px;">-->
                                    <!--                <t t-esc="faq.x_answers"/>-->
                                    <!--            </span>-->
                                    <!--        </p>-->
                                    <!--    </div>-->
                                    <!--</div>-->
                                    <div id="myCollapse" class="accordion container text-start" role="tablist">
                                        <div class="card" data-name="Item" role="presentation" style="background: transparent; border: none;">
                                            <a href="#" role="tab" data-bs-toggle="collapse" aria-expanded="true" class="card-header text-black" t-att-data-bs-target="'#body_'+str(faq.id)" style="border: none; padding-left: 5px; transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;">
                                                <t t-esc="faq.x_question"/>
                                                <!--<p class="fa-solid fa-chevron-down" style="float: right;"/>-->
                                                <p class="fa-solid fa-chevron-down" style="float: right; cursor: pointer; transition: transform 0.3s;" onclick="toggleChevron(this)" data-oe-model="ir.ui.view" data-oe-id="4448" data-oe-field="arch" data-oe-xpath="/t[1]/t[1]/div[1]/div[1]/section[3]/div[1]/div[2]/t[1]/div[1]/div[1]/a[1]/p[1]"/>

                                                <script>
                                                function toggleChevron(element) {
                                                    element.classList.toggle('rotated');
                                                }
                                                </script>
                                                
                                                <style>
                                                .rotated {
                                                    transform: rotate(180deg);
                                                }
                                                </style>

                                            </a>
                                            <div class="collapse" data-bs-parent="#myCollapse" role="tabpanel" t-att-id="'body_'+str(faq.id)" style="border: none;background-color: transparent;">
                                                <div class="card-body" style="background-color: transparent !important; padding-left: 15px;">
                                                    <p class="card-text o_default_snippet_text">
                                                        <t t-esc="faq.x_answers"/>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </t>
                                        <div id="myCollapse" class="accordion container text-start" role="tablist">
                                            <div class="card" data-name="Item" role="presentation" style="background: transparent; border: none;">
                                                <a href="#" role="tab" data-bs-toggle="collapse" aria-expanded="true" class="card-header text-black" data-bs-target="#myCollapseTab105725_1" style="border: none; padding-left: 5px; transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;">
                                                    Terms of service
                                                    <!--<p class="fa-solid fa-chevron-down" style="float: right;"/>-->
                                                    <p class="fa-solid fa-chevron-down" style="float: right; cursor: pointer; transition: transform 0.3s;" onclick="toggleChevron(this)" data-oe-model="ir.ui.view" data-oe-id="4448" data-oe-field="arch" data-oe-xpath="/t[1]/t[1]/div[1]/div[1]/section[3]/div[1]/div[2]/t[1]/div[1]/div[1]/a[1]/p[1]"/>

                                                    <script>
                                                    function toggleChevron(element) {
                                                        element.classList.toggle('rotated');
                                                    }
                                                    </script>
                                                    
                                                    <style>
                                                    .rotated {
                                                        transform: rotate(180deg);
                                                    }
                                                    </style>

                                                </a>
                                                <div class="collapse" data-bs-parent="#myCollapse" role="tabpanel" id="myCollapseTab105725_1" style="border: none;background-color: transparent;">
                                                    <div class="card-body" style="background-color: transparent !important; padding-left: 15px;">
                                                        <p class="card-text o_default_snippet_text">These terms of service ("Terms", "Agreement") are an agreement between the website ("Website operator", "us", "we" or "our") and you ("User", "you" or "your"). This Agreement sets forth the general terms and conditions of your use of this website and any of its products or services (collectively, "Website" or "Services").</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                            </div>
                        </div>
                    </section>
                </div>
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"/>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"/>
                <!--<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"/>-->
                <!--<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"/>-->
                <script>
                    $(document).ready(function(){
                        $('.owl-carousel').owlCarousel({
                            loop: false,
                            margin: 10,
                            nav: true,
                            responsive: {
                                0: { items: 1 },
                                600: { items: 3 },
                                1000: { items: 4 }
                            }
                        });
                    });

                    let currentStoryIndex = 0;
                    let currentSubStoryIndex = 0;
                    let storyTimer;
                    let isPaused = false;
                    const storyDuration = 5000;
                    const stories = [
                    <t t-foreach="topics" t-as="topic" t-foreach-index="topic_index">
                        <!--<t t-set="tutorials" t-value="request.env['tutorials'].search([])"/>-->
                        <t t-set="tutorials" t-value="request.env['tutorials'].search([['x_topic_id','=',topic.id]])"/>
                        {
                        <t t-if="tutorials">
                            subStories: [
                            <t t-foreach="tutorials" t-as="tutorial" t-foreach-index="tutorial_index">
                                {
                                <t t-if="tutorial.name">
                                    "title": "<t t-esc="tutorial.name"/>",
                                </t>
                                <t t-else="">
                                    "title": "",
                                </t>
                                <t t-if="tutorial.x_description">
                                    "description": "<t t-esc="tutorial.x_description"/>",
                                </t>
                                <t t-else="">
                                    "description": "",
                                </t>
                                <t t-if="tutorial.x_background_colour and not tutorial.x_file_link">
                                    "backgroundColor": "<t t-esc="tutorial.x_background_colour"/>",
                                    "contentType": "text"
                                </t>
                                <t t-if="not tutorial.x_file_link and not tutorial.x_background_colour">
                                    "backgroundColor": "black",
                                    "contentType": "text"
                                </t>
                                <t t-else=""/>
                                <t t-if="tutorial.x_file_link">
                                    <t t-if=" '.mp4' in tutorial.x_file_link">
                                        "videoSrc": "<t t-esc="tutorial.x_file_link"/>",
                                        "contentType": "video"
                                    </t>
                                    <t t-elif="'.png' in tutorial.x_file_link or '.jpg' in tutorial.x_file_link or '.jpeg' in tutorial.x_file_link">
                                        "backgroundImage": "<t t-esc="tutorial.x_file_link"/>",
                                        "contentType": "text"
                                    </t>
                                    <t t-else=""/>
                                </t>
                                }
                                <t t-if="tutorial_index + 1 &lt; len(tutorials)">,</t>
                            </t>
                            ]
                        </t>
                        }
                        <t t-if="topic_index + 1 &lt; len(topics)">,</t>
                    </t>
                    ];

                    function openStory(index) {
                        if (!stories[index] || !stories[index].subStories || stories[index].subStories.length === 0) {
                            console.error("Story or subStories are not properly defined for index", index);
                            return;
                        }
                        currentStoryIndex = index;
                        currentSubStoryIndex = 0;
                        document.getElementById('storyView').style.left = '0';
                        startStory();
    
                        const navbar = document.querySelector('nav.navbar');
                        const navbar2 = document.getElementById("o_main_navbar");
                        if (navbar){ navbar.style.display = 'none';}
                        if (navbar2){ navbar2.style.display = 'none';}
                    }

                    function closeStory() {
                        document.getElementById('storyView').style.left = '100%';
                        clearInterval(storyTimer);
                        isPaused = false;
                        const navbar = document.querySelector('nav.navbar');
                        const navbar2 = document.getElementById("o_main_navbar");
                        if (navbar){ navbar.style.display = 'block';}
                        if (navbar2){ navbar2.style.display = 'block';}
                    }

                    function startStory() {
                        clearInterval(storyTimer);
                        setTimeout(() =&gt; {
                            if (!stories[currentStoryIndex] || !stories[currentStoryIndex].subStories || stories[currentStoryIndex].subStories.length === 0) {
                                console.error("Story or subStories are not properly defined for currentStoryIndex",
                                currentStoryIndex);
                                return;
                            }
                            const story = stories[currentStoryIndex];
                            const subStory = story.subStories[currentSubStoryIndex];
                            document.getElementById('storyTitle').innerText = subStory.title;
                            document.getElementById('storyText').innerText = subStory.description;
                            document.getElementById('knowMoreButton').style.display = subStory.contentType === 'text' ? 'block'
                            : 'none';
                            document.getElementById('storyVideo').style.display = subStory.contentType === 'video' ? 'block' :
                            'none';
                            document.getElementById('storyIframe').style.display = subStory.contentType === 'iframe' ? 'block' :
                            'none';
        
                            if (subStory.backgroundColor) {
                                document.getElementById('storyContainer').style.backgroundColor = subStory.backgroundColor;
                                document.getElementById('storyContainer').style.backgroundImage = '';
                            }
                            
                            else if (subStory.backgroundImage) {
                                document.getElementById('storyContainer').style.backgroundImage =
                                `url(${subStory.backgroundImage})`;
                            }
                            
                            else if (subStory.backgroundGif) {
                                document.getElementById('storyContainer').style.backgroundImage = `url(${subStory.backgroundGif})`;
                            }
        
                            if (subStory.videoSrc) {
                                document.getElementById('storyVideo').src = subStory.videoSrc;
                            }
                            
                            updateProgressBar();
                            
                            if (!isPaused) {
                                storyTimer = setInterval(nextSubStory, storyDuration);
                            }
                        }, 100);
                    }

                    function nextSubStory() {
                        const story = stories[currentStoryIndex];
                        const subStory = story.subStories[currentSubStoryIndex];
                        if (!stories[currentStoryIndex] || !stories[currentStoryIndex].subStories || stories[currentStoryIndex].subStories.length === 0) {
                            console.error("Story or subStories are not properly defined for currentStoryIndex",
                            currentStoryIndex);
                            return;
                        }
                        currentSubStoryIndex++;
                        if (currentSubStoryIndex &gt;= stories[currentStoryIndex].subStories.length) {
                            closeStory();
                            return;
                        }
                        startStory();
                        if (subStory.videoSrc) {
                            var videoElement = document.getElementById('storyVideo');
                            videoElement.pause();
                        }
                    }

                    function prevSubStory() {
                        if (currentSubStoryIndex &gt; 0) {
                            currentSubStoryIndex--;
                            startStory();
                        }
                    }

                    function updateProgressBar() {
                        if (!stories[currentStoryIndex] || !stories[currentStoryIndex].subStories || stories[currentStoryIndex].subStories.length === 0) {
                            console.error("Story or subStories are not properly defined for currentStoryIndex",
                            currentStoryIndex);
                            return;
                        }
                        const progressContainer = document.getElementById('progressContainer');
                        progressContainer.innerHTML = '';
                        for (let i = 0; i &lt; stories[currentStoryIndex].subStories.length; i++) {
                            const progressBar = document.createElement('div');
                            progressBar.className = 'progress-bar';
                            if (i === currentSubStoryIndex) {
                                progressBar.classList.add('progress-bar-active');
                            } 
                            else if (i &lt; currentSubStoryIndex) {
                                progressBar.style.backgroundColor = 'white';
                            }
                            progressContainer.appendChild(progressBar);
                        }
                    }

                    function pauseStory() {
                        clearInterval(storyTimer);
                        isPaused = true;
                    }

                    function resumeStory() {
                        if (isPaused) {
                            storyTimer = setInterval(nextSubStory, storyDuration);
                            isPaused = false;
                        }
                    }

                    document.getElementById('storyContainer').addEventListener('mousedown', pauseStory);
                    document.getElementById('storyContainer').addEventListener('mouseup', resumeStory);
                    document.getElementById('storyContainer').addEventListener('mouseleave', resumeStory);
                    document.getElementById('storyContainer').addEventListener('click', function(event) {
                        const storyContainer = document.getElementById('storyContainer');
                        const x = event.clientX - storyContainer.getBoundingClientRect().left;
                        if (x &lt; storyContainer.clientWidth / 3) {
                            prevSubStory();
                        } 
                        else if (x &gt; (storyContainer.clientWidth * 2 / 3)) {
                            nextSubStory();
                        }
                    });

                    document.getElementById('storyContainer').addEventListener('touchstart', pauseStory);
                    document.getElementById('storyContainer').addEventListener('touchend', function(event) {
                        resumeStory();
                        if (event.changedTouches.length &gt; 0) {
                            const touch = event.changedTouches[0];
                            const storyContainer = document.getElementById('storyContainer');
                            const x = touch.clientX - storyContainer.getBoundingClientRect().left;
                            if (x &lt; storyContainer.clientWidth / 3) {
                                prevSubStory();
                            }
                            else if (x &gt; (storyContainer.clientWidth * 2 / 3)) {
                                nextSubStory();
                            }
                        }
                    });
                </script>

            </div>
        </t>
	</template>
	<template id="howitsworks" name="howitsworks">
		<t t-call="website.layout">
			<!--.o_js_ripple_effect {-->
			<!--    transform-style: none important;-->
			<!--    position: relative !important;-->
			<!--    overflow: hidden !important;-->
			<!--}-->
			<div id="wrap" class="oe_structure oe_empty">
				<t t-set="service_id" t-value="request.params.get('service_id')"/>
				<t t-set="howitsworks" t-value="request.env['howitsworks'].search([['x_howitsworks_id','=',service_id]])"/>
				<t t-set="service" t-value="request.env['document_drafting'].sudo().search([['id','=',service_id]])"/>
				<t t-set="product" t-value="request.env['product.template'].sudo().search([['id','=',service.x_product_ids.id]])"/>
				<!--<script>-->
				<!--    alert('<t t-esc="service_id"/>');-->
				<!--    alert('<t t-esc="service.id"/>');-->
				<!--    alert('<t t-esc="service.x_product_ids.id"/>');-->
				<!--</script>-->
				<t t-set="faqs" t-value="request.env['faqs'].search([('x_is_in_cart','=','True')])"/>
				<t t-set="no_of_step" t-value="0"/>
				<style>
                    .card {
                    width: 200px;
                    text-align: left;
                    margin: 20px;
                    }
                    .rating {
                    color: #FFD700;
                    font-size: 1.3rem;
                    }
                    .price {
                    font-size: 1.5rem;
                    font-weight: bold;
                    margin: 10px 0;
                    }
                    .time {
                    margin: 10px 0;
                    font-size: 1rem;
                    }
                    .btn-add {
                    color: purple;
                    border: 1px solid purple;
                    }
                    .btn-add:hover {
                    background-color: purple;
                    color: white;
                    }
                </style>
				<div class="container mt-5">
					<h4>Extra Addons you can add with your Service which might be Usefull for you!</h4>
					<div class="row justify-content-left">
						<div class="card bg-transparent border-0">
							<t t-foreach="product.accessory_product_ids" t-as="addons">
								<div class="card-body" style="border: 1px solid; background-color: transparent !important;">
									<img t-attf-src="data:image/jpeg;base64,{{ addons.image_1920 }}" style="mix-blend-mode: multiply; height: 80px; border-radius: 10px;" loading="lazy"/>
									<h5 class="card-title">
										<b>
											<t t-esc="addons.name"/>
										</b>
									</h5>
									<p class="price">₹
										<t t-esc="addons.list_price"/>
									</p>
									<!--<p class="time">45 mins</p>-->
									<button class="btn btn-add">Add</button>
								</div>
							</t>
						</div>
						<!--<div class="card bg-transparent border-0">-->
						<!--    <div class="card-body" style="border: 1px solid; background-color: transparent !important;">-->
						<!--        <h5 class="card-title">-->
						<!--            <b>Window AC</b>-->
						<!--        </h5>-->
						<!--        <p style="font-size: 1.2rem;">-->
						<!--            <span class="rating">★</span> 4.83 (402.8K)-->
						<!--        </p>-->
						<!--        <p class="price">₹649</p>-->
						<!--        <p class="time">45 mins</p>-->
						<!--        <button class="btn btn-add">Add</button>-->
						<!--    </div>-->
						<!--</div>-->
					</div>
				</div>
				<section class="s_title pt40 pb40 o_colored_level s_parallax_no_overflow_hidden o_cc o_cc1" data-vcss="001" data-scroll-background-ratio="0" data-snippet="s_title" data-name="Title" style="">
					<div class="s_allow_columns container">
						<h1 style="text-align: center;">
							<strong>How Its Works</strong>
						</h1>
					</div>
				</section>
				<section class="s_timeline pt24 pb48 o_colored_level" data-snippet="s_timeline" data-name="Timeline" style="background-image: none;" t-if="howitsworks">
					<div class="s_timeline_line container" style="border-color: rgb(0, 0, 0) !important;">
						<t t-foreach="howitsworks" t-as="hiw">
							<t t-if="hiw.id%2 == 0">
								<div class="s_timeline_row d-block d-md-flex flex-row-reverse" data-name="Row">
									<div class="s_timeline_date">
										<t t-esc="no_of_step %2 == 0 + 1"/>
									</div>
									<div class="s_timeline_content d-flex">
										<div class="s_timeline_card s_card card bg-white w-100" data-name="Card" data-snippet="s_card">
											<div class="card-body" style="text-align: justify;">
												<div class="img_container mb-4" style="margin: 0 auto; width: fit-content;">
													<img class="" t-att-src="image_data_uri (hiw.x_image)" alt="img" style="height: 204px  !important; " data-quality="100"/>
												</div>
												<h3 class="card-title">
													<t t-esc="hiw.name"/>
												</h3>
												<p class="card-text">
													<t t-esc="hiw.x_description"/>
												</p>
											</div>
										</div>
										<i class="fa fa-1x fa-child rounded-circle s_timeline_icon"/>
									</div>
									<div class="s_timeline_content"/></div>
							</t>
							<t t-else="">
								<div class="s_timeline_row d-block d-md-flex flex-row " data-name="Row">
									<div class="s_timeline_date">
										<!--<img class="rounded-circle " t-att-src="image_data_uri (hiw.x_image)"  alt="img"   style="width: 104px !important;height: 104px  !important;" data-quality="100"/>-->
									</div>
									<div class="s_timeline_content d-flex">
										<div class="s_timeline_card s_card card bg-white w-100" data-name="Card" data-snippet="s_card">
											<div class="card-body" style="text-align: justify;">
												<div class="img_container mb-4" style="margin: 0 auto; width: fit-content;">
													<img class="" t-att-src="image_data_uri (hiw.x_image)" alt="img" style="height: 204px  !important; " data-quality="100"/>
												</div>
												<h3 class="card-title">
													<t t-esc="hiw.name"/>
												</h3>
												<p class="card-text">
													<t t-esc="hiw.x_description"/>
												</p>
											</div>
										</div>
										<i class="fa fa-1x fa-child rounded-circle s_timeline_icon"/>
									</div>
									<div class="s_timeline_content"/></div>
							</t>
						</t>
					</div>
				</section>
				<div class="container">
					<!--<a role="button" class="btn btn-lg s_website_form_send" data-original-title="" title="" style="background: #FDCE4D;">Checkout</a>-->
					<t t-set="upgrade_product" t-value="env['product.template'].sudo().search([['id','=',service.x_product_ids.id]])"/>
					<form method="post" action="/shop/cart/update">
						<!--<form method="post" action="/shop/cart">-->
						<input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
						<input type="hidden" name="product_id" t-att-value="upgrade_product.id"/>
						<input type="hidden" name="add_qty" value="1"/>
						<!--<button type="submit" class="btn btn-primary">Upgrade</button>-->
						<p style="text-align: center; margin-top: 18px;" data-original-title="" title="" aria-describedby="tooltip769269">&amp;nbsp;
                            
							<span style="font-size: 14px;">
								<button type="submit" class="btn btn-outline-dark rounded-pill mt-3 px-4 py-2 shadow-sm">Check Out
                                </button>
							</span>
						</p>
					</form>
					<!--<t t-esc="service.x_product_ids.id"/>-->
					<!--<t t-esc="upgrade_product"/>-->
				</div>
				<section class="s_faq_collapse pt10 pb32 o_colored_level s_faq_collapse_light" data-snippet="s_faq_collapse" data-name="Accordion" style="background-image: none;" t-if="faqs">
					<!--<section class="s_faq_collapse pt10 pb32 o_colored_level s_faq_collapse_light" data-snippet="s_faq_collapse" data-name="Accordion" style="background-color: rgb(26, 41, 66) !important; background-image: none;">-->
					<h2 class="pb20" style="text-align: center;">
						<strong>FAQs</strong>
					</h2>
					<!--<div class="o_container_small">-->
					<!--    <div id="myCollapse1717583574552" class="accordion" role="tablist">-->
					<!--          <div class="card bg-white" data-name="Item">-->
					<!--            <a href="#" role="tab" data-toggle="collapse" aria-expanded="false" class="card-header collapsed" t-att-data-target= "'#body_'+str(faq.id)"><t t-esc="faq.x_question"/></a>-->
					<!--            <div class="collapse" data-parent="#myCollapse1717583574552" role="tabpanel" t-att-id="'body_'+str(faq.id)" >-->
					<!--              <div class="card-body">-->
					<!--                <p class="card-text o_default_snippet_text" style="margin: 0 1rem;"><t t-esc="faq.x_answers"/></p>-->
					<!--              </div>-->
					<!--            </div>-->
					<!--          </div>-->
					<!--    </div>-->
					<!--</div>-->
					<t t-foreach="faqs" t-as="faq">
						<div class="container collapse-container" style="transition: max-height 0.3s ease-out;">
							<a type="button" class="btn collapse_heading" data-toggle="collapse" style="transform-style: none important; width: 100%; text-align: left; background: transparent; font-size: 20px; transition: max-height 0.3s ease-out;" t-att-data-target="'#body_'+str(faq.id)" aria-expanded="true">
								<t t-esc="faq.x_question"/>
								<p class="dropdown-toggle" style="float: right;"/>
							</a>
							<div t-att-id="'body_'+str(faq.id)" class="collapse_content collapse" style="padding: 20px;">
								<p style="text-align: left;">
									<span style="font-size: 18px;">
										<t t-esc="faq.x_answers"/>
									</span>
								</p>
							</div>
						</div>
					</t>
				</section>
			</div>
		</t>
	</template>
</odoo>