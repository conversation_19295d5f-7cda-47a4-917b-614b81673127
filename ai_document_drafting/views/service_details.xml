<?xml version="1.0" encoding="utf-8"?>
 <odoo>
    <data>
        <record id="action_document_drafting" model="ir.actions.act_window">
            <field name="name">Document Drafting</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">document_drafting</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_document_drafting_tree" model="ir.ui.view">
            <field name="name">document_drafting.tree</field>
            <field name="model">document_drafting</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="x_service_id"/>
                    <field name="x_state_id" options="{'no_open': True, 'no_quick_create': True}"/>
                    <field name="x_language_id" options="{'no_open': True, 'no_quick_create': True}"  widget="many2many_tags"/>
                    <field name="x_cost"/>
                    
                </tree>
            </field>
        </record>

         <record id="view_document_drafting_form" model="ir.ui.view">
            <field name="name">document_drafting.form</field>
            <field name="model">document_drafting</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <field name="x_image" widget="image" class="oe_avatar" options="{&quot;preview_image&quot;: &quot;avatar_128&quot;}"/>
                    <group>
                      <group>
                        <field name="name"/>
                        <field name="x_description"/>
                        <field name="x_service_id"/>
                        <field name="x_language_id" options="{'no_open': True, 'no_create': True}" widget="many2many_tags"/>
                      </group>
                      <group>
                       <field name="x_state_id" options="{'no_open': True, 'no_quick_create': True}"/>
                        <field name="x_cost"/>
                        <field name="x_product_ids"/>
                        <field name="x_custom_url"/>
                      </group>
                      </group>
                      <group>
                        <field name="x_desc"/>
                      </group>
                      <notebook>
                      <page string="Dynamic Fields">
                            <field name="x_dynamic_fields">
                                <tree editable="bottom">
                                    <field name="name"/>
                                    <field name="label"/>
                                    <field name="field_type"/>
                                    <field name="relation_model"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Tips For The Service">
                          <field name="x_tips"/>
                        </page>
                        <page string="About The Service">
                        <field name="x_about"/>
                        </page>
                        <page string="How It Works  ">
                        <field name="x_howitsworks"/>
                        </page>
                        <page string="FAQS About Services  ">
                        <field name="x_faqs"/>
                        </page>
                        <page string="Topics">
                        <field name="x_topics"/>                                 
                        </page>
                      </notebook>
                    
                     </sheet>
                     <div class="oe_chatter">
                               <field name="message_follower_ids"/>
                               <field name="activity_ids"/>
                               <field name="message_ids"
                                      options="{'post_refresh': 'recipients'}"/>
                            </div>
                 </form>
            </field>
        </record>

          


    
        <!-- <menuitem id="menu_settings"  parent="menu_diary_management"  name="Settings" sequence="15"/> -->
        <menuitem id="menu_document_drafting" name="Document Drafting"  sequence="10"/>
        <menuitem id="menu_document_draftings"  parent="menu_document_drafting" name="Document Drafting Services" action="ai_document_drafting.action_document_drafting" sequence="3"/>


    </data>
</odoo>