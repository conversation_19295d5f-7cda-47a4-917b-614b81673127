<odoo>
<template id="document_drafting_code" inherit_id="portal.portal_my_home" name="Custom Document Drafting Code">
    <xpath expr="//div[@class='o_portal_docs row g-2']" position="inside">
        <div class="o_portal_category row g-2 mt-3" id="portal_document_drafting_category">
            <div class="o_portal_index_card col-md-6">
                <a href="/my/services" title="Document Drafting"
                   class="d-flex justify-content-start gap-2 gap-md-3 align-items-center py-3 pe-2 px-md-3 h-100 rounded text-decoration-none text-reset text-bg-light">
                    <div class="o_portal_icon align-self-start">
                        <img src="/project/static/src/img/folder.svg" loading="lazy" alt="Document Drafting Icon"/>
                    </div>
                    <div>
                        <h5 class="mt-0 mb-1">Document Drafting</h5>
                        <p class="mb-0 text-muted">Access drafting services and manage your documents</p>
                    </div>
                </a>
            </div>
        </div>
    </xpath>
</template>

    <!-- Breadcrumb and Menu Entry Template -->
    <template id="portal_layout" name="Portal Layout: Drafting Services" inherit_id="portal.portal_breadcrumbs" priority="40">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'service_listing'" class="breadcrumb-item active">
                Services
            </li>
            <li t-if="page_name == 'service_detail'" class="breadcrumb-item">
                <a t-attf-href="/my/services">Services</a>
            </li>
            <li t-if="page_name == 'service_detail'" class="breadcrumb-item active text-truncate col-8 col-lg-10">
                <t t-esc="service.name"/>
            </li>
        </xpath>
    </template>

    <!-- Main Services Listing Template -->
    <template id="portal_my_services" name="My Services">
        <t t-call="portal.portal_layout">
            <t t-set="page_name" t-value="'service_listing'"/>
            <div class="container">
                <h1>Available Drafting Services</h1>
                <div class="row" style="display: flex; flex-wrap: wrap; gap: 1rem;">
                    <t t-if="services">
                        <t t-foreach="services" t-as="service">
                            <div class="col-md-4 d-flex align-items-stretch">
                                <div class="card" style="width: 100%;">
                                    <img t-if="service.x_image" t-att-src="('/web/image/document_drafting/%d/x_image' % service.id)" class="card-img-top"/>
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <a t-att-href="'/my/services/%d' % service.id">
                                                <t t-esc="service.name"/>
                                            </a>
                                        </h5>
                                        <p class="card-text"><t t-esc="service.x_description or ''"/></p>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </t>
                    <t t-else="">
                        <div class="alert alert-warning" role="alert">
                            No services available.
                        </div>
                    </t>
                </div>
            </div>
        </t>
    </template>


    <!-- Service Detail Page -->
   <template id="portal_my_service" name="Service Detail">
        <t t-call="portal.portal_layout">
            <t t-set="page_name" t-value="'service_detail'"/>
            <t t-set="service" t-value="service"/>
            <div class="container">
                <h1>Service: <t t-esc="service.name"/></h1>

                <div class="row mt-4">
                    <div class="col-12">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>State</th>
                                    <th>Languages</th>
                                    <th>Cost</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><t t-esc="service.x_description or ''"/></td>
                                    <td><t t-esc="service.x_state_id.name or ''"/></td>
                                    <td><t t-esc="', '.join(service.x_language_id.mapped('name')) or ''"/></td>
                                    <td><t t-esc="service.x_cost or ''"/></td>
                                    <td><t t-esc="service.x_status or ''"/></td>
                                    
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </t>
    </template>

</odoo>