<odoo>
    <data>
        <!-- Website Page Record -->
        <record id="document_drafting_page" model="website.page">
            <field name="name">Document Draftings</field>
            <field name="url">/document-drafting</field>
            <field name="view_id" ref="ai_document_drafting.document_drafting_webpage"/>
        </record>

        <record id="howitsworks_page" model="website.page">
            <field name="name">How Its Works</field>
            <field name="url">/howitsworks</field>
            <field name="view_id" ref="ai_document_drafting.howitsworks"/>
        </record>

        <record id="document_drafting_description_page" model="website.page">
            <field name="name">Document Drafting Description</field>
            <field name="url">/document-drafting-description</field>
            <field name="view_id" ref="ai_document_drafting.document_drafting_description"/>
        </record>

        <record id="sub_document_drafting_page" model="website.page">
            <field name="name">Sub Document Drafting</field>
            <field name="url">/sub-document-drafting</field>
            <field name="view_id" ref="ai_document_drafting.sub_document_drafting"/>
        </record>

        <record id="document_drafting_sub_description_page" model="website.page">
            <field name="name">Document Drafting Sub Description</field>
            <field name="url">/document_drafting_sub_description</field>
            <field name="view_id" ref="ai_document_drafting.document_drafting_sub_description"/>
        </record>
        <record id="menu_document_drafting_services" model="website.menu">
            <field name="name">My Services</field>
            <field name="url">/my/services</field>
            <field name="parent_id" ref="website.main_menu"/>
            <field name="sequence" type="int">10</field>
        </record>
    </data>
</odoo>
