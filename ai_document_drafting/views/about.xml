<?xml version="1.0" encoding="UTF-8" ?>

<odoo>
    <data>
        <record id="action_about" model="ir.actions.act_window">
            <field name="name">About</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">about</field>
            <field name="view_mode">tree,form</field>
        </record>

         <record id="view_about_tree" model="ir.ui.view">
            <field name="name">about.tree</field>
            <field name="model">about</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                </tree>
            </field>
        </record>

         <record id="view_about_form" model="ir.ui.view">
            <field name="name">about.form</field>
            <field name="model">about</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                    <group>
                    
                        <field name="name"/>
                        <!-- <field name="x_about_id"/> -->
                        <field name="x_inclusion"/>
                        <field name="x_sequence"/>

                      
                     </group>
                     </sheet>
                     <div class="oe_chatter">
                               <field name="message_follower_ids"/>
                               <field name="activity_ids"/>
                               <field name="message_ids"
                                      options="{'post_refresh': 'recipients'}"/>
                            </div>
                 </form>
            </field>
        </record>

          


    
        <!-- <menuitem id="menu_settings"  parent="menu_diary_management"  name="Settings" sequence="15"/> -->
        <!-- <menuitem id="menu_document_drafting" name="document_drafting"  sequence="10"/> -->
        <menuitem id="menu_document_drafting" name="Document Drafting"  sequence="10"/>
        <menuitem id="menu_about"  parent="menu_document_drafting" name="About" action="ai_document_drafting.action_about" sequence="11"/>


    </data>
</odoo>