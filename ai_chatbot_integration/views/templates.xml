<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Override Legal Document Conversation Template -->
    <template id="legal_document_conversation" name="Legal Document Conversation" inherit_id="ai_legal_document_core.legal_document_conversation">
        <xpath expr="//script" position="replace">
            <script type="text/javascript">
                <![CDATA[
                $(document).ready(function() {
                    // Scroll to bottom of conversation
                    var conversationDiv = document.getElementById('conversation-messages');
                    if (conversationDiv) {
                        conversationDiv.scrollTop = conversationDiv.scrollHeight;
                    }

                    // Handle form submission
                    $('#conversation-form').submit(function(e) {
                        e.preventDefault();

                        var requestId = $('input[name="request_id"]').val();
                        var message = $('input[name="message"]').val();

                        if (!message) return;

                        // Clear input
                        $('input[name="message"]').val('');

                        // Add message to conversation (optimistic UI update)
                        var userMessageHtml = `
                            <div class="message mb-3 user-message">
                                <div class="message-bubble p-3 rounded bg-primary text-white">
                                    <div class="message-header mb-2">
                                        <strong>${$('input[name="message"]').data('user-name') || 'You'}</strong>
                                        <small class="text-muted ms-2">Just now</small>
                                    </div>
                                    <div class="message-content">
                                        ${message}
                                    </div>
                                </div>
                            </div>
                        `;
                        $('#conversation-messages').append(userMessageHtml);

                        // Scroll to bottom
                        conversationDiv.scrollTop = conversationDiv.scrollHeight;

                        // Show loading indicator
                        var loadingHtml = `
                            <div id="ai-loading" class="message mb-3 ai-message">
                                <div class="message-bubble p-3 rounded bg-light">
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">AI is thinking</span>
                                        <div class="spinner-border spinner-border-sm" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        $('#conversation-messages').append(loadingHtml);
                        conversationDiv.scrollTop = conversationDiv.scrollHeight;

                        // Send message to server
                        $.ajax({
                            url: '/legal-documents/conversation/send',
                            type: 'POST',
                            dataType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify({
                                jsonrpc: '2.0',
                                method: 'call',
                                params: {
                                    request_id: requestId,
                                    message: message
                                }
                            }),
                            success: function(data) {
                                // Remove loading indicator
                                $('#ai-loading').remove();

                                if (data.result &amp;&amp; data.result.success) {
                                    // Add AI response to conversation
                                    var aiMessage = data.result.message;
                                    var hasForm = data.result.has_form;
                                    var formData = data.result.form_data;

                                    var aiMessageHtml = `
                                        <div class="message mb-3 ai-message">
                                            <div class="message-bubble p-3 rounded bg-light">
                                                <div class="message-header mb-2">
                                                    <strong>AI Assistant</strong>
                                                    <small class="text-muted ms-2">Just now</small>
                                                </div>
                                                <div class="message-content">
                                                    ${window.AIMessageFormatter ? window.AIMessageFormatter.formatAIMessage(aiMessage) : aiMessage.replace(/\n/g, '<br/>')}
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                    $('#conversation-messages').append(aiMessageHtml);

                                    // If there's form data, render the form
                                    if (hasForm &amp;&amp; formData &amp;&amp; formData.form) {
                                        renderDynamicForm(formData.form, requestId);
                                    }

                                    // Scroll to bottom
                                    conversationDiv.scrollTop = conversationDiv.scrollHeight;
                                } else {
                                    console.error('Error sending message:', data.result ? data.result.error : 'Unknown error');

                                    // Show error message
                                    var errorHtml = `
                                        <div class="message mb-3 ai-message">
                                            <div class="message-bubble p-3 rounded bg-danger text-white">
                                                <div class="message-header mb-2">
                                                    <strong>Error</strong>
                                                    <small class="text-muted ms-2">Just now</small>
                                                </div>
                                                <div class="message-content">
                                                    Sorry, there was an error processing your message. Please try again.
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                    $('#conversation-messages').append(errorHtml);
                                    conversationDiv.scrollTop = conversationDiv.scrollHeight;
                                }
                            },
                            error: function(xhr, status, error) {
                                // Remove loading indicator
                                $('#ai-loading').remove();

                                console.error('AJAX error:', error);

                                // Show error message
                                var errorHtml = `
                                    <div class="message mb-3 ai-message">
                                        <div class="message-bubble p-3 rounded bg-danger text-white">
                                            <div class="message-header mb-2">
                                                <strong>Error</strong>
                                                <small class="text-muted ms-2">Just now</small>
                                            </div>
                                            <div class="message-content">
                                                Sorry, there was an error processing your message. Please try again.
                                            </div>
                                        </div>
                                    </div>
                                `;
                                $('#conversation-messages').append(errorHtml);
                                conversationDiv.scrollTop = conversationDiv.scrollHeight;
                            }
                        });
                    });

                    // Function to render dynamic form
                    function renderDynamicForm(formFields, requestId) {
                        var formHtml = `
                            <div class="message mb-3 ai-message">
                                <div class="message-bubble p-3 rounded bg-light">
                                    <div class="message-header mb-2">
                                        <strong>Form</strong>
                                        <small class="text-muted ms-2">Please fill out the form</small>
                                    </div>
                                    <div class="dynamic-form p-3 bg-white border rounded">
                                        <form id="dynamic-form" class="needs-validation" novalidate="novalidate">
                                            <input type="hidden" name="request_id" value="${requestId}">
                        `;

                        // Add form fields
                        formFields.forEach(function(field) {
                            var fieldHtml = '';
                            var requiredAttr = field.required ? 'required' : '';
                            var requiredStar = field.required ? '<span class="text-danger">*</span>' : '';

                            switch (field.type) {
                                case 'text':
                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                                            <input type="text" class="form-control" id="${field.name}" name="${field.name}" ${requiredAttr}/>
                                            <div class="invalid-feedback">Please provide a value for this field.</div>
                                        </div>
                                    `;
                                    break;

                                case 'textarea':
                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                                            <textarea class="form-control" id="${field.name}" name="${field.name}" rows="3" ${requiredAttr}></textarea>
                                            <div class="invalid-feedback">Please provide a value for this field.</div>
                                        </div>
                                    `;
                                    break;

                                case 'select':
                                    var options = '';
                                    if (field.options &amp;&amp; Array.isArray(field.options)) {
                                        options = '<option value="">Select an option</option>';
                                        field.options.forEach(function(option) {
                                            options += `<option value="${option}">${option}</option>`;
                                        });
                                    }

                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                                            <select class="form-select" id="${field.name}" name="${field.name}" ${requiredAttr}>
                                                ${options}
                                            </select>
                                            <div class="invalid-feedback">Please select an option.</div>
                                        </div>
                                    `;
                                    break;

                                case 'date':
                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                                            <input type="date" class="form-control" id="${field.name}" name="${field.name}" ${requiredAttr}>
                                            <div class="invalid-feedback">Please provide a valid date.</div>
                                        </div>
                                    `;
                                    break;

                                case 'number':
                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                                            <input type="number" class="form-control" id="${field.name}" name="${field.name}" ${requiredAttr}>
                                            <div class="invalid-feedback">Please provide a valid number.</div>
                                        </div>
                                    `;
                                    break;

                                case 'checkbox':
                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="${field.name}" name="${field.name}" ${requiredAttr}>
                                                <label class="form-check-label" for="${field.name}">${field.label} ${requiredStar}</label>
                                                <div class="invalid-feedback">This field is required.</div>
                                            </div>
                                        </div>
                                    `;
                                    break;

                                case 'radio':
                                    var options = '';
                                    if (field.options &amp;&amp; Array.isArray(field.options)) {
                                        field.options.forEach(function(option, index) {
                                            options += `
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="${field.name}" id="${field.name}_${index}" value="${option}" ${index === 0 &amp;&amp; field.required ? 'required' : ''}>
                                                    <label class="form-check-label" for="${field.name}_${index}">
                                                        ${option}
                                                    </label>
                                                </div>
                                            `;
                                        });
                                    }

                                    fieldHtml = `
                                        <div class="form-group mb-3">
                                            <label>${field.label} ${requiredStar}</label>
                                            ${options}
                                            <div class="invalid-feedback">Please select an option.</div>
                                        </div>
                                    `;
                                    break;
                            }

                            formHtml += fieldHtml;
                        });

                        formHtml += `
                                            <div class="form-group mt-4">
                                                <button type="submit" class="btn btn-primary">Submit</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        `;

                        $('#conversation-messages').append(formHtml);

                        // Handle form submission
                        $('#dynamic-form').submit(function(e) {
                            e.preventDefault();

                            // Form validation
                            if (!this.checkValidity()) {
                                e.stopPropagation();
                                $(this).addClass('was-validated');
                                return;
                            }

                            var requestId = $(this).find('input[name="request_id"]').val();
                            var formData = {};

                            // Collect form data
                            $(this).serializeArray().forEach(function(item) {
                                if (item.name !== 'request_id') {
                                    formData[item.name] = item.value;
                                }
                            });

                            // Add checkboxes that are not checked (not included in serializeArray)
                            $(this).find('input[type="checkbox"]').each(function() {
                                var name = $(this).attr('name');
                                if (!(name in formData)) {
                                    formData[name] = false;
                                } else {
                                    formData[name] = true;
                                }
                            });

                            // Remove the form
                            $(this).closest('.message').remove();

                            // Show loading indicator
                            var loadingHtml = `
                                <div id="ai-loading" class="message mb-3 ai-message">
                                    <div class="message-bubble p-3 rounded bg-light">
                                        <div class="d-flex align-items-center">
                                            <span class="me-2">Processing form submission</span>
                                            <div class="spinner-border spinner-border-sm" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                            $('#conversation-messages').append(loadingHtml);
                            conversationDiv.scrollTop = conversationDiv.scrollHeight;

                            // Submit form data
                            $.ajax({
                                url: '/legal-documents/conversation/submit-form',
                                type: 'POST',
                                dataType: 'json',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    jsonrpc: '2.0',
                                    method: 'call',
                                    params: {
                                        request_id: requestId,
                                        form_data: formData
                                    }
                                }),
                                success: function(data) {
                                    // Remove loading indicator
                                    $('#ai-loading').remove();

                                    if (data.result &amp;&amp; data.result.success) {
                                        // Add form submission message
                                        var formSubmissionHtml = `
                                            <div class="message mb-3 user-message">
                                                <div class="message-bubble p-3 rounded bg-primary text-white">
                                                    <div class="message-header mb-2">
                                                        <strong>You</strong>
                                                        <small class="text-muted ms-2">Just now</small>
                                                    </div>
                                                    <div class="message-content">
                                                        <strong>Form submitted with the following information:</strong><br/>
                                                        <ul>
                                        `;

                                        for (var key in formData) {
                                            formSubmissionHtml += `<li><strong>${key}:</strong> ${formData[key]}</li>`;
                                        }

                                        formSubmissionHtml += `
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                        $('#conversation-messages').append(formSubmissionHtml);

                                        // Add AI response
                                        var aiMessage = data.result.message;
                                        var hasForm = data.result.has_form;
                                        var formData = data.result.form_data;

                                        var aiMessageHtml = `
                                            <div class="message mb-3 ai-message">
                                                <div class="message-bubble p-3 rounded bg-light">
                                                    <div class="message-header mb-2">
                                                        <strong>AI Assistant</strong>
                                                        <small class="text-muted ms-2">Just now</small>
                                                    </div>
                                                    <div class="message-content">
                                                        ${window.AIMessageFormatter ? window.AIMessageFormatter.formatAIMessage(aiMessage) : aiMessage.replace(/\n/g, '<br/>')}
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                        $('#conversation-messages').append(aiMessageHtml);

                                        // If there's form data, render the form
                                        if (hasForm &amp;&amp; formData &amp;&amp; formData.form) {
                                            renderDynamicForm(formData.form, requestId);
                                        }

                                        // Scroll to bottom
                                        conversationDiv.scrollTop = conversationDiv.scrollHeight;
                                    } else {
                                        console.error('Error submitting form:', data.result ? data.result.error : 'Unknown error');

                                        // Show error message
                                        var errorHtml = `
                                            <div class="message mb-3 ai-message">
                                                <div class="message-bubble p-3 rounded bg-danger text-white">
                                                    <div class="message-header mb-2">
                                                        <strong>Error</strong>
                                                        <small class="text-muted ms-2">Just now</small>
                                                    </div>
                                                    <div class="message-content">
                                                        Sorry, there was an error processing your form submission. Please try again.
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                        $('#conversation-messages').append(errorHtml);
                                        conversationDiv.scrollTop = conversationDiv.scrollHeight;
                                    }
                                },
                                error: function(xhr, status, error) {
                                    // Remove loading indicator
                                    $('#ai-loading').remove();

                                    console.error('AJAX error:', error);

                                    // Show error message
                                    var errorHtml = `
                                        <div class="message mb-3 ai-message">
                                            <div class="message-bubble p-3 rounded bg-danger text-white">
                                                <div class="message-header mb-2">
                                                    <strong>Error</strong>
                                                    <small class="text-muted ms-2">Just now</small>
                                                </div>
                                                <div class="message-content">
                                                    Sorry, there was an error processing your form submission. Please try again.
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                    $('#conversation-messages').append(errorHtml);
                                    conversationDiv.scrollTop = conversationDiv.scrollHeight;
                                }
                            });
                        });
                    }
                });
                ]]>
            </script>
        </xpath>
    </template>
</odoo>
