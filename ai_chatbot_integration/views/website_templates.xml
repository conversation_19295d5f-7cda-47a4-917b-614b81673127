<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Main AI Legal Assistant Page -->
    <template id="legal_assistant_page" name="AI Legal Assistant">
        <t t-call="website.layout">
            <t t-set="pageName" t-value="'legal_assistant'"/>
            <t t-set="title">AI Legal Assistant</t>
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-lg-12 text-center mb-5">
                            <h1 class="display-4">
                                <i class="fa fa-balance-scale text-primary me-3"></i>
                                AI Legal Assistant
                            </h1>
                            <p class="lead">Get professional legal documents with AI-powered assistance</p>
                        </div>
                    </div>

                    <!-- Language Selection -->
                    <div class="row mb-4">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title text-center mb-4">
                                        <i class="fa fa-globe text-primary me-2"></i>
                                        Select Your Language
                                    </h5>
                                    <div class="row g-2">
                                        <!-- Static language buttons to avoid template conflicts -->
                                        <div class="col-lg-3 col-md-6">
                                            <a href="/legal-assistant?lang=en_US"
                                               t-att-class="'btn w-100 language-btn' + (' btn-primary' if selected_language == 'en_US' else ' btn-outline-primary')">
                                                <div class="d-flex flex-column align-items-center">
                                                    <span class="fw-bold">English</span>
                                                    <small class="badge mt-1 bg-secondary">6 services</small>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="/legal-assistant?lang=hi_IN"
                                               t-att-class="'btn w-100 language-btn' + (' btn-primary' if selected_language == 'hi_IN' else ' btn-outline-primary')">
                                                <div class="d-flex flex-column align-items-center">
                                                    <span class="fw-bold">हिंदी</span>
                                                    <small class="badge mt-1 bg-secondary">4 services</small>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="/legal-assistant?lang=es_ES"
                                               t-att-class="'btn w-100 language-btn' + (' btn-primary' if selected_language == 'es_ES' else ' btn-outline-primary')">
                                                <div class="d-flex flex-column align-items-center">
                                                    <span class="fw-bold">Español</span>
                                                    <small class="badge mt-1 bg-secondary">5 services</small>
                                                </div>
                                            </a>
                                        </div>
                                        <div class="col-lg-3 col-md-6">
                                            <a href="/legal-assistant?lang=fr_FR"
                                               t-att-class="'btn w-100 language-btn' + (' btn-primary' if selected_language == 'fr_FR' else ' btn-outline-primary')">
                                                <div class="d-flex flex-column align-items-center">
                                                    <span class="fw-bold">Français</span>
                                                    <small class="badge mt-1 bg-secondary">3 services</small>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Section -->
                    <div class="row mb-4">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title text-center mb-3">
                                        <i class="fa fa-search text-primary me-2"></i>
                                        Search Legal Services
                                    </h5>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fa fa-search"></i>
                                        </span>
                                        <input type="text" id="service-search" class="form-control form-control-lg"
                                               placeholder="Search for legal services (e.g., contract, agreement, NDA...)"/>
                                        <button class="btn btn-outline-secondary" type="button" id="clear-search">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fa fa-info-circle me-1"></i>
                                            Live search - results update as you type
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Selection -->
                    <div class="row">
                        <div class="col-lg-12">
                            <h3 class="text-center mb-4">
                                <i class="fa fa-briefcase text-primary me-2"></i>
                                Choose Your Legal Service
                            </h3>
                            <div class="row" id="services-container">
                                <t t-if="services">
                                    <t t-foreach="services" t-as="svc">
                                        <div class="col-lg-4 col-md-6 mb-4 service-item"
                                             t-att-data-service-name="str(svc.get('name', '')).lower()"
                                             t-att-data-service-description="str(svc.get('description', '')).lower()">
                                            <div t-att-class="'card h-100 service-card shadow-sm' + (' border-warning' if svc.get('is_custom') else '')"
                                                 style="transition: all 0.3s ease; cursor: pointer;"
                                                 t-att-onclick="'window.location.href=\'/legal-services/' + svc.get('code', 'service') + '\''">
                                                <div class="card-body text-center position-relative">
                                                    <t t-if="svc.get('is_custom')">
                                                        <div class="position-absolute top-0 end-0 m-2">
                                                            <span class="badge bg-warning text-dark">
                                                                <i class="fa fa-star me-1"></i>Custom
                                                            </span>
                                                        </div>
                                                    </t>

                                                    <div class="service-icon mb-3">
                                                        <i t-att-class="'fa fa-3x text-primary ' + ('fa-magic' if svc.get('is_custom') else 'fa-file-text')"></i>
                                                    </div>

                                                    <h5 class="card-title fw-bold" t-esc="svc.get('name', 'Unknown Service')"/>
                                                    <p class="card-text text-muted" t-esc="svc.get('description', 'No description available')"/>

                                                    <div class="price-tag mb-3">
                                                        <span class="h4 text-success fw-bold">
                                                            $<t t-esc="svc.get('price', 299)"/>
                                                        </span>
                                                        <small class="text-muted d-block">Starting price</small>
                                                    </div>

                                                    <div class="supported-languages mb-3">
                                                        <small class="text-muted">
                                                            <i class="fa fa-globe me-1"></i>
                                                            Available in:
                                                            <t t-if="svc.get('language_ids')">
                                                                <t t-foreach="svc.get('language_ids', [])" t-as="lang_info">
                                                                    <span class="badge bg-light text-dark me-1" t-esc="lang_info.get('name', 'Unknown')"/>
                                                                </t>
                                                            </t>
                                                            <t t-else="">
                                                                <span class="badge bg-light text-dark">English</span>
                                                            </t>
                                                        </small>
                                                    </div>

                                                    <div class="btn btn-primary btn-lg w-100 service-btn">
                                                        <i class="fa fa-robot me-2"></i>
                                                        <t t-if="svc.get('is_custom')">
                                                            Get Custom Solution
                                                        </t>
                                                        <t t-else="">
                                                            Start AI Assistant
                                                        </t>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </t>
                                </t>

                                <!-- Fallback message if no services for selected language -->
                                <t t-if="not services">
                                    <div class="col-lg-12">
                                        <div class="alert alert-warning text-center">
                                            <h5><i class="fa fa-info-circle me-2"></i>No services available</h5>
                                            <p>No legal services are available for the selected language. Please try a different language or contact support.</p>
                                            <a href="/legal-assistant?lang=en_US" class="btn btn-primary">
                                                <i class="fa fa-globe me-2"></i>Switch to English
                                            </a>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </div>
                    </div>

                    <!-- Full Page Chatbot Interface -->
                    <div id="fullpage-chatbot" class="row mt-5" style="display: none;">
                        <div class="col-lg-8 offset-lg-2">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fa fa-robot me-2"></i>
                                        AI Legal Assistant
                                        <button class="btn btn-sm btn-outline-light float-end" id="close-fullpage-chat">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </h5>
                                </div>
                                <div class="card-body p-0">
                                    <div id="fullpage-chat-messages" class="chat-messages-container" style="height: 500px; overflow-y: auto; padding: 20px;">
                                        <!-- Messages will be added here -->
                                    </div>

                                    <!-- Form Container -->
                                    <div id="fullpage-form-container" class="p-3 border-top" style="display: none;">
                                        <!-- AI-generated forms will appear here -->
                                    </div>

                                    <!-- Chat Input -->
                                    <div class="card-footer">
                                        <div class="input-group">
                                            <input type="text" id="fullpage-chat-input" class="form-control"
                                                   placeholder="Type your message..."/>
                                            <button class="btn btn-primary" id="fullpage-send-message">
                                                <i class="fa fa-paper-plane"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom CSS for animations and styling -->
            <style>
                .service-card {
                    transition: all 0.3s ease;
                    border: 1px solid #e0e0e0;
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                }

                .service-card .card-body {
                    display: flex;
                    flex-direction: column;
                    flex-grow: 1;
                }

                .service-card .service-btn {
                    margin-top: auto;
                }

                .service-card:hover {
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
                    border-color: #007bff;
                }

                .service-card:hover .service-icon i {
                    transform: scale(1.05);
                    transition: transform 0.3s ease;
                    color: #0056b3 !important;
                }

                .service-card:hover .service-btn {
                    background: linear-gradient(45deg, #007bff, #0056b3);
                    transform: scale(1.01);
                }

                .language-btn {
                    transition: all 0.2s ease;
                    border-radius: 8px;
                }

                .language-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                }

                .service-item {
                    transition: all 0.3s ease;
                }

                .service-item.hidden {
                    display: none !important;
                }

                .search-highlight {
                    background-color: #fff3cd;
                    padding: 2px 4px;
                    border-radius: 3px;
                }

                #service-search {
                    border-radius: 8px;
                }

                .card {
                    border-radius: 12px;
                }

                .btn {
                    border-radius: 8px;
                }

                .service-icon i {
                    transition: all 0.3s ease;
                }

                .no-results {
                    text-align: center;
                    padding: 40px;
                    color: #6c757d;
                }
            </style>

            <!-- JavaScript for search functionality and animations -->
            <script>
                <![CDATA[
                document.addEventListener('DOMContentLoaded', function() {
                    const searchInput = document.getElementById('service-search');
                    const clearButton = document.getElementById('clear-search');
                    const servicesContainer = document.getElementById('services-container');
                    const serviceItems = document.querySelectorAll('.service-item');

                    if (searchInput && clearButton && servicesContainer) {
                        // Live search functionality
                        searchInput.addEventListener('input', function() {
                            const searchTerm = this.value.toLowerCase().trim();
                            let visibleCount = 0;

                            serviceItems.forEach(function(item) {
                                const serviceName = item.getAttribute('data-service-name') || '';
                                const serviceDescription = item.getAttribute('data-service-description') || '';

                                if (!searchTerm || serviceName.includes(searchTerm) || serviceDescription.includes(searchTerm)) {
                                    item.classList.remove('hidden');
                                    visibleCount++;

                                    // Highlight search terms
                                    if (searchTerm) {
                                        highlightSearchTerm(item, searchTerm);
                                    } else {
                                        removeHighlight(item);
                                    }
                                } else {
                                    item.classList.add('hidden');
                                    removeHighlight(item);
                                }
                            });

                            // Show/hide no results message
                            toggleNoResultsMessage(visibleCount === 0 && searchTerm);

                            // Show/hide clear button
                            clearButton.style.display = searchTerm ? 'block' : 'none';
                        });

                        // Clear search functionality
                        clearButton.addEventListener('click', function() {
                            searchInput.value = '';
                            serviceItems.forEach(function(item) {
                                item.classList.remove('hidden');
                                removeHighlight(item);
                            });
                            toggleNoResultsMessage(false);
                            clearButton.style.display = 'none';
                            searchInput.focus();
                        });

                        // Function to highlight search terms
                        function highlightSearchTerm(item, term) {
                            const title = item.querySelector('.card-title');
                            const description = item.querySelector('.card-text');

                            if (title && description) {
                                // Store original text if not already stored
                                if (!title.getAttribute('data-original-text')) {
                                    title.setAttribute('data-original-text', title.textContent);
                                }
                                if (!description.getAttribute('data-original-text')) {
                                    description.setAttribute('data-original-text', description.textContent);
                                }

                                const titleText = title.getAttribute('data-original-text');
                                const descText = description.getAttribute('data-original-text');

                                title.innerHTML = titleText.replace(new RegExp('(' + term + ')', 'gi'), '<span class="search-highlight">$1</span>');
                                description.innerHTML = descText.replace(new RegExp('(' + term + ')', 'gi'), '<span class="search-highlight">$1</span>');
                            }
                        }

                        // Function to remove highlights
                        function removeHighlight(item) {
                            const title = item.querySelector('.card-title');
                            const description = item.querySelector('.card-text');

                            if (title && title.getAttribute('data-original-text')) {
                                title.innerHTML = title.getAttribute('data-original-text');
                            }
                            if (description && description.getAttribute('data-original-text')) {
                                description.innerHTML = description.getAttribute('data-original-text');
                            }
                        }

                        // Function to toggle no results message
                        function toggleNoResultsMessage(show) {
                            let noResultsDiv = document.getElementById('no-results');

                            if (show && !noResultsDiv) {
                                noResultsDiv = document.createElement('div');
                                noResultsDiv.id = 'no-results';
                                noResultsDiv.className = 'col-12 no-results';
                                noResultsDiv.innerHTML = `
                                    <div class="text-center py-5">
                                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No services found</h5>
                                        <p class="text-muted">Try adjusting your search terms or browse all available services.</p>
                                        <button class="btn btn-outline-primary" onclick="document.getElementById('service-search').value=''; document.getElementById('service-search').dispatchEvent(new Event('input'));">
                                            <i class="fa fa-refresh me-2"></i>Show All Services
                                        </button>
                                    </div>
                                `;
                                servicesContainer.appendChild(noResultsDiv);
                            } else if (!show && noResultsDiv) {
                                noResultsDiv.remove();
                            }
                        }

                        // Initialize clear button state
                        clearButton.style.display = 'none';

                        // Add keyboard shortcuts
                        document.addEventListener('keydown', function(e) {
                            // Focus search on Ctrl+F or Cmd+F
                            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                                e.preventDefault();
                                searchInput.focus();
                            }

                            // Clear search on Escape
                            if (e.key === 'Escape' && document.activeElement === searchInput) {
                                clearButton.click();
                            }
                        });

                        // Add some visual feedback for search
                        searchInput.addEventListener('focus', function() {
                            this.parentElement.classList.add('border-primary');
                        });

                        searchInput.addEventListener('blur', function() {
                            this.parentElement.classList.remove('border-primary');
                        });
                    }
                });
                ]]>
            </script>
        </t>
    </template>

    <!-- Legal Services Overview Page -->
    <template id="legal_services_page" name="Legal Services">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-lg-12 text-center mb-5">
                            <h1 class="display-4">Legal Services</h1>
                            <p class="lead">Professional legal document services powered by AI</p>
                        </div>
                    </div>

                    <div class="row">
                        <t t-foreach="service_groups" t-as="group_name">
                            <div class="col-lg-12 mb-5">
                                <h3 class="mb-4" t-esc="group_name"/>
                                <div class="row">
                                    <t t-foreach="service_groups[group_name]" t-as="service">
                                        <div class="col-lg-4 col-md-6 mb-4">
                                            <div class="card h-100">
                                                <div class="card-body">
                                                    <h5 class="card-title" t-esc="service.name"/>
                                                    <p class="card-text" t-esc="service.description"/>
                                                    <div class="price-tag mb-3">
                                                        <span class="h5 text-success">
                                                            $<t t-esc="service.price or 299"/>
                                                        </span>
                                                    </div>
                                                    <a t-attf-href="/legal-services/#{service.name.lower().replace(' ', '-')}"
                                                       class="btn btn-primary">
                                                        Get Started
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- AI Assistant Service Page -->
    <template id="ai_assistant_service_page" name="AI Assistant Service">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-lg-12 text-center mb-5">
                            <h1 class="display-4">AI Legal Document Assistant</h1>
                            <p class="lead">Choose a service to start your AI-powered legal document creation</p>
                        </div>
                    </div>

                    <div class="row">
                        <t t-foreach="services" t-as="service">
                            <div class="col-lg-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <h5 class="card-title" t-esc="service.name"/>
                                        <p class="card-text" t-esc="service.description"/>
                                        <div class="price-tag mb-3">
                                            <span class="h4 text-success">
                                                $<t t-esc="service.price or 299"/>
                                            </span>
                                        </div>
                                        <a t-attf-href="/legal-services/#{service.name.lower().replace(' ', '-').replace('agreement', '').replace('contract', '').replace('drafting', '').strip()}"
                                           class="btn btn-primary btn-lg">
                                            Start AI Chat
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </t>
    </template>

    <!-- Service-Specific Chatbot Page -->
    <template id="service_chatbot_page" name="Service Chatbot">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty" t-att-data-service-type="service_type">
                <div class="container-fluid mt-3">
                    <div class="row">
                        <!-- Service Info Sidebar -->
                        <div class="col-lg-3 col-md-4">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">Service Information</h6>
                                </div>
                                <div class="card-body">
                                    <h5 t-esc="service.name"/>
                                    <p class="small" t-esc="service.description"/>
                                    <div class="price-info mb-3">
                                        <strong class="text-success">
                                            $<t t-esc="service.product_id.list_price or 299"/>
                                        </strong>
                                    </div>
                                    <div class="service-features">
                                        <h6>What's Included:</h6>
                                        <ul class="list-unstyled small">
                                            <li><i class="fa fa-robot text-success me-2"></i>AI-powered document creation</li>
                                            <li><i class="fa fa-file-text text-success me-2"></i>Professional legal formatting</li>
                                            <li><i class="fa fa-download text-success me-2"></i>Instant download</li>
                                            <li><i class="fa fa-envelope text-success me-2"></i>Email delivery</li>
                                            <li><i class="fa fa-user-shield text-success me-2"></i>Expert review</li>
                                        </ul>
                                    </div>

                                    <!-- Language Selection for Service -->
                                    <t t-if="service_languages and len(service_languages) > 1">
                                        <div class="service-languages mt-3">
                                            <h6>Available Languages:</h6>
                                            <div class="btn-group-vertical w-100" role="group">
                                                <t t-foreach="service_languages" t-as="lang">
                                                    <button type="button" class="btn btn-outline-secondary btn-sm language-selector-btn mb-1"
                                                            t-att-data-lang="lang['code']">
                                                        <t t-esc="lang['name']"/>
                                                    </button>
                                                </t>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>

                        <!-- Main Chat Interface -->
                        <div class="col-lg-9 col-md-8">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <i class="fa fa-robot text-primary me-2"></i>
                                            AI Legal Assistant - <t t-esc="service.name"/>
                                        </h5>
                                        <div class="chat-status">
                                            <span class="badge bg-success">Online</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-body p-0">
                                    <!-- Chat Messages Area -->
                                    <div id="chat-messages-area" class="chat-messages-container"
                                         style="height: 600px; overflow-y: auto; padding: 20px; background-color: #f8f9fa;">

                                        <!-- Welcome Message -->
                                        <div class="message ai-message mb-3">
                                            <div class="d-flex">
                                                <div class="avatar me-3">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fa fa-robot"></i>
                                                    </div>
                                                </div>
                                                <div class="message-content">
                                                    <div class="message-bubble bg-white p-3 rounded shadow-sm">
                                                        <div class="message-header mb-2">
                                                            <strong>AI Legal Assistant</strong>
                                                            <small class="text-muted ms-2" id="welcome-time"></small>
                                                        </div>
                                                        <div class="message-text">
                                                            Welcome! I'm your AI legal assistant for <strong t-esc="service.name"/>.
                                                            I'll help you create a professional legal document by gathering the necessary information
                                                            and guiding you through the process. Let's get started!
                                                            <br/><br/>
                                                            How can I help you today?
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Form Container for AI-generated forms -->
                                    <div id="form-container-area" class="p-3 border-top bg-light" style="display: none;">
                                        <h6 class="mb-3">Please fill out the following information:</h6>
                                        <div id="dynamic-form-content">
                                            <!-- AI-generated forms will appear here -->
                                        </div>
                                    </div>

                                    <!-- Payment Container -->
                                    <div id="payment-container-area" class="p-3 border-top bg-warning bg-opacity-10" style="display: none;">
                                        <div class="payment-request-content">
                                            <!-- Payment request will appear here -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Chat Input Footer -->
                                <div class="card-footer">
                                    <div class="input-group">
                                        <input type="text" id="chat-message-input" class="form-control"
                                               placeholder="Type your message here..."/>
                                        <button class="btn btn-primary" id="send-chat-message">
                                            <i class="fa fa-paper-plane"></i>
                                            <span class="d-none d-md-inline ms-2">Send</span>
                                        </button>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fa fa-info-circle me-1"></i>
                                            Press Enter to send your message
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Initialize service type for JavaScript -->
            <script type="text/javascript">
                window.chatbotServiceType = '<t t-esc="service_type"/>';
                console.log('Service type set:', window.chatbotServiceType);

                // Debug formatter availability
                window.addEventListener('load', function() {
                    console.log('=== FORMATTER DEBUG INFO ===');
                    console.log('AIMessageFormatter available:', typeof window.AIMessageFormatter !== 'undefined');
                    if (window.AIMessageFormatter) {
                        console.log('formatAIMessage function:', typeof window.AIMessageFormatter.formatAIMessage);
                        console.log('escapeHtml function:', typeof window.AIMessageFormatter.escapeHtml);

                        // Test basic formatting
                        var testMessage = "This is **bold** and *italic* text with a list:\n1. First item\n2. Second item";
                        var formatted = window.AIMessageFormatter.formatAIMessage(testMessage);
                        console.log('Test formatting result:', formatted);
                    }
                    console.log('=== END FORMATTER DEBUG ===');
                });
            </script>
        </t>
    </template>

    <!-- Chatbot JavaScript Assets -->
    <template id="chatbot_assets" name="Chatbot Assets" inherit_id="website.layout">
        <xpath expr="//head" position="inside">
            <!-- Load message formatter first -->
            <script type="text/javascript" src="/ai_chatbot_integration/static/src/js/message_formatter.js"></script>
            <script type="text/javascript">
                <![CDATA[
                console.log('=== CHATBOT SCRIPT START ===');

                // Ensure message formatter is available
                if (typeof window.AIMessageFormatter === 'undefined') {
                    console.error('AIMessageFormatter not loaded! Loading fallback...');
                    // Simple fallback formatter
                    window.AIMessageFormatter = {
                        formatAIMessage: function(message) {
                            if (!message) return '';
                            return message.replace(/\n/g, '<br/>');
                        },
                        escapeHtml: function(text) {
                            if (!text) return '';
                            var map = {
                                '&': '&amp;',
                                '<': '&lt;',
                                '>': '&gt;',
                                '"': '&quot;',
                                "'": '&#039;'
                            };
                            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
                        }
                    };
                } else {
                    console.log('AIMessageFormatter loaded successfully');
                }

                // Set welcome time immediately when script loads
                function setWelcomeTime() {
                    var welcomeTimeEl = document.getElementById('welcome-time');
                    if (welcomeTimeEl) {
                        welcomeTimeEl.textContent = new Date().toLocaleTimeString();
                        console.log('Welcome time set');
                    }
                }

                // Try to set welcome time immediately
                setWelcomeTime();

                // Wait for page to load
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('DOM loaded, initializing chatbot...');

                    // Set welcome time again after DOM loads
                    setWelcomeTime();

                    // Check if we're on a chatbot page
                    if (!document.getElementById('send-chat-message')) {
                        console.log('Not a chatbot page, skipping initialization');
                        return;
                    }

                    var serviceType = window.chatbotServiceType || 'CONTRACT_DRAFT';
                    var currentConversationId = null;

                    console.log('Service type:', serviceType);
                    console.log('Send button exists:', document.getElementById('send-chat-message') ? 'YES' : 'NO');
                    console.log('Chat input exists:', document.getElementById('chat-message-input') ? 'YES' : 'NO');

                    // Simple event binding
                    var sendButton = document.getElementById('send-chat-message');
                    var chatInput = document.getElementById('chat-message-input');

                    if (sendButton) {
                        sendButton.addEventListener('click', function() {
                            console.log('=== SEND BUTTON CLICKED ===');
                            sendMessage();
                        });
                        console.log('Send button event bound');
                    }

                    if (chatInput) {
                        chatInput.addEventListener('keypress', function(e) {
                            if (e.key === 'Enter') {
                                console.log('Enter key pressed');
                                sendMessage();
                            }
                        });
                        console.log('Chat input event bound');
                    }

                    // Start conversation
                    startConversation();

                    function startConversation() {
                        console.log('Starting conversation...');

                        fetch('/chatbot/start-conversation', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                params: {
                                    service_type: serviceType,
                                    language: 'en_US'
                                }
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Start conversation response:', data);
                            var result = data.result || data;
                            if (result.success) {
                                currentConversationId = result.conversation_id;
                                console.log('Conversation ID:', currentConversationId);
                            } else {
                                console.error('Start conversation failed:', result.error);
                            }
                        })
                        .catch(error => {
                            console.error('Start conversation error:', error);
                        });
                    }

                    function sendMessage() {
                        var message = chatInput.value.trim();
                        console.log('Sending message:', message);

                        if (!message || !currentConversationId) {
                            console.log('No message or conversation ID');
                            return;
                        }

                        // Clear input
                        chatInput.value = '';

                        // Add user message to chat
                        addMessage(message, false);

                        fetch('/chatbot/send-message', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                params: {
                                    conversation_id: currentConversationId,
                                    message: message
                                }
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Send message response:', data);
                            var result = data.result || data;
                            if (result.success) {
                                addMessage(result.ai_response, true);
                            } else {
                                addMessage('Error: ' + (result.error || 'Unknown error'), true);
                            }
                        })
                        .catch(error => {
                            console.error('Send message error:', error);
                            addMessage('Connection error. Please try again.', true);
                        });
                    }

                    function addMessage(message, isAI) {
                        var chatArea = document.getElementById('chat-messages-area');
                        if (!chatArea) return;

                        // Check if AI message contains a form
                        var hasForm = false;
                        var cleanMessage = message;
                        var formData = null;

                        if (isAI && message.includes('FORM_START') && message.includes('FORM_END')) {
                            hasForm = true;
                            console.log('Form detected in AI message');

                            // Extract form data
                            var formStart = message.indexOf('FORM_START') + 'FORM_START'.length;
                            var formEnd = message.indexOf('FORM_END');
                            var formJson = message.substring(formStart, formEnd).trim();

                            try {
                                formData = JSON.parse(formJson);
                                console.log('Parsed form data:', formData);

                                // Remove form from message
                                cleanMessage = message.substring(0, message.indexOf('FORM_START')) +
                                             message.substring(formEnd + 'FORM_END'.length);
                                cleanMessage = cleanMessage.trim();
                            } catch (e) {
                                console.error('Error parsing form JSON:', e);
                                hasForm = false;
                            }
                        }

                        var messageDiv = document.createElement('div');
                        messageDiv.className = 'message ' + (isAI ? 'ai-message' : 'user-message') + ' mb-3';

                        var messageHtml = '<div class="d-flex ' + (isAI ? '' : 'justify-content-end') + '">';
                        if (isAI) {
                            messageHtml += '<div class="avatar me-3"><div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;"><i class="fa fa-robot"></i></div></div>';
                        }
                        messageHtml += '<div class="message-content" style="max-width: 70%;"><div class="message-bubble ' + (isAI ? 'bg-white' : 'bg-primary text-white') + ' p-3 rounded shadow-sm">';
                        if (isAI) {
                            messageHtml += '<div class="message-header mb-2"><strong>AI Assistant</strong><small class="text-muted ms-2">' + new Date().toLocaleTimeString() + '</small></div>';
                        }
                        // Format AI messages properly with enhanced formatting
                        var formattedMessage;
                        if (isAI) {
                            console.log('Formatting AI message:', cleanMessage);
                            if (window.AIMessageFormatter && typeof window.AIMessageFormatter.formatAIMessage === 'function') {
                                formattedMessage = window.AIMessageFormatter.formatAIMessage(cleanMessage);
                                console.log('Formatted message:', formattedMessage);
                            } else {
                                console.warn('AIMessageFormatter not available, using basic formatting');
                                formattedMessage = cleanMessage.replace(/\n/g, '<br/>');
                            }
                        } else {
                            // For user messages, just escape HTML and convert newlines
                            formattedMessage = (window.AIMessageFormatter && window.AIMessageFormatter.escapeHtml ?
                                window.AIMessageFormatter.escapeHtml(cleanMessage) : cleanMessage).replace(/\n/g, '<br/>');
                        }
                        messageHtml += '<div class="message-text">' + formattedMessage + '</div>';

                        // Add form if present
                        if (hasForm && formData) {
                            messageHtml += renderFormInMessage(formData);
                        }

                        messageHtml += '</div></div>';
                        if (!isAI) {
                            messageHtml += '<div class="avatar ms-3"><div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;"><i class="fa fa-user"></i></div></div>';
                        }
                        messageHtml += '</div>';

                        messageDiv.innerHTML = messageHtml;
                        chatArea.appendChild(messageDiv);
                        chatArea.scrollTop = chatArea.scrollHeight;

                        // Bind form events if form was added
                        if (hasForm && formData) {
                            bindFormEvents(messageDiv);
                        }
                    }

                    function renderFormInMessage(formData) {
                        console.log('Rendering form in message:', formData);

                        var formHtml = '<div class="ai-form-container mt-3 p-3 border rounded bg-light">';
                        formHtml += '<h6 class="mb-3"><i class="fa fa-wpforms me-2"></i>' + (formData.title || 'Please provide information') + '</h6>';
                        formHtml += '<form class="ai-generated-form">';

                        if (formData.fields && Array.isArray(formData.fields)) {
                            formData.fields.forEach(function(field, index) {
                                formHtml += '<div class="mb-3">';
                                formHtml += '<label class="form-label">' + field.label;
                                if (field.required) formHtml += ' <span class="text-danger">*</span>';
                                formHtml += '</label>';

                                if (field.type === 'text' || field.type === 'email') {
                                    formHtml += '<input type="' + field.type + '" class="form-control" name="' + field.name + '"';
                                    if (field.required) formHtml += ' required';
                                    formHtml += '>';
                                } else if (field.type === 'textarea') {
                                    formHtml += '<textarea class="form-control" name="' + field.name + '" rows="3"';
                                    if (field.required) formHtml += ' required';
                                    formHtml += '></textarea>';
                                } else if (field.type === 'select') {
                                    formHtml += '<select class="form-control" name="' + field.name + '"';
                                    if (field.required) formHtml += ' required';
                                    formHtml += '>';
                                    formHtml += '<option value="">Choose...</option>';
                                    if (field.options && Array.isArray(field.options)) {
                                        field.options.forEach(function(option) {
                                            formHtml += '<option value="' + option + '">' + option + '</option>';
                                        });
                                    }
                                    formHtml += '</select>';
                                } else if (field.type === 'date') {
                                    formHtml += '<input type="date" class="form-control" name="' + field.name + '"';
                                    if (field.required) formHtml += ' required';
                                    formHtml += '>';
                                } else if (field.type === 'number') {
                                    formHtml += '<input type="number" class="form-control" name="' + field.name + '"';
                                    if (field.required) formHtml += ' required';
                                    formHtml += '>';
                                }

                                formHtml += '</div>';
                            });
                        }

                        formHtml += '<div class="d-flex gap-2">';
                        formHtml += '<button type="submit" class="btn btn-primary btn-sm"><i class="fa fa-check me-1"></i>Submit</button>';
                        formHtml += '<button type="button" class="btn btn-outline-secondary btn-sm form-skip"><i class="fa fa-arrow-right me-1"></i>Skip</button>';
                        formHtml += '</div>';
                        formHtml += '</form></div>';

                        return formHtml;
                    }

                    function bindFormEvents(messageDiv) {
                        console.log('Binding form events');

                        var form = messageDiv.querySelector('.ai-generated-form');
                        var skipBtn = messageDiv.querySelector('.form-skip');

                        if (form) {
                            form.addEventListener('submit', function(e) {
                                e.preventDefault();
                                console.log('Form submitted');

                                var formData = new FormData(form);
                                var formObject = {};
                                for (var pair of formData.entries()) {
                                    formObject[pair[0]] = pair[1];
                                }

                                console.log('Form data:', formObject);

                                // Create a message summarizing the form submission
                                var summaryMessage = "Form submitted with the following information:\n\n";
                                for (var key in formObject) {
                                    if (formObject[key]) {
                                        summaryMessage += "• " + key + ": " + formObject[key] + "\n";
                                    }
                                }

                                // Add user message showing form submission
                                addMessage(summaryMessage, false);

                                // Send to AI
                                sendFormDataToAI(formObject);

                                // Hide the form
                                form.style.display = 'none';
                            });
                        }

                        if (skipBtn) {
                            skipBtn.addEventListener('click', function() {
                                console.log('Form skipped');
                                addMessage("I'll skip this form for now.", false);
                                sendMessage();
                                form.style.display = 'none';
                            });
                        }
                    }

                    function sendFormDataToAI(formData) {
                        console.log('Sending form data to AI:', formData);

                        var message = "Form data: " + JSON.stringify(formData);

                        fetch('/chatbot/send-message', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                params: {
                                    conversation_id: currentConversationId,
                                    message: message
                                }
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            console.log('Form data response:', data);
                            var result = data.result || data;
                            if (result.success) {
                                addMessage(result.ai_response, true);
                            } else {
                                addMessage('Error processing form: ' + (result.error || 'Unknown error'), true);
                            }
                        })
                        .catch(error => {
                            console.error('Form data error:', error);
                            addMessage('Connection error. Please try again.', true);
                        });
                    }

                    console.log('Chatbot initialization complete');
                });

                console.log('=== CHATBOT SCRIPT END ===');
                ]]>
            </script>
        </xpath>
    </template>

    <!-- My Legal Documents Portal -->
    <template id="my_legal_documents_page" name="My Legal Documents">
        <t t-call="website.layout">
            <div id="wrap" class="oe_structure oe_empty">
                <div class="container mt-5">
                    <div class="row">
                        <div class="col-lg-12">
                            <h1 class="display-4 mb-4">My Legal Documents</h1>

                            <t t-if="not document_requests">
                                <div class="alert alert-info">
                                    <h5>No documents yet</h5>
                                    <p>You haven't created any legal documents yet. Start by using our AI Legal Assistant!</p>
                                    <a href="/legal-assistant" class="btn btn-primary">
                                        <i class="fa fa-robot me-2"></i>Start AI Assistant
                                    </a>
                                </div>
                            </t>

                            <t t-if="document_requests">
                                <div class="row">
                                    <t t-foreach="document_requests" t-as="request">
                                        <div class="col-lg-6 mb-4">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h5 class="card-title" t-esc="request.service_id.name"/>
                                                    <p class="card-text">
                                                        <strong>Status:</strong>
                                                        <span t-att-class="'badge bg-primary' if request.state == 'draft'
                                                                           else 'badge bg-warning' if request.state == 'in_progress'
                                                                           else 'badge bg-info' if request.state == 'processing'
                                                                           else 'badge bg-success' if request.state == 'completed'
                                                                           else 'badge bg-secondary'"
                                                              t-esc="request.state.title()"/>
                                                    </p>
                                                    <p class="card-text">
                                                        <small class="text-muted">
                                                            Created: <t t-esc="request.create_date.strftime('%B %d, %Y')"/>
                                                        </small>
                                                    </p>
                                                    <div class="btn-group">
                                                        <a t-attf-href="/my/legal-documents/#{request.id}" class="btn btn-outline-primary btn-sm">
                                                            View Details
                                                        </a>
                                                        <t t-if="request.state == 'completed'">
                                                            <a t-attf-href="/my/legal-documents/#{request.id}/download" class="btn btn-success btn-sm">
                                                                <i class="fa fa-download me-1"></i>Download
                                                            </a>
                                                        </t>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>