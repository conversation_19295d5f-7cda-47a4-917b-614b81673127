<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Legal Document Conversation Portal Template -->
    <template id="portal_legal_document_conversation" name="Legal Document AI Conversation">
        <t t-call="portal.portal_layout">
            <t t-set="additional_title">AI Legal Assistant</t>

            <!-- Custom CSS and JS Assets -->
            <t t-call-assets="ai_chatbot_integration.chatbot_assets" t-css="false"/>

            <!-- Ensure message formatter is loaded -->
            <script type="text/javascript" src="/ai_chatbot_integration/static/src/js/message_formatter.js"></script>

            <div class="legal-chatbot-container">
                <!-- Header Section -->
                <div class="chatbot-header">
                    <div class="container">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h1 class="chatbot-title">
                                    <i class="fa fa-robot me-3"></i>
                                    AI Legal Assistant
                                </h1>
                                <p class="chatbot-subtitle">
                                    Professional legal document drafting powered by AI
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="status-indicator">
                                    <span class="status-dot"></span>
                                    <span class="status-text">AI Assistant Online</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Chat Interface -->
                <div class="chat-interface">
                    <div class="container">
                        <div class="row justify-content-center">
                            <div class="col-lg-10 col-xl-8">

                                <!-- Document Request Info Card -->
                                <div class="request-info-card" t-if="document_request">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fa fa-file-text me-2"></i>
                                            <t t-esc="document_request.name"/>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>Service:</strong> <t t-esc="document_request.service_id.name"/><br/>
                                                <strong>Language:</strong> <t t-esc="document_request.language_id.name"/>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>Status:</strong>
                                                <span t-attf-class="badge status-badge status-#{document_request.state}">
                                                    <t t-esc="document_request.state"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Conversation Messages -->
                                <div id="conversation-messages" class="conversation-container">
                                    <!-- Welcome Message -->
                                    <div class="message ai-message welcome-message">
                                        <div class="message-bubble">
                                            <div class="message-header">
                                                <strong>AI Legal Assistant</strong>
                                                <small class="text-muted">Just now</small>
                                            </div>
                                            <div class="message-content">
                                                <p>Welcome to your AI Legal Assistant! I'm here to help you draft professional legal documents.</p>
                                                <p>I can assist you with contracts, agreements, wills, and other legal documents. Let's start by understanding your specific needs.</p>
                                                <div class="quick-actions">
                                                    <button class="quick-action-btn" data-message="I need help with a contract">
                                                        <i class="fa fa-handshake"></i> Contract
                                                    </button>
                                                    <button class="quick-action-btn" data-message="I need to create a will">
                                                        <i class="fa fa-scroll"></i> Will
                                                    </button>
                                                    <button class="quick-action-btn" data-message="I need an NDA">
                                                        <i class="fa fa-shield"></i> NDA
                                                    </button>
                                                    <button class="quick-action-btn" data-message="I need a lease agreement">
                                                        <i class="fa fa-home"></i> Lease
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Existing Conversation Messages -->
                                    <t t-foreach="conversations" t-as="conversation">
                                        <div t-attf-class="message #{conversation.is_ai and 'ai-message' or 'user-message'}">
                                            <div t-attf-class="message-bubble #{conversation.is_ai and '' or 'user-bubble'}">
                                                <div class="message-header">
                                                    <strong>
                                                        <t t-esc="conversation.is_ai and 'AI Assistant' or conversation.user_id.name"/>
                                                    </strong>
                                                    <small class="text-muted">
                                                        <t t-esc="conversation.create_date" t-options="{'widget': 'relative'}"/>
                                                    </small>
                                                </div>
                                                <div class="message-content">
                                                    <t t-if="conversation.is_ai">
                                                        <script>
                                                            document.addEventListener('DOMContentLoaded', function() {
                                                                console.log('Formatting AI message in portal template');
                                                                if (window.AIMessageFormatter &amp;&amp; typeof window.AIMessageFormatter.formatAIMessage === 'function') {
                                                                    var messageElement = document.querySelector('[data-message-id="<t t-esc="conversation.id"/>"]');
                                                                    if (messageElement) {
                                                                        var originalMessage = '<t t-esc="conversation.message"/>';
                                                                        var formattedMessage = window.AIMessageFormatter.formatAIMessage(originalMessage);
                                                                        messageElement.innerHTML = formattedMessage;
                                                                        console.log('Formatted portal AI message:', formattedMessage);
                                                                    }
                                                                } else {
                                                                    console.warn('AIMessageFormatter not available in portal template');
                                                                }
                                                            });
                                                        </script>
                                                        <div t-attf-data-message-id="{{conversation.id}}">
                                                            <t t-raw="conversation.message"/>
                                                        </div>
                                                    </t>
                                                    <t t-else="">
                                                        <t t-raw="conversation.message"/>
                                                    </t>
                                                </div>

                                                <!-- Survey Form Integration -->
                                                <t t-if="conversation.has_form and conversation.form_data">
                                                    <div class="survey-form-container">
                                                        <t t-call="ai_chatbot_integration.embedded_survey_form">
                                                            <t t-set="form_data" t-value="conversation.form_data"/>
                                                            <t t-set="conversation_id" t-value="conversation.id"/>
                                                        </t>
                                                    </div>
                                                </t>
                                            </div>
                                        </div>
                                    </t>

                                    <!-- Typing Indicator -->
                                    <div id="typing-indicator" class="message ai-message typing-message" style="display: none;">
                                        <div class="message-bubble">
                                            <div class="typing-dots">
                                                <span></span>
                                                <span></span>
                                                <span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Message Input -->
                                <div class="message-input-container">
                                    <form id="conversation-form" class="message-input-form">
                                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                        <input type="hidden" name="request_id" t-att-value="document_request.id if document_request else ''"/>

                                        <div class="input-group">
                                            <input type="text"
                                                   name="message"
                                                   class="form-control message-input"
                                                   placeholder="Type your message here..."
                                                   autocomplete="off"
                                                   maxlength="1000"/>
                                            <button type="submit" class="btn btn-primary send-button">
                                                <i class="fa fa-paper-plane"></i>
                                                <span class="btn-text">Send</span>
                                            </button>
                                        </div>

                                        <div class="input-footer">
                                            <small class="text-muted">
                                                <i class="fa fa-info-circle me-1"></i>
                                                Press Enter to send • AI responses are generated in real-time
                                            </small>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced JavaScript -->
            <script type="text/javascript">
                <![CDATA[
                $(document).ready(function() {
                    initializeLegalChatbot();
                });
                ]]>
            </script>
        </t>
    </template>

    <!-- Embedded Survey Form Template -->
    <template id="embedded_survey_form" name="Embedded Survey Form">
        <div class="embedded-survey-form">
            <div class="survey-header">
                <h6 class="survey-title">
                    <i class="fa fa-clipboard-list me-2"></i>
                    Please provide the following information
                </h6>
                <p class="survey-description">This information will help me create a more accurate document for you.</p>
            </div>

            <form class="survey-form" t-att-data-conversation-id="conversation_id">
                <!-- Dynamic form fields will be rendered here by JavaScript -->
                <div class="survey-fields"></div>

                <div class="survey-actions">
                    <button type="submit" class="btn btn-primary survey-submit-btn">
                        <i class="fa fa-check me-2"></i>
                        Submit Information
                    </button>
                    <button type="button" class="btn btn-outline-secondary survey-skip-btn">
                        <i class="fa fa-arrow-right me-2"></i>
                        Skip for now
                    </button>
                </div>
            </form>
        </div>
    </template>
</odoo>
