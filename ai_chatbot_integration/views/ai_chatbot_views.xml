<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Chatbot Conversation Views -->
    <record id="view_ai_chatbot_conversation_form" model="ir.ui.view">
        <field name="name">ai.chatbot.conversation.form</field>
        <field name="model">ai.chatbot.conversation</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="active,completed,failed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="config_id"/>
                            <field name="document_request_id"/>
                            <field name="language_id"/>
                        </group>
                        <group>
                            <field name="create_date" readonly="1"/>
                            <field name="has_form_data" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="System Prompt" name="system_prompt">
                            <field name="system_prompt"/>
                        </page>
                        <page string="Conversation History" name="conversation_history">
                            <field name="conversation_history" widget="html"/>
                        </page>
                        <page string="Form Data" name="form_data" invisible="has_form_data == False">
                            <field name="form_data" widget="html"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
    <record id="view_ai_chatbot_conversation_tree" model="ir.ui.view">
        <field name="name">ai.chatbot.conversation.tree</field>
        <field name="model">ai.chatbot.conversation</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="config_id"/>
                <field name="document_request_id"/>
                <field name="language_id"/>
                <field name="create_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    
    <record id="view_ai_chatbot_conversation_search" model="ir.ui.view">
        <field name="name">ai.chatbot.conversation.search</field>
        <field name="model">ai.chatbot.conversation</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="document_request_id"/>
                <field name="config_id"/>
                <separator/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Configuration" name="groupby_config" context="{'group_by': 'config_id'}"/>
                    <filter string="Document Request" name="groupby_request" context="{'group_by': 'document_request_id'}"/>
                    <filter string="Language" name="groupby_language" context="{'group_by': 'language_id'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!-- Actions -->
    <record id="action_ai_chatbot_conversation" model="ir.actions.act_window">
        <field name="name">Chatbot Conversations</field>
        <field name="res_model">ai.chatbot.conversation</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
    </record>
</odoo>
