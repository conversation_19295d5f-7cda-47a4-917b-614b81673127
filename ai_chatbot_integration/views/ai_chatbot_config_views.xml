<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Chatbot Configuration Views -->
    <record id="view_ai_chatbot_config_form" model="ir.ui.view">
        <field name="name">ai.chatbot.config.form</field>
        <field name="model">ai.chatbot.config</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="test_connection" string="Test Connection" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Configuration Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active"/>
                            <field name="api_type"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="api_key" password="True"/>
                            <field name="api_url" placeholder="Leave empty for default URL"/>
                            <field name="api_model" placeholder="Leave empty for default model"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Parameters" name="parameters">
                            <group>
                                <field name="temperature"/>
                                <field name="max_tokens"/>
                                <field name="top_p"/>
                            </group>
                        </page>
                        <page string="System Prompt" name="system_prompt">
                            <field name="system_prompt" placeholder="Enter the default system prompt for the AI..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_ai_chatbot_config_tree" model="ir.ui.view">
        <field name="name">ai.chatbot.config.tree</field>
        <field name="model">ai.chatbot.config</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="api_type"/>
                <field name="active"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_ai_chatbot_config_search" model="ir.ui.view">
        <field name="name">ai.chatbot.config.search</field>
        <field name="model">ai.chatbot.config</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="api_type"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="API Type" name="groupby_api_type" context="{'group_by': 'api_type'}"/>
                    <filter string="Company" name="groupby_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_ai_chatbot_config" model="ir.actions.act_window">
        <field name="name">Chatbot Configurations</field>
        <field name="res_model">ai.chatbot.config</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
    </record>
</odoo>
