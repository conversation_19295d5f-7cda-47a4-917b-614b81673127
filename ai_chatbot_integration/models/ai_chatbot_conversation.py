from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
import logging
import requests
import json
import re

_logger = logging.getLogger(__name__)


class AIChatbotConversation(models.Model):
    _name = 'ai.chatbot.conversation'
    _description = 'AI Chatbot Conversation'
    _order = 'create_date desc'

    name = fields.Char(string='Reference', readonly=True, copy=False, default='New')

    # Configuration
    config_id = fields.Many2one('ai.chatbot.config', string='Chatbot Configuration', required=True)

    # Related document
    document_request_id = fields.Many2one('legal.document.request', string='Document Request')

    # Conversation history
    conversation_history = fields.Text(string='Conversation History', readonly=True)

    # System prompt
    system_prompt = fields.Text(string='System Prompt')

    # Language
    language_id = fields.Many2one('res.lang', string='Language')

    # Status
    state = fields.Selection([
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], string='Status', default='active')

    # Form data
    has_form_data = fields.Boolean(string='Has Form Data', default=False)
    form_data = fields.Text(string='Form Data')

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('ai.chatbot.conversation') or 'New'
        return super().create(vals_list)

    def send_message(self, message, user_id=None):
        """Send a message to the AI and get a response"""
        self.ensure_one()

        if not message:
            return {'error': 'Message cannot be empty'}

        # Get the configuration
        config = self.config_id
        if not config:
            return {'error': 'Chatbot configuration not found'}

        # Update conversation history
        history = json.loads(self.conversation_history or '[]')

        # Add user message to history
        user_message = {
            'role': 'user',
            'content': message,
            'user_id': user_id or self.env.user.id,
            'timestamp': fields.Datetime.now().isoformat(),
        }
        history.append(user_message)

        # Create conversation message in the document request
        if self.document_request_id:
            self.env['legal.document.conversation'].sudo().create({
                'request_id': self.document_request_id.id,
                'user_id': user_id or self.env.user.id,
                'is_ai': False,
                'message': message,
            })

        # Prepare messages for API
        api_messages = []

        # Add system prompt
        if self.system_prompt:
            api_messages.append({
                'role': 'system',
                'content': self.system_prompt
            })
        elif config.system_prompt:
            api_messages.append({
                'role': 'system',
                'content': config.system_prompt
            })

        # Add conversation history
        for msg in history:
            api_messages.append({
                'role': msg['role'],
                'content': msg['content']
            })

        try:
            # Call the appropriate API based on configuration
            if config.api_type == 'groq':
                response = self._call_groq_api(config, api_messages)
            elif config.api_type == 'grok3':
                response = self._call_grok3_api(config, api_messages)
            elif config.api_type == 'openai':
                response = self._call_openai_api(config, api_messages)
            else:
                # Fallback to Groq for backward compatibility
                _logger.warning("Unknown API type '%s', falling back to Groq", config.api_type)
                response = self._call_groq_api(config, api_messages)

            # Process the response
            if response and 'content' in response:
                ai_message = {
                    'role': 'assistant',
                    'content': response['content'],
                    'timestamp': fields.Datetime.now().isoformat(),
                }
                history.append(ai_message)

                # Update conversation history
                self.conversation_history = json.dumps(history)

                # Check for form data
                form_data = self._extract_form_data(response['content'])
                has_form = bool(form_data)

                # Create AI response in the document request
                if self.document_request_id:
                    self.env['legal.document.conversation'].sudo().create({
                        'request_id': self.document_request_id.id,
                        'is_ai': True,
                        'message': response['content'],
                        'has_form': has_form,
                        'form_data': json.dumps(form_data) if form_data else False,
                    })

                # Update form data if present
                if has_form:
                    self.has_form_data = True
                    self.form_data = json.dumps(form_data)

                return {
                    'success': True,
                    'message': response['content'],
                    'has_form': has_form,
                    'form_data': form_data if has_form else None,
                }
            else:
                return {'error': 'Invalid response from AI API'}

        except Exception as e:
            _logger.error("Error sending message to AI API: %s", str(e))
            return {'error': str(e)}

    def _call_grok3_api(self, config, messages):
        """Call the Grok3 API"""
        headers = {
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'messages': messages,
            'temperature': config.temperature,
            'max_tokens': config.max_tokens,
            'top_p': config.top_p,
        }

        url = config.api_url or 'https://api.grok.ai/v1/chat/completions'

        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()

        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']

        return None

    def _call_groq_api(self, config, messages):
        """Call the Groq API (exactly like test_groq.py)"""
        headers = {
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': config.api_model or 'llama3-70b-8192',
            'messages': messages,
            'temperature': config.temperature or 0.7,
        }

        url = config.api_url or 'https://api.groq.com/openai/v1/chat/completions'

        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()

        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']
        return None

    def _call_openai_api(self, config, messages):
        """Call the OpenAI API"""
        headers = {
            'Authorization': f'Bearer {config.api_key}',
            'Content-Type': 'application/json'
        }

        data = {
            'model': config.api_model or 'gpt-3.5-turbo',
            'messages': messages,
            'temperature': config.temperature,
            'max_tokens': config.max_tokens,
            'top_p': config.top_p,
        }

        url = config.api_url or 'https://api.openai.com/v1/chat/completions'

        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        result = response.json()

        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']

        return None

    def _extract_form_data(self, content):
        """Extract form data from the AI response"""
        # Look for form data in JSON format
        form_data_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
        if form_data_match:
            try:
                form_data = json.loads(form_data_match.group(1))
                if isinstance(form_data, dict) and 'form' in form_data:
                    return form_data
            except json.JSONDecodeError:
                pass

        # Look for form data in a specific format
        form_data_match = re.search(r'<form>(.*?)</form>', content, re.DOTALL)
        if form_data_match:
            try:
                form_content = form_data_match.group(1)
                # Parse the form content into a structured format
                form_data = self._parse_form_content(form_content)
                return {'form': form_data}
            except Exception:
                pass

        return None

    def _parse_form_content(self, content):
        """Parse form content into a structured format"""
        form_fields = []

        # Look for field definitions
        field_matches = re.finditer(r'<field\s+type="([^"]+)"\s+name="([^"]+)"\s+label="([^"]+)"(?:\s+required="([^"]+)")?(?:\s+options="([^"]+)")?\s*/?>', content)

        for match in field_matches:
            field_type = match.group(1)
            field_name = match.group(2)
            field_label = match.group(3)
            field_required = match.group(4) == 'true' if match.group(4) else False
            field_options = match.group(5)

            field = {
                'type': field_type,
                'name': field_name,
                'label': field_label,
                'required': field_required,
            }

            if field_options:
                try:
                    options = json.loads(field_options.replace("'", '"'))
                    field['options'] = options
                except json.JSONDecodeError:
                    pass

            form_fields.append(field)

        return form_fields
