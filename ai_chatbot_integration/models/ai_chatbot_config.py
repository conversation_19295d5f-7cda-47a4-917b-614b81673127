from odoo import api, fields, models, _
import logging
import requests
import json

_logger = logging.getLogger(__name__)


class AIChatbotConfig(models.Model):
    _name = 'ai.chatbot.config'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'AI Chatbot Configuration'

    name = fields.Char(string='Name', required=True, tracking=True)
    active = fields.Boolean(string='Active', default=True, tracking=True)

    # API Configuration
    api_type = fields.Selection([
        ('groq', 'Groq API'),
        ('openai', 'OpenAI API'),
        ('custom', 'Custom API'),
    ], string='API Type', required=True, default='groq', tracking=True)

    api_key = fields.Char(string='API Key', required=True)
    api_url = fields.Char(string='API URL', size=500,
                          help="Full API URL (leave empty for default)")
    api_model = fields.Char(string='API Model',
                           help="Model to use for API calls")

    # Default Parameters
    temperature = fields.Float(string='Temperature', default=0.7, tracking=True,
                              help="Controls randomness in the response. Lower values make responses more deterministic.")
    max_tokens = fields.Integer(string='Max Tokens', default=1000, tracking=True,
                               help="Maximum number of tokens to generate in the response.")
    top_p = fields.Float(string='Top P', default=0.9, tracking=True,
                        help="Controls diversity via nucleus sampling.")

    # System Prompt
    system_prompt = fields.Text(string='System Prompt', tracking=True,
                               help="Default system prompt to set the behavior of the AI.")

    # Company
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    _sql_constraints = [
        ('name_uniq', 'unique(name)', 'Configuration name must be unique!')
    ]

    def test_connection(self):
        """Test the connection to the AI API"""
        self.ensure_one()

        # Debug: Log the current api_type value
        _logger.info("Testing connection with api_type: '%s'", self.api_type)

        try:
            if self.api_type == 'groq':
                # Test Groq API connection (exactly like your test_groq.py)
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }

                # Use the model from your test_groq.py
                model = self.api_model or 'llama3-70b-8192'

                data = {
                    'model': model,
                    'messages': [
                        {'role': 'user', 'content': 'Hello, are you working?'}
                    ],
                    'temperature': 0.7
                }

                # Use the exact URL from your test_groq.py
                url = self.api_url or 'https://api.groq.com/openai/v1/chat/completions'
                _logger.info("Testing Groq API at URL: %s with model: %s", url, model)

                response = requests.post(url, headers=headers, json=data, timeout=10)
                response.raise_for_status()

                if response.status_code == 200:
                    reply = response.json()
                    ai_response = reply["choices"][0]["message"]["content"]

                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Connection Successful'),
                            'message': _('Successfully connected to Groq API!\n\nModel: %s\nResponse: %s') % (model, ai_response[:200]),
                            'sticky': False,
                            'type': 'success',
                        }
                    }
                else:
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Connection Failed'),
                            'message': _('Failed: %s %s') % (response.status_code, response.text),
                            'sticky': True,
                            'type': 'danger',
                        }
                    }

            elif self.api_type == 'openai':
                # Test OpenAI API connection
                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }

                data = {
                    'model': self.api_model or 'gpt-3.5-turbo',
                    'messages': [
                        {'role': 'system', 'content': 'You are a helpful assistant.'},
                        {'role': 'user', 'content': 'Hello, are you working?'}
                    ],
                    'temperature': self.temperature,
                    'max_tokens': 50
                }

                url = self.api_url or 'https://api.openai.com/v1/chat/completions'

                response = requests.post(url, headers=headers, json=data, timeout=10)
                response.raise_for_status()

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Connection Successful'),
                        'message': _('Successfully connected to OpenAI API.'),
                        'sticky': False,
                        'type': 'success',
                    }
                }

            else:
                # Fallback: Try Groq API if no specific type matches (for backward compatibility)
                _logger.warning("Unknown api_type '%s', falling back to Groq API", self.api_type)

                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }

                model = self.api_model or 'llama3-70b-8192'

                data = {
                    'model': model,
                    'messages': [
                        {'role': 'user', 'content': 'Hello, are you working?'}
                    ],
                    'temperature': 0.7
                }

                url = self.api_url or 'https://api.groq.com/openai/v1/chat/completions'
                _logger.info("Fallback: Testing Groq API at URL: %s with model: %s", url, model)

                response = requests.post(url, headers=headers, json=data, timeout=10)
                response.raise_for_status()

                if response.status_code == 200:
                    reply = response.json()
                    ai_response = reply["choices"][0]["message"]["content"]

                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Connection Successful'),
                            'message': _('Successfully connected to API!\n\nModel: %s\nResponse: %s') % (model, ai_response[:200]),
                            'sticky': False,
                            'type': 'success',
                        }
                    }
                else:
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Connection Failed'),
                            'message': _('Failed: %s %s') % (response.status_code, response.text),
                            'sticky': True,
                            'type': 'danger',
                        }
                    }

        except requests.exceptions.RequestException as e:
            _logger.error("API connection error: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Connection Failed'),
                    'message': _('Failed to connect to API: %s') % str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }
        except Exception as e:
            _logger.error("Unexpected error: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('An unexpected error occurred: %s') % str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }
