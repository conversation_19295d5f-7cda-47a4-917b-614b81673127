// AI Chatbot Main JavaScript
console.log('=== CHATBOT MAIN JS LOADED ===');

// Global variables
window.chatbotCurrentConversationId = null;
window.chatbotServiceType = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Chatbot Main');

    // Check if we're on a chatbot page
    if (document.getElementById('send-chat-message')) {
        console.log('Chatbot page detected, initializing...');
        initializeChatbot();
    }
});

function initializeChatbot() {
    console.log('Initializing chatbot...');

    // Wait for jQuery to be available
    var checkJQuery = setInterval(function() {
        if (typeof jQuery !== 'undefined') {
            clearInterval(checkJQuery);
            console.log('jQuery found, setting up chatbot...');
            setupChatbot();
        }
    }, 100);
}

function setupChatbot() {
    var $ = jQuery;

    console.log('Setting up chatbot with jQuery...');
    console.log('Send button exists:', $('#send-chat-message').length > 0);
    console.log('Chat input exists:', $('#chat-message-input').length > 0);

    // Get service type from page
    var serviceTypeElement = document.querySelector('[data-service-type]');
    if (serviceTypeElement) {
        window.chatbotServiceType = serviceTypeElement.getAttribute('data-service-type');
    } else {
        // Try to extract from URL
        var urlParts = window.location.pathname.split('/');
        window.chatbotServiceType = urlParts[urlParts.length - 1] || 'CONTRACT_DRAFT';
    }

    console.log('Service type:', window.chatbotServiceType);

    // Set welcome time
    var welcomeTimeEl = document.getElementById('welcome-time');
    if (welcomeTimeEl) {
        welcomeTimeEl.textContent = new Date().toLocaleTimeString();
        console.log('Welcome time set');
    }

    // Bind events
    $('#send-chat-message').off('click').on('click', function() {
        console.log('Send button clicked!');
        sendMessage();
    });

    $('#chat-message-input').off('keypress').on('keypress', function(e) {
        if (e.which === 13) {
            console.log('Enter key pressed!');
            sendMessage();
        }
    });

    console.log('Event handlers bound');

    // Start conversation
    startConversation();
}

function startConversation() {
    console.log('Starting conversation...');
    var $ = jQuery;

    $.ajax({
        url: '/chatbot/start-conversation',
        method: 'POST',
        data: JSON.stringify({
            params: {
                service_type: window.chatbotServiceType,
                language: 'en_US'
            }
        }),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log('Start conversation success:', response);
            var result = response.result || response;
            if (result.success) {
                window.chatbotCurrentConversationId = result.conversation_id;
                console.log('Conversation ID:', window.chatbotCurrentConversationId);
            } else {
                console.error('Start conversation failed:', result.error);
            }
        },
        error: function(xhr, status, error) {
            console.error('Start conversation error:', error, xhr.responseText);
        }
    });
}

function sendMessage() {
    var $ = jQuery;
    var message = $('#chat-message-input').val().trim();
    console.log('Sending message:', message);

    if (!message || !window.chatbotCurrentConversationId) {
        console.log('No message or conversation ID');
        return;
    }

    // Clear input
    $('#chat-message-input').val('');

    // Add user message to chat
    addMessage(message, false);

    $.ajax({
        url: '/chatbot/send-message',
        method: 'POST',
        data: JSON.stringify({
            params: {
                conversation_id: window.chatbotCurrentConversationId,
                message: message
            }
        }),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log('Send message success:', response);
            var result = response.result || response;
            if (result.success) {
                // Check if response contains a form
                if (result.show_form && result.form_data) {
                    console.log('Form detected in response:', result.form_data);
                    addMessage(result.ai_response, true);
                    renderForm(result.form_data);
                } else {
                    addMessage(result.ai_response, true);
                }
            } else {
                addMessage('Error: ' + (result.error || 'Unknown error'), true);
            }
        },
        error: function(xhr, status, error) {
            console.error('Send message error:', error, xhr.responseText);
            addMessage('Connection error. Please try again.', true);
        }
    });
}

function addMessage(message, isAI) {
    var $ = jQuery;

    // Format message content based on type with enhanced logging
    var formattedMessage;
    if (isAI) {
        console.log('Formatting AI message in chatbot_main.js:', message);
        if (window.AIMessageFormatter && typeof window.AIMessageFormatter.formatAIMessage === 'function') {
            formattedMessage = window.AIMessageFormatter.formatAIMessage(message);
            console.log('Formatted AI message:', formattedMessage);
        } else {
            console.warn('AIMessageFormatter not available in chatbot_main.js, using basic formatting');
            formattedMessage = message.replace(/\n/g, '<br/>');
        }
    } else {
        // Escape HTML for user messages and fallback
        formattedMessage = (window.AIMessageFormatter && window.AIMessageFormatter.escapeHtml ?
            window.AIMessageFormatter.escapeHtml(message) : message).replace(/\n/g, '<br/>');
    }

    var messageHtml = '<div class="message ' + (isAI ? 'ai-message' : 'user-message') + ' mb-3">' +
        '<div class="d-flex ' + (isAI ? '' : 'justify-content-end') + '">' +
        (isAI ? '<div class="avatar me-3"><div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;"><i class="fa fa-robot"></i></div></div>' : '') +
        '<div class="message-content" style="max-width: 70%;"><div class="message-bubble ' + (isAI ? 'bg-white' : 'bg-primary text-white') + ' p-3 rounded shadow-sm">' +
        (isAI ? '<div class="message-header mb-2"><strong>AI Assistant</strong><small class="text-muted ms-2">' + new Date().toLocaleTimeString() + '</small></div>' : '') +
        '<div class="message-text">' + formattedMessage + '</div></div></div>' +
        (!isAI ? '<div class="avatar ms-3"><div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;"><i class="fa fa-user"></i></div></div>' : '') +
        '</div></div>';

    $('#chat-messages-area').append(messageHtml);
    $('#chat-messages-area').scrollTop($('#chat-messages-area')[0].scrollHeight);
}

function renderForm(formData) {
    console.log('Rendering form:', formData);
    var $ = jQuery;

    var formHtml = '<div class="ai-form-container mt-3 p-3 border rounded bg-light">';
    formHtml += '<h6 class="mb-3"><i class="fa fa-form"></i> ' + (formData.title || 'Please fill out this form') + '</h6>';
    formHtml += '<form class="ai-generated-form">';

    if (formData.fields && Array.isArray(formData.fields)) {
        formData.fields.forEach(function(field, index) {
            formHtml += '<div class="mb-3">';
            formHtml += '<label class="form-label">' + field.label;
            if (field.required) formHtml += ' <span class="text-danger">*</span>';
            formHtml += '</label>';

            if (field.type === 'text' || field.type === 'email') {
                formHtml += '<input type="' + field.type + '" class="form-control" name="' + field.name + '" ' + (field.required ? 'required' : '') + '>';
            } else if (field.type === 'textarea') {
                formHtml += '<textarea class="form-control" name="' + field.name + '" rows="3" ' + (field.required ? 'required' : '') + '></textarea>';
            } else if (field.type === 'select' && field.options) {
                formHtml += '<select class="form-control" name="' + field.name + '" ' + (field.required ? 'required' : '') + '>';
                formHtml += '<option value="">Choose...</option>';
                field.options.forEach(function(option) {
                    formHtml += '<option value="' + option + '">' + option + '</option>';
                });
                formHtml += '</select>';
            }
            formHtml += '</div>';
        });
    }

    formHtml += '<button type="submit" class="btn btn-primary">Submit Form</button>';
    formHtml += '</form></div>';

    $('#chat-messages-area').append(formHtml);
    $('#chat-messages-area').scrollTop($('#chat-messages-area')[0].scrollHeight);

    // Bind form submission
    $('.ai-generated-form').last().on('submit', function(e) {
        e.preventDefault();
        submitForm($(this));
    });
}

function submitForm(form) {
    console.log('Submitting form...');
    var $ = jQuery;
    var formData = {};
    form.find('input, textarea, select').each(function() {
        formData[$(this).attr('name')] = $(this).val();
    });

    console.log('Form data:', formData);

    // Send form data as a message
    var formMessage = 'Form submitted with the following data:\n';
    Object.keys(formData).forEach(function(key) {
        formMessage += '- ' + key + ': ' + formData[key] + '\n';
    });

    // Add form submission message
    addMessage('Form submitted successfully', false);

    // Send to AI for processing
    $.ajax({
        url: '/chatbot/send-message',
        method: 'POST',
        data: JSON.stringify({
            params: {
                conversation_id: window.chatbotCurrentConversationId,
                message: formMessage
            }
        }),
        contentType: 'application/json',
        dataType: 'json',
        success: function(response) {
            console.log('Form submission response:', response);
            var result = response.result || response;
            if (result.success) {
                addMessage(result.ai_response, true);

                // Remove the form after successful submission
                form.closest('.ai-form-container').fadeOut();
            } else {
                addMessage('Error processing form: ' + (result.error || 'Unknown error'), true);
            }
        },
        error: function(xhr, status, error) {
            console.error('Form submission error:', error);
            addMessage('Error submitting form. Please try again.', true);
        }
    });
}

console.log('=== CHATBOT MAIN JS LOADED COMPLETE ===');
