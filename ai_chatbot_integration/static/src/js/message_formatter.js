/**
 * AI Message Formatter - Enhanced markdown-style formatting for AI responses
 * Compatible with Odoo 18 and handles proper HTML escaping
 */

(function() {
    'use strict';

    // HTML escape function to prevent XSS
    function escapeHtml(text) {
        if (!text) return '';
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    /**
     * Enhanced AI message formatter with comprehensive markdown support
     * @param {string} message - Raw AI message text
     * @returns {string} - Formatted HTML
     */
    function formatAIMessage(message) {
        if (!message) return '';

        // Escape HTML first to prevent XSS
        var escaped = escapeHtml(message);

        // Convert markdown-like formatting
        var formatted = escaped
            // Bold text: **text** or __text__
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<strong>$1</strong>')

            // Italic text: *text* or _text_ (but avoid conflicts with bold)
            // Process single underscores first (word boundaries)
            .replace(/\b_([^_\s]+(?:\s+[^_\s]+)*)_\b/g, '<em>$1</em>')
            // Process single asterisks (not preceded or followed by asterisks)
            .replace(/(^|[^*])\*([^*\s]+(?:\s+[^*\s]+)*)\*([^*]|$)/g, '$1<em>$2</em>$3')

            // Code blocks: ```code``` or `code`
            .replace(/```([\s\S]*?)```/g, '<pre class="bg-light p-2 rounded border"><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code class="bg-light px-1 rounded border">$1</code>')

            // Headers: ### Header, ## Header, # Header
            .replace(/^### (.*$)/gm, '<h5 class="mt-3 mb-2 fw-bold">$1</h5>')
            .replace(/^## (.*$)/gm, '<h4 class="mt-3 mb-2 fw-bold">$1</h4>')
            .replace(/^# (.*$)/gm, '<h3 class="mt-3 mb-2 fw-bold">$1</h3>')

            // Numbered lists: 1. item, 2. item, etc.
            .replace(/^\d+\.\s+(.*$)/gm, '<li class="mb-1">$1</li>')

            // Bullet lists: * item, - item, + item
            .replace(/^[\*\-\+]\s+(.*$)/gm, '<li class="mb-1">$1</li>')

            // Blockquotes: > text
            .replace(/^>\s+(.*$)/gm, '<blockquote class="border-start border-3 border-primary ps-3 mb-2 fst-italic">$1</blockquote>')

            // Horizontal rules: --- or ***
            .replace(/^(---|\*\*\*)$/gm, '<hr class="my-3">')

            // Links: [text](url) - basic support
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>')

            // Line breaks
            .replace(/\n/g, '<br/>');

        // Post-process to wrap consecutive list items in proper list tags
        formatted = wrapListItems(formatted);

        // Clean up formatting issues
        formatted = cleanupFormatting(formatted);

        return formatted;
    }

    /**
     * Wrap consecutive list items in proper ol/ul tags
     * @param {string} html - HTML with list items
     * @returns {string} - HTML with proper list structure
     */
    function wrapListItems(html) {
        // Split by line breaks to process line by line
        var lines = html.split('<br/>');
        var result = [];
        var currentList = [];
        var listType = null; // 'ol' for numbered, 'ul' for bullets

        for (var i = 0; i < lines.length; i++) {
            var line = lines[i].trim();

            // Check if this line contains a list item
            var isListItem = /<li class="mb-1">/.test(line);

            if (isListItem) {
                // Determine list type from the original content
                // This is a heuristic - we'll use 'ol' for now and can improve later
                var newListType = 'ol';

                if (currentList.length === 0) {
                    // Starting a new list
                    listType = newListType;
                }

                if (listType === newListType) {
                    // Continue current list
                    currentList.push(line);
                } else {
                    // Different list type, close current and start new
                    if (currentList.length > 0) {
                        result.push('<' + listType + ' class="ps-3 mb-3">');
                        result = result.concat(currentList);
                        result.push('</' + listType + '>');
                        currentList = [];
                    }
                    listType = newListType;
                    currentList.push(line);
                }
            } else {
                // Not a list item, close any open list
                if (currentList.length > 0) {
                    result.push('<' + listType + ' class="ps-3 mb-3">');
                    result = result.concat(currentList);
                    result.push('</' + listType + '>');
                    currentList = [];
                    listType = null;
                }

                // Add the non-list line
                if (line) {
                    result.push(line);
                }
            }
        }

        // Close any remaining list
        if (currentList.length > 0) {
            result.push('<' + listType + ' class="ps-3 mb-3">');
            result = result.concat(currentList);
            result.push('</' + listType + '>');
        }

        return result.join('<br/>');
    }

    /**
     * Clean up formatting issues
     * @param {string} html - HTML to clean
     * @returns {string} - Cleaned HTML
     */
    function cleanupFormatting(html) {
        return html
            // Remove excessive line breaks
            .replace(/<br\/><br\/>/g, '<br/>')
            .replace(/(<br\/>){3,}/g, '<br/><br/>')

            // Clean up after headers
            .replace(/(<\/h[3-5]>)<br\/>/g, '$1')

            // Clean up after code blocks
            .replace(/(<\/pre>)<br\/>/g, '$1')

            // Clean up after lists
            .replace(/(<\/ol>)<br\/>/g, '$1')
            .replace(/(<\/ul>)<br\/>/g, '$1')

            // Clean up after blockquotes
            .replace(/(<\/blockquote>)<br\/>/g, '$1')

            // Clean up after horizontal rules
            .replace(/(<hr[^>]*>)<br\/>/g, '$1')

            // Ensure proper spacing around block elements
            .replace(/(<h[3-5][^>]*>)/g, '<br/>$1')
            .replace(/(<ol[^>]*>)/g, '<br/>$1')
            .replace(/(<ul[^>]*>)/g, '<br/>$1')
            .replace(/(<blockquote[^>]*>)/g, '<br/>$1')
            .replace(/(<hr[^>]*>)/g, '<br/>$1')

            // Remove leading line breaks
            .replace(/^<br\/>/, '');
    }

    /**
     * Format AI message for display in conversation
     * @param {string} message - Raw AI message
     * @param {boolean} preserveNewlines - Whether to preserve original newlines
     * @returns {string} - Formatted HTML
     */
    function formatConversationMessage(message, preserveNewlines) {
        if (preserveNewlines === false) {
            // Remove extra newlines for compact display
            message = message.replace(/\n{3,}/g, '\n\n');
        }
        return formatAIMessage(message);
    }

    /**
     * Create a formatted message bubble HTML
     * @param {string} message - Raw message text
     * @param {boolean} isAI - Whether this is an AI message
     * @param {object} options - Additional options
     * @returns {string} - Complete message HTML
     */
    function createMessageBubble(message, isAI, options) {
        options = options || {};
        var timestamp = options.timestamp || 'Just now';
        var sender = isAI ? 'AI Assistant' : (options.sender || 'You');
        var bubbleClass = isAI ? 'bg-light' : 'bg-primary text-white';
        var headerClass = isAI ? 'text-muted' : 'text-light';

        var formattedMessage = isAI ? formatAIMessage(message) : escapeHtml(message).replace(/\n/g, '<br/>');

        return `
            <div class="message mb-3 ${isAI ? 'ai-message' : 'user-message'}">
                <div class="message-bubble p-3 rounded ${bubbleClass}">
                    <div class="message-header mb-2">
                        <strong>${sender}</strong>
                        <small class="${headerClass} ms-2">${timestamp}</small>
                    </div>
                    <div class="message-content">
                        ${formattedMessage}
                    </div>
                </div>
            </div>
        `;
    }

    // Export functions to global scope for use in templates
    window.AIMessageFormatter = {
        formatAIMessage: formatAIMessage,
        formatConversationMessage: formatConversationMessage,
        createMessageBubble: createMessageBubble,
        escapeHtml: escapeHtml
    };

    // Backward compatibility
    window.formatAIMessage = formatAIMessage;
    window.escapeHtml = escapeHtml;

})();
