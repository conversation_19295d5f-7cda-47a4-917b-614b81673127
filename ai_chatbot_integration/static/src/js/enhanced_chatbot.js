/**
 * Enhanced Legal Chatbot Interface
 * Modern, animated chatbot with survey integration for lawyers
 */

/**
 * Enhanced Legal Chatbot Interface
 * Simple implementation for Odoo 17 Community Edition
 */
$(document).ready(function() {
    'use strict';

    // Global chatbot state
    var chatbotState = {
        isTyping: false,
        messageQueue: [],
        currentSurvey: null,
        animationDelay: 0
    };

    /**
     * Initialize the legal chatbot interface
     */
    function initializeLegalChatbot() {
        console.log('Initializing Enhanced Legal Chatbot...');

        // Initialize components
        initializeMessageHandling();
        initializeQuickActions();
        initializeSurveyIntegration();
        initializeAnimations();
        initializeTypingEffects();

        // Auto-scroll to bottom
        scrollToBottom();

        // Focus on input
        $('.message-input').focus();
    }

    /**
     * Initialize message handling
     */
    function initializeMessageHandling() {
        $('#conversation-form').on('submit', function(e) {
            e.preventDefault();
            handleMessageSubmit();
        });

        // Enter key handling
        $('.message-input').on('keypress', function(e) {
            if (e.which === 13 && !e.shiftKey) {
                e.preventDefault();
                handleMessageSubmit();
            }
        });

        // Auto-resize textarea
        $('.message-input').on('input', function() {
            autoResizeInput(this);
        });
    }

    /**
     * Initialize quick action buttons
     */
    function initializeQuickActions() {
        $(document).on('click', '.quick-action-btn', function() {
            var message = $(this).data('message');
            $('.message-input').val(message);
            handleMessageSubmit();
        });
    }

    /**
     * Initialize survey integration
     */
    function initializeSurveyIntegration() {
        $(document).on('submit', '.survey-form', function(e) {
            e.preventDefault();
            handleSurveySubmit($(this));
        });

        $(document).on('click', '.survey-skip-btn', function() {
            var $form = $(this).closest('.survey-form');
            skipSurvey($form);
        });
    }

    /**
     * Initialize animations
     */
    function initializeAnimations() {
        // Animate existing messages
        $('.message').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });

        // Initialize intersection observer for scroll animations
        if ('IntersectionObserver' in window) {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            });

            $('.message').each(function() {
                observer.observe(this);
            });
        }
    }

    /**
     * Initialize typing effects
     */
    function initializeTypingEffects() {
        // Add typing indicator styles
        var typingCSS = `
            .typing-dots {
                display: flex;
                align-items: center;
                padding: 1rem;
            }
            .typing-dots span {
                height: 8px;
                width: 8px;
                background-color: var(--accent-color);
                border-radius: 50%;
                display: inline-block;
                margin: 0 2px;
                animation: typing 1.4s infinite ease-in-out;
            }
            .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
            .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        `;

        if (!$('#typing-styles').length) {
            $('<style id="typing-styles">').text(typingCSS).appendTo('head');
        }
    }

    /**
     * Handle message submission
     */
    function handleMessageSubmit() {
        var $input = $('.message-input');
        var message = $input.val().trim();

        if (!message || chatbotState.isTyping) {
            return;
        }

        // Clear input
        $input.val('');
        autoResizeInput($input[0]);

        // Add user message with animation
        addUserMessage(message);

        // Show typing indicator
        showTypingIndicator();

        // Send message to server
        sendMessageToAI(message);
    }

    /**
     * Add user message to conversation
     */
    function addUserMessage(message) {
        var userMessageHtml = `
            <div class="message user-message" style="animation-delay: 0s;">
                <div class="message-bubble user-bubble">
                    <div class="message-header">
                        <strong>You</strong>
                        <small class="text-muted">Just now</small>
                    </div>
                    <div class="message-content">
                        ${escapeHtml(message)}
                    </div>
                </div>
            </div>
        `;

        $('#conversation-messages').append(userMessageHtml);
        scrollToBottom();
    }

    /**
     * Show typing indicator
     */
    function showTypingIndicator() {
        chatbotState.isTyping = true;
        $('#typing-indicator').show();
        scrollToBottom();
    }

    /**
     * Hide typing indicator
     */
    function hideTypingIndicator() {
        chatbotState.isTyping = false;
        $('#typing-indicator').hide();
    }

    /**
     * Send message to AI backend
     */
    function sendMessageToAI(message) {
        var requestId = $('input[name="request_id"]').val();
        var csrfToken = $('input[name="csrf_token"]').val();

        $.ajax({
            url: '/legal-documents/conversation/send',
            method: 'POST',
            data: {
                'request_id': requestId,
                'message': message,
                'csrf_token': csrfToken
            },
            timeout: 30000,
            success: function(response) {
                hideTypingIndicator();

                if (response.success) {
                    addAIMessage(response.message, response.has_form, response.form_data);
                } else {
                    addErrorMessage(response.error || 'An error occurred while processing your request.');
                }
            },
            error: function(xhr, status, error) {
                hideTypingIndicator();
                console.error('AI Request failed:', error);
                addErrorMessage('Sorry, I encountered an error. Please try again.');
            }
        });
    }

    /**
     * Add AI message to conversation
     */
    function addAIMessage(message, hasForm, formData) {
        var aiMessageHtml = `
            <div class="message ai-message" style="animation-delay: 0s;">
                <div class="message-bubble">
                    <div class="message-header">
                        <strong>AI Legal Assistant</strong>
                        <small class="text-muted">Just now</small>
                    </div>
                    <div class="message-content">
                        ${window.AIMessageFormatter && typeof window.AIMessageFormatter.formatAIMessage === 'function' ?
                            window.AIMessageFormatter.formatAIMessage(message) :
                            message.replace(/\n/g, '<br/>')}
                    </div>
                </div>
            </div>
        `;

        $('#conversation-messages').append(aiMessageHtml);

        // Add survey form if needed
        if (hasForm && formData) {
            setTimeout(function() {
                addSurveyForm(formData);
            }, 500);
        }

        scrollToBottom();
    }

    /**
     * Add survey form to conversation
     */
    function addSurveyForm(formData) {
        var surveyHtml = `
            <div class="message ai-message survey-message" style="animation-delay: 0s;">
                <div class="message-bubble">
                    <div class="embedded-survey-form">
                        <div class="survey-header">
                            <h6 class="survey-title">
                                <i class="fa fa-clipboard-list me-2"></i>
                                ${formData.title || 'Please provide the following information'}
                            </h6>
                            <p class="survey-description">
                                ${formData.description || 'This information will help me create a more accurate document for you.'}
                            </p>
                        </div>

                        <form class="survey-form" data-form-id="${formData.id || ''}">
                            <div class="survey-fields">
                                ${renderSurveyFields(formData.fields || [])}
                            </div>

                            <div class="survey-actions">
                                <button type="submit" class="btn btn-primary survey-submit-btn">
                                    <i class="fa fa-check me-2"></i>
                                    Submit Information
                                </button>
                                <button type="button" class="btn btn-outline-secondary survey-skip-btn">
                                    <i class="fa fa-arrow-right me-2"></i>
                                    Skip for now
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        $('#conversation-messages').append(surveyHtml);
        scrollToBottom();

        // Initialize form animations
        setTimeout(function() {
            $('.survey-fields .form-group').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });
        }, 100);
    }

    /**
     * Render survey fields
     */
    function renderSurveyFields(fields) {
        var fieldsHtml = '';

        fields.forEach(function(field, index) {
            var fieldHtml = '';
            var required = field.required ? 'required' : '';
            var requiredStar = field.required ? '<span class="text-danger">*</span>' : '';

            switch (field.type) {
                case 'text':
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                            <input type="text" class="form-control" id="${field.name}" name="${field.name}" ${required}
                                   placeholder="${field.placeholder || ''}" />
                            ${field.help ? `<small class="form-text text-muted">${field.help}</small>` : ''}
                        </div>
                    `;
                    break;

                case 'textarea':
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                            <textarea class="form-control" id="${field.name}" name="${field.name}" ${required}
                                      rows="3" placeholder="${field.placeholder || ''}"></textarea>
                            ${field.help ? `<small class="form-text text-muted">${field.help}</small>` : ''}
                        </div>
                    `;
                    break;

                case 'select':
                    var optionsHtml = '';
                    if (field.options) {
                        field.options.forEach(function(option) {
                            optionsHtml += `<option value="${option.value}">${option.label}</option>`;
                        });
                    }
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                            <select class="form-control" id="${field.name}" name="${field.name}" ${required}>
                                <option value="">Please select...</option>
                                ${optionsHtml}
                            </select>
                            ${field.help ? `<small class="form-text text-muted">${field.help}</small>` : ''}
                        </div>
                    `;
                    break;

                case 'date':
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                            <input type="date" class="form-control" id="${field.name}" name="${field.name}" ${required} />
                            ${field.help ? `<small class="form-text text-muted">${field.help}</small>` : ''}
                        </div>
                    `;
                    break;

                case 'number':
                    fieldHtml = `
                        <div class="form-group">
                            <label for="${field.name}">${field.label} ${requiredStar}</label>
                            <input type="number" class="form-control" id="${field.name}" name="${field.name}" ${required}
                                   placeholder="${field.placeholder || ''}" />
                            ${field.help ? `<small class="form-text text-muted">${field.help}</small>` : ''}
                        </div>
                    `;
                    break;
            }

            fieldsHtml += fieldHtml;
        });

        return fieldsHtml;
    }

    // Utility functions
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    function formatAIMessage(message) {
        if (!message) return '';

        // Escape HTML first to prevent XSS
        var escaped = escapeHtml(message);

        // Convert markdown-like formatting
        var formatted = escaped
            // Bold text: **text** or __text__
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<strong>$1</strong>')

            // Italic text: *text* or _text_
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/_(.*?)_/g, '<em>$1</em>')

            // Code blocks: ```code``` or `code`
            .replace(/```([\s\S]*?)```/g, '<pre class="bg-light p-2 rounded"><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code class="bg-light px-1 rounded">$1</code>')

            // Headers: ## Header or # Header
            .replace(/^### (.*$)/gm, '<h5 class="mt-3 mb-2">$1</h5>')
            .replace(/^## (.*$)/gm, '<h4 class="mt-3 mb-2">$1</h4>')
            .replace(/^# (.*$)/gm, '<h3 class="mt-3 mb-2">$1</h3>')

            // Lists: numbered and bulleted
            .replace(/^\d+\.\s+(.*$)/gm, '<li class="mb-1">$1</li>')
            .replace(/^[\*\-\+]\s+(.*$)/gm, '<li class="mb-1">$1</li>')

            // Line breaks
            .replace(/\n/g, '<br/>');

        // Wrap consecutive list items in proper list tags
        formatted = formatted
            .replace(/(<li class="mb-1">.*?<\/li>)(<br\/>)*(?=<li class="mb-1">)/g, '$1')
            .replace(/(<li class="mb-1">.*?<\/li>)(<br\/>)*(?!<li class="mb-1">)/g, '</ol>$1')
            .replace(/(?<!<\/ol>)(<li class="mb-1">)/g, '<ol class="ps-3">$1');

        // Clean up any remaining issues
        formatted = formatted
            .replace(/<br\/><br\/>/g, '<br/>')
            .replace(/(<\/h[3-5]>)<br\/>/g, '$1')
            .replace(/(<\/pre>)<br\/>/g, '$1')
            .replace(/(<\/ol>)<br\/>/g, '$1');

        return formatted;
    }

    function autoResizeInput(input) {
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 120) + 'px';
    }

    function scrollToBottom() {
        var container = $('#conversation-messages');
        container.animate({
            scrollTop: container[0].scrollHeight
        }, 300);
    }

    function addErrorMessage(error) {
        var errorHtml = `
            <div class="message ai-message error-message">
                <div class="message-bubble bg-danger text-white">
                    <div class="message-header">
                        <strong>System</strong>
                        <small class="text-light">Just now</small>
                    </div>
                    <div class="message-content">
                        <i class="fa fa-exclamation-triangle me-2"></i>
                        ${escapeHtml(error)}
                    </div>
                </div>
            </div>
        `;
        $('#conversation-messages').append(errorHtml);
        scrollToBottom();
    }

    // Export the initialization function
    window.initializeLegalChatbot = initializeLegalChatbot;

    // Initialize if elements exist
    if ($('.legal-chatbot-container').length) {
        initializeLegalChatbot();
    }
});
