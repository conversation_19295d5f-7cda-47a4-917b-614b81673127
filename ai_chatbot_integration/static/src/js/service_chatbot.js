/**
 * Service-Specific Chatbot JavaScript
 * Handles full-page chatbot functionality for specific legal services
 */
$(document).ready(function() {
    'use strict';

    var ServiceChatbot = {
        currentConversationId: null,
        currentDocumentRequestId: null,
        serviceType: null,

        /**
         * Initialize the service chatbot
         */
        init: function(serviceType) {
            this.serviceType = serviceType;
            this.bindEvents();
            this.startConversation();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;

            // Send message on button click
            $(document).on('click', '#send-chat-message', function() {
                self.sendMessage();
            });

            // Send message on Enter key
            $(document).on('keypress', '#chat-message-input', function(e) {
                if (e.which === 13) {
                    self.sendMessage();
                }
            });

            // Handle form submissions
            $(document).on('submit', '.dynamic-form', function(e) {
                e.preventDefault();
                self.submitForm($(this));
            });

            // Handle payment buttons
            $(document).on('click', '.payment-btn', function() {
                var paymentUrl = $(this).data('payment-url');
                if (paymentUrl) {
                    window.open(paymentUrl, '_blank');
                }
            });
        },

        /**
         * Start a new conversation
         */
        startConversation: function() {
            var self = this;

            $.ajax({
                url: '/chatbot/start-conversation',
                method: 'POST',
                data: JSON.stringify({
                    params: {
                        service_type: this.serviceType,
                        language: $('html').attr('lang') || 'en_US'
                    }
                }),
                contentType: 'application/json',
                dataType: 'json',
                success: function(response) {
                    var result = response.result || response;
                    if (result.success) {
                        self.currentConversationId = result.conversation_id;
                        self.currentDocumentRequestId = result.document_request_id;

                        // Add welcome message
                        self.addMessage(result.welcome_message, true);
                    } else {
                        self.showError('Failed to start conversation: ' + (result.error || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    self.showError('Failed to connect to AI assistant: ' + error);
                }
            });
        },

        /**
         * Send a message to the AI
         */
        sendMessage: function() {
            var message = $('#chat-message-input').val().trim();
            if (!message || !this.currentConversationId) return;

            // Clear input
            $('#chat-message-input').val('');

            // Add user message
            this.addMessage(message, false);

            // Show typing indicator
            this.showTypingIndicator();

            var self = this;

            $.ajax({
                url: '/chatbot/send-message',
                method: 'POST',
                data: JSON.stringify({
                    params: {
                        conversation_id: this.currentConversationId,
                        message: message
                    }
                }),
                contentType: 'application/json',
                dataType: 'json',
                success: function(response) {
                    self.hideTypingIndicator();

                    var result = response.result || response;
                    if (result.success) {
                        // Add AI response
                        self.addMessage(result.ai_response, true);

                        // Handle form generation
                        if (result.show_form && result.form_data) {
                            self.showForm(result.form_data);
                        }

                        // Handle payment request
                        if (result.request_payment && result.payment_info) {
                            self.showPaymentRequest(result.payment_info);
                        }
                    } else {
                        self.addMessage('Sorry, I encountered an error: ' + (result.error || 'Unknown error'), true);
                    }
                },
                error: function(xhr, status, error) {
                    self.hideTypingIndicator();
                    self.addMessage('Sorry, I\'m having trouble connecting. Please try again.', true);
                }
            });
        },

        /**
         * Add a message to the chat
         */
        addMessage: function(message, isAI) {
            var messageHtml = `
                <div class="message ${isAI ? 'ai-message' : 'user-message'} mb-3">
                    <div class="d-flex ${isAI ? '' : 'justify-content-end'}">
                        ${isAI ? `
                            <div class="avatar me-3">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 40px; height: 40px;">
                                    <i class="fa fa-robot"></i>
                                </div>
                            </div>
                        ` : ''}
                        <div class="message-content" style="max-width: 70%;">
                            <div class="message-bubble ${isAI ? 'bg-white' : 'bg-primary text-white'} p-3 rounded shadow-sm">
                                ${isAI ? `
                                    <div class="message-header mb-2">
                                        <strong>AI Legal Assistant</strong>
                                        <small class="text-muted ms-2">${new Date().toLocaleTimeString()}</small>
                                    </div>
                                ` : ''}
                                <div class="message-text">${this.escapeHtml(message)}</div>
                            </div>
                        </div>
                        ${!isAI ? `
                            <div class="avatar ms-3">
                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 40px; height: 40px;">
                                    <i class="fa fa-user"></i>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            $('#chat-messages-area').append(messageHtml);
            this.scrollToBottom();
        },

        /**
         * Show typing indicator
         */
        showTypingIndicator: function() {
            var typingHtml = `
                <div id="typing-indicator" class="message ai-message mb-3">
                    <div class="d-flex">
                        <div class="avatar me-3">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 40px; height: 40px;">
                                <i class="fa fa-robot"></i>
                            </div>
                        </div>
                        <div class="message-content">
                            <div class="message-bubble bg-white p-3 rounded shadow-sm">
                                <div class="typing-dots">
                                    <span class="dot"></span>
                                    <span class="dot"></span>
                                    <span class="dot"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#chat-messages-area').append(typingHtml);
            this.scrollToBottom();
        },

        /**
         * Hide typing indicator
         */
        hideTypingIndicator: function() {
            $('#typing-indicator').remove();
        },

        /**
         * Show AI-generated form
         */
        showForm: function(formData) {
            var formHtml = `
                <form class="dynamic-form">
                    <h6 class="mb-3">${formData.title || 'Please provide the following information:'}</h6>
            `;

            formData.fields.forEach(function(field) {
                formHtml += `
                    <div class="mb-3">
                        <label class="form-label">${field.label}${field.required ? ' *' : ''}</label>
                `;

                if (field.type === 'text' || field.type === 'email') {
                    formHtml += `
                        <input type="${field.type}" class="form-control" name="${field.name}"
                               ${field.required ? 'required' : ''}>
                    `;
                } else if (field.type === 'textarea') {
                    formHtml += `
                        <textarea class="form-control" name="${field.name}" rows="3"
                                  ${field.required ? 'required' : ''}></textarea>
                    `;
                } else if (field.type === 'select') {
                    formHtml += `<select class="form-control" name="${field.name}" ${field.required ? 'required' : ''}>`;
                    formHtml += `<option value="">Choose...</option>`;
                    field.options.forEach(function(option) {
                        formHtml += `<option value="${option}">${option}</option>`;
                    });
                    formHtml += `</select>`;
                } else if (field.type === 'date') {
                    formHtml += `
                        <input type="date" class="form-control" name="${field.name}"
                               ${field.required ? 'required' : ''}>
                    `;
                } else if (field.type === 'number') {
                    formHtml += `
                        <input type="number" class="form-control" name="${field.name}"
                               ${field.required ? 'required' : ''}>
                    `;
                }

                formHtml += `</div>`;
            });

            formHtml += `
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fa fa-paper-plane me-2"></i>Submit Information
                        </button>
                    </div>
                </form>
            `;

            $('#form-container-area').html(formHtml).show();
            this.scrollToBottom();
        },

        /**
         * Submit form data
         */
        submitForm: function($form) {
            var formData = {};
            $form.find('input, select, textarea').each(function() {
                var $field = $(this);
                formData[$field.attr('name')] = $field.val();
            });

            // Convert form data to message
            var message = "I've filled out the form with the following information:\n\n";
            for (var field in formData) {
                if (formData[field]) {
                    message += `${field}: ${formData[field]}\n`;
                }
            }

            // Hide form
            $('#form-container-area').hide();

            // Add form submission as user message
            this.addMessage("Form submitted with the provided information.", false);

            // Send form data to AI
            this.sendFormData(formData);
        },

        /**
         * Send form data to AI
         */
        sendFormData: function(formData) {
            // For now, just send as a regular message
            // This can be enhanced to use a specific form submission endpoint
            var message = "Form data: " + JSON.stringify(formData);

            this.showTypingIndicator();

            var self = this;

            $.ajax({
                url: '/chatbot/send-message',
                method: 'POST',
                data: JSON.stringify({
                    params: {
                        conversation_id: this.currentConversationId,
                        message: message
                    }
                }),
                contentType: 'application/json',
                dataType: 'json',
                success: function(response) {
                    self.hideTypingIndicator();

                    var result = response.result || response;
                    if (result.success) {
                        self.addMessage(result.ai_response, true);

                        if (result.request_payment && result.payment_info) {
                            self.showPaymentRequest(result.payment_info);
                        }
                    } else {
                        self.addMessage('Sorry, I encountered an error processing your form: ' + (result.error || 'Unknown error'), true);
                    }
                },
                error: function() {
                    self.hideTypingIndicator();
                    self.addMessage('Sorry, I\'m having trouble processing your form.', true);
                }
            });
        },

        /**
         * Show payment request
         */
        showPaymentRequest: function(paymentInfo) {
            var paymentHtml = `
                <div class="payment-request p-4 text-center">
                    <h5 class="mb-3">Ready to Proceed with Payment</h5>
                    <div class="payment-details mb-4">
                        <p><strong>Service:</strong> ${paymentInfo.service_name}</p>
                        <p><strong>Amount:</strong> <span class="h4 text-success">$${paymentInfo.amount}</span></p>
                    </div>
                    <button class="btn btn-success btn-lg payment-btn" data-payment-url="${paymentInfo.payment_url}">
                        <i class="fa fa-credit-card me-2"></i>Pay Now
                    </button>
                    <p class="small text-muted mt-3">
                        Your document will be generated immediately after payment confirmation.
                    </p>
                </div>
            `;

            $('#payment-container-area').html(paymentHtml).show();
            this.scrollToBottom();
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.addMessage('Error: ' + message, true);
        },

        /**
         * Scroll to bottom of chat
         */
        scrollToBottom: function() {
            var chatArea = $('#chat-messages-area');
            chatArea.scrollTop(chatArea[0].scrollHeight);
        },

        /**
         * Escape HTML to prevent XSS
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };

    // Export for global access
    window.ServiceChatbot = ServiceChatbot;
});
