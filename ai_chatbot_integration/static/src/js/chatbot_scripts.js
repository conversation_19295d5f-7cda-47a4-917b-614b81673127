/**
 * AI Chatbot Integration Scripts
 * Main chatbot functionality for website integration
 */
$(document).ready(function() {
    'use strict';

    var AIChatbot = {
        isOpen: false,
        currentConversationId: null,

        /**
         * Initialize the chatbot
         */
        init: function() {
            this.createChatbotWidget();
            this.bindEvents();
        },

        /**
         * Create the chatbot widget HTML
         */
        createChatbotWidget: function() {
            var chatbotHtml = `
                <div id="ai-chatbot-widget" class="ai-chatbot-widget">
                    <!-- Chatbot Toggle Button -->
                    <div id="chatbot-toggle" class="chatbot-toggle">
                        <i class="fa fa-comments"></i>
                        <span class="chatbot-badge">AI Legal Assistant</span>
                    </div>

                    <!-- Chatbot Window -->
                    <div id="chatbot-window" class="chatbot-window" style="display: none;">
                        <div class="chatbot-header">
                            <div class="chatbot-title">
                                <i class="fa fa-balance-scale me-2"></i>
                                AI Legal Assistant
                            </div>
                            <div class="chatbot-controls">
                                <button id="chatbot-minimize" class="btn btn-sm">
                                    <i class="fa fa-minus"></i>
                                </button>
                                <button id="chatbot-close" class="btn btn-sm">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="chatbot-body">
                            <!-- Service Selection -->
                            <div id="service-selection" class="service-selection">
                                <div class="welcome-message">
                                    <h5>Welcome to AI Legal Assistant!</h5>
                                    <p>I can help you with various legal documents. Please select a service:</p>
                                </div>

                                <div class="service-buttons">
                                    <button class="btn btn-outline-primary service-btn" data-service="employment">
                                        <i class="fa fa-briefcase"></i> Employment Contracts
                                    </button>
                                    <button class="btn btn-outline-primary service-btn" data-service="nda">
                                        <i class="fa fa-shield-alt"></i> Non-Disclosure Agreements
                                    </button>
                                    <button class="btn btn-outline-primary service-btn" data-service="business">
                                        <i class="fa fa-building"></i> Business Formation
                                    </button>
                                    <button class="btn btn-outline-primary service-btn" data-service="will">
                                        <i class="fa fa-heart"></i> Wills & Estate Planning
                                    </button>
                                    <button class="btn btn-outline-primary service-btn" data-service="real-estate">
                                        <i class="fa fa-home"></i> Real Estate Documents
                                    </button>
                                    <button class="btn btn-outline-primary service-btn" data-service="ip">
                                        <i class="fa fa-lightbulb"></i> Intellectual Property
                                    </button>
                                </div>
                            </div>

                            <!-- Chat Interface -->
                            <div id="chat-interface" class="chat-interface" style="display: none;">
                                <div id="chat-messages" class="chat-messages"></div>

                                <!-- Form Container for AI-generated forms -->
                                <div id="form-container" class="form-container" style="display: none;"></div>

                                <div class="chat-input">
                                    <div class="input-group">
                                        <input type="text" id="chat-input" class="form-control" placeholder="Type your message...">
                                        <button id="send-message" class="btn btn-primary">
                                            <i class="fa fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add chatbot to body if it doesn't exist
            if (!$('#ai-chatbot-widget').length) {
                $('body').append(chatbotHtml);
            }
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            var self = this;

            // Toggle chatbot
            $(document).on('click', '#chatbot-toggle', function() {
                self.toggleChatbot();
            });

            // Close chatbot
            $(document).on('click', '#chatbot-close', function() {
                self.closeChatbot();
            });

            // Minimize chatbot
            $(document).on('click', '#chatbot-minimize', function() {
                self.minimizeChatbot();
            });

            // Service selection
            $(document).on('click', '.service-btn', function() {
                var service = $(this).data('service');
                self.startService(service);
            });

            // Send message
            $(document).on('click', '#send-message', function() {
                self.sendMessage();
            });

            // Enter key in chat input
            $(document).on('keypress', '#chat-input', function(e) {
                if (e.which === 13) {
                    self.sendMessage();
                }
            });
        },

        /**
         * Toggle chatbot visibility
         */
        toggleChatbot: function() {
            if (this.isOpen) {
                this.closeChatbot();
            } else {
                this.openChatbot();
            }
        },

        /**
         * Open chatbot
         */
        openChatbot: function() {
            $('#chatbot-window').slideDown(300);
            $('#chatbot-toggle').addClass('active');
            this.isOpen = true;
        },

        /**
         * Close chatbot
         */
        closeChatbot: function() {
            $('#chatbot-window').slideUp(300);
            $('#chatbot-toggle').removeClass('active');
            this.isOpen = false;
        },

        /**
         * Minimize chatbot
         */
        minimizeChatbot: function() {
            this.closeChatbot();
        },

        /**
         * Start a specific service
         */
        startService: function(service) {
            $('#service-selection').hide();
            $('#chat-interface').show();

            // Add welcome message for the service
            var welcomeMessage = this.getServiceWelcomeMessage(service);
            this.addMessage(welcomeMessage, true);

            // Initialize conversation for this service
            this.initializeConversation(service);
        },

        /**
         * Get welcome message for service
         */
        getServiceWelcomeMessage: function(service) {
            var messages = {
                'employment': 'Hello! I\'m here to help you create an employment contract. I\'ll need some information about the position and terms. Let\'s start!',
                'nda': 'Hi! I\'ll help you create a non-disclosure agreement. I\'ll need details about the parties and confidential information involved.',
                'business': 'Welcome! I\'ll assist you with business formation. Let\'s discuss what type of business entity you\'d like to create.',
                'will': 'Hello! I\'m here to help you with estate planning. This is an important process, and I\'ll guide you through it step by step.',
                'real-estate': 'Hi! I\'ll help you with real estate documents. Let me know what type of property transaction you\'re working on.',
                'ip': 'Welcome! I\'ll assist you with intellectual property protection. Let\'s discuss what type of IP you need to protect.'
            };

            return messages[service] || 'Hello! How can I help you with your legal document needs?';
        },

        /**
         * Initialize conversation
         */
        initializeConversation: function(service) {
            var self = this;

            // Create a new conversation session
            $.ajax({
                url: '/chatbot/start-conversation',
                method: 'POST',
                data: {
                    service_type: service,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        self.currentConversationId = response.conversation_id;
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Failed to initialize conversation:', error);
                }
            });
        },

        /**
         * Send message
         */
        sendMessage: function() {
            var message = $('#chat-input').val().trim();
            if (!message) return;

            // Clear input
            $('#chat-input').val('');

            // Add user message
            this.addMessage(message, false);

            // Send to AI
            this.sendToAI(message);
        },

        /**
         * Add message to chat
         */
        addMessage: function(message, isAI) {
            // Format message based on type with enhanced checking
            var formattedMessage;
            if (isAI) {
                console.log('Formatting AI message in chatbot_scripts.js:', message);
                if (window.AIMessageFormatter && typeof window.AIMessageFormatter.formatAIMessage === 'function') {
                    formattedMessage = window.AIMessageFormatter.formatAIMessage(message);
                    console.log('Formatted AI message:', formattedMessage);
                } else {
                    console.warn('AIMessageFormatter not available in chatbot_scripts.js, using basic formatting');
                    formattedMessage = this.escapeHtml(message).replace(/\n/g, '<br/>');
                }
            } else {
                formattedMessage = this.escapeHtml(message).replace(/\n/g, '<br/>');
            }

            var messageHtml = `
                <div class="message ${isAI ? 'ai-message' : 'user-message'}">
                    <div class="message-content">
                        <div class="message-text">${formattedMessage}</div>
                        <div class="message-time">${new Date().toLocaleTimeString()}</div>
                    </div>
                </div>
            `;

            $('#chat-messages').append(messageHtml);
            this.scrollToBottom();
        },

        /**
         * Send message to AI
         */
        sendToAI: function(message) {
            var self = this;

            // Show typing indicator
            this.showTypingIndicator();

            $.ajax({
                url: '/chatbot/send-message',
                method: 'POST',
                data: {
                    conversation_id: this.currentConversationId,
                    message: message,
                    csrf_token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(response) {
                    self.hideTypingIndicator();

                    if (response.success) {
                        // Add AI response
                        self.addMessage(response.ai_response, true);

                        // Check if AI wants to show a form
                        if (response.show_form && response.form_data) {
                            self.showForm(response.form_data);
                        }

                        // Check if AI is requesting payment
                        if (response.request_payment && response.payment_info) {
                            self.showPaymentRequest(response.payment_info);
                        }
                    } else {
                        self.addMessage('Sorry, I encountered an error. Please try again.', true);
                    }
                },
                error: function(xhr, status, error) {
                    self.hideTypingIndicator();
                    self.addMessage('Sorry, I\'m having trouble connecting. Please try again.', true);
                    console.error('AI request failed:', error);
                }
            });
        },

        /**
         * Show typing indicator
         */
        showTypingIndicator: function() {
            var typingHtml = `
                <div id="typing-indicator" class="message ai-message">
                    <div class="message-content">
                        <div class="typing-dots">
                            <span></span><span></span><span></span>
                        </div>
                    </div>
                </div>
            `;
            $('#chat-messages').append(typingHtml);
            this.scrollToBottom();
        },

        /**
         * Hide typing indicator
         */
        hideTypingIndicator: function() {
            $('#typing-indicator').remove();
        },

        /**
         * Show form generated by AI
         */
        showForm: function(formData) {
            // This will be implemented to show dynamic forms
            console.log('Form data received:', formData);
            // TODO: Implement form rendering
        },

        /**
         * Show payment request
         */
        showPaymentRequest: function(paymentInfo) {
            var paymentHtml = `
                <div class="payment-request">
                    <div class="payment-info">
                        <h6>Ready to proceed with payment</h6>
                        <p>Service: ${paymentInfo.service_name}</p>
                        <p>Amount: $${paymentInfo.amount}</p>
                        <button class="btn btn-success" onclick="window.open('${paymentInfo.payment_url}', '_blank')">
                            Pay Now
                        </button>
                    </div>
                </div>
            `;
            $('#chat-messages').append(paymentHtml);
            this.scrollToBottom();
        },

        /**
         * Scroll chat to bottom
         */
        scrollToBottom: function() {
            var chatMessages = $('#chat-messages');
            chatMessages.scrollTop(chatMessages[0].scrollHeight);
        },

        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };

    // Language Selection Handler
    var LanguageSelector = {
        selectedLanguage: 'en_US',

        init: function() {
            this.bindEvents();
            this.setDefaultLanguage();
        },

        bindEvents: function() {
            var self = this;

            // Handle language button clicks on main page
            $(document).on('click', '.language-btn', function(e) {
                e.preventDefault();

                var $btn = $(this);
                var langCode = $btn.data('lang');

                // If button has onclick attribute, use that for navigation
                if ($btn.attr('onclick')) {
                    window.location.href = '/legal-assistant?lang=' + langCode;
                    return;
                }

                // Update UI
                $('.language-btn').removeClass('active');
                $btn.addClass('active');

                // Store selection
                self.selectedLanguage = langCode;
                localStorage.setItem('selected_language', langCode);

                // Show feedback
                self.showLanguageSelected($btn.find('span').first().text() || $btn.text());
            });

            // Handle service language selector clicks
            $(document).on('click', '.language-selector-btn', function(e) {
                e.preventDefault();

                var $btn = $(this);
                var langCode = $btn.data('lang');

                // Update UI
                $('.language-selector-btn').removeClass('active');
                $btn.addClass('active');

                // Store selection
                self.selectedLanguage = langCode;
                localStorage.setItem('selected_language', langCode);

                // Show feedback
                self.showLanguageSelected($btn.text());
            });
        },

        setDefaultLanguage: function() {
            // Check for stored language preference
            var storedLang = localStorage.getItem('selected_language');
            if (storedLang) {
                this.selectedLanguage = storedLang;
                $('.language-btn[data-lang="' + storedLang + '"]').addClass('active');
            } else {
                // Default to English
                $('.language-btn[data-lang="en_US"]').addClass('active');
            }
        },

        showLanguageSelected: function(langName) {
            // Create a temporary feedback message
            var $feedback = $('<div class="alert alert-success alert-dismissible fade show mt-3" role="alert">')
                .html('<i class="fa fa-check-circle me-2"></i>Language set to: <strong>' + langName + '</strong>')
                .append('<button type="button" class="btn-close" data-bs-dismiss="alert"></button>');

            // Add to language card
            $('.language-btn').closest('.card').after($feedback);

            // Auto-dismiss after 3 seconds
            setTimeout(function() {
                $feedback.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        },

        getSelectedLanguage: function() {
            return this.selectedLanguage;
        }
    };

    // Initialize chatbot
    AIChatbot.init();

    // Initialize language selector
    LanguageSelector.init();

    // Export for global access
    window.AIChatbot = AIChatbot;
    window.LanguageSelector = LanguageSelector;
});
