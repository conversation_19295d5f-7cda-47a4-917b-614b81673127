/* AI Legal Chatbot Styles - Modern Lawyer Portal */

/* Global Variables */
:root {
    --primary-color: #1a365d;
    --secondary-color: #2d3748;
    --accent-color: #3182ce;
    --success-color: #38a169;
    --warning-color: #d69e2e;
    --danger-color: #e53e3e;
    --light-bg: #f7fafc;
    --dark-bg: #1a202c;
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Enhanced Conversation Styles */
.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    opacity: 0;
    animation-fill-mode: forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes typing {
    0%, 50%, 100% { opacity: 1; }
    25%, 75% { opacity: 0.5; }
}

.user-message {
    justify-content: flex-end;
}

.ai-message {
    justify-content: flex-start;
    position: relative;
}

.ai-message::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: 2px;
    opacity: 0.7;
}

.message-bubble {
    max-width: 85%;
    border-radius: 1.5rem !important;
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    position: relative;
    backdrop-filter: blur(10px);
}

.message-bubble:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.user-message .message-bubble {
    border-bottom-right-radius: 0.5rem !important;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-message .message-bubble {
    border-bottom-left-radius: 0.5rem !important;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

/* AI Avatar */
.ai-message::after {
    content: '🤖';
    position: absolute;
    left: -45px;
    top: 10px;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: var(--shadow-md);
    animation: pulse 2s infinite;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.message-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
}

/* Typing Indicator */
.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.typing-dots .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots .dot:nth-child(1) { animation-delay: 0s; }
.typing-dots .dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dots .dot:nth-child(3) { animation-delay: 0.4s; }

/* Enhanced Survey Form Styles */
.dynamic-form, .o_survey_form {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-top: 1.5rem;
    box-shadow: var(--shadow-xl);
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.dynamic-form::before, .o_survey_form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--success-color));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

.dynamic-form:hover, .o_survey_form:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-4px);
}

.dynamic-form .form-group, .o_survey_form .form-group {
    margin-bottom: 2rem;
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
}

.dynamic-form .form-group:nth-child(1) { animation-delay: 0.1s; }
.dynamic-form .form-group:nth-child(2) { animation-delay: 0.2s; }
.dynamic-form .form-group:nth-child(3) { animation-delay: 0.3s; }
.dynamic-form .form-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dynamic-form label, .o_survey_form label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    font-size: 1rem;
    display: block;
    position: relative;
}

.dynamic-form label::after, .o_survey_form label::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
    transition: width 0.3s ease;
}

.dynamic-form .form-group:focus-within label::after,
.o_survey_form .form-group:focus-within label::after {
    width: 100%;
}

/* Enhanced Chatbot Container */
.legal-chatbot-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--light-bg) 0%, #ffffff 100%);
    position: relative;
}

.legal-chatbot-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Header Styles */
.chatbot-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.chatbot-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.chatbot-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.chatbot-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    background: rgba(255,255,255,0.1);
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 12px;
    height: 12px;
    background: var(--success-color);
    border-radius: 50%;
    margin-right: 0.75rem;
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 0 rgba(56, 161, 105, 0.7);
}

.status-text {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Chat Interface */
.chat-interface {
    padding: 3rem 0;
    position: relative;
    z-index: 1;
}

/* Request Info Card */
.request-info-card {
    background: rgba(255,255,255,0.95);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.request-info-card .card-header {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    color: white;
    padding: 1.5rem;
    border: none;
}

.request-info-card .card-body {
    padding: 1.5rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.status-draft { background: var(--warning-color); color: white; }
.status-in_progress { background: var(--accent-color); color: white; }
.status-completed { background: var(--success-color); color: white; }
.status-cancelled { background: var(--danger-color); color: white; }

/* Conversation Container */
.conversation-container {
    max-height: 600px;
    overflow-y: auto;
    padding: 2rem;
    background: rgba(255,255,255,0.8);
    border-radius: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    scrollbar-width: thin;
    scrollbar-color: var(--accent-color) transparent;
}

.conversation-container::-webkit-scrollbar {
    width: 8px;
}

.conversation-container::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

.conversation-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: 4px;
}

/* Welcome Message */
.welcome-message .message-bubble {
    background: linear-gradient(135deg, rgba(49, 130, 206, 0.1), rgba(26, 54, 93, 0.1));
    border: 2px solid var(--accent-color);
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.quick-action-btn {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    color: white;
    border: none;
    padding: 0.75rem 1.25rem;
    border-radius: 2rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
}

.quick-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* Message Input Container */
.message-input-container {
    background: rgba(255,255,255,0.95);
    border-radius: 1.5rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.message-input-form .input-group {
    border-radius: 2rem;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.message-input {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    background: white;
    resize: none;
    min-height: 50px;
    max-height: 120px;
}

.message-input:focus {
    outline: none;
    box-shadow: none;
}

.send-button {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border: none;
    padding: 1rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.input-footer {
    margin-top: 1rem;
    text-align: center;
}

/* Survey Form Enhancements */
.survey-form-container {
    margin-top: 1.5rem;
}

.survey-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.survey-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.survey-description {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.survey-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.survey-submit-btn, .survey-skip-btn {
    padding: 0.75rem 2rem;
    border-radius: 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.survey-submit-btn {
    background: linear-gradient(135deg, var(--success-color), #2f855a);
    border: none;
    color: white;
}

/* Typing Indicator Styles */
.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.typing-dots .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--accent-color);
    animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dots .dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots .dot:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-dots .dot:nth-child(3) {
    animation-delay: 0s;
}

@keyframes typing-bounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Service Card Styles */
.service-card {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    border: 1px solid var(--border-color);
    cursor: pointer;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--accent-color);
}

.service-icon {
    transition: all 0.3s ease;
}

.service-card:hover .service-icon i {
    transform: scale(1.1);
    color: var(--accent-color) !important;
}

/* Payment Request Styles */
.payment-request {
    background: linear-gradient(135deg, rgba(56, 161, 105, 0.1), rgba(72, 187, 120, 0.1));
    border: 2px solid var(--success-color);
    border-radius: 1rem;
    margin: 1rem 0;
    animation: slideInUp 0.6s ease forwards;
}

.payment-btn {
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    box-shadow: var(--shadow-md);
}

.payment-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* Language Selection Styles */
.language-btn {
    transition: all 0.3s ease;
    border: 2px solid var(--accent-color) !important;
    margin: 0 2px;
    font-weight: 500;
    min-width: 200px;
}

.language-btn:hover {
    background-color: var(--accent-color) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.language-btn.active {
    background-color: var(--accent-color) !important;
    color: white !important;
    border-color: var(--accent-color) !important;
}

.language-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.25) !important;
}

/* Service Language Selector */
.language-selector-btn {
    transition: all 0.2s ease;
    border: 1px solid #dee2e6 !important;
    font-size: 0.875rem;
}

.language-selector-btn:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

.language-selector-btn.active {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

/* Service Count Badge */
.language-btn .badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}

/* Vertical Button Groups */
.btn-group-vertical .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
}

.btn-group-vertical .btn:not(:last-child) {
    margin-bottom: 0.25rem;
}

/* Remove default Bootstrap button group borders */
.btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group > .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatbot-title {
        font-size: 2rem;
    }

    .message-bubble {
        max-width: 95%;
    }

    .conversation-container {
        padding: 1rem;
        max-height: 400px;
    }

    .dynamic-form, .o_survey_form {
        padding: 1.5rem;
    }

    .quick-actions {
        justify-content: center;
    }

    .quick-action-btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .language-btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        margin: 2px;
    }

    .btn-group {
        flex-wrap: wrap;
        justify-content: center;
    }
}

.survey-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .chatbot-title {
        font-size: 2rem;
    }

    .chat-interface {
        padding: 1.5rem 0;
    }

    .conversation-container {
        padding: 1rem;
        max-height: 400px;
    }

    .quick-actions {
        flex-direction: column;
    }

    .quick-action-btn {
        justify-content: center;
    }

    .survey-actions {
        flex-direction: column;
    }

    .ai-message::after {
        display: none;
    }

    .ai-message::before {
        display: none;
    }
}
