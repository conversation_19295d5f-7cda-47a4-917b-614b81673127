<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Message Formatting Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>AI Message Formatting Test</h1>
        <p>This page tests the enhanced AI message formatting functionality.</p>

        <div class="row">
            <div class="col-md-6">
                <h3>Raw AI Response</h3>
                <textarea id="rawMessage" class="form-control" rows="15" placeholder="Enter AI response with markdown formatting...">A Non-Disclosure Agreement (NDA) is a common legal document used to protect confidential information. To create an NDA, I'll need to know a bit more information. Please answer the following questions:

**1. What is the purpose of the NDA?** (e.g., sharing business ideas, discussing a potential partnership, hiring a consultant, etc.)

**2. Who are the parties involved?** (e.g., individual, company, organization, etc.)

**3. What type of confidential information will be shared?** (e.g., trade secrets, business strategies, technical information, etc.)

**4. How long should the NDA remain?** (e.g., specific duration, until a certain event occurs, etc.)

**5. Are there any specific obligations or restrictions you want to include?** (e.g., non-compete clause, non-solicitation clause, etc.)

## Key Features of an NDA:

- **Confidentiality**: Protects sensitive information
- **Duration**: Specifies how long the agreement lasts
- **Scope**: Defines what information is covered
- **Penalties**: Outlines consequences for breach

### Important Notes:

> Always consult with a legal professional before finalizing any legal document.

You can also use `inline code` for specific terms or ```code blocks``` for longer examples.

Once I have this information, I can generate a draft NDA for you. Let me know your answers to these questions, and I'll get started!</textarea>
                <button id="formatBtn" class="btn btn-primary mt-2">Format Message</button>
            </div>
            <div class="col-md-6">
                <h3>Formatted Output</h3>
                <div id="formattedMessage" class="border p-3" style="min-height: 300px; background-color: #f8f9fa;">
                    <em>Click "Format Message" to see the formatted output</em>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>Test Cases</h3>
                <button class="btn btn-secondary me-2" onclick="testBold()">Test Bold</button>
                <button class="btn btn-secondary me-2" onclick="testItalic()">Test Italic</button>
                <button class="btn btn-secondary me-2" onclick="testLists()">Test Lists</button>
                <button class="btn btn-secondary me-2" onclick="testCode()">Test Code</button>
                <button class="btn btn-secondary me-2" onclick="testHeaders()">Test Headers</button>
                <button class="btn btn-success me-2" onclick="testComplex()">Test Complex</button>
            </div>
        </div>
    </div>

    <script src="/ai_chatbot_integration/static/src/js/message_formatter.js"></script>
    <script>
        $(document).ready(function() {
            $('#formatBtn').click(function() {
                var rawMessage = $('#rawMessage').val();
                var formatted = window.AIMessageFormatter.formatAIMessage(rawMessage);
                $('#formattedMessage').html(formatted);
            });
        });

        function testBold() {
            $('#rawMessage').val('This is **bold text** and this is __also bold__. Mixed with *italic* and normal text.');
            $('#formatBtn').click();
        }

        function testItalic() {
            $('#rawMessage').val('This is *italic text* and this is _also italic_. But **bold** should not conflict.');
            $('#formatBtn').click();
        }

        function testLists() {
            $('#rawMessage').val('Here are numbered items:\n1. First item with **bold** text\n2. Second item with *italic* text\n3. Third item with `code`\n\nAnd bullet points:\n- Point one\n- Point two with _emphasis_\n- Point three\n\nMixed content after lists.');
            $('#formatBtn').click();
        }

        function testCode() {
            $('#rawMessage').val('Here is `inline code` and here is a code block:\n\n```javascript\nfunction hello() {\n    console.log("Hello World!");\n    return "formatted";\n}\n```\n\nBack to normal text with `more inline code`.');
            $('#formatBtn').click();
        }

        function testHeaders() {
            $('#rawMessage').val('# Main Header\n\nSome content under main header.\n\n## Sub Header\n\nContent with **bold** and *italic*.\n\n### Sub Sub Header\n\nRegular text follows with:\n1. Numbered list\n2. Another item\n\n- Bullet point\n- Another bullet');
            $('#formatBtn').click();
        }

        function testComplex() {
            $('#rawMessage').val('# Legal Document Analysis\n\n## Overview\n\nThis document contains **important** information about:\n\n1. **Contract Terms**: Key provisions and *conditions*\n2. **Liability**: Understanding your `legal obligations`\n3. **Compliance**: Meeting regulatory requirements\n\n### Key Points:\n\n- Review all sections **carefully**\n- Consult with legal counsel\n- Keep records of all communications\n\n> **Important**: This is not legal advice. Always consult with a qualified attorney.\n\n```\nDocument Status: DRAFT\nLast Modified: 2024-01-01\nVersion: 1.0\n```\n\nFor questions, contact our legal team.');
            $('#formatBtn').click();
        }
    </script>
</body>
</html>
