<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- AI Chatbot Security Groups -->
        <record id="group_ai_chatbot_user" model="res.groups">
            <field name="name">AI Chatbot User</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>
        
        <record id="group_ai_chatbot_manager" model="res.groups">
            <field name="name">AI Chatbot Manager</field>
            <field name="category_id" ref="base.module_category_services"/>
            <field name="implied_ids" eval="[(4, ref('group_ai_chatbot_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
    
    <data noupdate="1">
        <!-- Multi-company rules -->
        <record id="ai_chatbot_config_comp_rule" model="ir.rule">
            <field name="name">AI Chatbot Config: multi-company</field>
            <field name="model_id" ref="model_ai_chatbot_config"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>
