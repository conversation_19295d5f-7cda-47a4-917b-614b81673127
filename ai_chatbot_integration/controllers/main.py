from odoo import http, _
from odoo.http import request
import logging
import json

_logger = logging.getLogger(__name__)


class AIChatbotController(http.Controller):

    @http.route(['/legal-documents/conversation/send'], type='json', auth='user', website=True)
    def send_message(self, **post):
        """Send a message to the AI chatbot and get a response"""
        request_id = int(post.get('request_id', 0))
        message = post.get('message', '')

        if not request_id or not message:
            return {'error': 'Missing parameters'}

        document_request = request.env['legal.document.request'].sudo().browse(request_id)

        # Check if the request exists and belongs to the current user
        if not document_request.exists() or document_request.partner_id.id != request.env.user.partner_id.id:
            return {'error': 'Invalid request'}

        # Create the user message
        conversation = request.env['legal.document.conversation'].sudo().create({
            'request_id': request_id,
            'user_id': request.env.user.id,
            'is_ai': False,
            'message': message,
        })

        # Get or create AI chatbot conversation
        chatbot_conversation = request.env['ai.chatbot.conversation'].sudo().search([
            ('document_request_id', '=', request_id)
        ], limit=1)

        if not chatbot_conversation:
            # Get default configuration
            config = request.env['ai.chatbot.config'].sudo().search([('active', '=', True)], limit=1)
            if not config:
                return {'error': 'No active chatbot configuration found'}

            # Create system prompt based on the service
            service = document_request.service_id
            system_prompt = f"""You are an AI legal assistant helping with {service.name}.

The user has selected the language: {document_request.language_id.name}.

Your task is to gather all necessary information from the user to draft a {service.name} document.

{service.instructions or ''}

When you need to collect specific information, use the form format like this:
```json
{{
  "form": [
    {{
      "type": "text",
      "name": "field_name",
      "label": "Field Label",
      "required": true
    }},
    {{
      "type": "select",
      "name": "another_field",
      "label": "Select Option",
      "options": ["Option 1", "Option 2", "Option 3"],
      "required": false
    }}
  ]
}}
```

Only use the form format when you need to collect specific structured information.
Respond in {document_request.language_id.name} language.
"""

            # Create the chatbot conversation
            chatbot_conversation = request.env['ai.chatbot.conversation'].sudo().create({
                'name': f'Conversation for {document_request.name}',
                'config_id': config.id,
                'document_request_id': request_id,
                'system_prompt': system_prompt,
                'language_id': document_request.language_id.id,
                'conversation_history': '[]',
            })

        # Send message to AI and get response
        response = chatbot_conversation.send_message(message, request.env.user.id)

        if 'error' in response:
            return {'error': response['error']}

        return {
            'success': True,
            'message': response.get('message', ''),
            'has_form': response.get('has_form', False),
            'form_data': response.get('form_data'),
        }

    @http.route(['/legal-documents/conversation/submit-form'], type='json', auth='user', website=True)
    def submit_form(self, **post):
        """Submit a form response to the AI chatbot"""
        request_id = int(post.get('request_id', 0))
        conversation_id = int(post.get('conversation_id', 0))
        form_data = post.get('form_data', {})

        if not request_id or not conversation_id or not form_data:
            return {'error': 'Missing parameters'}

        document_request = request.env['legal.document.request'].sudo().browse(request_id)
        conversation = request.env['legal.document.conversation'].sudo().browse(conversation_id)

        # Check if the request exists and belongs to the current user
        if not document_request.exists() or document_request.partner_id.id != request.env.user.partner_id.id:
            return {'error': 'Invalid request'}

        # Check if the conversation exists and belongs to the request
        if not conversation.exists() or conversation.request_id.id != request_id:
            return {'error': 'Invalid conversation'}

        # Create form response
        form_response = request.env['legal.document.form.response'].sudo().create({
            'request_id': request_id,
            'conversation_id': conversation_id,
            'form_data': json.dumps(conversation.form_data) if conversation.form_data else '{}',
            'response_data': json.dumps(form_data),
        })

        # Format the form response as a message
        message = "Form submitted with the following information:\n\n"
        for field, value in form_data.items():
            message += f"- {field}: {value}\n"

        # Create user message for the form submission
        user_conversation = request.env['legal.document.conversation'].sudo().create({
            'request_id': request_id,
            'user_id': request.env.user.id,
            'is_ai': False,
            'message': message,
        })

        # Get AI chatbot conversation
        chatbot_conversation = request.env['ai.chatbot.conversation'].sudo().search([
            ('document_request_id', '=', request_id)
        ], limit=1)

        if not chatbot_conversation:
            return {'error': 'Chatbot conversation not found'}

        # Send form data to AI and get response
        response = chatbot_conversation.send_message(message, request.env.user.id)

        if 'error' in response:
            return {'error': response['error']}

        return {
            'success': True,
            'message': response.get('message', ''),
            'has_form': response.get('has_form', False),
            'form_data': response.get('form_data'),
        }

    # Website Pages Controllers

    @http.route(['/legal-assistant-test'], type='http', auth='public', website=True)
    def legal_assistant_test(self, **kwargs):
        """Test route to verify basic functionality"""
        return """
        <html>
        <head>
            <title>Chatbot Test</title>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        </head>
        <body>
            <h1>AI Legal Assistant Test Page</h1>
            <div>
                <input type="text" id="test-input" placeholder="Type a message...">
                <button id="test-send">Send Test</button>
            </div>
            <div id="test-result"></div>

            <script>
            $('#test-send').click(function() {
                var message = $('#test-input').val();
                $('#test-result').html('Testing...');

                $.ajax({
                    url: '/chatbot/start-conversation',
                    method: 'POST',
                    data: JSON.stringify({
                        params: {
                            service_type: 'employment',
                            language: 'en_US'
                        }
                    }),
                    contentType: 'application/json',
                    dataType: 'json',
                    success: function(response) {
                        $('#test-result').html('Start conversation result: ' + JSON.stringify(response, null, 2));
                    },
                    error: function(xhr, status, error) {
                        $('#test-result').html('Error: ' + error + ' - ' + xhr.responseText);
                    }
                });
            });
            </script>
        </body>
        </html>
        """

    @http.route(['/legal-services/CONTRACT_DRAFT/test'], type='http', auth='public', website=True)
    def contract_draft_test(self, **kwargs):
        """Simple test page for CONTRACT_DRAFT"""
        return """
        <html>
        <head>
            <title>Contract Draft Test</title>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        </head>
        <body>
            <h1>Contract Draft Chatbot Test</h1>
            <div>
                <input type="text" id="test-input" placeholder="Type a message..." style="width: 300px;">
                <button id="test-send">Send</button>
            </div>
            <div id="test-result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

            <script>
            console.log('=== CONTRACT DRAFT TEST PAGE ===');

            $('#test-send').click(function() {
                console.log('Test send clicked');
                var message = $('#test-input').val();
                $('#test-result').html('Testing start conversation...');

                $.ajax({
                    url: '/chatbot/start-conversation',
                    method: 'POST',
                    data: JSON.stringify({
                        params: {
                            service_type: 'CONTRACT_DRAFT',
                            language: 'en_US'
                        }
                    }),
                    contentType: 'application/json',
                    dataType: 'json',
                    success: function(response) {
                        console.log('Start conversation response:', response);
                        $('#test-result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        $('#test-result').html('Error: ' + error + '<br>Response: ' + xhr.responseText);
                    }
                });
            });
            </script>
        </body>
        </html>
        """

    @http.route(['/legal-assistant'], type='http', auth='public', website=True)
    def legal_assistant_page(self, **kwargs):
        """Main AI Legal Assistant page"""
        # Get selected language from URL parameter
        try:
            selected_language = request.httprequest.args.get('lang', 'en_US')
        except:
            selected_language = 'en_US'

        # Static services data for now
        all_services_data = [
            {
                'name': 'Employment Contract Drafting',
                'description': 'Professional employment agreements and contracts',
                'code': 'CONTRACT_DRAFT',
                'price': 299,
                'language_ids': [
                    {'name': 'English', 'code': 'en_US'},
                    {'name': 'हिंदी', 'code': 'hi_IN'},
                    {'name': 'Español', 'code': 'es_ES'}
                ],
            },
            {
                'name': 'Non-Disclosure Agreement',
                'description': 'Protect your confidential information',
                'code': 'NDA',
                'price': 199,
                'language_ids': [
                    {'name': 'English', 'code': 'en_US'},
                    {'name': 'Español', 'code': 'es_ES'}
                ],
            },
            {
                'name': 'Business Partnership Agreement',
                'description': 'Legal agreements for business partnerships',
                'code': 'PARTNERSHIP',
                'price': 399,
                'language_ids': [
                    {'name': 'English', 'code': 'en_US'},
                    {'name': 'हिंदी', 'code': 'hi_IN'}
                ],
            },
            {
                'name': 'Rental Agreement',
                'description': 'Property rental and lease agreements',
                'code': 'RENTAL',
                'price': 249,
                'language_ids': [
                    {'name': 'English', 'code': 'en_US'},
                    {'name': 'हिंदी', 'code': 'hi_IN'},
                    {'name': 'Español', 'code': 'es_ES'},
                    {'name': 'Français', 'code': 'fr_FR'}
                ],
            },
            {
                'name': 'Service Agreement',
                'description': 'Professional service contracts and agreements',
                'code': 'SERVICE',
                'price': 279,
                'language_ids': [
                    {'name': 'English', 'code': 'en_US'},
                    {'name': 'Español', 'code': 'es_ES'},
                    {'name': 'Français', 'code': 'fr_FR'}
                ],
            },
            {
                'name': 'Custom Legal Document',
                'description': 'Tailored legal documents for specific requirements',
                'code': 'CUSTOM',
                'price': 499,
                'language_ids': [
                    {'name': 'English', 'code': 'en_US'},
                    {'name': 'हिंदी', 'code': 'hi_IN'},
                    {'name': 'Español', 'code': 'es_ES'},
                    {'name': 'Français', 'code': 'fr_FR'}
                ],
                'is_custom': True
            }
        ]

        # Filter services based on selected language
        services_data = []
        for service in all_services_data:
            # Check if service supports the selected language
            supported_languages = [lang['code'] for lang in service['language_ids']]
            if selected_language in supported_languages:
                services_data.append(service)

        # Calculate service counts for each language
        language_counts = {}
        for lang_code in ['en_US', 'hi_IN', 'es_ES', 'fr_FR']:
            count = 0
            for service in all_services_data:
                supported_languages = [lang['code'] for lang in service['language_ids']]
                if lang_code in supported_languages:
                    count += 1
            language_counts[lang_code] = count

        # Language list with active state and correct counts
        language_list = [
            {'code': 'en_US', 'name': 'English', 'service_count': language_counts['en_US'], 'is_selected': selected_language == 'en_US'},
            {'code': 'hi_IN', 'name': 'हिंदी', 'service_count': language_counts['hi_IN'], 'is_selected': selected_language == 'hi_IN'},
            {'code': 'es_ES', 'name': 'Español', 'service_count': language_counts['es_ES'], 'is_selected': selected_language == 'es_ES'},
            {'code': 'fr_FR', 'name': 'Français', 'service_count': language_counts['fr_FR'], 'is_selected': selected_language == 'fr_FR'},
        ]

        values = {
            'services': services_data,
            'languages': language_list,
            'selected_language': selected_language,
            'page_title': 'AI Legal Assistant',
            'all_services': all_services_data,  # For debugging
        }

        return request.render('ai_chatbot_integration.legal_assistant_page', values)

    @http.route(['/legal-services'], type='http', auth='public', website=True)
    def legal_services_page(self, **kwargs):
        """Legal Services overview page"""
        # Get available services with categories
        services = request.env['legal.document.service'].sudo().search([('active', '=', True)])

        # Group services by type
        service_groups = {}
        for service in services:
            service_type = service.service_type_id.name if service.service_type_id else 'Other'
            if service_type not in service_groups:
                service_groups[service_type] = []
            service_groups[service_type].append(service)

        values = {
            'services': services,
            'service_groups': service_groups,
            'page_title': 'Legal Services',
        }

        return request.render('ai_chatbot_integration.legal_services_page', values)

    @http.route(['/legal-services/ai-assistant'], type='http', auth='public', website=True)
    def ai_assistant_service_page(self, **kwargs):
        """AI Assistant service selection page"""
        # Get available services
        services = request.env['legal.document.service'].sudo().search([('active', '=', True)])

        values = {
            'services': services,
            'page_title': 'AI Legal Document Assistant',
        }

        return request.render('ai_chatbot_integration.ai_assistant_service_page', values)

    @http.route(['/legal-services/<string:service_type>'], type='http', auth='public', website=True)
    def service_specific_page(self, service_type, **kwargs):
        """Service-specific chatbot page"""
        _logger.info("=== SERVICE PAGE DEBUG ===")
        _logger.info("Service type requested: %s", service_type)
        _logger.info("Kwargs: %s", kwargs)

        # Map service types to service codes
        service_mapping = {
            'employment': 'employment',
            'nda': 'NDA',
            'business-formation': 'BIZ_FORM',
            'will': 'WILL_PREP',
            'real-estate': 'LEASE_AGR',
            'ip': 'IP_PROTECT',
            'CONTRACT_DRAFT': 'CONTRACT_DRAFT',
            'NDA': 'NDA',
            'BIZ_FORM': 'BIZ_FORM',
            'WILL_PREP': 'WILL_PREP',
            'LEASE_AGR': 'LEASE_AGR',
            'POA': 'POA'
        }

        service_code = service_mapping.get(service_type, 'CONTRACT_DRAFT')

        # Get the specific service with product relationship
        service = request.env['legal.document.service'].sudo().search([
            ('code', '=', service_code)
        ], limit=1)

        if not service:
            # Get first available service as fallback
            service = request.env['legal.document.service'].sudo().search([('active', '=', True)], limit=1)

        # Ensure product_id and language_ids are loaded
        if service:
            service.read(['name', 'description', 'product_id', 'language_ids'])

        # Get available languages for this service
        service_languages = []
        if service and service.language_ids:
            for lang in service.language_ids:
                service_languages.append({
                    'code': lang.code,
                    'name': lang.name,
                })

        values = {
            'service': service,
            'service_type': service_type,
            'service_languages': service_languages,
            'page_title': f'{service.name if service else "Legal Service"} - AI Assistant',
        }

        _logger.info("Template values: %s", values)
        _logger.info("About to render template: ai_chatbot_integration.service_chatbot_page")

        try:
            result = request.render('ai_chatbot_integration.service_chatbot_page', values)
            _logger.info("Template rendered successfully")
            return result
        except Exception as e:
            _logger.error("Template rendering failed: %s", str(e))
            return {'error': f'Template rendering failed: {str(e)}'}

    @http.route(['/chatbot/test'], type='http', auth='public', website=True)
    def test_route(self, **post):
        """Test route to check if HTTP is working"""
        _logger.info("=== TEST ROUTE CALLED ===")
        _logger.info("Post data: %s", post)
        return f"Test route working! Data: {post}"

    @http.route(['/chatbot/start-conversation-http'], type='http', auth='public', website=True, methods=['POST'])
    def start_conversation_http(self, **post):
        """HTTP version of start conversation for testing"""
        _logger.info("=== START CONVERSATION HTTP ===")
        _logger.info("Post data: %s", post)

        import json
        response = {
            'success': True,
            'conversation_id': 999,
            'document_request_id': 999,
            'service_name': 'Test Service',
            'welcome_message': 'Hello! This is a test response. The form parsing is ready to work!'
        }

        return json.dumps(response)

    @http.route(['/chatbot/start-conversation'], type='json', auth='public', website=True)
    def start_conversation(self, **post):
        """Start a new chatbot conversation"""
        _logger.info("=== START CONVERSATION DEBUG ===")
        _logger.info("Post data: %s", post)

        try:
            service_type = post.get('service_type', '')
            language = post.get('language', 'en_US')

            _logger.info("Service type: %s", service_type)
            _logger.info("Language: %s", language)

            # Get or create user
            if request.env.user._is_public():
                # For public users, create a temporary session
                session_id = getattr(request.session, 'sid', 'anonymous')
                partner = request.env['res.partner'].sudo().create({
                    'name': 'Website Visitor',
                    'email': f'visitor_{session_id}@temp.com',
                    'is_company': False,
                })
                _logger.info("Created temporary partner: %s (ID: %s)", partner.name, partner.id)
            else:
                partner = request.env.user.partner_id
                _logger.info("Using existing partner: %s (ID: %s)", partner.name, partner.id)

            # Get service
            service = request.env['legal.document.service'].sudo().search([
                ('code', '=', service_type)
            ], limit=1)

            if not service:
                service = request.env['legal.document.service'].sudo().search([('active', '=', True)], limit=1)

            if not service:
                return {'error': 'No services available'}

            _logger.info("Found service: %s (ID: %s)", service.name, service.id)

            # Get language
            lang = request.env['res.lang'].sudo().search([('code', '=', language)], limit=1)
            if not lang:
                lang = request.env['res.lang'].sudo().search([('code', '=', 'en_US')], limit=1)

            if not lang:
                return {'error': 'No language available'}

            _logger.info("Found language: %s (ID: %s)", lang.name, lang.id)

            # Create document request
            document_request = request.env['legal.document.request'].sudo().create({
                'partner_id': partner.id,
                'service_id': service.id,
                'language_id': lang.id,
                'state': 'draft',
            })
            _logger.info("Document request created: ID=%s", document_request.id)

            # Get chatbot config - prioritize service-specific config
            config = service.chatbot_config_id
            if not config:
                # Fallback to any active configuration
                config = request.env['ai.chatbot.config'].sudo().search([('active', '=', True)], limit=1)

            if not config:
                return {'error': 'No chatbot configuration available'}

            _logger.info("Using chatbot config: ID=%s, Name=%s, API Type=%s",
                        config.id, config.name, config.api_type)
            _logger.info("API Key: %s...", config.api_key[:20] if config.api_key else 'None')

            # Create chatbot conversation
            conversation = request.env['ai.chatbot.conversation'].sudo().create({
                'name': f'Website Chat - {service.name}',
                'config_id': config.id,
                'document_request_id': document_request.id,
                'system_prompt': f"You are an AI legal assistant for {service.name}. Help create legal documents by asking questions and generating forms when needed.",
                'language_id': lang.id,
                'conversation_history': '[]',
            })
            _logger.info("Conversation created: ID=%s", conversation.id)

            return {
                'success': True,
                'conversation_id': conversation.id,
                'document_request_id': document_request.id,
                'service_name': service.name,
                'welcome_message': f"Hello! I'm your AI legal assistant for {service.name}. How can I help you today?"
            }

        except Exception as e:
            _logger.error("Error in start_conversation: %s", str(e))
            import traceback
            _logger.error("Traceback: %s", traceback.format_exc())
            return {'error': f'Start conversation failed: {str(e)}'}



    @http.route(['/chatbot/send-message'], type='json', auth='public', website=True)
    def send_chatbot_message(self, **post):
        """Send message to chatbot and get AI response"""
        conversation_id = int(post.get('conversation_id', 0))
        message = post.get('message', '')

        if not conversation_id or not message:
            return {'error': 'Missing parameters'}

        # Get conversation
        conversation = request.env['ai.chatbot.conversation'].sudo().browse(conversation_id)
        if not conversation.exists():
            return {'error': 'Conversation not found'}

        # Send message and get AI response
        try:
            _logger.info("=== SEND MESSAGE DEBUG ===")
            _logger.info("Conversation ID: %s", conversation_id)
            _logger.info("Message: %s", message)
            _logger.info("Conversation exists: %s", conversation.exists())
            _logger.info("Conversation config: %s", conversation.config_id.name if conversation.config_id else 'None')

            response = conversation.send_message(message, request.env.user.id)
            _logger.info("Response from send_message: %s", response)

            # Check if response contains a form
            has_form = False
            form_data = None
            ai_response = response.get('message', '')
            _logger.info("AI Response extracted: %s", ai_response)

            # Parse form from AI response
            if 'FORM_START' in ai_response and 'FORM_END' in ai_response:
                try:
                    form_start = ai_response.find('FORM_START') + len('FORM_START')
                    form_end = ai_response.find('FORM_END')
                    form_json = ai_response[form_start:form_end].strip()
                    form_data = json.loads(form_json)
                    has_form = True

                    # Remove form from AI response
                    ai_response = ai_response[:ai_response.find('FORM_START')] + ai_response[form_end + len('FORM_END'):]
                    ai_response = ai_response.strip()
                except:
                    pass

            # Check if AI is requesting payment
            request_payment = False
            payment_info = None
            if any(keyword in ai_response.lower() for keyword in ['payment', 'pay now', 'proceed with payment', '$']):
                request_payment = True
                service = conversation.document_request_id.service_id
                payment_info = {
                    'service_name': service.name,
                    'amount': service.price or 299,
                    'payment_url': f'/payment/process/{conversation.document_request_id.id}'
                }

            return {
                'success': True,
                'ai_response': ai_response,
                'show_form': has_form,
                'form_data': form_data,
                'request_payment': request_payment,
                'payment_info': payment_info
            }

        except Exception as e:
            return {'error': f'Failed to get AI response: {str(e)}'}

    @http.route(['/my/legal-documents'], type='http', auth='user', website=True)
    def my_legal_documents(self, **kwargs):
        """User's legal documents portal"""
        # Get user's document requests
        document_requests = request.env['legal.document.request'].search([
            ('partner_id', '=', request.env.user.partner_id.id)
        ])

        values = {
            'document_requests': document_requests,
            'page_title': 'My Legal Documents',
        }

        return request.render('ai_chatbot_integration.my_legal_documents_page', values)

    @http.route('/chatbot/test-formatting', type='http', auth='public', website=True)
    def test_formatting_page(self, **kwargs):
        """Test page for message formatting - serves the static HTML file"""
        import os
        from odoo.modules import get_module_path

        # Get the path to the static test file
        module_path = get_module_path('ai_chatbot_integration')
        test_file_path = os.path.join(module_path, 'static', 'test_formatting.html')

        try:
            with open(test_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            return f"Error loading test file: {str(e)}"