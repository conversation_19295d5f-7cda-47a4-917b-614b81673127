{
    'name': 'AI Chatbot Integration',
    'version': '1.0',
    'category': 'Tools',
    'summary': 'AI Chatbot integration with Grok3 API for legal document drafting',
    'description': """
        AI Chatbot Integration Module
        ============================

        This module provides AI chatbot integration with Grok3 API for legal document drafting.

        Features:
        - Modern, minimal chatbot UI
        - Integration with Grok3 API
        - Context-aware conversation handling
        - Support for dynamic form generation
        - Multi-language support
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'base',
        'web',
        'website',
        'mail',
        'ai_legal_document_core',
    ],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/ai_chatbot_views.xml',
        'views/ai_chatbot_config_views.xml',
        'views/templates.xml',
        'views/portal_chatbot_templates.xml',
        'views/website_templates.xml',
        'views/menu_views.xml',
        'data/ai_chatbot_data.xml',
    ],
    'assets': {
        'website.assets_frontend': [
            'ai_chatbot_integration/static/src/css/chatbot_styles.css',
            # Load message formatter first to ensure it's available
            ('prepend', 'ai_chatbot_integration/static/src/js/message_formatter.js'),
            'ai_chatbot_integration/static/src/js/chatbot_scripts.js',
            'ai_chatbot_integration/static/src/js/enhanced_chatbot.js',
            'ai_chatbot_integration/static/src/js/service_chatbot.js',
            'ai_chatbot_integration/static/src/js/chatbot_main.js',
        ],
        'web.assets_backend': [
            'ai_chatbot_integration/static/src/css/chatbot_styles.css',
            'ai_chatbot_integration/static/src/js/message_formatter.js',
        ],
    },
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
