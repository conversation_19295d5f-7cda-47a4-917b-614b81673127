/* FontAwesome Icon Fallback CSS for RTI Module */

/* Ensure FontAwesome is loaded */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Fallback styles for common icons used in RTI module */
.fa, .fas, .far, .fal, .fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "FontAwesome", sans-serif;
    font-weight: 900;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* Specific icon fallbacks with Unicode characters */
.fa-check-circle::before {
    content: "\f058";
}

.fa-exclamation-circle::before {
    content: "\f06a";
}

.fa-exclamation-triangle::before {
    content: "\f071";
}

.fa-info-circle::before {
    content: "\f05a";
}

.fa-download::before {
    content: "\f019";
}

.fa-times-circle::before {
    content: "\f057";
}

.fa-arrow-left::before {
    content: "\f060";
}

.fa-spinner::before {
    content: "\f110";
}

.fa-chevron-right::before {
    content: "\f054";
}

.fa-chevron-left::before {
    content: "\f053";
}

.fa-check::before {
    content: "\f00c";
}

/* Spinner animation */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Size classes */
.fa-5x {
    font-size: 5em;
}

.fa-3x {
    font-size: 3em;
}

.fa-2x {
    font-size: 2em;
}

.fa-lg {
    font-size: 1.33333em;
    line-height: 0.75em;
    vertical-align: -0.0667em;
}

.fa-sm {
    font-size: 0.875em;
}

.fa-xs {
    font-size: 0.75em;
}

/* Text color utilities */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* Margin utilities for icons */
.me-1, .mr-1 {
    margin-right: 0.25rem !important;
}

.me-2, .mr-2 {
    margin-right: 0.5rem !important;
}

.ms-2, .ml-2 {
    margin-left: 0.5rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

/* Button icon spacing */
.btn .fa {
    margin-right: 0.5rem;
}

.btn .fa:last-child {
    margin-right: 0;
    margin-left: 0.5rem;
}

/* Ensure icons are visible in all contexts */
.fa:before {
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    line-height: 1em;
    margin-left: 0.2em;
    margin-right: 0.2em;
    font-size: inherit;
    text-rendering: auto;
}

/* Fix for missing icons - show text fallback */
.fa-check-circle:not(:before) {
    content: "✓";
}

.fa-exclamation-circle:not(:before) {
    content: "!";
}

.fa-info-circle:not(:before) {
    content: "i";
}

.fa-download:not(:before) {
    content: "↓";
}
