#!/usr/bin/env python3
"""
Test script for AI Module Generator
Tests the complete module generation workflow
"""

import xmlrpc.client
import sys
import os
import time

def test_module_generation():
    """Test the AI Module Generator by creating a new module"""
    
    print("🚀 Starting AI Module Generator Test")
    print("=" * 50)
    
    # Connection details
    url = 'http://localhost:8069'
    db = 'oneclickvakil.com'  # Try the main database first
    username = 'admin'
    password = 'admin'
    
    try:
        # Connect to Odoo
        print("📡 Connecting to Odoo...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed")
            return False
            
        print(f"✅ Connected successfully (UID: {uid})")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Check if AI Module Generator is installed
        print("🔍 Checking AI Module Generator installation...")
        module_ids = models.execute_kw(db, uid, password,
            'ir.module.module', 'search',
            [[('name', '=', 'ai_module_generator'), ('state', '=', 'installed')]])
        
        if not module_ids:
            print("❌ AI Module Generator is not installed")
            return False
            
        print("✅ AI Module Generator is installed")
        
        # Get available templates
        print("📋 Fetching available templates...")
        template_ids = models.execute_kw(db, uid, password,
            'module.template', 'search',
            [[('state', '=', 'active')]])
        
        if not template_ids:
            print("❌ No active templates found")
            return False
            
        templates = models.execute_kw(db, uid, password,
            'module.template', 'read',
            [template_ids], {'fields': ['name', 'code', 'description']})
        
        print(f"✅ Found {len(templates)} active templates:")
        for template in templates:
            print(f"  - {template['name']} ({template['code']})")
        
        # Use the first template for testing
        test_template = templates[0]
        print(f"\n🎯 Using template: {test_template['name']}")
        
        # Create module generator wizard
        print("🧙 Creating module generator wizard...")
        wizard_data = {
            'template_id': test_template['id'],
            'module_name': 'test_booking_system',
            'module_title': 'Test Booking System',
            'module_description': 'Test module generated by AI Module Generator',
            'module_author': 'Oneclickvakil',
            'module_version': '1.0.0',
            'module_category': 'Services',
            'custom_prefix': 'ovakil',
            'include_website': True,
            'include_tests': True,
            'include_demo_data': True,
        }
        
        wizard_id = models.execute_kw(db, uid, password,
            'module.generator.wizard', 'create', [wizard_data])
        
        print(f"✅ Wizard created (ID: {wizard_id})")
        
        # Generate the module
        print("⚙️ Generating module...")
        result = models.execute_kw(db, uid, password,
            'module.generator.wizard', 'action_generate_module', [wizard_id])
        
        print("✅ Module generation completed!")
        
        # Check if module was created
        module_path = '/mnt/extra-addons/ovakil_test_booking_system'
        if os.path.exists(module_path):
            print(f"✅ Module directory created: {module_path}")
            
            # Check key files
            key_files = [
                '__manifest__.py',
                'models/__init__.py',
                'views/views.xml',
                'views/website_templates.xml',
                'controllers/main.py',
                'controllers/portal.py',
                'security/ir.model.access.csv',
                'data/data.xml',
                'data/cron_jobs.xml'
            ]
            
            print("📁 Checking generated files:")
            for file_path in key_files:
                full_path = os.path.join(module_path, file_path)
                if os.path.exists(full_path):
                    print(f"  ✅ {file_path}")
                else:
                    print(f"  ❌ {file_path} (MISSING)")
            
            # Check template syntax in website_templates.xml
            print("\n🔍 Checking template syntax...")
            template_file = os.path.join(module_path, 'views/website_templates.xml')
            if os.path.exists(template_file):
                with open(template_file, 'r') as f:
                    content = f.read()
                
                # Check for our fixes
                if 'get_field_display' in content:
                    print("  ❌ Still contains get_field_display (should be fixed)")
                else:
                    print("  ✅ No get_field_display found (correctly fixed)")
                
                if '<t t-field=' in content:
                    print("  ❌ Contains t-field on <t> elements (should be fixed)")
                else:
                    print("  ✅ No t-field on <t> elements (correctly fixed)")
                
                # Count proper t-field usage
                import re
                proper_t_field = len(re.findall(r'<(?!t)[^>]*t-field=', content))
                print(f"  ✅ Found {proper_t_field} properly placed t-field directives")
            
            print(f"\n🎉 Module generation test completed successfully!")
            print(f"📦 Generated module: ovakil_test_booking_system")
            print(f"📂 Location: {module_path}")
            
            return True
            
        else:
            print(f"❌ Module directory not found: {module_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_module_generation()
    sys.exit(0 if success else 1)
