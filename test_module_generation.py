#!/usr/bin/env python3
"""
Test script to verify AI Module Generator preserves all fixes and functionality
"""

import os
import sys
import subprocess
import time
import requests
import json

def test_module_generation():
    """Test that AI Module Generator preserves all fixes"""
    
    print("🧪 TESTING AI MODULE GENERATOR FIXES PRESERVATION")
    print("=" * 60)
    
    # Test module name
    test_module_name = "test_ai_fixes_verification"
    test_module_path = f"/mnt/extra-addons/{test_module_name}"
    
    print(f"📋 Test Module: {test_module_name}")
    print(f"📁 Test Path: {test_module_path}")
    
    # Check if test module already exists and remove it
    if os.path.exists(test_module_path):
        print(f"🗑️  Removing existing test module...")
        subprocess.run(["rm", "-rf", test_module_path])
    
    # Test data for module generation
    test_data = {
        "module_name": test_module_name,
        "module_title": "Test AI Fixes Verification",
        "module_description": "Test module to verify all AI Module Generator fixes are preserved",
        "fields": [
            {
                "name": "customer_name",
                "label": "Customer Name", 
                "field_type": "char",
                "required": True
            },
            {
                "name": "customer_email",
                "label": "Email Address",
                "field_type": "email", 
                "required": True
            },
            {
                "name": "assigned_user",
                "label": "Assigned User",
                "field_type": "many2one",
                "relation_model": "res.users",
                "widget": "many2one_dropdown",
                "required": False
            },
            {
                "name": "priority",
                "label": "Priority",
                "field_type": "selection",
                "selection_options": [
                    ("low", "Low"),
                    ("medium", "Medium"), 
                    ("high", "High"),
                    ("urgent", "Urgent")
                ],
                "required": True
            },
            {
                "name": "feedback_comments",
                "label": "Comments",
                "field_type": "text",
                "required": False
            },
            {
                "name": "is_active",
                "label": "Is Active",
                "field_type": "boolean",
                "default": True,
                "required": False
            }
        ]
    }
    
    print(f"📝 Test Fields: {len(test_data['fields'])} fields defined")
    
    # Test checklist
    tests = {
        "2_fields_per_row": False,
        "no_gradients_css": False,
        "proper_headings": False,
        "api_endpoints": False,
        "many2one_dropdown": False,
        "enhanced_js": False,
        "fallback_mechanism": False,
        "submit_button_position": False
    }
    
    print("\n🔍 VERIFICATION CHECKLIST:")
    print("-" * 30)
    
    # Note: Since we can't programmatically generate modules through the web interface,
    # we'll verify the AI Module Generator code directly
    
    # Test 1: Check 2 fields per row functionality
    print("1️⃣  Testing 2 fields per row layout...")
    wizard_file = "/mnt/extra-addons/ai_module_generator/wizards/module_generator_wizard.py"
    
    if os.path.exists(wizard_file):
        with open(wizard_file, 'r') as f:
            content = f.read()
            if "_add_fields_with_row_layout" in content and "col-md-6" in content:
                tests["2_fields_per_row"] = True
                print("   ✅ 2 fields per row layout method found")
            else:
                print("   ❌ 2 fields per row layout method missing")
    
    # Test 2: Check CSS without gradients
    print("2️⃣  Testing CSS without gradients...")
    css_file = "/mnt/extra-addons/ai_module_generator/static/src/css/enhanced_forms.css"
    
    if os.path.exists(css_file):
        with open(css_file, 'r') as f:
            css_content = f.read()
            if "linear-gradient" not in css_content:
                tests["no_gradients_css"] = True
                print("   ✅ CSS has no gradients")
            else:
                print("   ❌ CSS still contains gradients")
    
    # Test 3: Check proper heading styles
    print("3️⃣  Testing proper heading styles...")
    if os.path.exists(css_file):
        with open(css_file, 'r') as f:
            css_content = f.read()
            if ".form-title" in css_content and "font-weight: 600" in css_content:
                tests["proper_headings"] = True
                print("   ✅ Proper heading styles found")
            else:
                print("   ❌ Proper heading styles missing")
    
    # Test 4: Check API endpoints
    print("4️⃣  Testing API endpoints...")
    api_file = "/mnt/extra-addons/ai_module_generator/controllers/api.py"
    
    if os.path.exists(api_file):
        with open(api_file, 'r') as f:
            api_content = f.read()
            if "/api/users/list" in api_content and "/api/users/search" in api_content:
                tests["api_endpoints"] = True
                print("   ✅ API endpoints found")
            else:
                print("   ❌ API endpoints missing")
    
    # Test 5: Check many2one dropdown generation
    print("5️⃣  Testing many2one dropdown generation...")
    if os.path.exists(wizard_file):
        with open(wizard_file, 'r') as f:
            content = f.read()
            if "_generate_many2one_field_html" in content and "many2one_dropdown" in content:
                tests["many2one_dropdown"] = True
                print("   ✅ Many2one dropdown generation found")
            else:
                print("   ❌ Many2one dropdown generation missing")
    
    # Test 6: Check enhanced JavaScript
    print("6️⃣  Testing enhanced JavaScript...")
    js_file = "/mnt/extra-addons/ai_module_generator/static/src/js/enhanced_forms.js"
    
    if os.path.exists(js_file):
        with open(js_file, 'r') as f:
            js_content = f.read()
            if "populateAssignedUserDropdown" in js_content and "fetch('/api/users/list" in js_content:
                tests["enhanced_js"] = True
                print("   ✅ Enhanced JavaScript found")
            else:
                print("   ❌ Enhanced JavaScript missing")
    
    # Test 7: Check fallback mechanism
    print("7️⃣  Testing fallback mechanism...")
    if os.path.exists(js_file):
        with open(js_file, 'r') as f:
            js_content = f.read()
            if "showFallbackUsers" in js_content and "Administrator" in js_content:
                tests["fallback_mechanism"] = True
                print("   ✅ Fallback mechanism found")
            else:
                print("   ❌ Fallback mechanism missing")
    
    # Test 8: Check submit button positioning
    print("8️⃣  Testing submit button positioning...")
    if os.path.exists(wizard_file):
        with open(wizard_file, 'r') as f:
            content = f.read()
            if "form-submit-section" in content and "btn-submit-enhanced" in content:
                tests["submit_button_position"] = True
                print("   ✅ Submit button positioning found")
            else:
                print("   ❌ Submit button positioning missing")
    
    # Calculate results
    passed_tests = sum(tests.values())
    total_tests = len(tests)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n📊 TEST RESULTS:")
    print("=" * 30)
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 ALL TESTS PASSED! AI Module Generator preserves all fixes!")
    elif success_rate >= 80:
        print("⚠️  Most tests passed, minor issues detected")
    else:
        print("❌ Multiple issues detected, fixes may not be preserved")
    
    print(f"\n📋 DETAILED RESULTS:")
    print("-" * 30)
    for test_name, result in tests.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    return success_rate == 100

if __name__ == "__main__":
    success = test_module_generation()
    sys.exit(0 if success else 1)
